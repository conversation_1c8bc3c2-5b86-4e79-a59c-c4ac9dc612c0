// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		7C2BD7D32DE148380082180E /* Auth in Frameworks */ = {isa = PBXBuildFile; productRef = 7C2BD7D22DE148380082180E /* Auth */; };
		7C2BD7D52DE1483B0082180E /* Functions in Frameworks */ = {isa = PBXBuildFile; productRef = 7C2BD7D42DE1483B0082180E /* Functions */; };
		7C2BD7D72DE1483E0082180E /* PostgREST in Frameworks */ = {isa = PBXBuildFile; productRef = 7C2BD7D62DE1483E0082180E /* PostgREST */; };
		7C2BD7D92DE148400082180E /* Realtime in Frameworks */ = {isa = PBXBuildFile; productRef = 7C2BD7D82DE148400082180E /* Realtime */; };
		7C2BD7DB2DE148430082180E /* Storage in Frameworks */ = {isa = PBXBuildFile; productRef = 7C2BD7DA2DE148430082180E /* Storage */; };
		7C2BD7DD2DE148450082180E /* Supabase in Frameworks */ = {isa = PBXBuildFile; productRef = 7C2BD7DC2DE148450082180E /* Supabase */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		7C2BD7902DE03D870082180E /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 7C2BD7732DE03D850082180E /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 7C2BD77A2DE03D850082180E;
			remoteInfo = NIRA;
		};
		7C2BD79A2DE03D870082180E /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 7C2BD7732DE03D850082180E /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 7C2BD77A2DE03D850082180E;
			remoteInfo = NIRA;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		7C2BD77B2DE03D850082180E /* NIRA.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = NIRA.app; sourceTree = BUILT_PRODUCTS_DIR; };
		7C2BD78F2DE03D870082180E /* NIRATests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = NIRATests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		7C2BD7992DE03D870082180E /* NIRAUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = NIRAUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		7C2BD7A12DE03D870082180E /* Exceptions for "NIRA" folder in "NIRA" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = 7C2BD77A2DE03D850082180E /* NIRA */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		7C2BD77D2DE03D850082180E /* NIRA */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				7C2BD7A12DE03D870082180E /* Exceptions for "NIRA" folder in "NIRA" target */,
			);
			path = NIRA;
			sourceTree = "<group>";
		};
		7C2BD7922DE03D870082180E /* NIRATests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = NIRATests;
			sourceTree = "<group>";
		};
		7C2BD79C2DE03D870082180E /* NIRAUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = NIRAUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		7C2BD7782DE03D850082180E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				7C2BD7D72DE1483E0082180E /* PostgREST in Frameworks */,
				7C2BD7D52DE1483B0082180E /* Functions in Frameworks */,
				7C2BD7DD2DE148450082180E /* Supabase in Frameworks */,
				7C2BD7D32DE148380082180E /* Auth in Frameworks */,
				7C2BD7DB2DE148430082180E /* Storage in Frameworks */,
				7C2BD7D92DE148400082180E /* Realtime in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7C2BD78C2DE03D870082180E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7C2BD7962DE03D870082180E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		7C2BD7722DE03D850082180E = {
			isa = PBXGroup;
			children = (
				7C2BD77D2DE03D850082180E /* NIRA */,
				7C2BD7922DE03D870082180E /* NIRATests */,
				7C2BD79C2DE03D870082180E /* NIRAUITests */,
				7C2BD7D12DE148380082180E /* Frameworks */,
				7C2BD77C2DE03D850082180E /* Products */,
			);
			sourceTree = "<group>";
		};
		7C2BD77C2DE03D850082180E /* Products */ = {
			isa = PBXGroup;
			children = (
				7C2BD77B2DE03D850082180E /* NIRA.app */,
				7C2BD78F2DE03D870082180E /* NIRATests.xctest */,
				7C2BD7992DE03D870082180E /* NIRAUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		7C2BD7D12DE148380082180E /* Frameworks */ = {
			isa = PBXGroup;
			children = (
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		7C2BD77A2DE03D850082180E /* NIRA */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 7C2BD7A22DE03D870082180E /* Build configuration list for PBXNativeTarget "NIRA" */;
			buildPhases = (
				7C2BD7772DE03D850082180E /* Sources */,
				7C2BD7782DE03D850082180E /* Frameworks */,
				7C2BD7792DE03D850082180E /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				7C2BD77D2DE03D850082180E /* NIRA */,
			);
			name = NIRA;
			packageProductDependencies = (
				7C2BD7D22DE148380082180E /* Auth */,
				7C2BD7D42DE1483B0082180E /* Functions */,
				7C2BD7D62DE1483E0082180E /* PostgREST */,
				7C2BD7D82DE148400082180E /* Realtime */,
				7C2BD7DA2DE148430082180E /* Storage */,
				7C2BD7DC2DE148450082180E /* Supabase */,
			);
			productName = NIRA;
			productReference = 7C2BD77B2DE03D850082180E /* NIRA.app */;
			productType = "com.apple.product-type.application";
		};
		7C2BD78E2DE03D870082180E /* NIRATests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 7C2BD7A72DE03D870082180E /* Build configuration list for PBXNativeTarget "NIRATests" */;
			buildPhases = (
				7C2BD78B2DE03D870082180E /* Sources */,
				7C2BD78C2DE03D870082180E /* Frameworks */,
				7C2BD78D2DE03D870082180E /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				7C2BD7912DE03D870082180E /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				7C2BD7922DE03D870082180E /* NIRATests */,
			);
			name = NIRATests;
			packageProductDependencies = (
			);
			productName = NIRATests;
			productReference = 7C2BD78F2DE03D870082180E /* NIRATests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		7C2BD7982DE03D870082180E /* NIRAUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 7C2BD7AA2DE03D870082180E /* Build configuration list for PBXNativeTarget "NIRAUITests" */;
			buildPhases = (
				7C2BD7952DE03D870082180E /* Sources */,
				7C2BD7962DE03D870082180E /* Frameworks */,
				7C2BD7972DE03D870082180E /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				7C2BD79B2DE03D870082180E /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				7C2BD79C2DE03D870082180E /* NIRAUITests */,
			);
			name = NIRAUITests;
			packageProductDependencies = (
			);
			productName = NIRAUITests;
			productReference = 7C2BD7992DE03D870082180E /* NIRAUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		7C2BD7732DE03D850082180E /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1620;
				LastUpgradeCheck = 1620;
				TargetAttributes = {
					7C2BD77A2DE03D850082180E = {
						CreatedOnToolsVersion = 16.2;
					};
					7C2BD78E2DE03D870082180E = {
						CreatedOnToolsVersion = 16.2;
						TestTargetID = 7C2BD77A2DE03D850082180E;
					};
					7C2BD7982DE03D870082180E = {
						CreatedOnToolsVersion = 16.2;
						TestTargetID = 7C2BD77A2DE03D850082180E;
					};
				};
			};
			buildConfigurationList = 7C2BD7762DE03D850082180E /* Build configuration list for PBXProject "NIRA" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 7C2BD7722DE03D850082180E;
			minimizedProjectReferenceProxies = 1;
			packageReferences = (
				7C2BD7D02DE147C80082180E /* XCRemoteSwiftPackageReference "supabase-swift" */,
			);
			preferredProjectObjectVersion = 77;
			productRefGroup = 7C2BD77C2DE03D850082180E /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				7C2BD77A2DE03D850082180E /* NIRA */,
				7C2BD78E2DE03D870082180E /* NIRATests */,
				7C2BD7982DE03D870082180E /* NIRAUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		7C2BD7792DE03D850082180E /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7C2BD78D2DE03D870082180E /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7C2BD7972DE03D870082180E /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		7C2BD7772DE03D850082180E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7C2BD78B2DE03D870082180E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7C2BD7952DE03D870082180E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		7C2BD7912DE03D870082180E /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 7C2BD77A2DE03D850082180E /* NIRA */;
			targetProxy = 7C2BD7902DE03D870082180E /* PBXContainerItemProxy */;
		};
		7C2BD79B2DE03D870082180E /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 7C2BD77A2DE03D850082180E /* NIRA */;
			targetProxy = 7C2BD79A2DE03D870082180E /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		7C2BD7A32DE03D870082180E /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = NIRA/NIRA.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"NIRA/Preview Content\"";
				DEVELOPMENT_TEAM = QNW477Q52S;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = NIRA/Info.plist;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.md.NIRA;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		7C2BD7A42DE03D870082180E /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = NIRA/NIRA.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"NIRA/Preview Content\"";
				DEVELOPMENT_TEAM = QNW477Q52S;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = NIRA/Info.plist;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.md.NIRA;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		7C2BD7A52DE03D870082180E /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		7C2BD7A62DE03D870082180E /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		7C2BD7A82DE03D870082180E /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = QNW477Q52S;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.md.NIRATests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/NIRA.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/NIRA";
			};
			name = Debug;
		};
		7C2BD7A92DE03D870082180E /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = QNW477Q52S;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.md.NIRATests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/NIRA.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/NIRA";
			};
			name = Release;
		};
		7C2BD7AB2DE03D870082180E /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = QNW477Q52S;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.md.NIRAUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = NIRA;
			};
			name = Debug;
		};
		7C2BD7AC2DE03D870082180E /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = QNW477Q52S;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.md.NIRAUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = NIRA;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		7C2BD7762DE03D850082180E /* Build configuration list for PBXProject "NIRA" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7C2BD7A52DE03D870082180E /* Debug */,
				7C2BD7A62DE03D870082180E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		7C2BD7A22DE03D870082180E /* Build configuration list for PBXNativeTarget "NIRA" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7C2BD7A32DE03D870082180E /* Debug */,
				7C2BD7A42DE03D870082180E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		7C2BD7A72DE03D870082180E /* Build configuration list for PBXNativeTarget "NIRATests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7C2BD7A82DE03D870082180E /* Debug */,
				7C2BD7A92DE03D870082180E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		7C2BD7AA2DE03D870082180E /* Build configuration list for PBXNativeTarget "NIRAUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7C2BD7AB2DE03D870082180E /* Debug */,
				7C2BD7AC2DE03D870082180E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		7C2BD7D02DE147C80082180E /* XCRemoteSwiftPackageReference "supabase-swift" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/supabase/supabase-swift";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 2.5.1;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		7C2BD7D22DE148380082180E /* Auth */ = {
			isa = XCSwiftPackageProductDependency;
			package = 7C2BD7D02DE147C80082180E /* XCRemoteSwiftPackageReference "supabase-swift" */;
			productName = Auth;
		};
		7C2BD7D42DE1483B0082180E /* Functions */ = {
			isa = XCSwiftPackageProductDependency;
			package = 7C2BD7D02DE147C80082180E /* XCRemoteSwiftPackageReference "supabase-swift" */;
			productName = Functions;
		};
		7C2BD7D62DE1483E0082180E /* PostgREST */ = {
			isa = XCSwiftPackageProductDependency;
			package = 7C2BD7D02DE147C80082180E /* XCRemoteSwiftPackageReference "supabase-swift" */;
			productName = PostgREST;
		};
		7C2BD7D82DE148400082180E /* Realtime */ = {
			isa = XCSwiftPackageProductDependency;
			package = 7C2BD7D02DE147C80082180E /* XCRemoteSwiftPackageReference "supabase-swift" */;
			productName = Realtime;
		};
		7C2BD7DA2DE148430082180E /* Storage */ = {
			isa = XCSwiftPackageProductDependency;
			package = 7C2BD7D02DE147C80082180E /* XCRemoteSwiftPackageReference "supabase-swift" */;
			productName = Storage;
		};
		7C2BD7DC2DE148450082180E /* Supabase */ = {
			isa = XCSwiftPackageProductDependency;
			package = 7C2BD7D02DE147C80082180E /* XCRemoteSwiftPackageReference "supabase-swift" */;
			productName = Supabase;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = 7C2BD7732DE03D850082180E /* Project object */;
}
