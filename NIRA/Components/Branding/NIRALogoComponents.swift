//
//  NIRALogoComponents.swift
//  NIRA
//
//  Created by NIRA Team on 2025-01-29.
//

import SwiftUI

// MARK: - NIRA Logo Components

struct NIRALogo: View {
    let size: LogoSize
    let style: LogoStyle
    let animated: Bool
    
    @State private var animationOffset: CGFloat = 0
    @State private var glowIntensity: Double = 0.5
    
    init(size: LogoSize = .medium, style: LogoStyle = .full, animated: Bool = false) {
        self.size = size
        self.style = style
        self.animated = animated
    }
    
    var body: some View {
        Group {
            switch style {
            case .full:
                fullLogo
            case .icon:
                iconOnly
            case .text:
                textOnly
            case .minimal:
                minimalLogo
            }
        }
        .onAppear {
            if animated {
                startAnimation()
            }
        }
    }
    
    private var fullLogo: some View {
        HStack(spacing: size.spacing) {
            logoIcon
            logoText
        }
    }
    
    private var iconOnly: some View {
        logoIcon
    }
    
    private var textOnly: some View {
        logoText
    }
    
    private var minimalLogo: some View {
        VStack(spacing: 4) {
            logoIcon
                .scaleEffect(0.8)
            Text("NIRA")
                .font(.system(size: size.fontSize * 0.6, weight: .bold, design: .rounded))
                .foregroundColor(.niraPrimary)
        }
    }
    
    private var logoIcon: some View {
        ZStack {
            // Background circle with gradient
            Circle()
                .fill(
                    LinearGradient(
                        colors: [.niraPrimary, .niraSecondary],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .frame(width: size.iconSize, height: size.iconSize)
                .shadow(color: .niraPrimary.opacity(0.3), radius: 8, x: 0, y: 4)
            
            // Inner design - Language learning symbol
            VStack(spacing: 2) {
                // Globe/World symbol
                Image(systemName: "globe")
                    .font(.system(size: size.iconSize * 0.3, weight: .medium))
                    .foregroundColor(.white)
                
                // Chat bubbles for conversation
                HStack(spacing: 1) {
                    Circle()
                        .fill(Color.white.opacity(0.8))
                        .frame(width: 3, height: 3)
                    Circle()
                        .fill(Color.white.opacity(0.6))
                        .frame(width: 2, height: 2)
                    Circle()
                        .fill(Color.white.opacity(0.4))
                        .frame(width: 1, height: 1)
                }
                .offset(x: animated ? animationOffset : 0)
            }
            
            // Glow effect for premium feel
            if animated {
                Circle()
                    .stroke(Color.white.opacity(0.3), lineWidth: 2)
                    .frame(width: size.iconSize + 4, height: size.iconSize + 4)
                    .opacity(glowIntensity)
            }
        }
    }
    
    private var logoText: some View {
        VStack(alignment: .leading, spacing: 2) {
            Text("NIRA")
                .font(.system(size: size.fontSize, weight: .bold, design: .rounded))
                .foregroundColor(.niraPrimary)
                .tracking(1.2)
            
            Text("Language Learning")
                .font(.system(size: size.fontSize * 0.35, weight: .medium, design: .rounded))
                .foregroundColor(.niraSecondary)
                .tracking(0.5)
        }
    }
    
    private func startAnimation() {
        withAnimation(.easeInOut(duration: 2.0).repeatForever(autoreverses: true)) {
            animationOffset = 3
        }
        
        withAnimation(.easeInOut(duration: 1.5).repeatForever(autoreverses: true)) {
            glowIntensity = 1.0
        }
    }
}

// MARK: - Logo Configurations

enum LogoSize {
    case small, medium, large, extraLarge
    
    var iconSize: CGFloat {
        switch self {
        case .small: return 24
        case .medium: return 40
        case .large: return 60
        case .extraLarge: return 80
        }
    }
    
    var fontSize: CGFloat {
        switch self {
        case .small: return 16
        case .medium: return 24
        case .large: return 32
        case .extraLarge: return 40
        }
    }
    
    var spacing: CGFloat {
        switch self {
        case .small: return 8
        case .medium: return 12
        case .large: return 16
        case .extraLarge: return 20
        }
    }
}

enum LogoStyle {
    case full, icon, text, minimal
}

// MARK: - Specialized Logo Variants

struct NIRAAppIcon: View {
    let size: CGFloat
    
    var body: some View {
        ZStack {
            // App icon background
            RoundedRectangle(cornerRadius: size * 0.2)
                .fill(
                    LinearGradient(
                        colors: [.niraPrimary, .niraSecondary, .niraAccent],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .frame(width: size, height: size)
            
            // Icon content
            VStack(spacing: size * 0.05) {
                Image(systemName: "globe")
                    .font(.system(size: size * 0.25, weight: .medium))
                    .foregroundColor(.white)
                
                Text("NIRA")
                    .font(.system(size: size * 0.15, weight: .bold, design: .rounded))
                    .foregroundColor(.white)
                    .tracking(1)
            }
        }
        .shadow(color: .black.opacity(0.2), radius: size * 0.05, x: 0, y: size * 0.02)
    }
}

struct NIRASplashLogo: View {
    @State private var scale: CGFloat = 0.8
    @State private var opacity: Double = 0
    @State private var rotation: Double = 0
    
    var body: some View {
        VStack(spacing: 20) {
            NIRALogo(size: .extraLarge, style: .icon, animated: true)
                .scaleEffect(scale)
                .opacity(opacity)
                .rotationEffect(.degrees(rotation))
            
            VStack(spacing: 8) {
                Text("NIRA")
                    .font(.system(size: 48, weight: .bold, design: .rounded))
                    .foregroundColor(.niraPrimary)
                    .tracking(2)
                    .opacity(opacity)
                
                Text("Master Languages Through Culture")
                    .font(.system(size: 18, weight: .medium, design: .rounded))
                    .foregroundColor(.niraSecondary)
                    .opacity(opacity)
            }
        }
        .onAppear {
            withAnimation(.spring(response: 1.0, dampingFraction: 0.6).delay(0.2)) {
                scale = 1.0
                opacity = 1.0
            }
            
            withAnimation(.easeInOut(duration: 0.8).delay(0.5)) {
                rotation = 360
            }
        }
    }
}

// MARK: - Brand Colors Extension

extension Color {
    static let niraBrand = Color("NiraPrimary")
    static let niraSecondaryBrand = Color("NiraSecondary")
    static let niraAccentBrand = Color("NiraAccent")
    
    // Brand gradient combinations
    static let niraPrimaryGradient = [Color.niraPrimary, Color.niraSecondary]
    static let niraAccentGradient = [Color.niraAccent, Color.niraPrimary]
    static let niraNeutralGradient = [Color.niraSecondary, Color.gray.opacity(0.3)]
}

#Preview("Full Logo") {
    VStack(spacing: 30) {
        NIRALogo(size: .small, style: .full)
        NIRALogo(size: .medium, style: .full, animated: true)
        NIRALogo(size: .large, style: .full)
        
        HStack(spacing: 20) {
            NIRALogo(size: .medium, style: .icon)
            NIRALogo(size: .medium, style: .text)
            NIRALogo(size: .medium, style: .minimal)
        }
        
        NIRAAppIcon(size: 120)
    }
    .padding()
}
