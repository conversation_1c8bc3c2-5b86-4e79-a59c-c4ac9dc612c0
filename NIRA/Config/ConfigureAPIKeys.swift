//
//  ConfigureAPIKeys.swift
//  NIRA
//
//  Created for API Key Configuration
//

import Foundation

// MARK: - API Key Configuration Helper
// This file helps you configure your real API keys securely

#if DEBUG
struct APIKeyConfigurator {
    
    // MARK: - Configuration Method
    
    /// Configure your real API keys here
    /// This method should only be called once during development setup
    static func configureProductionKeys() {
        print("🔧 Configuring production API keys...")
        
        // ⚠️ REPLACE THESE WITH YOUR REAL API KEYS ⚠️
        
        // 1. 🔑 GEMINI API KEY
        // Get from: https://ai.google.dev/tutorials/setup
        let geminiAPIKey = "AIzaSyAYasSocME9nPr7HP625EEx4cofwm5MH3Q"
        
        // 2. 🗄️ SUPABASE URL
        // Get from: https://app.supabase.com/project/your-project/settings/api
        let supabaseURL = "https://lyaojebttnqilmdosmjk.supabase.co"
        
        // 3. 🔐 SUPABASE ANON KEY
        // Get from: https://app.supabase.com/project/your-project/settings/api
        let supabaseAnonKey = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"
        
        // 4. 🤖 OPENAI API KEY (Optional)
        // Get from: https://platform.openai.com/api-keys
        let openAIAPIKey = "***********************************************************************************************************************************************************************" // Optional
        
        // ⚠️ VALIDATION CHECKS ⚠️
        guard !geminiAPIKey.contains("YOUR_REAL_") else {
            print("❌ Please replace YOUR_REAL_GEMINI_API_KEY_HERE with your actual Gemini API key")
            return
        }
        
        guard !supabaseURL.contains("your-project-id") else {
            print("❌ Please replace your-project-id with your actual Supabase project ID")
            return
        }
        
        guard !supabaseAnonKey.contains("YOUR_REAL_") else {
            print("❌ Please replace YOUR_REAL_SUPABASE_ANON_KEY_HERE with your actual Supabase anon key")
            return
        }
        
        // Store keys securely in keychain
        do {
            try SecureAPIKeys.storeAPIKeys(
                gemini: geminiAPIKey,
                supabaseURL: supabaseURL,
                supabaseKey: supabaseAnonKey,
                openAI: openAIAPIKey.contains("YOUR_REAL_") ? nil : openAIAPIKey
            )
            
            print("✅ API keys stored securely in keychain!")
            print("✅ All features will now be activated")
            
            // Validate configuration
            try SecureAPIKeys.validateConfiguration()
            print("✅ Configuration validated successfully")
            
        } catch {
            print("❌ Error storing API keys: \(error)")
        }
    }
    
    // MARK: - Status Check
    
    static func checkCurrentConfiguration() {
        print("\n🔍 Current API Key Configuration Status:")
        print("=====================================")
        
        let geminiConfigured = !SecureAPIKeys.geminiAPIKey.isEmpty
        let supabaseURLConfigured = !SecureAPIKeys.supabaseURL.isEmpty
        let supabaseKeyConfigured = !SecureAPIKeys.supabaseAnonKey.isEmpty
        let openAIConfigured = !SecureAPIKeys.openAIAPIKey.isEmpty
        
        print("🔑 Gemini API Key: \(geminiConfigured ? "✅ Configured" : "❌ Missing")")
        print("🗄️ Supabase URL: \(supabaseURLConfigured ? "✅ Configured" : "❌ Missing")")
        print("🔐 Supabase Anon Key: \(supabaseKeyConfigured ? "✅ Configured" : "❌ Missing")")
        print("🤖 OpenAI API Key: \(openAIConfigured ? "✅ Configured" : "⚠️ Optional")")
        
        let isFullyConfigured = SecureAPIKeys.isConfigured
        print("\n🎯 Overall Status: \(isFullyConfigured ? "✅ READY FOR PRODUCTION" : "⚠️ DEVELOPMENT MODE")")
        
        if !isFullyConfigured {
            print("\n📝 To activate all features:")
            print("1. Edit ConfigureAPIKeys.swift")
            print("2. Replace placeholder values with real API keys")
            print("3. Call APIKeyConfigurator.configureProductionKeys()")
            print("4. Restart the app")
        }
    }
    
    // MARK: - Clear Configuration (for testing)
    
    static func clearAllKeys() {
        print("🗑️ Clearing all stored API keys...")
        // This would require implementing a clear method in SecureAPIKeys
        print("⚠️ Manual keychain clearing required for security")
    }
}

// MARK: - Quick Setup Instructions

/*
 🚀 QUICK SETUP GUIDE:
 
 1. GET YOUR API KEYS:
    • Gemini: https://ai.google.dev/tutorials/setup
    • Supabase: https://app.supabase.com/project/your-project/settings/api
    • OpenAI (optional): https://platform.openai.com/api-keys
 
 2. REPLACE PLACEHOLDERS ABOVE:
    • Replace "YOUR_REAL_GEMINI_API_KEY_HERE" with your actual Gemini key
    • Replace "your-project-id" with your actual Supabase project ID
    • Replace "YOUR_REAL_SUPABASE_ANON_KEY_HERE" with your actual Supabase anon key
 
 3. RUN CONFIGURATION:
    • Add this line to your app startup code:
      APIKeyConfigurator.configureProductionKeys()
    • Or call it from Xcode console during debugging
 
 4. VERIFY SETUP:
    • Call APIKeyConfigurator.checkCurrentConfiguration()
    • Look for "✅ READY FOR PRODUCTION" status
 
 5. SECURITY NOTES:
    • Keys are stored securely in iOS Keychain
    • This file can be safely committed (no actual keys stored here)
    • Keys persist between app launches
    • Only accessible to your app
 */

#endif
