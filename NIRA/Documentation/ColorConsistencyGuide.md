# NIRA Color Consistency Guide

## Overview
This guide ensures consistent color usage across all NIRA app views and components, maintaining the premium European-style design aesthetic.

## Color System Architecture

### Primary Theme Colors
```swift
// Core NIRA Theme Colors (defined in ColorExtensions.swift)
.niraThemeBlue      // #2563EB - Primary brand color
.niraThemeTeal      // #0891B2 - Secondary brand color  
.niraThemeCyan      // #06B6D4 - Accent color
.niraThemeIndigo    // #4F46E5 - Deep accent color
.niraThemeSuccess   // #059669 - Success states
.niraThemeWarning   // #EA580C - Warning states
```

### CEFR Level Color Progression
```swift
// A1 → C2 Color Progression
.levelA1Color       // Light Blue (Beginner)
.levelA2Color       // Cyan (Elementary)
.levelB1Color       // Blue (Intermediate)
.levelB2Color       // Teal (Upper Intermediate)
.levelC1Color       // Indigo (Advanced)
.levelC2Color       // Purple (Proficiency)
```

### Asset Catalog Colors
```swift
// Dark mode adaptive colors
.cardBackground     // "NiraCard"
.primaryText        // "NiraPrimaryText"
.secondaryText      // "NiraSecondaryText"
.glassBackground    // "NiraOverlay"
```

## View-Specific Color Usage

### 1. HomeView (Dashboard)
- **Background**: Dynamic European gradients (Alpine, Mediterranean, Tuscan, Swiss)
- **Cards**: `.cardBackground` with `.niraGradientStart/.niraGradientEnd`
- **Quick Actions**: Emoji colors (`.emojiPurple`, `.emojiBlue`, `.emojiGreen`)
- **Text**: `.primaryText` and `.secondaryText`

### 2. LessonsView
- **Header**: `.niraThemeBlue` gradient background
- **Level Chips**: CEFR level color progression
- **Cards**: `.cardBackground` with level-specific gradients
- **Filters**: `.niraThemeBlue` for language, level-specific for difficulty

### 3. SimulationsView
- **Background**: `.niraThemeTeal.opacity(0.05)` gradient
- **Language Filter**: `.niraThemeBlue` chips
- **Category Filter**: `.niraThemeTeal` chips
- **Cards**: Difficulty-based color progression
- **Benefits**: `.niraThemeTeal` accents

### 4. AgentsView
- **Background**: `.niraThemeIndigo.opacity(0.05)` gradient
- **Header**: `.niraThemeIndigo` for language display
- **Agent Cards**: Personality-based colors with gradients
- **Empty State**: `.niraThemeIndigo` accents

## Component Color Standards

### ModernLessonCard
```swift
// Uses CEFR level color system
private var levelColor: Color {
    return Color.getLevelColor(for: lesson.difficultyLevel ?? 1)
}

private var levelGradient: [Color] {
    return Color.getLevelGradient(for: lesson.difficultyLevel ?? 1)
}
```

### ModernSimulationCard
```swift
// Difficulty-based color progression
private var difficultyColor: Color {
    switch simulation.difficultyLevel {
    case 1: return .levelA1Color
    case 2: return .levelA2Color
    // ... continues through C2
    }
}
```

### ModernAgentCard
```swift
// Personality-based color mapping
private var personalityColor: Color {
    switch agent.personality.lowercased() {
    case "friendly": return .emojiGreen
    case "professional": return .niraThemeBlue
    case "casual": return .emojiOrange
    case "formal": return .niraThemeIndigo
    // ... etc
    }
}
```

## Language-Specific Colors
```swift
// Consistent language color mapping
.englishColor       // levelA2Color
.spanishColor       // niraThemeTeal
.frenchColor        // niraThemeBlue
.germanColor        // niraThemeCyan
.italianColor       // niraThemeIndigo
.portugueseColor    // levelB1Color
.japaneseColor      // levelC1Color
.tamilColor         // levelA1Color
```

## Gradient Usage Patterns

### European-Inspired Gradients
```swift
.alpineGradient         // Night/Dark themes
.mediterraneanGradient  // Afternoon themes
.tuscanGradient        // Evening themes
.swissGradient         // Morning themes
.parisianGradient      // Special occasions
.nordicGradient        // Minimalist themes
```

### Level-Specific Gradients
```swift
.levelA1Gradient    // Light Blue → Cyan
.levelA2Gradient    // Cyan → Teal
.levelB1Gradient    // Blue → Teal
.levelB2Gradient    // Teal → Indigo
.levelC1Gradient    // Indigo → Purple
.levelC2Gradient    // Purple → Deep Purple
```

## Best Practices

### 1. Always Use Helper Functions
```swift
// ✅ Good - Uses helper function
Color.getLevelColor(for: difficultyLevel)

// ❌ Bad - Hardcoded color
Color.blue
```

### 2. Maintain Semantic Meaning
- **Blue family**: Primary actions, lessons, learning
- **Teal family**: Secondary actions, simulations, practice
- **Indigo family**: AI agents, advanced features
- **Green family**: Success, completion, achievements
- **Orange family**: Warnings, streaks, energy

### 3. Respect Dark Mode
```swift
// ✅ Good - Uses adaptive colors
.foregroundColor(.primaryText)

// ❌ Bad - Fixed color
.foregroundColor(.black)
```

### 4. Consistent Opacity Usage
- **Background overlays**: 0.05 - 0.1
- **Card shadows**: 0.1 - 0.2
- **Interactive states**: 0.2 - 0.3
- **Disabled states**: 0.5 - 0.6

## Implementation Checklist

### New Components
- [ ] Uses ColorExtensions.swift colors
- [ ] Implements appropriate gradients
- [ ] Supports dark mode
- [ ] Follows semantic color meaning
- [ ] Uses helper functions for level colors

### Existing Components
- [ ] Replace hardcoded colors with theme colors
- [ ] Update gradients to use NIRA system
- [ ] Ensure dark mode compatibility
- [ ] Test color accessibility
- [ ] Verify semantic consistency

## Color Accessibility

### Contrast Ratios
- **Primary text**: Minimum 4.5:1 contrast
- **Secondary text**: Minimum 3:1 contrast
- **Interactive elements**: Minimum 3:1 contrast
- **Focus indicators**: Minimum 3:1 contrast

### Testing Tools
- Use Xcode Accessibility Inspector
- Test with VoiceOver enabled
- Verify in both light and dark modes
- Check with color blindness simulators

## Future Considerations

### Theming Support
The color system is designed to support future theming:
- User-selectable color schemes
- Cultural/regional color preferences
- Accessibility-focused high contrast themes
- Seasonal or event-based themes

### Expansion Guidelines
When adding new colors:
1. Add to ColorExtensions.swift
2. Follow semantic naming conventions
3. Provide both single colors and gradients
4. Test in light and dark modes
5. Update this documentation
