# NIRA MCP Integration Guide

## Overview

NIRA leverages Model Context Protocol (MCP) servers to enhance development capabilities and provide advanced features. This document outlines the integration of ElevenLabs and 21st.dev Magic MCP servers and their role in NIRA's development and functionality.

## What is MCP?

Model Context Protocol (MCP) is an open standard that enables AI assistants to securely connect to external data sources and tools. MCP servers provide standardized interfaces for AI models to interact with various services, APIs, and tools.

## Integrated MCP Servers

### 1. ElevenLabs MCP Server

#### What it is
The official ElevenLabs MCP server provides access to advanced text-to-speech, voice cloning, and audio processing capabilities through a standardized interface.

#### Installation & Configuration
```json
{
  "mcpServers": {
    "ElevenLabs": {
      "command": "/Users/<USER>/.local/bin/uvx",
      "args": ["elevenlabs-mcp"],
      "env": {
        "ELEVENLABS_API_KEY": "***************************************************"
      }
    }
  }
}
```

#### Core Capabilities
- **Text-to-Speech**: Convert text to natural-sounding speech in multiple languages
- **Voice Cloning**: Create custom voices from audio samples
- **Speech-to-Speech**: Transform voice characteristics and styles
- **Speech-to-Text**: Transcribe audio with speaker identification
- **Sound Effects**: Generate custom audio effects from text descriptions
- **Audio Isolation**: Extract clean audio from recordings
- **Voice Design**: Create unique voice variations and previews
- **AI Agents**: Build conversational AI with voice capabilities
- **Phone Integration**: Enable outbound calls with AI agents

### 2. 21st.dev Cline MCP Server

#### What it is
21st.dev Cline is a comprehensive development MCP server that provides access to modern development tools, code generation, and project management capabilities.

#### Installation & Configuration
```json
{
  "mcpServers": {
    "21st-dev-magic": {
      "command": "npx",
      "args": ["@21st-dev/magic"],
      "env": {
        "MAGIC_API_KEY": "d3c18f0df32d547b2f2d036443c4bab0b240fd94b0e6ce505953c6a6d88ab019"
      }
    }
  }
}
```

#### Core Capabilities
- **Component Builder**: Generate modern React/SwiftUI components
- **Component Inspiration**: Browse and adapt existing UI patterns
- **Component Refiner**: Improve and optimize existing components
- **Logo Search**: Access professional logos in JSX/TSX/SVG formats
- **Design System**: Consistent styling and component libraries
- **UI/UX Analysis**: Comprehensive interface evaluation
- **Accessibility Audit**: WCAG compliance checking
- **Performance Optimization**: Component and rendering improvements

### 3. Supabase MCP Server

#### What it is
Supabase MCP server provides direct integration with Supabase database, authentication, and storage services through a standardized interface.

#### Installation & Configuration
```json
{
  "mcpServers": {
    "supabase": {
      "command": "npx",
      "args": ["-y", "@supabase/mcp-server@latest"],
      "env": {
        "SUPABASE_URL": "https://lyaojebttnqilmdosmjk.supabase.co",
        "SUPABASE_ANON_KEY": "your_supabase_anon_key"
      }
    }
  }
}
```

#### Core Capabilities
- **Database Operations**: Direct SQL queries and table management
- **Authentication**: User management and session handling
- **Storage**: File upload and management
- **Real-time**: Live data synchronization
- **Edge Functions**: Serverless function execution

### 4. Sequential Thinking MCP Server

#### What it is
Sequential Thinking MCP server enhances AI reasoning capabilities by providing structured thinking processes and step-by-step problem solving.

#### Installation & Configuration
```json
{
  "mcpServers": {
    "sequential-thinking": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-sequential-thinking@latest"]
    }
  }
}
```

#### Core Capabilities
- **Step-by-step Reasoning**: Break down complex problems
- **Logical Flow**: Maintain coherent thought processes
- **Decision Trees**: Structured decision making
- **Problem Decomposition**: Split complex tasks into manageable parts

### 5. Internet Search MCP Server

#### What it is
Internet Search MCP server provides real-time web search capabilities for accessing current information and research.

#### Installation & Configuration
```json
{
  "mcpServers": {
    "internet-search": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-brave-search@latest"],
      "env": {
        "BRAVE_API_KEY": "your_brave_api_key"
      }
    }
  }
}
```

#### Core Capabilities
- **Web Search**: Real-time internet search results
- **News Search**: Current events and news articles
- **Academic Search**: Research papers and scholarly content
- **Image Search**: Visual content discovery
- **Safe Search**: Content filtering and safety

## How NIRA Uses These MCP Servers

### ElevenLabs Integration in NIRA

#### 1. Multilingual Voice Content
```swift
// Example: Generate pronunciation guides
func generatePronunciationGuide(for word: String, language: Language) {
    // Uses ElevenLabs TTS to create audio pronunciation
    // Supports all 50 NIRA languages across 3 tiers
}
```

#### 2. AI Conversation Partners
- **Tier 1 Languages**: Full voice-enabled AI agents for conversation practice
- **Cultural Context**: Native speaker voices for authentic pronunciation
- **Persona-Based Voices**: Different voices for each simulation persona
- **Real-time Conversation**: Live voice chat with AI language partners

#### 3. Immersive Audio Scenarios
- **Cultural Sound Effects**: Ambient audio for realistic simulations
- **Pronunciation Feedback**: Voice analysis and correction
- **Audio Lessons**: Spoken explanations and instructions
- **Storytelling**: Narrative-driven language learning content

#### 4. Voice Cloning for Authenticity
- **Native Speaker Voices**: Clone authentic regional accents
- **Consistent Characters**: Maintain voice consistency across lessons
- **Cultural Authenticity**: Preserve linguistic nuances and intonation

### 21st.dev Magic Integration in NIRA

#### 1. UI Component Development
```swift
// Example: Enhanced language selection interface
struct LanguageSelectionView: View {
    // Built using 21st.dev Magic components
    // Professional, accessible, and performant
}
```

#### 2. Design System Enhancement
- **Consistent Branding**: Professional logo variations and brand assets
- **Component Library**: Reusable UI elements across the app
- **Accessibility Compliance**: WCAG AA standard adherence
- **Performance Optimization**: Smooth animations and interactions

#### 3. Competitive Analysis
- **UI/UX Benchmarking**: Compare against Duolingo, Babbel, and competitors
- **Modern Design Patterns**: Implement cutting-edge interface designs
- **User Experience Optimization**: Data-driven UX improvements
- **Cross-platform Consistency**: Unified design across iOS, Android, and web

#### 4. Rapid Prototyping
- **Component Generation**: Quick creation of new interface elements
- **Design Iteration**: Fast refinement of existing components
- **A/B Testing**: Multiple design variations for optimization
- **Responsive Design**: Adaptive layouts for all screen sizes

## Development Workflow Integration

### Phase 1: Design & Planning
1. **21st.dev Magic**: Generate initial UI mockups and components
2. **Competitive Analysis**: Benchmark against industry leaders
3. **Accessibility Planning**: Ensure inclusive design from start

### Phase 2: Content Creation
1. **ElevenLabs**: Generate voice content for all 50 languages
2. **Audio Asset Creation**: Sound effects and ambient audio
3. **Voice Character Development**: Consistent persona voices

### Phase 3: Implementation
1. **Component Integration**: Implement 21st.dev Magic components
2. **Voice Integration**: Add ElevenLabs audio capabilities
3. **Performance Optimization**: Ensure smooth user experience

### Phase 4: Testing & Refinement
1. **UI/UX Testing**: Validate design effectiveness
2. **Voice Quality Assurance**: Test audio clarity and authenticity
3. **Accessibility Validation**: Ensure compliance and usability

## Technical Architecture

### MCP Server Communication
```mermaid
graph TD
    A[NIRA App] --> B[Claude Desktop/AI Assistant]
    B --> C[ElevenLabs MCP Server]
    B --> D[21st.dev Magic MCP Server]
    C --> E[ElevenLabs API]
    D --> F[21st.dev Component Library]
    E --> G[Voice/Audio Assets]
    F --> H[UI Components/Logos]
    G --> A
    H --> A
```

### Data Flow
1. **Development Request**: AI assistant receives development task
2. **MCP Server Selection**: Choose appropriate server (ElevenLabs/21st.dev)
3. **API Interaction**: Server communicates with external service
4. **Asset Generation**: Create voice/UI assets
5. **Integration**: Incorporate assets into NIRA codebase

## Benefits for NIRA

### Enhanced User Experience
- **Professional Audio**: High-quality voice content across all languages
- **Modern UI/UX**: Industry-leading interface design
- **Accessibility**: Inclusive design for all users
- **Performance**: Optimized components and smooth interactions

### Development Efficiency
- **Rapid Prototyping**: Quick iteration on designs and features
- **Consistent Quality**: Standardized components and voice assets
- **Automated Generation**: AI-powered content and component creation
- **Best Practices**: Built-in accessibility and performance optimization

### Competitive Advantage
- **Superior Audio Quality**: Professional voice content vs. competitors
- **Modern Design**: Cutting-edge UI/UX that exceeds industry standards
- **Comprehensive Features**: Full-featured experience across all tiers
- **Cultural Authenticity**: Native-quality pronunciation and context

## Cost Optimization

### ElevenLabs Usage
- **Tier 1**: Full voice features (21 languages)
- **Tier 2**: Limited voice features (18 languages)
- **Tier 3**: Text-only with optional voice (11 languages)
- **Estimated Cost**: ~$50-100/month for full voice content generation

### 21st.dev Magic Usage
- **Component Generation**: One-time creation costs
- **Logo Assets**: Permanent additions to brand library
- **Design Optimization**: Ongoing refinement and improvement
- **Estimated Cost**: ~$20-50/month for active development

## Future Enhancements

### ElevenLabs Roadmap
- **Real-time Voice Chat**: Live conversation with AI tutors
- **Voice Emotion Recognition**: Detect learner engagement and mood
- **Adaptive Voice Speed**: Adjust speaking rate based on proficiency
- **Multi-speaker Dialogues**: Complex conversation scenarios

### 21st.dev Magic Roadmap
- **Advanced Animations**: Micro-interactions and delightful UX
- **AR/VR Components**: Immersive learning interface elements
- **Adaptive UI**: Personalized interface based on learning style
- **Cross-platform Expansion**: Web and desktop component libraries

## Implementation Examples

### ElevenLabs Integration Examples

#### 1. Generate Pronunciation Audio
```swift
// Request through MCP: Generate pronunciation for vocabulary
func generatePronunciationAudio(word: String, language: String) {
    // MCP call: text_to_speech_ElevenLabs
    // Parameters: text=word, voice_name=language_native_voice
    // Output: Audio file saved to project assets
}
```

#### 2. Create AI Conversation Partner
```swift
// Request through MCP: Create language-specific AI agent
func createLanguageAgent(language: String, persona: SimulationPersona) {
    // MCP call: create_agent_ElevenLabs
    // Parameters: name, system_prompt, voice_id, language
    // Result: AI agent ready for conversation practice
}
```

#### 3. Generate Cultural Sound Effects
```swift
// Request through MCP: Create ambient audio for scenarios
func generateScenarioAudio(scenario: String) {
    // MCP call: text_to_sound_effects_ElevenLabs
    // Parameters: text="bustling Tokyo street market"
    // Output: Immersive background audio
}
```

### 21st.dev Magic Integration Examples

#### 1. Generate Language Selection Component
```swift
// Request through MCP: Create modern language picker
// Command: /ui language selection grid with flags and progress
// Output: Professional SwiftUI component with animations
```

#### 2. Refine Existing Components
```swift
// Request through MCP: Improve simulation player interface
// Command: /21 refine SimulationPlayerView for better UX
// Output: Enhanced component with modern design patterns
```

#### 3. Add Brand Logos
```swift
// Request through MCP: Add language flag icons
// Command: /logo France Germany Spain Japan
// Output: Professional SVG/TSX logo components
```

## Practical Usage in NIRA Development

### Daily Development Workflow

#### Morning: UI/UX Enhancement
1. **Review current interface** using 21st.dev Magic analysis
2. **Generate new components** for planned features
3. **Refine existing components** based on user feedback
4. **Add brand assets** and logos as needed

#### Afternoon: Content Creation
1. **Generate voice content** for new lessons using ElevenLabs
2. **Create AI conversation partners** for simulation practice
3. **Generate sound effects** for cultural scenarios
4. **Test audio quality** and make adjustments

#### Evening: Integration & Testing
1. **Integrate new components** into NIRA codebase
2. **Add voice assets** to appropriate lessons
3. **Test accessibility** and performance
4. **Commit changes** with proper documentation

### Feature Development Examples

#### New Language Addition (Tier 1)
1. **Voice Setup**: Create native speaker voice using ElevenLabs
2. **UI Components**: Generate language-specific interface elements
3. **AI Agent**: Create conversation partner with cultural context
4. **Audio Content**: Generate pronunciation guides and sound effects
5. **Testing**: Validate voice quality and UI responsiveness

#### Simulation Enhancement
1. **Audio Immersion**: Add background sounds and voice acting
2. **Visual Polish**: Enhance UI with modern design patterns
3. **Accessibility**: Ensure screen reader compatibility
4. **Performance**: Optimize for smooth animations

## Troubleshooting & Best Practices

### ElevenLabs Best Practices
- **Voice Consistency**: Use same voice ID for character continuity
- **Audio Quality**: Test on multiple devices and speakers
- **Cost Management**: Cache generated audio to avoid regeneration
- **Language Authenticity**: Use native speakers for voice cloning

### 21st.dev Magic Best Practices
- **Component Reusability**: Create modular, reusable components
- **Design Consistency**: Follow established brand guidelines
- **Accessibility First**: Include accessibility from initial design
- **Performance Optimization**: Test on various device sizes

### Common Issues & Solutions

#### ElevenLabs Issues
- **API Rate Limits**: Implement proper retry logic and caching
- **Voice Quality**: Test with different voice models and settings
- **File Management**: Organize audio assets with clear naming conventions

#### 21st.dev Magic Issues
- **Component Integration**: Ensure proper SwiftUI compatibility
- **Design Consistency**: Maintain brand guidelines across components
- **Performance**: Optimize complex animations and transitions

## Conclusion

The integration of ElevenLabs and 21st.dev Magic MCP servers positions NIRA as a cutting-edge language learning platform with:

- **World-class audio capabilities** for authentic language immersion
- **Professional UI/UX design** that exceeds competitor standards
- **Efficient development workflow** with AI-powered asset generation
- **Scalable architecture** supporting 50 languages across 3 feature tiers

These MCP integrations enable NIRA to deliver a premium language learning experience while maintaining development efficiency and cost-effectiveness.

## Quick Reference

### ElevenLabs MCP Commands
- `text_to_speech_ElevenLabs` - Generate speech from text
- `create_agent_ElevenLabs` - Create AI conversation partner
- `voice_clone_ElevenLabs` - Clone voice from audio sample
- `text_to_sound_effects_ElevenLabs` - Generate sound effects
- `speech_to_text_ElevenLabs` - Transcribe audio to text

### 21st.dev Magic MCP Commands
- `/ui [description]` - Generate new UI component
- `/21 refine [component]` - Improve existing component
- `/logo [company names]` - Search and add logos
- `21st_magic_component_inspiration` - Browse component library
- `21st_magic_component_refiner` - Enhance component design
