# NIRA MCP Quick Setup Guide

## Prerequisites

- macOS with <PERSON> installed
- Terminal access
- NIRA project cloned locally
- Valid API keys for ElevenLabs and 21st.dev Magic

## 1. Install Required Tools

### Install uv (Python Package Manager)
```bash
curl -LsSf https://astral.sh/uv/install.sh | sh
```

### Install Node.js (for 21st.dev Magic)
```bash
# Using Homebrew
brew install node

# Or download from https://nodejs.org/
```

## 2. Configure <PERSON>

### Locate Claude Desktop Config
```bash
# macOS location
~/Library/Application\ Support/Claude/claude_desktop_config.json
```

### Add MCP Server Configuration
```json
{
  "mcpServers": {
    "ElevenLabs": {
      "command": "/Users/<USER>/.local/bin/uvx",
      "args": ["elevenlabs-mcp"],
      "env": {
        "ELEVENLABS_API_KEY": "***************************************************",
        "ELEVENLABS_MCP_BASE_PATH": "/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Apps/NIRA/Assets/Audio"
      }
    },
    "21st-dev-magic": {
      "command": "npx",
      "args": ["@21st-dev/magic"],
      "env": {
        "MAGIC_API_KEY": "d3c18f0df32d547b2f2d036443c4bab0b240fd94b0e6ce505953c6a6d88ab019"
      }
    }
  }
}
```

## 3. Test MCP Servers

### Test ElevenLabs MCP
```bash
# Set environment variable
export ELEVENLABS_API_KEY=***************************************************

# Test the server
/Users/<USER>/.local/bin/uvx elevenlabs-mcp --help
```

### Test 21st.dev Magic MCP
```bash
# Set environment variable
export MAGIC_API_KEY=d3c18f0df32d547b2f2d036443c4bab0b240fd94b0e6ce505953c6a6d88ab019

# Test the server
npx @21st-dev/magic --help
```

## 4. Restart Claude Desktop

1. Quit Claude Desktop completely
2. Reopen Claude Desktop
3. Look for MCP server indicators in the interface

## 5. Verify Integration

### Test ElevenLabs Integration
Ask Claude:
```
Generate a pronunciation audio file for the word "hello" in English using ElevenLabs
```

### Test 21st.dev Magic Integration
Ask Claude:
```
/ui Create a modern language selection component for NIRA
```

## 6. Common Commands

### ElevenLabs Commands
```
# Generate speech
Generate audio for "Bonjour, comment allez-vous?" in French

# Create AI agent
Create a French conversation partner for NIRA with a friendly personality

# Generate sound effects
Create ambient audio for a Parisian café scene

# Clone voice
Clone a voice from this audio file: [path/to/audio.wav]
```

### 21st.dev Magic Commands
```
# Generate component
/ui language progress indicator with animations

# Refine existing component
/21 refine the SimulationPlayerView for better accessibility

# Add logos
/logo France Germany Spain Japan

# Get inspiration
Show me modern language learning app components
```

## 7. Project Integration

### Audio Assets Organization
```
NIRA/Assets/Audio/
├── Voices/
│   ├── Tier1/
│   │   ├── English/
│   │   ├── Spanish/
│   │   └── French/
│   ├── Tier2/
│   └── Tier3/
├── SoundEffects/
│   ├── Cultural/
│   └── Ambient/
└── Pronunciations/
    ├── Vocabulary/
    └── Phrases/
```

### Component Organization
```
NIRA/Components/
├── Generated/
│   ├── ElevenLabs/
│   └── Magic/
├── Branding/
└── Custom/
```

## 8. Development Workflow

### Daily Workflow
1. **Morning**: Review UI/UX with 21st.dev Magic
2. **Afternoon**: Generate audio content with ElevenLabs
3. **Evening**: Integrate and test new assets

### Feature Development
1. **Design**: Use 21st.dev Magic for UI components
2. **Audio**: Use ElevenLabs for voice content
3. **Integration**: Add to NIRA codebase
4. **Testing**: Validate functionality and performance

## 9. Troubleshooting

### ElevenLabs Issues
```bash
# Check API key
echo $ELEVENLABS_API_KEY

# Test connection
curl -H "xi-api-key: $ELEVENLABS_API_KEY" https://api.elevenlabs.io/v1/voices

# Check uvx installation
which uvx
/Users/<USER>/.local/bin/uvx --version
```

### 21st.dev Magic Issues
```bash
# Check API key
echo $MAGIC_API_KEY

# Test npm/npx
which npx
npx --version

# Clear npm cache
npm cache clean --force
```

### Claude Desktop Issues
```bash
# Check config file
cat ~/Library/Application\ Support/Claude/claude_desktop_config.json

# Check logs (if available)
tail -f ~/Library/Logs/Claude/mcp-server-*.log
```

## 10. Best Practices

### Security
- Never commit API keys to version control
- Use environment variables for sensitive data
- Regularly rotate API keys
- Monitor API usage for unusual activity

### Performance
- Cache generated audio files
- Optimize component code for performance
- Test on various device sizes
- Monitor API rate limits

### Quality
- Test audio on multiple devices
- Validate component accessibility
- Follow NIRA brand guidelines
- Document all generated assets

## 11. Quick Reference

### File Paths
- **Claude Config**: `~/Library/Application Support/Claude/claude_desktop_config.json`
- **Audio Assets**: `NIRA/Assets/Audio/`
- **Components**: `NIRA/Components/Generated/`
- **Documentation**: `NIRA/Documentation/`

### Environment Variables
```bash
export ELEVENLABS_API_KEY=***************************************************
export MAGIC_API_KEY=d3c18f0df32d547b2f2d036443c4bab0b240fd94b0e6ce505953c6a6d88ab019
export ELEVENLABS_MCP_BASE_PATH=/Users/<USER>/Library/Mobile\ Documents/com~apple~CloudDocs/Apps/NIRA/Assets/Audio
```

### Common Commands
```bash
# Test ElevenLabs
uvx elevenlabs-mcp --help

# Test 21st.dev Magic
npx @21st-dev/magic --help

# Restart Claude Desktop
killall Claude && open -a Claude

# Check MCP server status
ps aux | grep -E "(uvx|npx)"
```

## 12. Support Resources

### Documentation
- [ElevenLabs MCP GitHub](https://github.com/elevenlabs/elevenlabs-mcp)
- [21st.dev Magic Documentation](https://21st.dev/docs)
- [MCP Protocol Specification](https://github.com/modelcontextprotocol)

### Community
- [ElevenLabs Discord](https://discord.gg/elevenlabs)
- [21st.dev Community](https://21st.dev/community)
- [Claude Desktop Support](https://support.anthropic.com)

### NIRA Team
- Check `NIRA/Documentation/` for project-specific guides
- Review `CHANGELOG.md` for recent updates
- Consult team members for advanced integration questions

---

**Setup complete! You can now use ElevenLabs and 21st.dev Magic MCP servers to enhance NIRA development.** 🚀
