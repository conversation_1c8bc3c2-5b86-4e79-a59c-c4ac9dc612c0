# NIRA MCP Technical Specifications

## Overview

This document provides detailed technical specifications for the Model Context Protocol (MCP) server integrations in NIRA, including API endpoints, data structures, and implementation details.

## ElevenLabs MCP Server Specifications

### Server Configuration
```json
{
  "name": "ElevenLabs",
  "version": "0.4.0",
  "command": "/Users/<USER>/.local/bin/uvx",
  "args": ["elevenlabs-mcp"],
  "environment": {
    "ELEVENLABS_API_KEY": "***************************************************",
    "ELEVENLABS_MCP_BASE_PATH": "/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Apps/NIRA/Assets/Audio"
  }
}
```

### Available Tools

#### 1. Text-to-Speech Generation
```typescript
interface TextToSpeechParams {
  text: string;                    // Text to convert to speech
  voice_name?: string;             // Voice name (e.g., "<PERSON>", "<PERSON>")
  voice_id?: string;               // Alternative to voice_name
  stability?: number;              // 0.0-1.0, voice stability
  similarity_boost?: number;       // 0.0-1.0, similarity to original
  style?: number;                  // 0.0-1.0, style exaggeration
  use_speaker_boost?: boolean;     // Enhance similarity
  speed?: number;                  // 0.7-1.2, speech speed
  language?: string;               // ISO 639-1 language code
  output_format?: string;          // Audio format (mp3_44100_128, etc.)
  output_directory?: string;       // Save location
}

interface TextToSpeechResponse {
  file_path: string;               // Path to generated audio file
  voice_used: string;              // Voice name that was used
  duration_seconds: number;        // Audio duration
  file_size_bytes: number;         // File size
}
```

#### 2. Voice Cloning
```typescript
interface VoiceCloneParams {
  name: string;                    // Name for the cloned voice
  files: string[];                 // Array of audio file paths
  description?: string;            // Voice description
}

interface VoiceCloneResponse {
  voice_id: string;                // Generated voice ID
  voice_name: string;              // Voice name
  status: string;                  // "ready" | "processing" | "failed"
  preview_url?: string;            // Preview audio URL
}
```

#### 3. AI Agent Creation
```typescript
interface CreateAgentParams {
  name: string;                    // Agent name
  first_message: string;           // Initial greeting
  system_prompt: string;           // Agent behavior instructions
  voice_id?: string;               // Voice for the agent
  language?: string;               // Agent language
  llm?: string;                    // LLM model to use
  temperature?: number;            // Response creativity (0.0-1.0)
  max_tokens?: number;             // Maximum response length
  max_duration_seconds?: number;   // Max conversation duration
}

interface CreateAgentResponse {
  agent_id: string;                // Generated agent ID
  agent_url: string;               // Agent interaction URL
  phone_number?: string;           // Phone number if enabled
  status: string;                  // Agent status
}
```

#### 4. Sound Effects Generation
```typescript
interface SoundEffectsParams {
  text: string;                    // Description of sound effect
  duration_seconds?: number;       // Duration (0.5-5.0)
  output_format?: string;          // Audio format
  output_directory?: string;       // Save location
}

interface SoundEffectsResponse {
  file_path: string;               // Path to generated audio
  duration_seconds: number;        // Actual duration
  description: string;             // Effect description
}
```

### NIRA-Specific Voice Mapping

#### Tier 1 Languages (Full Voice Support)
```typescript
const tier1VoiceMapping = {
  "en": { voice_id: "21m00Tcm4TlvDq8ikWAM", name: "Rachel" },
  "es": { voice_id: "XB0fDUnXU5powFXDhCwa", name: "Matilda" },
  "fr": { voice_id: "ThT5KcBeYPX3keUQqHPh", name: "Dorothy" },
  "de": { voice_id: "yoZ06aMxZJJ28mfd3POQ", name: "Sam" },
  "it": { voice_id: "AZnzlk1XvdvUeBnXmlld", name: "Domi" },
  "pt": { voice_id: "onwK4e9ZLuTAKqWW03F9", name: "Daniel" },
  "ru": { voice_id: "Yko7PKHZNXotIFUBG7I9", name: "Emily" },
  "ja": { voice_id: "bVMeCyTHy58xNoL34h3p", name: "Jeremy" },
  "ko": { voice_id: "LcfcDJNUP1GQjkzn1xUU", name: "Liam" },
  "zh": { voice_id: "XrExE9yKIg1WjnnlVkGX", name: "Matilda" },
  // ... additional Tier 1 languages
};
```

## 21st.dev Magic MCP Server Specifications

### Server Configuration
```json
{
  "name": "21st-dev-magic",
  "version": "latest",
  "command": "npx",
  "args": ["@21st-dev/magic"],
  "environment": {
    "MAGIC_API_KEY": "d3c18f0df32d547b2f2d036443c4bab0b240fd94b0e6ce505953c6a6d88ab019"
  }
}
```

### Available Tools

#### 1. Component Builder
```typescript
interface ComponentBuilderParams {
  message: string;                 // User request description
  searchQuery: string;             // Component search terms
  absolutePathToCurrentFile: string; // Target file path
  absolutePathToProjectDirectory: string; // Project root
  context: string;                 // Additional context
}

interface ComponentBuilderResponse {
  component_code: string;          // Generated SwiftUI/React code
  component_name: string;          // Component name
  dependencies: string[];          // Required imports
  usage_example: string;           // How to use the component
}
```

#### 2. Component Refiner
```typescript
interface ComponentRefinerParams {
  userMessage: string;             // Refinement request
  absolutePathToRefiningFile: string; // File to refine
  context: string;                 // Specific improvements needed
}

interface ComponentRefinerResponse {
  refined_code: string;            // Improved component code
  improvements: string[];          // List of improvements made
  performance_notes: string;       // Performance considerations
  accessibility_notes: string;     // Accessibility improvements
}
```

#### 3. Logo Search
```typescript
interface LogoSearchParams {
  queries: string[];               // Company/brand names
  format: "JSX" | "TSX" | "SVG";   // Output format
}

interface LogoSearchResponse {
  logos: Array<{
    company: string;               // Company name
    component_name: string;        // Component name (e.g., "GitHubIcon")
    code: string;                  // Logo component code
    import_statement: string;      // How to import
    usage_example: string;         // Usage example
  }>;
}
```

### NIRA-Specific Component Library

#### Language Selection Components
```swift
// Generated via: /ui language selection grid with tier indicators
struct TieredLanguageSelectionView: View {
    let languages: [Language]
    let onLanguageSelected: (Language) -> Void
    
    var body: some View {
        LazyVGrid(columns: gridColumns, spacing: 16) {
            ForEach(languages) { language in
                LanguageCard(
                    language: language,
                    tier: language.tier,
                    onTap: { onLanguageSelected(language) }
                )
            }
        }
    }
}
```

#### Simulation Player Components
```swift
// Generated via: /21 refine SimulationPlayerView for better accessibility
struct AccessibleSimulationPlayerView: View {
    @State private var currentDialogue: SimulationDialogue
    @State private var isPlaying: Bool = false
    
    var body: some View {
        VStack(spacing: 20) {
            SimulationHeaderComponent(simulation: simulation, persona: persona)
            SimulationProgressComponent(progress: progress, persona: persona)
            DialogueCardComponent(dialogue: currentDialogue, persona: persona)
        }
        .accessibilityElement(children: .contain)
        .accessibilityLabel("Simulation Player")
    }
}
```

## Integration Architecture

### MCP Communication Flow
```mermaid
sequenceDiagram
    participant Dev as Developer
    participant AI as AI Assistant
    participant MCP as MCP Server
    participant API as External API
    participant NIRA as NIRA App

    Dev->>AI: Request feature enhancement
    AI->>MCP: Call appropriate MCP tool
    MCP->>API: Make API request
    API->>MCP: Return data/assets
    MCP->>AI: Process and format response
    AI->>Dev: Provide implementation code
    Dev->>NIRA: Integrate into codebase
```

### File Organization
```
NIRA/
├── Assets/
│   ├── Audio/
│   │   ├── Voices/
│   │   │   ├── Tier1/
│   │   │   ├── Tier2/
│   │   │   └── Tier3/
│   │   ├── SoundEffects/
│   │   └── Pronunciations/
│   └── Images/
│       ├── Logos/
│       └── Icons/
├── Components/
│   ├── Generated/          # 21st.dev Magic components
│   ├── Branding/          # Brand system components
│   └── Custom/            # Custom NIRA components
└── Services/
    ├── AudioService.swift  # ElevenLabs integration
    └── UIService.swift     # 21st.dev Magic integration
```

## Performance Considerations

### ElevenLabs Optimization
- **Audio Caching**: Cache generated audio files to avoid regeneration
- **Batch Processing**: Generate multiple audio files in batches
- **Quality Settings**: Use appropriate quality settings for different use cases
- **Compression**: Optimize audio file sizes for mobile delivery

### 21st.dev Magic Optimization
- **Component Reusability**: Create modular, reusable components
- **Code Splitting**: Separate large components into smaller modules
- **Performance Testing**: Test components on various device sizes
- **Accessibility**: Ensure all components meet WCAG AA standards

## Security Considerations

### API Key Management
- **Environment Variables**: Store API keys in secure environment variables
- **Key Rotation**: Regularly rotate API keys for security
- **Access Control**: Limit API key permissions to necessary scopes
- **Monitoring**: Monitor API usage for unusual activity

### Data Privacy
- **Audio Data**: Ensure user audio data is handled securely
- **Voice Cloning**: Obtain proper consent for voice cloning
- **Data Retention**: Follow data retention policies for generated content
- **Compliance**: Ensure GDPR and other privacy regulation compliance

## Error Handling

### ElevenLabs Error Handling
```swift
enum ElevenLabsError: Error {
    case apiKeyInvalid
    case rateLimitExceeded
    case voiceNotFound
    case audioGenerationFailed
    case networkError(Error)
}

func handleElevenLabsError(_ error: ElevenLabsError) {
    switch error {
    case .rateLimitExceeded:
        // Implement retry with exponential backoff
        break
    case .voiceNotFound:
        // Fall back to default voice
        break
    // ... handle other errors
    }
}
```

### 21st.dev Magic Error Handling
```swift
enum MagicError: Error {
    case componentGenerationFailed
    case invalidComponentCode
    case compilationError
    case networkError(Error)
}

func handleMagicError(_ error: MagicError) {
    switch error {
    case .componentGenerationFailed:
        // Retry with simplified request
        break
    case .invalidComponentCode:
        // Request code refinement
        break
    // ... handle other errors
    }
}
```

## Monitoring & Analytics

### Usage Metrics
- **API Call Volume**: Track MCP server usage
- **Response Times**: Monitor server response performance
- **Error Rates**: Track and analyze error patterns
- **Cost Tracking**: Monitor API usage costs

### Quality Metrics
- **Audio Quality**: User feedback on generated audio
- **Component Performance**: UI component load times
- **User Satisfaction**: Overall feature satisfaction scores
- **Accessibility Compliance**: Automated accessibility testing results

## Conclusion

These technical specifications provide the foundation for implementing and maintaining MCP server integrations in NIRA. The specifications ensure consistent, high-quality implementation while maintaining security, performance, and user experience standards.
