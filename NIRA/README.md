# NIRA Language Learning App 🌍

NIRA is an advanced AI-powered language learning application that provides personalized, interactive language learning experiences through intelligent agents and immersive simulations.

## 🚀 **Latest Release: v2.0.0 - 50 Language Expansion**

**Major Update**: NIRA now supports **50 total languages** with a comprehensive 3-tier feature support system!

**Previous Status**: AI Agents with Google Gemini 2.0 Flash integration are fully operational with simplified, consistent interface across all language tutors.

### ✅ **What's Working Now**
- **50 Language Support**: Comprehensive coverage with 3-tier feature system
- **AI Chat Interface**: ChatGPT-like experience with all language tutors
- **Conversation History**: Access to past conversations with each tutor
- **Attachments**: Photo, voice, document, and camera support
- **Real-time Responses**: Powered by Google Gemini 2.0 Flash
- **Consistent Experience**: All agents work identically across supported languages

## 🌍 **Supported Languages (50 Total)**

### **Tier 1 Languages (21) - Full Features + Voice**
European: English, Spanish, French, German, Italian, Portuguese, Dutch, Swedish, Norwegian, Russian
Asian: Chinese, Japanese, Korean, Hindi, Tamil, Telugu, Bengali, Vietnamese, Thai, Indonesian, Arabic

### **Tier 2 Languages (18) - Partial Features**
Indian: Kannada, Malayalam, Marathi, Punjabi, Gujarati, Odia, Assamese, Konkani, Sindhi, Bhojpuri, Maithili
Others: Swahili, Hebrew, Greek, Turkish, Farsi, Tagalog, Ukrainian

### **Tier 3 Languages (11) - Basic Features**
African/Indigenous: Danish, Xhosa, Zulu, Amharic, Quechua, Maori, Cherokee, Navajo, Hawaiian, Inuktitut, Yoruba

### **New in v2.0.0 (24 Languages Added)**
- **European**: Polish, Czech, Hungarian, Romanian, Bulgarian, Croatian, Serbian, Slovak, Slovenian, Estonian, Latvian, Lithuanian, Maltese
- **Celtic**: Irish, Welsh, Scots, Manx, Cornish, Breton
- **Regional**: Basque, Catalan, Galician, Urdu

## Features

### 🤖 **AI-Powered Tutors**
- **French Teacher** (👩‍🏫) - Patient & cultural, specializes in conversational French
- **French Chef** (👨‍🍳) - Enthusiastic, teaches through cuisine & culture
- **Spanish Teacher** (👩‍🎨) - Creative & energetic, Latin American Spanish focus
- **Business Spanish Teacher** (👨‍💼) - Professional, business communication specialist
- **Japanese Teacher** (👩‍🎓) - Gentle & traditional, culture & etiquette focused
- **Modern Japanese Teacher** (👨‍💻) - Tech-savvy, anime & pop culture approach
- **Tamil Teacher** (👩‍🏫) - Warm & encouraging, literature & poetry specialist
- **Tamil Cinema Teacher** (👨‍🎭) - Dramatic, teaches through movies & expressions
- **English Teacher** (👩‍💼) - Professional, business English specialist
- **American English Teacher** (👨‍🎸) - Casual & fun, slang & culture focused

### 🎯 **Adaptive Learning System**
- AI-powered personalized lessons
- Real-time difficulty adjustment
- Comprehensive progress tracking
- Learning analytics and insights

### 🏆 **Achievement System**

NIRA features a comprehensive achievement system designed to motivate and reward learners for their progress and dedication.

#### **Achievement Categories**

**📈 Progress Achievements**
- **First Steps** (🎯) - Complete your first lesson (10 points)
- **Getting Started** (📚) - Complete 5 lessons (25 points)
- **Dedicated Learner** (🎓) - Complete 25 lessons (75 points)
- **Scholar** (🏆) - Complete 100 lessons (200 points)
- **Word Collector** (📝) - Learn 50 new words (25 points)
- **Vocabulary Master** (📖) - Learn 500 new words (100 points)

**🎯 Performance Achievements**
- **Perfect Score** (⭐) - Get 100% accuracy on a lesson (15 points)
- **Accuracy Master** (🎯) - Maintain 90% accuracy over 10 lessons (50 points)
- **Speed Demon** (⚡) - Complete a lesson in under 5 minutes (30 points)

**📅 Consistency Achievements**
- **Streak Starter** (🔥) - Study for 3 days in a row (20 points)
- **Week Warrior** (🗓️) - Study for 7 days in a row (50 points)
- **Unstoppable** (💪) - Study for 30 days in a row (150 points)
- **Legend** (👑) - Study for 100 days in a row (500 points)

**👥 Social Achievements**
- **Social Butterfly** (👥) - Add your first friend (15 points)
- **Community Leader** (🤝) - Help 10 friends with their learning (75 points)

**⭐ Special Achievements**
- **Early Bird** (🌅) - Complete a lesson before 8 AM (20 points)
- **Night Owl** (🦉) - Complete a lesson after 10 PM (20 points)
- **Weekend Warrior** (🏖️) - Study on both Saturday and Sunday (40 points)
- **NIRA Explorer** (🗺️) - Try all available features in the app (100 points)

#### **Achievement Difficulty Levels**
- **🥉 Bronze** - Entry-level achievements for beginners
- **🥈 Silver** - Intermediate achievements requiring dedication
- **🥇 Gold** - Advanced achievements for committed learners
- **💎 Platinum** - Elite achievements for the most dedicated users

### 🏅 **Leaderboard System**
- Weekly, monthly, and all-time rankings
- Friend competitions
- Points-based scoring system
- Streak tracking and rewards

### 🎭 **Cultural Immersion**
- Real-world scenario simulations
- Cultural context learning
- Interactive conversations with AI tutors

### 📊 **Analytics Dashboard**
- Detailed progress tracking
- Learning pattern analysis
- Personalized recommendations
- Performance insights

## Supported Languages
- 🇫🇷 French
- 🇪🇸 Spanish
- 🇯🇵 Japanese
- 🇮🇳 Tamil
- 🇬🇧 English

## Technical Architecture

### AI Integration
- **Google Gemini 2.0 Flash** - Advanced AI responses with personality-specific prompts
- **Conversation Memory** - Context-aware multi-turn conversations
- **Real-time Chat** - ChatGPT-like interface with typing indicators
- **Attachment Support** - Photo, voice, document, and camera integration

### Backend Services
- **Supabase Integration** - Real-time database and authentication
- **AI-Powered Content Generation** - Dynamic lesson creation
- **Learning Analytics Service** - Progress tracking and insights
- **Achievement System** - Gamification and rewards

### Key Components
- **AIAgentChatView** - Modern chat interface for AI tutors
- **ChatViewModel** - Reactive state management with Gemini integration
- **ConversationHistoryView** - Access to past conversations
- **AdaptiveLearningModels** - Core data structures for learning system
- **LearningAnalyticsService** - Analytics and achievement management

## Getting Started

### Prerequisites
1. **Xcode 15+** with iOS 17+ support
2. **Google Gemini API Key** - Get from [Google AI Studio](https://ai.google.dev/tutorials/setup)
3. **Supabase Account** - For database and authentication

### Setup
1. Clone the repository
2. Open `NIRA.xcodeproj` in Xcode
3. Configure API keys in `Config/APIKeys.swift`:
   ```swift
   struct APIKeys {
       static let geminiAPIKey = "YOUR_GEMINI_API_KEY"
       static let supabaseURL = "YOUR_SUPABASE_URL"
       static let supabaseAnonKey = "YOUR_SUPABASE_ANON_KEY"
   }
   ```
4. Build and run the app

### Testing AI Agents
1. Navigate to the "AI Agents" tab
2. Select any language (French, Spanish, Japanese, Tamil, English)
3. Choose a tutor and start chatting
4. Test conversation history by tapping "History"
5. Test attachments by tapping the "+" button

## Current Development Status

### ✅ Completed Features
- **AI Chat Interface** - Fully functional with Gemini integration
- **All Language Tutors** - 10 distinct AI personalities working
- **Conversation History** - Persistent chat history for each tutor
- **Attachments** - Photo, voice, document, camera support
- **Lessons System** - Supabase-integrated lesson display
- **Cultural Simulations** - Interactive scenario-based learning
- **Achievement System** - Comprehensive gamification

## Phase 4: Advanced AI Features & Real-time Capabilities ✅

### Real-time Collaboration
- ✅ **RealtimeCollaborationService** - Multiplayer learning sessions with WebSocket communication
- ✅ **Session Management** - Create, join, and manage collaborative learning sessions
- ✅ **Live Activities** - Real-time synchronized learning activities and competitions
- ✅ **Voice Chat Integration** - Group voice conversations with AI tutors
- ✅ **Invitation System** - Send and receive session invitations with friends

### Advanced Pronunciation Assessment
- ✅ **PronunciationAssessmentService** - AI-powered pronunciation analysis and feedback
- ✅ **Real-time Speech Analysis** - Live pronunciation scoring with detailed phoneme analysis
- ✅ **Personalized Exercises** - Custom pronunciation exercises based on identified weak areas
- ✅ **Native Speaker Comparison** - Compare user pronunciation with native speaker samples
- ✅ **Progress Tracking** - Comprehensive pronunciation improvement tracking

### Performance Optimization
- ✅ **PerformanceOptimizationService** - Real-time performance monitoring and optimization
- ✅ **Intelligent Caching** - Smart caching strategies with LRU eviction and data compression
- ✅ **Memory Management** - Optimized memory usage with automatic cleanup and warnings
- ✅ **Network Optimization** - Adaptive content quality based on network conditions
- ✅ **Response Optimization** - API request optimization with batching and timeout handling

### 🚧 Next Development Priorities
1. **Augmented Reality** - Cultural immersion experiences with AR
2. **Advanced Analytics Dashboard** - Real-time learning insights and recommendations
3. **A/B Testing Framework** - Optimize learning approaches through experimentation
4. **Offline Mode Enhancement** - Comprehensive offline learning capabilities
5. **Advanced Gamification** - Tournaments, challenges, and competitive learning

## Achievement Progress Tracking

The app automatically tracks user progress and awards achievements based on:
- Lesson completion count
- Accuracy scores
- Study streaks
- Learning velocity
- Social interactions
- Time-based activities

Users can view their achievements in the "More" tab, which displays:
- All available achievements
- Earned vs. unearned status
- Achievement categories and filtering
- Points and difficulty levels
- Progress toward next achievements

## MCP Integration

NIRA leverages Model Context Protocol (MCP) servers for enhanced development capabilities:

### **ElevenLabs MCP Server**
- **Text-to-Speech**: Generate natural speech in 50+ languages
- **Voice Cloning**: Create authentic native speaker voices
- **AI Agents**: Build voice-enabled conversation partners
- **Sound Effects**: Generate immersive cultural audio scenarios
- **Speech Analysis**: Pronunciation assessment and feedback

### **21st.dev Magic MCP Server**
- **Component Generation**: Create modern UI components
- **Design System**: Professional branding and component library
- **Logo Integration**: Access to professional logos and icons
- **UI/UX Optimization**: Accessibility and performance enhancements
- **Competitive Analysis**: Benchmark against industry leaders

### **Documentation**
- 📚 [MCP Integration Guide](Documentation/MCP_Integration_Guide.md) - Comprehensive overview
- 🔧 [Technical Specifications](Documentation/MCP_Technical_Specifications.md) - Detailed API specs
- ⚡ [Quick Setup Guide](Documentation/MCP_Quick_Setup_Guide.md) - Fast setup instructions

## API Configuration

The app uses the following APIs:
- **Google Gemini 2.0 Flash** - AI conversation responses
- **Supabase** - Database, authentication, real-time updates
- **ElevenLabs** - Voice generation and audio processing (via MCP)
- **21st.dev Magic** - UI/UX components and design assets (via MCP)
- **OpenAI** (optional) - Fallback AI service

Configure all API keys in `Config/APIKeys.swift` before building.

---

*NIRA - Making language learning engaging, effective, and fun with AI-powered tutors!* 🚀