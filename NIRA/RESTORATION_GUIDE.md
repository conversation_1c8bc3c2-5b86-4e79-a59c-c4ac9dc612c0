# NIRA Restoration Guide

This guide provides instructions for restoring NIRA to previous states if needed.

## Quick Restoration Commands

### Restore to Pre-50-Language State
If you need to rollback the 50-language expansion:

```bash
# View available tags
git tag -l

# Restore to the state before v2.0.0 (if there was a previous tag)
# Note: Since this is the initial commit, there's no previous state to restore to
# But you can create a branch from any specific commit

# Create a backup branch of current state
git checkout -b backup-v2.0.0

# View commit history
git log --oneline

# Restore to a specific commit (replace COMMIT_HASH with actual hash)
git checkout -b restore-point COMMIT_HASH
```

### Restore Specific Files
If you only need to restore specific files:

```bash
# Restore Language enum to previous state
git checkout HEAD~1 -- NIRA/Models/User.swift

# Restore specific service files
git checkout HEAD~1 -- NIRA/Services/UserPreferencesService.swift
git checkout HEAD~1 -- NIRA/Services/GeminiLiveVoiceService.swift

# Restore FilterChip component
git checkout HEAD~1 -- NIRA/Views/Components/FilterChip.swift
git checkout HEAD~1 -- NIRA/Views/AgentsView.swift
```

## What Was Changed in v2.0.0

### Core Files Modified:
1. **NIRA/Models/User.swift** - Added 24 new language cases to Language enum
2. **NIRA/Services/UserPreferencesService.swift** - Added language preference mappings
3. **NIRA/Services/GeminiLiveVoiceService.swift** - Added voice model and locale mappings
4. **NIRA/Services/PronunciationAssessmentService.swift** - Added locale mappings and practice phrases
5. **NIRA/Services/RealtimeCollaborationService.swift** - Extended agent language mappings
6. **NIRA/Services/PineconeService.swift** - Added vector space handling
7. **NIRA/ContentView.swift** - Extended color theme mappings
8. **NIRA/Views/Simulation/SimulationBrowserView.swift** - Added UUID mappings
9. **NIRA/Views/Components/FilterChip.swift** - Enhanced with subtitle/icon support
10. **NIRA/Views/AgentsView.swift** - Removed duplicate FilterChip declaration

### New Files Added:
- **NIRA/CHANGELOG.md** - Comprehensive changelog
- **NIRA/.gitignore** - Git ignore rules
- **NIRA/RESTORATION_GUIDE.md** - This file

## Rollback Strategies

### Strategy 1: Selective Rollback
Keep the enhanced FilterChip but remove new languages:
```bash
# Keep FilterChip improvements
git checkout HEAD -- NIRA/Views/Components/FilterChip.swift

# Rollback language additions
git checkout HEAD~2 -- NIRA/Models/User.swift
git checkout HEAD~2 -- NIRA/Services/UserPreferencesService.swift
# ... repeat for other service files
```

### Strategy 2: Complete Rollback
Return to exact previous state:
```bash
# Create backup first
git tag backup-before-rollback

# Hard reset to previous commit (DANGEROUS - loses all changes)
git reset --hard HEAD~2

# Or safer: create new branch from previous state
git checkout -b pre-expansion HEAD~2
```

### Strategy 3: Cherry-pick Specific Features
Keep some improvements while removing others:
```bash
# Create new branch
git checkout -b selective-features

# Reset to base
git reset --hard HEAD~2

# Cherry-pick specific commits
git cherry-pick <commit-hash-for-filterchip-fix>
```

## Testing After Restoration

After any restoration, always:

1. **Build Test**:
```bash
cd /path/to/NIRA
xcodebuild -scheme NIRA -destination 'platform=iOS Simulator,name=iPhone 16' build
```

2. **Check for Compilation Errors**:
   - Look for missing language cases in switch statements
   - Verify FilterChip declarations
   - Check service file consistency

3. **Verify Functionality**:
   - Test language selection
   - Test agent conversations
   - Test voice features (if applicable)

## Emergency Contacts

If you encounter issues during restoration:
1. Check the git log for commit details
2. Review CHANGELOG.md for what was changed
3. Use `git status` to see current state
4. Use `git diff` to see what's different

## Backup Recommendations

Before making major changes in the future:
1. Always create a tag: `git tag -a v2.1.0 -m "Description"`
2. Create backup branches: `git checkout -b backup-before-changes`
3. Test thoroughly before committing
4. Update this restoration guide with new procedures

---

**Remember**: Git preserves history, so no changes are truly lost unless you force-delete them. When in doubt, create a backup branch first!
