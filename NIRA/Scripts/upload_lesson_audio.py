#!/usr/bin/env python3
"""
Script to upload lesson audio files to Supabase Storage and update lesson records
"""

import os
import json
import requests
from datetime import datetime
from pathlib import Path

# Supabase configuration
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"

# Audio files mapping to lesson IDs
AUDIO_FILES = [
    {
        "file_path": "/Users/<USER>/Desktop/tts_Welco_20250530_121828.mp3",
        "lesson_id": "70b6d6e6-1c47-41d5-88e0-5a2869d391be",
        "lesson_title": "Tamil Greetings and Respect",
        "storage_path": "tamil/a1/greetings_and_respect.mp3"
    },
    {
        "file_path": "/Users/<USER>/Desktop/tts_Welco_20250530_121843.mp3",
        "lesson_id": "f75c15de-b5a2-4a72-be25-33cdf9c35721",
        "lesson_title": "Tamil A1: Personal Information",
        "storage_path": "tamil/a1/personal_information.mp3"
    },
    {
        "file_path": "/Users/<USER>/Desktop/tts_Welco_20250530_121859.mp3",
        "lesson_id": "862e1f3a-6107-4682-9c1b-7ed84289a56e",
        "lesson_title": "Tamil A1: Numbers 1-100",
        "storage_path": "tamil/a1/numbers_1_100.mp3"
    }
]

def upload_file_to_storage(file_path: str, storage_path: str) -> str:
    """Upload file to Supabase Storage and return public URL"""

    # Read file
    with open(file_path, 'rb') as f:
        file_data = f.read()

    # Upload to storage
    headers = {
        'Authorization': f'Bearer {SUPABASE_ANON_KEY}',
        'Content-Type': 'audio/mpeg'
    }

    upload_url = f"{SUPABASE_URL}/storage/v1/object/lesson-audio/{storage_path}"

    response = requests.post(upload_url, headers=headers, data=file_data)

    if response.status_code in [200, 201]:
        # Return public URL
        public_url = f"{SUPABASE_URL}/storage/v1/object/public/lesson-audio/{storage_path}"
        print(f"✅ Uploaded {storage_path} successfully")
        return public_url
    else:
        print(f"❌ Failed to upload {storage_path}: {response.status_code} - {response.text}")
        return None

def update_lesson_with_audio(lesson_id: str, audio_url: str, lesson_title: str):
    """Update lesson record with audio URL and metadata"""

    # Get file size for metadata
    file_info = None
    for audio_file in AUDIO_FILES:
        if audio_file["lesson_id"] == lesson_id:
            file_path = audio_file["file_path"]
            if os.path.exists(file_path):
                file_size = os.path.getsize(file_path)
                file_info = {
                    "file_size": file_size,
                    "duration_estimate": file_size / 16000,  # Rough estimate
                    "voice_used": "Rachel",
                    "language": "en",
                    "generated_at": datetime.now().isoformat()
                }
            break

    # Update lesson in database
    headers = {
        'Authorization': f'Bearer {SUPABASE_ANON_KEY}',
        'Content-Type': 'application/json',
        'Prefer': 'return=minimal'
    }

    update_data = {
        'audio_url': audio_url,
        'has_audio': True,
        'audio_metadata': file_info or {},
        'updated_at': datetime.now().isoformat()
    }

    update_url = f"{SUPABASE_URL}/rest/v1/lessons?id=eq.{lesson_id}"

    response = requests.patch(update_url, headers=headers, json=update_data)

    if response.status_code in [200, 204]:
        print(f"✅ Updated lesson '{lesson_title}' with audio URL")
        return True
    else:
        print(f"❌ Failed to update lesson '{lesson_title}': {response.status_code} - {response.text}")
        return False

def main():
    """Main function to upload all audio files and update lessons"""
    print("🎙️ Starting lesson audio upload process...")

    success_count = 0
    total_count = len(AUDIO_FILES)

    for audio_file in AUDIO_FILES:
        file_path = audio_file["file_path"]
        lesson_id = audio_file["lesson_id"]
        lesson_title = audio_file["lesson_title"]
        storage_path = audio_file["storage_path"]

        print(f"\n📁 Processing: {lesson_title}")

        # Check if file exists
        if not os.path.exists(file_path):
            print(f"❌ File not found: {file_path}")
            continue

        # Upload to storage
        audio_url = upload_file_to_storage(file_path, storage_path)

        if audio_url:
            # Update lesson record
            if update_lesson_with_audio(lesson_id, audio_url, lesson_title):
                success_count += 1

    print(f"\n🎉 Upload complete! {success_count}/{total_count} lessons updated with audio")

if __name__ == "__main__":
    main()
