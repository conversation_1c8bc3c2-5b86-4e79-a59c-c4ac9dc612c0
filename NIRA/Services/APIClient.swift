import Foundation
import Combine

// MARK: - API Client for Hybrid Architecture

@MainActor
class APIClient: ObservableObject {
    static let shared = APIClient()
    
    private let baseURL: String
    private let session = URLSession.shared
    private var jwtToken: String?
    
    @Published var isConnected = false
    @Published var lastError: Error?
    
    private init() {
        #if DEBUG
        self.baseURL = "http://localhost:8080/api/v1"
        #else
        self.baseURL = "https://api.nira-app.com/api/v1"
        #endif
        
        checkConnectivity()
    }
    
    func setAuthToken(_ token: String) {
        self.jwtToken = token
    }
    
    // MARK: - Content API Methods
    
    func getPersonalizedLessons(
        language: String,
        count: Int = 10
    ) async throws -> PersonalizedLessonsResponse {
        let url = buildURL(path: "content/lessons/personalized", queryItems: [
            URLQueryItem(name: "language", value: language),
            URLQueryItem(name: "count", value: String(count))
        ])
        
        let request = try buildAuthenticatedRequest(url: url)
        let (data, response) = try await session.data(for: request)
        
        try validateResponse(response)
        return try JSONDecoder().decode(PersonalizedLessonsResponse.self, from: data)
    }
    
    func getLesson(id: String) async throws -> LessonResponse {
        let url = buildURL(path: "content/lesson/\(id)")
        let request = try buildAuthenticatedRequest(url: url)
        
        let (data, response) = try await session.data(for: request)
        try validateResponse(response)
        
        return try JSONDecoder().decode(LessonResponse.self, from: data)
    }
    
    func requestLessonBundle(
        language: String,
        bundleSize: Int,
        preferences: UserPreferences?
    ) async throws -> LessonBundleResponse {
        let url = buildURL(path: "content/lesson-bundle/request")
        var request = try buildAuthenticatedRequest(url: url, method: "POST")
        
        let requestBody = LessonBundleRequest(
            language: language,
            bundleSize: bundleSize,
            preferences: preferences
        )
        
        request.httpBody = try JSONEncoder().encode(requestBody)
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        
        let (data, response) = try await session.data(for: request)
        try validateResponse(response)
        
        return try JSONDecoder().decode(LessonBundleResponse.self, from: data)
    }
    
    func getLessonBundle(language: String) async throws -> LessonBundleResponse {
        let url = buildURL(path: "content/lesson-bundle/\(language)")
        let request = try buildAuthenticatedRequest(url: url)
        
        let (data, response) = try await session.data(for: request)
        try validateResponse(response)
        
        return try JSONDecoder().decode(LessonBundleResponse.self, from: data)
    }
    
    func getCacheRecommendations(
        language: String,
        currentCacheSize: Int
    ) async throws -> CacheRecommendationsResponse {
        let url = buildURL(path: "content/cache/recommendations", queryItems: [
            URLQueryItem(name: "language", value: language),
            URLQueryItem(name: "currentCacheSize", value: String(currentCacheSize))
        ])
        
        let request = try buildAuthenticatedRequest(url: url)
        let (data, response) = try await session.data(for: request)
        
        try validateResponse(response)
        return try JSONDecoder().decode(CacheRecommendationsResponse.self, from: data)
    }
    
    func prefetchContent(
        language: String,
        lessonCount: Int,
        deviceCapacity: Int?,
        connectionType: String = "wifi",
        batteryLevel: Double = 0.8
    ) async throws -> PrefetchResponse {
        let url = buildURL(path: "content/cache/prefetch")
        var request = try buildAuthenticatedRequest(url: url, method: "POST")
        
        let requestBody = PrefetchRequest(
            language: language,
            lessonCount: lessonCount,
            deviceCapacity: deviceCapacity
        )
        
        request.httpBody = try JSONEncoder().encode(requestBody)
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        
        let (data, response) = try await session.data(for: request)
        try validateResponse(response)
        
        return try JSONDecoder().decode(PrefetchResponse.self, from: data)
    }
    
    // MARK: - Private Helper Methods
    
    private func buildURL(path: String, queryItems: [URLQueryItem]? = nil) -> URL {
        var components = URLComponents(string: "\(baseURL)/\(path)")!
        components.queryItems = queryItems
        return components.url!
    }
    
    private func buildAuthenticatedRequest(url: URL, method: String = "GET") throws -> URLRequest {
        var request = URLRequest(url: url)
        request.httpMethod = method
        
        guard let token = jwtToken else {
            throw APIClientError.missingAuthToken
        }
        
        request.setValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
        return request
    }
    
    private func validateResponse(_ response: URLResponse) throws {
        guard let httpResponse = response as? HTTPURLResponse else {
            throw APIClientError.invalidResponse
        }
        
        switch httpResponse.statusCode {
        case 200...299:
            return
        case 401:
            throw APIClientError.unauthorized
        case 403:
            throw APIClientError.forbidden
        case 404:
            throw APIClientError.notFound
        case 429:
            throw APIClientError.rateLimitExceeded
        case 500...599:
            throw APIClientError.serverError
        default:
            throw APIClientError.httpError(httpResponse.statusCode)
        }
    }
    
    private func checkConnectivity() {
        // For testing: Always set as connected
        Task {
            await MainActor.run {
                self.isConnected = true
            }
        }
    }
}

// MARK: - Error Types

enum APIClientError: Error, LocalizedError {
    case missingAuthToken
    case invalidResponse
    case unauthorized
    case forbidden
    case notFound
    case rateLimitExceeded
    case serverError
    case httpError(Int)
    case networkError(Error)
    
    var errorDescription: String? {
        switch self {
        case .missingAuthToken:
            return "Authentication token is missing"
        case .invalidResponse:
            return "Invalid response from server"
        case .unauthorized:
            return "Unauthorized access"
        case .forbidden:
            return "Access forbidden"
        case .notFound:
            return "Resource not found"
        case .rateLimitExceeded:
            return "Rate limit exceeded"
        case .serverError:
            return "Server error"
        case .httpError(let code):
            return "HTTP error with status code: \(code)"
        case .networkError(let error):
            return "Network error: \(error.localizedDescription)"
        }
    }
}

// MARK: - Request/Response Types

struct LessonBundleRequest: Codable {
    let language: String
    let bundleSize: Int
    let preferences: UserPreferences?
}

struct PersonalizedLessonsResponse: Codable {
    let lessons: [APIClientLessonMetadata]
    let totalAvailable: Int
    let cacheRecommendations: [String]
}

struct LessonResponse: Codable {
    let lesson: APIClientLessonContent
}

struct LessonBundleResponse: Codable {
    let bundleId: String
    let lessonIds: [String]
    let priorityLessons: [APIClientLessonContent]
    let estimatedSize: Int
    let expiresAt: Date
    let cacheRecommendations: [CacheRecommendation]
}

struct CacheRecommendationsResponse: Codable {
    let recommendations: [CacheRecommendation]
    let optimalCacheSize: Int
    let refreshInterval: TimeInterval
}

struct CacheRecommendation: Codable {
    let lessonId: String
    let priority: PrefetchPriority
    let estimatedSize: Int
    let reason: String
}

struct PrefetchRequest: Codable {
    let language: String
    let lessonCount: Int
    let deviceCapacity: Int?
}

struct PrefetchResponse: Codable {
    let prefetchId: String
    let data: PrefetchData
    let downloadUrls: [String]
    let cacheStrategy: CacheStrategy
}

struct PrefetchData: Codable {
    let lessonIds: [String]
    let priority: PrefetchPriority
    let estimatedSize: Int
    let expiresAt: Date
}

struct CacheStrategy: Codable {
    let type: String
    let maxSize: Int
    let refreshPolicy: String
}

// Renamed to avoid conflicts with SupabaseClient types
struct APIClientLessonMetadata: Codable {
    let id: String
    let title: String
    let description: String
    let difficulty: String
    let estimatedDuration: Int
    let category: String
}

struct APIClientLessonContent: Codable {
    let id: String
    let title: String
    let description: String
    let content: String
    let vocabulary: [APIClientVocabularyItem]
    let exercises: [APIClientExercise]
    let culturalContext: CulturalContentDTO
    let language: String
    let difficulty: String
    let category: String
    let estimatedDuration: Int
    let topics: [String]
    let learningObjectives: [String]
}

struct APIClientExercise: Codable {
    let id: String
    let type: String
    let question: String
    let options: [String]?
    let correctAnswer: String
    let explanation: String?
    let points: Int
}

// Renamed to avoid conflicts with SupabaseClient types
struct APIClientVocabularyItem: Codable {
    let word: String
    let translation: String
    let partOfSpeech: String?
    let context: String?
    let difficulty: String?
}

struct CulturalContentDTO: Codable {
    let id: String
    let language: String
    let region: String
    let topic: String
    let complexity: String
    let tags: [String]
}

struct UserPreferences: Codable {
    let focusAreas: [String]
    let avoidTopics: [String]
    let preferredExerciseTypes: [String]
}

enum PrefetchPriority: String, Codable {
    case high = "high"
    case medium = "medium"
    case low = "low"
} 