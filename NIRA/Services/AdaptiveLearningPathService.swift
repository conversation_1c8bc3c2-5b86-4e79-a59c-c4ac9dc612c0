import Foundation
import Combine

// MARK: - Adaptive Learning Path Service with AI Agent Integration

@MainActor
class AdaptiveLearningPathService: ObservableObject {
    static let shared = AdaptiveLearningPathService()

    // MARK: - Published Properties
    @Published var currentLearningPath: AdaptiveLearningPath?
    @Published var pathRecommendations: [LearningPathRecommendation] = []
    @Published var isGeneratingPath = false
    @Published var pathProgress: LearningPathProgress?

    // MARK: - Dependencies
    private let agentOrchestrationService = AgentOrchestrationService.shared
    private let aiPersonalizationService = AIPersonalizationService.shared
    private let predictiveAnalyticsService = PredictiveAnalyticsService.shared
    private let userPreferencesService = UserPreferencesService.shared

    // MARK: - Path Generation State
    private var pathGenerationAgents: [AgentType] = [.tutor, .progressCoach, .assessmentAgent]
    private var learningPathHistory: [AdaptiveLearningPath] = []

    private init() {
        setupPathGeneration()
    }

    // MARK: - Public Methods

    func generateAdaptiveLearningPath(
        for user: User,
        language: Language,
        goals: [LearningGoal],
        timeframe: LearningTimeframe
    ) async throws -> AdaptiveLearningPath {
        isGeneratingPath = true
        defer { isGeneratingPath = false }

        // Create learning scenario for path generation
        let pathScenario = LearningScenario(
            type: .comprehensiveAssessment,
            difficulty: user.currentLevel?.toDifficultyLevel() ?? .beginner,
            objectives: goals.map { $0.description },
            culturalContext: nil
        )

        // Start multi-agent session for path generation
        let session = try await agentOrchestrationService.startMultiAgentSession(
            for: user,
            language: language,
            scenario: pathScenario
        )

        // Gather input from different agents
        let pathInput = UserInput(
            text: "Generate a personalized learning path for \(language.displayName) with goals: \(goals.map { $0.description }.joined(separator: ", "))",
            type: .text,
            timestamp: Date(),
            metadata: [
                "timeframe": timeframe.rawValue,
                "current_level": user.currentLevel?.rawValue ?? "beginner",
                "learning_goals": goals.map { $0.rawValue }
            ]
        )

        // Get agent recommendations
        let agentResponse = try await agentOrchestrationService.processUserInput(pathInput, session: session)

        // Synthesize path from agent responses
        let adaptivePath = try await synthesizeLearningPath(
            from: agentResponse,
            user: user,
            language: language,
            goals: goals,
            timeframe: timeframe
        )

        // Store and activate the path
        currentLearningPath = adaptivePath
        learningPathHistory.append(adaptivePath)

        // End the agent session
        await agentOrchestrationService.endMultiAgentSession(session.id)

        return adaptivePath
    }

    func updateLearningPathProgress(
        _ progress: LearningActivity,
        path: AdaptiveLearningPath
    ) async throws -> PathUpdateResult {
        guard var currentPath = currentLearningPath else {
            throw LearningPathError.noActivePath
        }

        // Update progress
        currentPath.activities.append(progress)
        currentPath.lastUpdated = Date()

        // Analyze progress with AI agents
        let progressAnalysis = try await analyzeProgressWithAgents(
            progress: progress,
            path: currentPath
        )

        // Determine if path needs adjustment
        if progressAnalysis.needsAdjustment {
            let adjustedPath = try await adjustLearningPath(
                currentPath,
                based: progressAnalysis
            )
            currentLearningPath = adjustedPath

            return PathUpdateResult(
                updatedPath: adjustedPath,
                adjustmentMade: true,
                recommendations: progressAnalysis.recommendations
            )
        } else {
            currentLearningPath = currentPath

            return PathUpdateResult(
                updatedPath: currentPath,
                adjustmentMade: false,
                recommendations: progressAnalysis.recommendations
            )
        }
    }

    func generateNextLearningActivity(
        for path: AdaptiveLearningPath
    ) async throws -> LearningActivity {
        // Use AI agents to determine the next optimal activity
        let nextActivityScenario = LearningScenario(
            type: determineNextActivityType(from: path),
            difficulty: calculateCurrentDifficulty(from: path),
            objectives: extractCurrentObjectives(from: path),
            culturalContext: path.culturalContext
        )

        let session = try await agentOrchestrationService.startMultiAgentSession(
            for: path.user,
            language: path.language,
            scenario: nextActivityScenario
        )

        let activityInput = UserInput(
            text: "Generate the next optimal learning activity based on current progress",
            type: .text,
            timestamp: Date(),
            metadata: [
                "path_id": path.id.uuidString,
                "completed_activities": path.activities.count,
                "current_performance": calculatePerformanceScore(from: path)
            ]
        )

        let agentResponse = try await agentOrchestrationService.processUserInput(activityInput, session: session)

        let nextActivity = try await synthesizeNextActivity(
            from: agentResponse,
            path: path
        )

        await agentOrchestrationService.endMultiAgentSession(session.id)

        return nextActivity
    }

    func getPersonalizedRecommendations(
        for user: User,
        language: Language
    ) async throws -> [LearningPathRecommendation] {
        // Generate recommendations using AI agents
        let recommendations = try await generateAgentBasedRecommendations(
            user: user,
            language: language
        )

        await MainActor.run {
            pathRecommendations = recommendations
        }

        return recommendations
    }

    // MARK: - Private Methods

    private func setupPathGeneration() {
        // Initialize path generation system
    }

    private func synthesizeLearningPath(
        from agentResponse: MultiAgentResponse,
        user: User,
        language: Language,
        goals: [LearningGoal],
        timeframe: LearningTimeframe
    ) async throws -> AdaptiveLearningPath {

        // Extract recommendations from different agents
        var pathComponents: [PathComponent] = []

        for (agentType, response) in agentResponse.agentResponses {
            let component = extractPathComponent(from: response, agentType: agentType)
            pathComponents.append(component)
        }

        // Create the adaptive learning path
        let path = AdaptiveLearningPath(
            id: UUID(),
            user: user,
            language: language,
            goals: goals,
            timeframe: timeframe,
            difficulty: user.currentLevel?.toDifficultyLevel() ?? .beginner,
            pathComponents: pathComponents,
            activities: [],
            milestones: generateMilestones(from: pathComponents, timeframe: timeframe),
            adaptationRules: createAdaptationRules(for: user, goals: goals),
            createdAt: Date(),
            lastUpdated: Date(),
            culturalContext: extractCulturalContext(from: agentResponse)
        )

        return path
    }

    private func analyzeProgressWithAgents(
        progress: LearningActivity,
        path: AdaptiveLearningPath
    ) async throws -> ProgressAnalysis {

        let analysisScenario = LearningScenario(
            type: .comprehensiveAssessment,
            difficulty: path.difficulty,
            objectives: ["Analyze learning progress", "Identify improvement areas"],
            culturalContext: path.culturalContext
        )

        let session = try await agentOrchestrationService.startMultiAgentSession(
            for: path.user,
            language: path.language,
            scenario: analysisScenario
        )

        let analysisInput = UserInput(
            text: "Analyze the latest learning activity and overall progress",
            type: .text,
            timestamp: Date(),
            metadata: [
                "activity_type": progress.type.rawValue,
                "performance_score": progress.performanceScore,
                "completion_time": progress.completionTime
            ]
        )

        let agentResponse = try await agentOrchestrationService.processUserInput(analysisInput, session: session)

        let analysis = synthesizeProgressAnalysis(from: agentResponse, progress: progress, path: path)

        await agentOrchestrationService.endMultiAgentSession(session.id)

        return analysis
    }

    private func adjustLearningPath(
        _ path: AdaptiveLearningPath,
        based analysis: ProgressAnalysis
    ) async throws -> AdaptiveLearningPath {

        var adjustedPath = path

        // Apply adjustments based on analysis
        if analysis.difficultyAdjustment != 0 {
            adjustedPath.difficulty = adjustDifficulty(
                current: path.difficulty,
                adjustment: analysis.difficultyAdjustment
            )
        }

        // Update path components based on recommendations
        for recommendation in analysis.recommendations {
            if let adjustment = recommendation.pathAdjustment {
                adjustedPath = applyPathAdjustment(adjustedPath, adjustment: adjustment)
            }
        }

        adjustedPath.lastUpdated = Date()

        return adjustedPath
    }

    private func extractPathComponent(from response: AgentResponse, agentType: AgentType) -> PathComponent {
        return PathComponent(
            id: UUID(),
            agentType: agentType,
            componentType: mapAgentToComponentType(agentType),
            content: response.content,
            priority: response.confidence,
            estimatedDuration: estimateDuration(for: agentType),
            prerequisites: [],
            learningObjectives: extractObjectives(from: response.content)
        )
    }

    private func mapAgentToComponentType(_ agentType: AgentType) -> PathComponentType {
        switch agentType {
        case .tutor: return .instruction
        case .conversationPartner: return .practice
        case .culturalGuide: return .cultural
        case .progressCoach: return .assessment
        case .scenarioDirector: return .simulation
        case .speechCoach: return .pronunciation
        case .assessmentAgent: return .evaluation
        }
    }

    private func generateMilestones(from components: [PathComponent], timeframe: LearningTimeframe) -> [LearningMilestone] {
        let milestoneCount = timeframe.suggestedMilestones
        let componentsPerMilestone = max(1, components.count / milestoneCount)

        var milestones: [LearningMilestone] = []

        for i in 0..<milestoneCount {
            let startIndex = i * componentsPerMilestone
            let endIndex = min(startIndex + componentsPerMilestone, components.count)
            let milestoneComponents = Array(components[startIndex..<endIndex])

            let milestone = LearningMilestone(
                title: "Milestone \(i + 1)",
                description: "Complete \(milestoneComponents.count) learning components",
                skillArea: .speaking, // Default skill area
                requiredLessons: milestoneComponents.count,
                rewardPoints: 100
            )

            milestones.append(milestone)
        }

        return milestones
    }

    private func createAdaptationRules(for user: User, goals: [LearningGoal]) -> [AdaptationRule] {
        return [
            AdaptationRule(
                condition: .performanceBelow(threshold: 0.6),
                action: .decreaseDifficulty,
                priority: .high
            ),
            AdaptationRule(
                condition: .performanceAbove(threshold: 0.9),
                action: .increaseDifficulty,
                priority: .medium
            ),
            AdaptationRule(
                condition: .timeSpentExceeds(minutes: 30),
                action: .suggestBreak,
                priority: .low
            )
        ]
    }

    // MARK: - Helper Methods

    private func determineNextActivityType(from path: AdaptiveLearningPath) -> LearningScenario.ScenarioType {
        let recentActivities = path.activities.suffix(3)
        let activityTypes = recentActivities.map { $0.type }

        // Vary activity types for engagement
        if !activityTypes.contains(.conversation) {
            return .conversation
        } else if !activityTypes.contains(.assessment) {
            return .comprehensiveAssessment
        } else {
            return .grammarFocus
        }
    }

    private func calculateCurrentDifficulty(from path: AdaptiveLearningPath) -> LearningScenario.DifficultyLevel {
        let recentPerformance = path.activities.suffix(5).map { $0.performanceScore }
        let averagePerformance = recentPerformance.isEmpty ? 0.5 : recentPerformance.reduce(0, +) / Double(recentPerformance.count)

        if averagePerformance > 0.8 {
            return path.difficulty == .beginner ? .intermediate : .advanced
        } else if averagePerformance < 0.6 {
            return path.difficulty == .advanced ? .intermediate : .beginner
        } else {
            return path.difficulty
        }
    }

    private func extractCurrentObjectives(from path: AdaptiveLearningPath) -> [String] {
        return path.goals.map { $0.description }
    }

    private func calculatePerformanceScore(from path: AdaptiveLearningPath) -> Double {
        let scores = path.activities.map { $0.performanceScore }
        return scores.isEmpty ? 0.5 : scores.reduce(0, +) / Double(scores.count)
    }

    private func synthesizeNextActivity(
        from agentResponse: MultiAgentResponse,
        path: AdaptiveLearningPath
    ) async throws -> LearningActivity {

        // Extract activity details from agent responses
        let activityType = determineActivityType(from: agentResponse)
        let content = agentResponse.synthesizedResponse

        return LearningActivity(
            id: UUID(),
            type: activityType,
            content: content,
            performanceScore: 0.0, // Will be updated when completed
            completionTime: 0.0, // Will be updated when completed
            timestamp: Date()
        )
    }

    private func determineActivityType(from response: MultiAgentResponse) -> LearningActivity.ActivityType {
        let content = response.synthesizedResponse.lowercased()

        if content.contains("conversation") || content.contains("dialogue") {
            return .conversation
        } else if content.contains("assessment") || content.contains("test") {
            return .assessment
        } else if content.contains("simulation") || content.contains("scenario") {
            return .simulation
        } else if content.contains("practice") {
            return .practice
        } else {
            return .lesson
        }
    }

    private func generateAgentBasedRecommendations(
        user: User,
        language: Language
    ) async throws -> [LearningPathRecommendation] {

        // Generate recommendations using AI agents
        let recommendationScenario = LearningScenario(
            type: .comprehensiveAssessment,
            difficulty: user.currentLevel?.toDifficultyLevel() ?? .beginner,
            objectives: ["Generate learning recommendations"],
            culturalContext: nil
        )

        let session = try await agentOrchestrationService.startMultiAgentSession(
            for: user,
            language: language,
            scenario: recommendationScenario
        )

        let recommendationInput = UserInput(
            text: "Generate personalized learning recommendations",
            type: .text,
            timestamp: Date(),
            metadata: [
                "user_level": user.currentLevel?.rawValue ?? "beginner",
                "target_language": language.rawValue
            ]
        )

        let agentResponse = try await agentOrchestrationService.processUserInput(recommendationInput, session: session)

        let recommendations = synthesizeRecommendations(from: agentResponse)

        await agentOrchestrationService.endMultiAgentSession(session.id)

        return recommendations
    }

    private func synthesizeRecommendations(from response: MultiAgentResponse) -> [LearningPathRecommendation] {
        var recommendations: [LearningPathRecommendation] = []

        for (agentType, agentResponse) in response.agentResponses {
            let recommendation = LearningPathRecommendation(
                id: UUID(),
                title: "\(agentType.displayName) Recommendation",
                description: agentResponse.content,
                priority: agentResponse.confidence,
                pathAdjustment: nil
            )
            recommendations.append(recommendation)
        }

        return recommendations.sorted { $0.priority > $1.priority }
    }

    private func synthesizeProgressAnalysis(
        from response: MultiAgentResponse,
        progress: LearningActivity,
        path: AdaptiveLearningPath
    ) -> ProgressAnalysis {

        let needsAdjustment = progress.performanceScore < 0.6 || progress.performanceScore > 0.9
        let difficultyAdjustment = progress.performanceScore < 0.6 ? -1 : (progress.performanceScore > 0.9 ? 1 : 0)

        let recommendations = synthesizeRecommendations(from: response)

        return ProgressAnalysis(
            needsAdjustment: needsAdjustment,
            difficultyAdjustment: difficultyAdjustment,
            recommendations: recommendations,
            strengths: extractStrengths(from: response),
            improvementAreas: extractImprovementAreas(from: response)
        )
    }

    private func extractStrengths(from response: MultiAgentResponse) -> [String] {
        return ["Good comprehension", "Consistent practice", "Improving vocabulary"]
    }

    private func extractImprovementAreas(from response: MultiAgentResponse) -> [String] {
        return ["Pronunciation clarity", "Grammar accuracy", "Speaking confidence"]
    }

    private func adjustDifficulty(
        current: LearningScenario.DifficultyLevel,
        adjustment: Int
    ) -> LearningScenario.DifficultyLevel {

        let levels: [LearningScenario.DifficultyLevel] = [.beginner, .intermediate, .advanced]
        guard let currentIndex = levels.firstIndex(of: current) else { return current }

        let newIndex = max(0, min(levels.count - 1, currentIndex + adjustment))
        return levels[newIndex]
    }

    private func applyPathAdjustment(
        _ path: AdaptiveLearningPath,
        adjustment: LearningPathRecommendation.PathAdjustment
    ) -> AdaptiveLearningPath {
        // Apply specific path adjustments based on recommendation
        var adjustedPath = path

        switch adjustment.type {
        case .modifyDifficulty:
            if let difficultyChange = adjustment.parameters["difficulty_change"] as? Int {
                adjustedPath.difficulty = adjustDifficulty(current: path.difficulty, adjustment: difficultyChange)
            }
        case .addComponent, .removeComponent, .changeSequence:
            // These would require more complex path modifications
            break
        }

        return adjustedPath
    }

    private func extractCulturalContext(from response: MultiAgentResponse) -> String? {
        // Extract cultural context from cultural guide agent response
        if let culturalResponse = response.agentResponses[.culturalGuide] {
            return culturalResponse.content
        }
        return nil
    }

    private func estimateDuration(for agentType: AgentType) -> TimeInterval {
        switch agentType {
        case .tutor: return 15 * 60 // 15 minutes
        case .conversationPartner: return 20 * 60 // 20 minutes
        case .culturalGuide: return 10 * 60 // 10 minutes
        case .progressCoach: return 5 * 60 // 5 minutes
        case .scenarioDirector: return 25 * 60 // 25 minutes
        case .speechCoach: return 15 * 60 // 15 minutes
        case .assessmentAgent: return 10 * 60 // 10 minutes
        }
    }

    private func extractObjectives(from content: String) -> [String] {
        // Extract learning objectives from agent response content
        return ["Improve language skills", "Practice communication", "Build confidence"]
    }

    private func calculateMilestoneDate(
        index: Int,
        total: Int,
        timeframe: LearningTimeframe
    ) -> Date {
        let totalDays: Int

        switch timeframe {
        case .oneMonth: totalDays = 30
        case .threeMonths: totalDays = 90
        case .sixMonths: totalDays = 180
        case .oneYear: totalDays = 365
        case .flexible: totalDays = 90
        }

        let daysPerMilestone = totalDays / total
        let targetDays = (index + 1) * daysPerMilestone

        return Calendar.current.date(byAdding: .day, value: targetDays, to: Date()) ?? Date()
    }
}

// MARK: - Extensions

extension SkillLevel {
    func toDifficultyLevel() -> LearningScenario.DifficultyLevel {
        switch self {
        case .beginner: return .beginner
        case .elementary: return .beginner
        case .intermediate: return .intermediate
        case .upperIntermediate: return .intermediate
        case .advanced: return .advanced
        case .mastery: return .advanced
        }
    }
}

// MARK: - Supporting Types

struct AdaptiveLearningPath {
    let id: UUID
    let user: User
    let language: Language
    let goals: [LearningGoal]
    let timeframe: LearningTimeframe
    var difficulty: LearningScenario.DifficultyLevel
    let pathComponents: [PathComponent]
    var activities: [LearningActivity]
    let milestones: [LearningMilestone]
    let adaptationRules: [AdaptationRule]
    let createdAt: Date
    var lastUpdated: Date
    let culturalContext: String?
}

struct PathComponent {
    let id: UUID
    let agentType: AgentType
    let componentType: PathComponentType
    let content: String
    let priority: Double
    let estimatedDuration: TimeInterval
    let prerequisites: [UUID]
    let learningObjectives: [String]
}

enum PathComponentType {
    case instruction
    case practice
    case cultural
    case assessment
    case simulation
    case pronunciation
    case evaluation
}

// LearningGoal is defined in AdaptiveLearningModels.swift

enum LearningTimeframe: String, CaseIterable {
    case oneMonth = "one_month"
    case threeMonths = "three_months"
    case sixMonths = "six_months"
    case oneYear = "one_year"
    case flexible = "flexible"

    var suggestedMilestones: Int {
        switch self {
        case .oneMonth: return 4
        case .threeMonths: return 6
        case .sixMonths: return 8
        case .oneYear: return 12
        case .flexible: return 6
        }
    }
}

struct LearningActivity {
    let id: UUID
    let type: ActivityType
    let content: String
    let performanceScore: Double
    let completionTime: TimeInterval
    let timestamp: Date

    enum ActivityType: String {
        case lesson, practice, assessment, simulation, conversation
    }
}

// LearningMilestone is defined in CurriculumService.swift

struct AdaptationRule {
    let condition: AdaptationCondition
    let action: AdaptationAction
    let priority: AdaptationPriority

    enum AdaptationCondition {
        case performanceBelow(threshold: Double)
        case performanceAbove(threshold: Double)
        case timeSpentExceeds(minutes: Int)
    }

    enum AdaptationAction {
        case decreaseDifficulty
        case increaseDifficulty
        case suggestBreak
    }

    enum AdaptationPriority {
        case low, medium, high
    }
}

struct ProgressAnalysis {
    let needsAdjustment: Bool
    let difficultyAdjustment: Int
    let recommendations: [LearningPathRecommendation]
    let strengths: [String]
    let improvementAreas: [String]
}

struct LearningPathRecommendation {
    let id: UUID
    let title: String
    let description: String
    let priority: Double
    let pathAdjustment: PathAdjustment?

    struct PathAdjustment {
        let type: AdjustmentType
        let parameters: [String: Any]

        enum AdjustmentType {
            case addComponent, removeComponent, modifyDifficulty, changeSequence
        }
    }
}

struct PathUpdateResult {
    let updatedPath: AdaptiveLearningPath
    let adjustmentMade: Bool
    let recommendations: [LearningPathRecommendation]
}

enum LearningPathError: Error {
    case noActivePath
    case pathGenerationFailed
    case invalidAdjustment
}
