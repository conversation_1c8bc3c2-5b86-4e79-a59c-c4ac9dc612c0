import Foundation
import Combine
import Supabase

// MARK: - Data Models

struct Tournament: Codable, Identifiable {
    let id: UUID
    let title: String
    let description: String
    let type: TournamentType
    let language: Language
    let skillFocus: SkillCategory?
    let startDate: Date
    let endDate: Date
    let maxParticipants: Int
    var currentParticipants: Int
    let entryRequirement: TournamentRequirement?
    let prizes: [TournamentPrize]
    let status: TournamentStatus
    let bracket: TournamentBracket?
    let rules: [String]
    let createdBy: UUID
    let createdAt: Date
}

enum TournamentType: String, Codable, CaseIterable {
    case singleElimination = "single_elimination"
    case doubleElimination = "double_elimination"
    case roundRobin = "round_robin"
    case leaderboard = "leaderboard"
    case teamBased = "team_based"
    case speedChallenge = "speed_challenge"
    case endurance = "endurance"
}

enum TournamentStatus: String, Codable {
    case upcoming = "upcoming"
    case registrationOpen = "registration_open"
    case inProgress = "in_progress"
    case completed = "completed"
    case cancelled = "cancelled"
}

struct TournamentRequirement: Codable {
    let minimumLevel: ProficiencyLevel?
    let minimumXP: Int?
    let requiredAchievements: [UUID]?
    let guildMembership: UUID?
}

struct TournamentPrize: Codable, Identifiable {
    let id: UUID
    let position: Int
    let title: String
    let description: String
    let xpReward: Int
    let badgeId: UUID?
    let certificateId: UUID?
    let specialReward: String?
}

struct TournamentBracket: Codable {
    let rounds: [TournamentRound]
    let currentRound: Int
    let participants: [TournamentParticipant]
}

struct TournamentRound: Codable, Identifiable {
    let id: UUID
    let roundNumber: Int
    let matches: [TournamentMatch]
    let startTime: Date
    let endTime: Date?
}

struct TournamentMatch: Codable, Identifiable {
    let id: UUID
    let participant1: UUID
    let participant2: UUID
    let winner: UUID?
    let score1: Double
    let score2: Double
    let status: MatchStatus
    let challengeType: EventChallengeType
    let startTime: Date
    let endTime: Date?
}

enum MatchStatus: String, Codable {
    case scheduled = "scheduled"
    case inProgress = "in_progress"
    case completed = "completed"
    case forfeit = "forfeit"
}

struct TournamentParticipant: Codable, Identifiable {
    let id: UUID
    let userId: UUID
    let tournamentId: UUID
    let registrationDate: Date
    let currentRound: Int
    let totalScore: Double
    let matchesWon: Int
    let matchesLost: Int
    let isEliminated: Bool
    let finalPosition: Int?
}

// MARK: - Guild System

struct Guild: Codable, Identifiable {
    let id: UUID
    let name: String
    let description: String
    let language: Language
    let level: GuildLevel
    let memberCount: Int
    let maxMembers: Int
    let totalXP: Int
    let weeklyGoal: Int
    let currentWeeklyXP: Int
    let emblem: String
    let isPublic: Bool
    let requirements: GuildRequirements?
    let perks: [GuildPerk]
    let createdBy: UUID
    let createdAt: Date
}

enum GuildLevel: String, Codable, CaseIterable {
    case bronze = "bronze"
    case silver = "silver"
    case gold = "gold"
    case platinum = "platinum"
    case diamond = "diamond"
    
    var requiredXP: Int {
        switch self {
        case .bronze: return 0
        case .silver: return 10000
        case .gold: return 50000
        case .platinum: return 150000
        case .diamond: return 500000
        }
    }
    
    var maxMembers: Int {
        switch self {
        case .bronze: return 10
        case .silver: return 20
        case .gold: return 30
        case .platinum: return 50
        case .diamond: return 100
        }
    }
}

struct GuildRequirements: Codable {
    let minimumLevel: ProficiencyLevel?
    let minimumXP: Int?
    let requiredLanguages: [Language]?
    let applicationRequired: Bool
}

struct GuildPerk: Codable, Identifiable {
    let id: UUID
    let name: String
    let description: String
    let type: PerkType
    let value: Double
    let isActive: Bool
}

enum PerkType: String, Codable {
    case xpBonus = "xp_bonus"
    case streakProtection = "streak_protection"
    case exclusiveContent = "exclusive_content"
    case prioritySupport = "priority_support"
    case customization = "customization"
}

struct GuildMember: Codable, Identifiable {
    let id: UUID
    let userId: UUID
    let guildId: UUID
    let role: GuildRole
    let joinDate: Date
    let contributedXP: Int
    let weeklyContribution: Int
    let isActive: Bool
}

enum GuildRole: String, Codable {
    case member = "member"
    case officer = "officer"
    case leader = "leader"
    case founder = "founder"
}

struct GuildChallenge: Codable, Identifiable {
    let id: UUID
    let guildId: UUID
    let title: String
    let description: String
    let targetXP: Int
    let currentXP: Int
    let startDate: Date
    let endDate: Date
    let reward: GuildReward
    let isCompleted: Bool
    let participants: [UUID]
}

struct GuildReward: Codable {
    let xpBonus: Int
    let specialBadge: UUID?
    let exclusiveContent: String?
    let customization: String?
}

// MARK: - Advanced Achievements

struct AdvancedAchievement: Codable, Identifiable {
    let id: UUID
    let title: String
    let description: String
    let category: AchievementCategory
    let tier: AchievementTier
    let requirements: [AchievementRequirement]
    let rewards: AchievementReward
    let isSecret: Bool
    let isLimited: Bool
    let expiryDate: Date?
    let iconUrl: String
    let badgeUrl: String?
    let createdAt: Date
}

enum AchievementCategory: String, Codable, CaseIterable {
    case learning = "learning"
    case social = "social"
    case competition = "competition"
    case consistency = "consistency"
    case mastery = "mastery"
    case exploration = "exploration"
    case community = "community"
}

enum AchievementTier: String, Codable, CaseIterable {
    case bronze = "bronze"
    case silver = "silver"
    case gold = "gold"
    case platinum = "platinum"
    case legendary = "legendary"
    
    var multiplier: Double {
        switch self {
        case .bronze: return 1.0
        case .silver: return 1.5
        case .gold: return 2.0
        case .platinum: return 3.0
        case .legendary: return 5.0
        }
    }
}

struct AchievementRequirement: Codable {
    let type: RequirementType
    let target: Double
    let timeframe: TimeInterval?
    let conditions: [String: String]?
}

enum RequirementType: String, Codable {
    case totalXP = "total_xp"
    case streakDays = "streak_days"
    case lessonsCompleted = "lessons_completed"
    case vocabularyMastered = "vocabulary_mastered"
    case conversationMinutes = "conversation_minutes"
    case tournamentWins = "tournament_wins"
    case guildContribution = "guild_contribution"
    case perfectScores = "perfect_scores"
    case languagesLearned = "languages_learned"
    case friendsReferred = "friends_referred"
}

struct AchievementReward: Codable {
    let xp: Int
    let badge: String?
    let title: String?
    let customization: String?
    let exclusiveContent: String?
    let specialPerk: String?
}

struct UserAchievement: Codable, Identifiable {
    let id: UUID
    let userId: UUID
    let achievementId: UUID
    let progress: Double
    var isCompleted: Bool
    var completedAt: Date?
    let isDisplayed: Bool
}

// MARK: - Seasonal Events

struct SeasonalEvent: Codable, Identifiable {
    let id: UUID
    let title: String
    let description: String
    let theme: String
    let startDate: Date
    let endDate: Date
    let challenges: [EventChallenge]
    let rewards: [EventReward]
    let leaderboard: EventLeaderboard?
    let isActive: Bool
}

struct EventChallenge: Codable, Identifiable {
    let id: UUID
    let title: String
    let description: String
    let type: EventChallengeType
    let target: Double
    let reward: EventReward
    let isCompleted: Bool
}

enum EventChallengeType: String, Codable {
    case dailyStreak = "daily_streak"
    case vocabularyChallenge = "vocabulary_challenge"
    case conversationGoal = "conversation_goal"
    case pronunciationPerfect = "pronunciation_perfect"
    case speedLearning = "speed_learning"
    case culturalExploration = "cultural_exploration"
}

struct EventReward: Codable, Identifiable {
    let id: UUID
    let title: String
    let description: String
    let type: RewardType
    let value: String
    let rarity: RewardRarity
}

enum RewardType: String, Codable {
    case xp = "xp"
    case badge = "badge"
    case avatar = "avatar"
    case theme = "theme"
    case title = "title"
    case emote = "emote"
}

enum RewardRarity: String, Codable {
    case common = "common"
    case rare = "rare"
    case epic = "epic"
    case legendary = "legendary"
}

struct EventLeaderboard: Codable {
    let participants: [EventParticipant]
    let lastUpdated: Date
}

struct EventParticipant: Codable, Identifiable {
    let id: UUID
    let userId: UUID
    let score: Double
    let rank: Int
    let rewards: [EventReward]
}

// MARK: - Leaderboard Types
// Note: LeaderboardEntry and RankChange are defined in SocialFeaturesService.swift

// MARK: - Service

@MainActor
class AdvancedGamificationService: ObservableObject {
    @Published var activeTournaments: [Tournament] = []
    @Published var userGuild: Guild?
    @Published var availableGuilds: [Guild] = []
    @Published var userAchievements: [UserAchievement] = []
    @Published var availableAchievements: [AdvancedAchievement] = []
    @Published var activeEvents: [SeasonalEvent] = []
    @Published var leaderboards: [String: [LeaderboardEntry]] = [:]
    @Published var isLoading = false
    
    private let supabaseClient: NIRASupabaseClient
    private let analyticsService: LearningAnalyticsService
    private let realtimeCollaborationService: RealtimeCollaborationService
    private var cancellables = Set<AnyCancellable>()
    
    init(
        supabaseClient: NIRASupabaseClient? = nil,
        analyticsService: LearningAnalyticsService? = nil,
        realtimeCollaborationService: RealtimeCollaborationService? = nil
    ) {
        self.supabaseClient = supabaseClient ?? .shared
        self.analyticsService = analyticsService ?? .shared
        self.realtimeCollaborationService = realtimeCollaborationService ?? .shared
        
        setupObservers()
        loadGamificationData()
    }
    
    // MARK: - Tournament Management
    
    func createTournament(
        title: String,
        type: TournamentType,
        language: Language,
        skillFocus: SkillCategory?,
        duration: TimeInterval,
        maxParticipants: Int
    ) async throws -> Tournament {
        isLoading = true
        defer { isLoading = false }
        
        let tournament = Tournament(
            id: UUID(),
            title: title,
            description: "Competitive \(language.displayName) tournament",
            type: type,
            language: language,
            skillFocus: skillFocus,
            startDate: Date().addingTimeInterval(3600), // Start in 1 hour
            endDate: Date().addingTimeInterval(duration),
            maxParticipants: maxParticipants,
            currentParticipants: 0,
            entryRequirement: nil,
            prizes: generateTournamentPrizes(type: type),
            status: .registrationOpen,
            bracket: nil,
            rules: generateTournamentRules(type: type),
            createdBy: UUID(), // Current user ID
            createdAt: Date()
        )
        
        // Save to database
        try await saveTournamentToDatabase(tournament)
        
        activeTournaments.append(tournament)
        
        // Track tournament creation
        analyticsService.trackInteraction(
            userId: UUID(), // Would get current user ID in real implementation
            interactionType: .exerciseAttempt, // Using closest available type
            contentType: .exercise,
            contentId: tournament.id.uuidString,
            isCorrect: nil,
            responseTime: nil,
            metadata: [
                "type": SupabaseAnyCodable(type.rawValue),
                "language": SupabaseAnyCodable(language.rawValue),
                "max_participants": SupabaseAnyCodable(maxParticipants)
            ]
        )
        
        return tournament
    }
    
    func joinTournament(_ tournamentId: UUID, userId: UUID) async throws {
        guard let tournamentIndex = activeTournaments.firstIndex(where: { $0.id == tournamentId }) else {
            throw GamificationError.tournamentNotFound
        }
        
        var tournament = activeTournaments[tournamentIndex]
        
        guard tournament.status == .registrationOpen else {
            throw GamificationError.registrationClosed
        }
        
        guard tournament.currentParticipants < tournament.maxParticipants else {
            throw GamificationError.tournamentFull
        }
        
        // Check requirements
        if let requirement = tournament.entryRequirement {
            try await validateTournamentRequirement(requirement, for: userId)
        }
        
        let participant = TournamentParticipant(
            id: UUID(),
            userId: userId,
            tournamentId: tournamentId,
            registrationDate: Date(),
            currentRound: 0,
            totalScore: 0,
            matchesWon: 0,
            matchesLost: 0,
            isEliminated: false,
            finalPosition: nil
        )
        
        // Save participant to database
        try await saveTournamentParticipant(participant)
        
        tournament.currentParticipants += 1
        activeTournaments[tournamentIndex] = tournament
        
        // Track tournament join
        analyticsService.trackInteraction(
            userId: userId,
            interactionType: .lessonStart,
            contentType: .exercise,
            contentId: tournamentId.uuidString,
            isCorrect: nil,
            responseTime: nil,
            metadata: [
                "tournament_id": SupabaseAnyCodable(tournamentId.uuidString),
                "type": SupabaseAnyCodable(tournament.type.rawValue)
            ]
        )
    }
    
    func startTournamentMatch(_ matchId: UUID) async throws {
        // Implementation for starting tournament matches
        // Would integrate with RealtimeCollaborationService for live matches
    }
    
    // MARK: - Guild Management
    
    func createGuild(
        name: String,
        description: String,
        language: Language,
        isPublic: Bool,
        requirements: GuildRequirements?
    ) async throws -> Guild {
        isLoading = true
        defer { isLoading = false }
        
        let guild = Guild(
            id: UUID(),
            name: name,
            description: description,
            language: language,
            level: .bronze,
            memberCount: 1,
            maxMembers: GuildLevel.bronze.maxMembers,
            totalXP: 0,
            weeklyGoal: 10000,
            currentWeeklyXP: 0,
            emblem: "default_emblem",
            isPublic: isPublic,
            requirements: requirements,
            perks: generateGuildPerks(level: .bronze),
            createdBy: UUID(), // Current user ID
            createdAt: Date()
        )
        
        // Save to database
        try await saveGuildToDatabase(guild)
        
        userGuild = guild
        
        // Track guild creation
        analyticsService.trackInteraction(
            userId: UUID(), // Would get current user ID in real implementation
            interactionType: .exerciseAttempt,
            contentType: .exercise,
            contentId: guild.id.uuidString,
            isCorrect: nil,
            responseTime: nil,
            metadata: [
                "language": SupabaseAnyCodable(language.rawValue),
                "is_public": SupabaseAnyCodable(isPublic)
            ]
        )
        
        return guild
    }
    
    func joinGuild(_ guildId: UUID, userId: UUID) async throws {
        guard let guild = availableGuilds.first(where: { $0.id == guildId }) else {
            throw GamificationError.guildNotFound
        }
        
        guard guild.memberCount < guild.maxMembers else {
            throw GamificationError.guildFull
        }
        
        // Check requirements
        if let requirements = guild.requirements {
            try await validateGuildRequirements(requirements, for: userId)
        }
        
        let member = GuildMember(
            id: UUID(),
            userId: userId,
            guildId: guildId,
            role: .member,
            joinDate: Date(),
            contributedXP: 0,
            weeklyContribution: 0,
            isActive: true
        )
        
        // Save member to database
        try await saveGuildMember(member)
        
        userGuild = guild
        
        // Track guild join
        analyticsService.trackInteraction(
            userId: userId,
            interactionType: .lessonStart,
            contentType: .exercise,
            contentId: guildId.uuidString,
            isCorrect: nil,
            responseTime: nil,
            metadata: [
                "guild_id": SupabaseAnyCodable(guildId.uuidString),
                "guild_level": SupabaseAnyCodable(guild.level.rawValue)
            ]
        )
    }
    
    func contributeToGuild(xp: Int) async {
        guard let guild = userGuild else { return }
        
        // Update guild XP and user contribution
        // Implementation would update database and trigger guild level checks
        
        analyticsService.trackInteraction(
            userId: UUID(), // Would get current user ID in real implementation
            interactionType: .exerciseAttempt,
            contentType: .exercise,
            contentId: guild.id.uuidString,
            isCorrect: nil,
            responseTime: nil,
            metadata: [
                "xp_contributed": SupabaseAnyCodable(xp),
                "guild_id": SupabaseAnyCodable(guild.id.uuidString)
            ]
        )
    }
    
    // MARK: - Achievement System
    
    func checkAchievementProgress(for userId: UUID) async {
        for achievement in availableAchievements {
            if let userAchievement = userAchievements.first(where: { $0.achievementId == achievement.id }) {
                if !userAchievement.isCompleted {
                    let progress = await calculateAchievementProgress(achievement, for: userId)
                    await updateAchievementProgress(userAchievement.id, progress: progress)
                    
                    if progress >= 1.0 {
                        await completeAchievement(achievement, for: userId)
                    }
                }
            }
        }
    }
    
    private func calculateAchievementProgress(_ achievement: AdvancedAchievement, for userId: UUID) async -> Double {
        var totalProgress: Double = 0
        
        for requirement in achievement.requirements {
            let progress = await calculateRequirementProgress(requirement, for: userId)
            totalProgress += progress / Double(achievement.requirements.count)
        }
        
        return min(1.0, totalProgress)
    }
    
    private func calculateRequirementProgress(_ requirement: AchievementRequirement, for userId: UUID) async -> Double {
        // Get user data and calculate progress based on requirement type
        switch requirement.type {
        case .totalXP:
            let userXP = await getUserTotalXP(userId)
            return Double(userXP) / requirement.target
            
        case .streakDays:
            let streakDays = await getUserStreakDays(userId)
            return Double(streakDays) / requirement.target
            
        case .lessonsCompleted:
            let lessonsCompleted = await getUserLessonsCompleted(userId)
            return Double(lessonsCompleted) / requirement.target
            
        case .vocabularyMastered:
            let vocabularyMastered = await getUserVocabularyMastered(userId)
            return Double(vocabularyMastered) / requirement.target
            
        case .conversationMinutes:
            let conversationMinutes = await getUserConversationMinutes(userId)
            return conversationMinutes / requirement.target
            
        case .tournamentWins:
            let tournamentWins = await getUserTournamentWins(userId)
            return Double(tournamentWins) / requirement.target
            
        case .guildContribution:
            let guildContribution = await getUserGuildContribution(userId)
            return Double(guildContribution) / requirement.target
            
        case .perfectScores:
            let perfectScores = await getUserPerfectScores(userId)
            return Double(perfectScores) / requirement.target
            
        case .languagesLearned:
            let languagesLearned = await getUserLanguagesLearned(userId)
            return Double(languagesLearned) / requirement.target
            
        case .friendsReferred:
            let friendsReferred = await getUserFriendsReferred(userId)
            return Double(friendsReferred) / requirement.target
        }
    }
    
    private func completeAchievement(_ achievement: AdvancedAchievement, for userId: UUID) async {
        // Mark achievement as completed
        if let index = userAchievements.firstIndex(where: { $0.achievementId == achievement.id }) {
            userAchievements[index].isCompleted = true
            userAchievements[index].completedAt = Date()
        }
        
        // Award rewards
        await awardAchievementRewards(achievement.rewards, to: userId)
        
        // Track achievement completion
        analyticsService.trackInteraction(
            userId: userId,
            interactionType: .exerciseAttempt,
            contentType: .exercise,
            contentId: achievement.id.uuidString,
            isCorrect: true,
            responseTime: nil,
            metadata: [
                "achievement_id": SupabaseAnyCodable(achievement.id.uuidString),
                "tier": SupabaseAnyCodable(achievement.tier.rawValue),
                "category": SupabaseAnyCodable(achievement.category.rawValue)
            ]
        )
    }
    
    // MARK: - Seasonal Events
    
    func createSeasonalEvent(
        title: String,
        theme: String,
        duration: TimeInterval,
        challenges: [EventChallenge]
    ) async throws -> SeasonalEvent {
        let event = SeasonalEvent(
            id: UUID(),
            title: title,
            description: "Special seasonal event: \(theme)",
            theme: theme,
            startDate: Date(),
            endDate: Date().addingTimeInterval(duration),
            challenges: challenges,
            rewards: generateEventRewards(),
            leaderboard: nil,
            isActive: true
        )
        
        // Save to database
        try await saveSeasonalEvent(event)
        
        activeEvents.append(event)
        
        // Track event creation
        analyticsService.trackInteraction(
            userId: UUID(), // Would get current user ID in real implementation
            interactionType: .lessonStart,
            contentType: .exercise,
            contentId: event.id.uuidString,
            isCorrect: nil,
            responseTime: nil,
            metadata: [
                "theme": SupabaseAnyCodable(theme),
                "duration": SupabaseAnyCodable(duration),
                "challenges_count": SupabaseAnyCodable(challenges.count)
            ]
        )
        
        return event
    }
    
    // MARK: - Leaderboards
    
    func updateLeaderboards() async {
        // Update various leaderboards
        leaderboards["global_xp"] = await getGlobalXPLeaderboard()
        leaderboards["weekly_streak"] = await getWeeklyStreakLeaderboard()
        leaderboards["tournament_wins"] = await getTournamentWinsLeaderboard()
        leaderboards["guild_contribution"] = await getGuildContributionLeaderboard()
    }
    
    // MARK: - Helper Methods
    
    private func setupObservers() {
        // Setup observers for analytics updates
        // Note: LearningAnalyticsService doesn't have published properties
        // This would be implemented when the analytics service is updated
    }
    
    private func loadGamificationData() {
        Task {
            isLoading = true
            defer { isLoading = false }
            
            do {
                activeTournaments = try await loadTournamentsFromDatabase()
                availableGuilds = try await loadGuildsFromDatabase()
                availableAchievements = try await loadAchievementsFromDatabase()
                activeEvents = try await loadActiveEventsFromDatabase()
                await updateLeaderboards()
            } catch {
                print("Failed to load gamification data: \(error)")
            }
        }
    }
    
    private func processInteractionsForAchievements() async {
        // Process recent interactions to update achievement progress
        // This would be implemented when analytics service provides interaction data
        // For now, we'll check achievement progress for current user
        let currentUserId = UUID() // Would get from user session
        await checkAchievementProgress(for: currentUserId)
    }
    
    // MARK: - Database Operations
    
    private func saveTournamentToDatabase(_ tournament: Tournament) async throws {
        // Save tournament to Supabase
    }
    
    private func saveTournamentParticipant(_ participant: TournamentParticipant) async throws {
        // Save participant to Supabase
    }
    
    private func saveGuildToDatabase(_ guild: Guild) async throws {
        // Save guild to Supabase
    }
    
    private func saveGuildMember(_ member: GuildMember) async throws {
        // Save guild member to Supabase
    }
    
    private func saveSeasonalEvent(_ event: SeasonalEvent) async throws {
        // Save seasonal event to Supabase
    }
    
    private func loadTournamentsFromDatabase() async throws -> [Tournament] {
        // Load tournaments from Supabase
        return []
    }
    
    private func loadGuildsFromDatabase() async throws -> [Guild] {
        // Load guilds from Supabase
        return []
    }
    
    private func loadAchievementsFromDatabase() async throws -> [AdvancedAchievement] {
        // Load achievements from Supabase
        return generateDefaultAchievements()
    }
    
    private func loadActiveEventsFromDatabase() async throws -> [SeasonalEvent] {
        // Load active events from Supabase
        return []
    }
    
    // MARK: - Data Generation
    
    private func generateTournamentPrizes(type: TournamentType) -> [TournamentPrize] {
        return [
            TournamentPrize(
                id: UUID(),
                position: 1,
                title: "Champion",
                description: "First place winner",
                xpReward: 5000,
                badgeId: UUID(),
                certificateId: UUID(),
                specialReward: "Golden Crown Avatar"
            ),
            TournamentPrize(
                id: UUID(),
                position: 2,
                title: "Runner-up",
                description: "Second place",
                xpReward: 3000,
                badgeId: UUID(),
                certificateId: nil,
                specialReward: "Silver Medal Badge"
            ),
            TournamentPrize(
                id: UUID(),
                position: 3,
                title: "Third Place",
                description: "Bronze medalist",
                xpReward: 1500,
                badgeId: UUID(),
                certificateId: nil,
                specialReward: "Bronze Trophy"
            )
        ]
    }
    
    private func generateTournamentRules(type: TournamentType) -> [String] {
        switch type {
        case .singleElimination:
            return [
                "Single elimination format",
                "One loss eliminates participant",
                "Matches are best of 3 rounds",
                "No time limit per match"
            ]
        case .leaderboard:
            return [
                "Accumulate points over tournament period",
                "Higher scores rank higher",
                "All participants can earn rewards",
                "Daily challenges available"
            ]
        default:
            return [
                "Follow tournament format rules",
                "Respect all participants",
                "Complete matches on time",
                "Have fun and learn!"
            ]
        }
    }
    
    private func generateGuildPerks(level: GuildLevel) -> [GuildPerk] {
        var perks: [GuildPerk] = []
        
        // Base perks for all guilds
        perks.append(GuildPerk(
            id: UUID(),
            name: "XP Bonus",
            description: "Extra XP for guild activities",
            type: .xpBonus,
            value: 0.1 * level.multiplier,
            isActive: true
        ))
        
        if level.rawValue != "bronze" {
            perks.append(GuildPerk(
                id: UUID(),
                name: "Streak Protection",
                description: "Protect learning streak once per week",
                type: .streakProtection,
                value: 1.0,
                isActive: true
            ))
        }
        
        return perks
    }
    
    private func generateDefaultAchievements() -> [AdvancedAchievement] {
        return [
            AdvancedAchievement(
                id: UUID(),
                title: "Language Explorer",
                description: "Learn vocabulary in 3 different languages",
                category: .exploration,
                tier: .gold,
                requirements: [
                    AchievementRequirement(
                        type: .languagesLearned,
                        target: 3,
                        timeframe: nil,
                        conditions: nil
                    )
                ],
                rewards: AchievementReward(
                    xp: 2000,
                    badge: "explorer_badge",
                    title: "Polyglot",
                    customization: "rainbow_theme",
                    exclusiveContent: nil,
                    specialPerk: nil
                ),
                isSecret: false,
                isLimited: false,
                expiryDate: nil,
                iconUrl: "achievement_explorer",
                badgeUrl: "badge_explorer",
                createdAt: Date()
            ),
            AdvancedAchievement(
                id: UUID(),
                title: "Tournament Champion",
                description: "Win 5 tournaments",
                category: .competition,
                tier: .platinum,
                requirements: [
                    AchievementRequirement(
                        type: .tournamentWins,
                        target: 5,
                        timeframe: nil,
                        conditions: nil
                    )
                ],
                rewards: AchievementReward(
                    xp: 5000,
                    badge: "champion_badge",
                    title: "Tournament Master",
                    customization: "champion_crown",
                    exclusiveContent: "champion_lessons",
                    specialPerk: "tournament_priority"
                ),
                isSecret: false,
                isLimited: false,
                expiryDate: nil,
                iconUrl: "achievement_champion",
                badgeUrl: "badge_champion",
                createdAt: Date()
            )
        ]
    }
    
    private func generateEventRewards() -> [EventReward] {
        return [
            EventReward(
                id: UUID(),
                title: "Seasonal XP Boost",
                description: "Double XP for 24 hours",
                type: .xp,
                value: "2x_multiplier",
                rarity: .rare
            ),
            EventReward(
                id: UUID(),
                title: "Festive Avatar",
                description: "Special seasonal avatar",
                type: .avatar,
                value: "festive_avatar_2024",
                rarity: .epic
            )
        ]
    }
    
    // MARK: - User Data Helpers
    
    private func getUserTotalXP(_ userId: UUID) async -> Int {
        // Get user's total XP from analytics
        return 0
    }
    
    private func getUserStreakDays(_ userId: UUID) async -> Int {
        // Get user's current streak
        return 0
    }
    
    private func getUserLessonsCompleted(_ userId: UUID) async -> Int {
        // Get completed lessons count
        return 0
    }
    
    private func getUserVocabularyMastered(_ userId: UUID) async -> Int {
        // Get mastered vocabulary count
        return 0
    }
    
    private func getUserConversationMinutes(_ userId: UUID) async -> Double {
        // Get total conversation time
        return 0
    }
    
    private func getUserTournamentWins(_ userId: UUID) async -> Int {
        // Get tournament wins count
        return 0
    }
    
    private func getUserGuildContribution(_ userId: UUID) async -> Int {
        // Get guild XP contribution
        return 0
    }
    
    private func getUserPerfectScores(_ userId: UUID) async -> Int {
        // Get perfect scores count
        return 0
    }
    
    private func getUserLanguagesLearned(_ userId: UUID) async -> Int {
        // Get languages learned count
        return 0
    }
    
    private func getUserFriendsReferred(_ userId: UUID) async -> Int {
        // Get friends referred count
        return 0
    }
    
    // MARK: - Leaderboard Helpers
    
    private func getGlobalXPLeaderboard() async -> [LeaderboardEntry] {
        // Get global XP leaderboard
        return []
    }
    
    private func getWeeklyStreakLeaderboard() async -> [LeaderboardEntry] {
        // Get weekly streak leaderboard
        return []
    }
    
    private func getTournamentWinsLeaderboard() async -> [LeaderboardEntry] {
        // Get tournament wins leaderboard
        return []
    }
    
    private func getGuildContributionLeaderboard() async -> [LeaderboardEntry] {
        // Get guild contribution leaderboard
        return []
    }
    
    // MARK: - Validation and Rewards
    
    private func validateTournamentRequirement(_ requirement: TournamentRequirement, for userId: UUID) async throws {
        // Validate tournament entry requirements
    }
    
    private func validateGuildRequirements(_ requirements: GuildRequirements, for userId: UUID) async throws {
        // Validate guild join requirements
    }
    
    private func updateAchievementProgress(_ achievementId: UUID, progress: Double) async {
        // Update achievement progress in database
    }
    
    private func awardAchievementRewards(_ rewards: AchievementReward, to userId: UUID) async {
        // Award achievement rewards to user
    }
}

// MARK: - Extensions

extension GuildLevel {
    var multiplier: Double {
        switch self {
        case .bronze: return 1.0
        case .silver: return 1.2
        case .gold: return 1.5
        case .platinum: return 2.0
        case .diamond: return 3.0
        }
    }
}

// MARK: - Errors

enum GamificationError: LocalizedError {
    case tournamentNotFound
    case registrationClosed
    case tournamentFull
    case guildNotFound
    case guildFull
    case requirementsNotMet
    case achievementNotFound
    
    var errorDescription: String? {
        switch self {
        case .tournamentNotFound:
            return "Tournament not found"
        case .registrationClosed:
            return "Tournament registration is closed"
        case .tournamentFull:
            return "Tournament is full"
        case .guildNotFound:
            return "Guild not found"
        case .guildFull:
            return "Guild is full"
        case .requirementsNotMet:
            return "Requirements not met"
        case .achievementNotFound:
            return "Achievement not found"
        }
    }
} 