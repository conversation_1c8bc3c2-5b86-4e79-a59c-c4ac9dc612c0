import Foundation
import Combine

class AgentCommunicationService: ObservableObject {
    @Published var isConnected: Bool = true // Always connected since we use direct API
    @Published var receivedMessage: AgentMessageResponse?

    init() {
        // No server setup needed - using direct API calls
    }

    // MARK: - REST API Methods

    func startConversation(
        agentTypes: [String],
        language: String,
        scenarioType: String?
    ) async throws -> AgentConversationResponse {

        // Create a conversation response using direct API - no server needed
        let mockConversation = AgentConversationResponse(
            sessionId: UUID(),
            agents: [getAgentName(for: language)],
            language: language,
            scenarioType: scenarioType,
            conversationHistory: [
                ConversationTurnResponse(
                    id: UUID(),
                    speakerType: "agent",
                    speakerId: "tutor_\(language)",
                    message: getInitialMessage(for: language),
                    timestamp: Date(),
                    metadata: nil as TurnMetadata?
                )
            ],
            isActive: true
        )

        // Connection is always true since we use direct API
        return mockConversation
    }

    func sendMessage(
        sessionId: UUID,
        message: String
    ) async throws -> AgentMessageResponse {

        // Use Gemini API directly for responses
        let aiResponse = try await generateAIResponse(for: message)

        let response = AgentMessageResponse(
            sessionId: sessionId,
            userMessage: message,
            agentResponse: AgentResponseData(
                agentId: "ai_tutor",
                agentType: "tutor",
                message: aiResponse,
                timestamp: Date(),
                metadata: nil as TurnMetadata?
            )
        )

        return response
    }

    private func generateAIResponse(for userMessage: String) async throws -> String {
        let prompt = """
        You are a friendly language learning AI tutor. The user just said: "\(userMessage)"

        Respond as a helpful language tutor would. Be encouraging, provide corrections if needed, and ask follow-up questions to keep the conversation going. Keep responses conversational and not too long.
        """

        // Use Gemini 2.0 Flash API directly
        let apiKey = SecureAPIKeys.geminiAPIKey
        guard !apiKey.isEmpty && !apiKey.contains("PLACEHOLDER"),
              let url = URL(string: "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=\(apiKey)") else {
            throw AgentCommunicationError.invalidURL
        }

        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")

        let requestBody = [
            "contents": [
                [
                    "parts": [
                        ["text": prompt]
                    ]
                ]
            ]
        ]

        request.httpBody = try JSONSerialization.data(withJSONObject: requestBody)

        let (data, response) = try await URLSession.shared.data(for: request)

        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw AgentCommunicationError.invalidResponse
        }

        if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any],
           let candidates = json["candidates"] as? [[String: Any]],
           let firstCandidate = candidates.first,
           let content = firstCandidate["content"] as? [String: Any],
           let parts = content["parts"] as? [[String: Any]],
           let firstPart = parts.first,
           let text = firstPart["text"] as? String {
            return text
        }

        return "I'm learning to respond better. Could you rephrase that?"
    }

    private func getAgentName(for language: String) -> String {
        switch language.lowercased() {
        case "french": return "Marie"
        case "spanish": return "Carlos"
        case "japanese": return "Yuki"
        case "tamil": return "Priya"
        case "korean": return "Minji"
        case "italian": return "Marco"
        case "german": return "Greta"
        case "hindi": return "Priya"
        case "chinese": return "Wei"
        case "portuguese": return "Sofia"
        case "telugu": return "Ravi"
        case "vietnamese": return "Linh"
        case "indonesian": return "Sari"
        case "arabic": return "Ahmed"
        default: return "Alex"
        }
    }

    private func getInitialMessage(for language: String) -> String {
        switch language.lowercased() {
        case "french": return "Bonjour! Je suis Marie, votre tutrice de français. Comment allez-vous aujourd'hui?"
        case "spanish": return "¡Hola! Soy Carlos, tu tutor de español. ¿Cómo estás hoy?"
        case "japanese": return "こんにちは！私はユキです。日本語の先生です。今日はどうですか？"
        case "tamil": return "வணக்கம்! நான் பிரியா, உங்கள் தமிழ் ஆசிரியர். நீங்கள் எப்படி இருக்கீங்க?"
        case "korean": return "안녕하세요! 저는 민지예요, 한국어 선생님입니다. 오늘 어떻게 지내세요?"
        case "italian": return "Ciao! Sono Marco, il tuo tutor di italiano. Come stai oggi?"
        case "german": return "Hallo! Ich bin Greta, deine Deutschlehrerin. Wie geht es dir heute?"
        case "hindi": return "नमस्ते! मैं प्रिया हूँ, आपकी हिंदी शिक्षिका। आज आप कैसे हैं?"
        case "chinese": return "你好！我是魏老师，你的中文老师。你今天怎么样？"
        case "portuguese": return "Olá! Eu sou Sofia, sua tutora de português. Como você está hoje?"
        case "telugu": return "నమస్కారం! నేను రవి, మీ తెలుగు గురువు. మీరు ఈరోజు ఎలా ఉన్నారు?"
        case "vietnamese": return "Xin chào! Tôi là Linh, giáo viên tiếng Việt của bạn. Hôm nay bạn thế nào?"
        case "indonesian": return "Halo! Saya Sari, guru bahasa Indonesia Anda. Apa kabar hari ini?"
        case "arabic": return "مرحباً! أنا أحمد، معلم اللغة العربية. كيف حالك اليوم؟"
        default: return "Hello! I'm your AI language tutor. How can I help you learn today?"
        }
    }

    // MARK: - Real-time Message Simulation

    func simulateRealTimeMessage(_ response: AgentMessageResponse) {
        // Simulate receiving a real-time message (without WebSocket)
        DispatchQueue.main.async {
            self.receivedMessage = response
        }
    }
}



// MARK: - Request Models

struct StartConversationRequest: Encodable {
    let agentTypes: [String]
    let language: String
    let scenarioType: String?

    func toDictionary() -> [String: Any] {
        var dict: [String: Any] = [
            "agentTypes": agentTypes,
            "language": language
        ]

        if let scenarioType = scenarioType {
            dict["scenarioType"] = scenarioType
        }

        return dict
    }
}

struct SendMessageRequest: Encodable {
    let message: String
    let context: [String: String]?

    func toDictionary() -> [String: Any] {
        var dict: [String: Any] = [
            "message": message
        ]

        if let context = context {
            dict["context"] = context
        }

        return dict
    }
}

// Note: Response models are defined in AgentConversationViewModel.swift

// MARK: - Error Types

enum AgentCommunicationError: Error, LocalizedError {
    case invalidURL
    case sessionNotAvailable
    case invalidResponse
    case serverError(Int)
    case networkError(Error)

    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "Invalid URL"
        case .sessionNotAvailable:
            return "URL session not available"
        case .invalidResponse:
            return "Invalid response from server"
        case .serverError(let code):
            return "Server error with code: \(code)"
        case .networkError(let error):
            return "Network error: \(error.localizedDescription)"
        }
    }
}