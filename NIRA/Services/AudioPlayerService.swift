import Foundation
import AVFoundation
import SwiftUI

@MainActor
class AudioPlayerService: ObservableObject {
    static let shared = AudioPlayerService()
    
    @Published var isPlaying = false
    @Published var currentAudioURL: String?
    @Published var playbackError: String?
    
    private var audioPlayer: AVAudioPlayer?
    private var audioSession: AVAudioSession
    
    private init() {
        self.audioSession = AVAudioSession.sharedInstance()
        setupAudioSession()
    }
    
    private func setupAudioSession() {
        do {
            try audioSession.setCategory(.playback, mode: .default, options: [.allowAirPlay, .allowBluetooth])
            try audioSession.setActive(true)
        } catch {
            print("❌ Failed to setup audio session: \(error)")
            playbackError = "Audio setup failed"
        }
    }
    
    func playAudio(from urlString: String) {
        // Stop any currently playing audio
        stopAudio()
        
        // Check if file exists locally first
        if let localURL = getLocalAudioURL(from: urlString) {
            playLocalAudio(url: localURL, originalURLString: urlString)
        } else {
            // For now, show that audio is not available
            playbackError = "Audio file not found locally"
            print("🔊 Audio file not found: \(urlString)")
            
            // TODO: In the future, we could download from a remote server
            // downloadAndPlayAudio(from: urlString)
        }
    }
    
    private func getLocalAudioURL(from urlString: String) -> URL? {
        // Extract filename from the full path
        let filename = URL(fileURLWithPath: urlString).lastPathComponent
        
        // Check in app bundle first
        if let bundleURL = Bundle.main.url(forResource: filename.replacingOccurrences(of: ".mp3", with: ""), withExtension: "mp3") {
            return bundleURL
        }
        
        // Check in documents directory
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
        let audioPath = documentsPath.appendingPathComponent("Audio").appendingPathComponent(filename)
        
        if FileManager.default.fileExists(atPath: audioPath.path) {
            return audioPath
        }
        
        // Check the original path if it exists
        let originalURL = URL(fileURLWithPath: urlString)
        if FileManager.default.fileExists(atPath: originalURL.path) {
            return originalURL
        }
        
        return nil
    }
    
    private func playLocalAudio(url: URL, originalURLString: String) {
        do {
            audioPlayer = try AVAudioPlayer(contentsOf: url)
            audioPlayer?.delegate = self
            audioPlayer?.prepareToPlay()
            
            if audioPlayer?.play() == true {
                isPlaying = true
                currentAudioURL = originalURLString
                playbackError = nil
                print("🔊 Playing audio: \(url.lastPathComponent)")
            } else {
                playbackError = "Failed to start playback"
                print("❌ Failed to start audio playback")
            }
        } catch {
            playbackError = "Audio playback error: \(error.localizedDescription)"
            print("❌ Audio playback error: \(error)")
        }
    }
    
    func stopAudio() {
        audioPlayer?.stop()
        audioPlayer = nil
        isPlaying = false
        currentAudioURL = nil
        playbackError = nil
    }
    
    func pauseAudio() {
        audioPlayer?.pause()
        isPlaying = false
    }
    
    func resumeAudio() {
        if audioPlayer?.play() == true {
            isPlaying = true
        }
    }
    
    // MARK: - Test Audio Generation
    
    func generateTestAudio(text: String, filename: String) {
        // This is a placeholder for future ElevenLabs integration
        print("🎵 Would generate audio for: '\(text)' -> \(filename)")
        
        // For now, create a placeholder file
        createPlaceholderAudio(filename: filename)
    }
    
    private func createPlaceholderAudio(filename: String) {
        // Create a simple beep sound as placeholder
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
        let audioDir = documentsPath.appendingPathComponent("Audio")
        
        // Create audio directory if it doesn't exist
        try? FileManager.default.createDirectory(at: audioDir, withIntermediateDirectories: true)
        
        let audioPath = audioDir.appendingPathComponent(filename)
        
        // Create a simple audio file (placeholder)
        let audioData = Data() // Empty data for now
        try? audioData.write(to: audioPath)
        
        print("📁 Created placeholder audio file: \(audioPath.path)")
    }
}

// MARK: - AVAudioPlayerDelegate

extension AudioPlayerService: AVAudioPlayerDelegate {
    nonisolated func audioPlayerDidFinishPlaying(_ player: AVAudioPlayer, successfully flag: Bool) {
        Task { @MainActor in
            isPlaying = false
            currentAudioURL = nil
            if !flag {
                playbackError = "Playback finished with error"
            }
        }
    }
    
    nonisolated func audioPlayerDecodeErrorDidOccur(_ player: AVAudioPlayer, error: Error?) {
        Task { @MainActor in
            isPlaying = false
            currentAudioURL = nil
            playbackError = "Audio decode error: \(error?.localizedDescription ?? "Unknown error")"
        }
    }
}

// MARK: - Audio Button Component

struct AudioButton: View {
    let audioURL: String?
    let size: CGFloat
    let color: Color
    @StateObject private var audioService = AudioPlayerService.shared
    
    init(audioURL: String?, size: CGFloat = 32, color: Color = .blue) {
        self.audioURL = audioURL
        self.size = size
        self.color = color
    }
    
    var body: some View {
        Button(action: {
            guard let audioURL = audioURL else {
                print("⚠️ No audio URL provided")
                return
            }
            
            if audioService.isPlaying && audioService.currentAudioURL == audioURL {
                audioService.stopAudio()
            } else {
                audioService.playAudio(from: audioURL)
            }
        }) {
            Image(systemName: isCurrentlyPlaying ? "stop.fill" : "speaker.wave.2.fill")
                .font(.system(size: size * 0.6, weight: .medium))
                .foregroundColor(audioURL != nil ? color : .gray)
                .frame(width: size, height: size)
                .background(
                    Circle()
                        .fill(audioURL != nil ? color.opacity(0.1) : Color.gray.opacity(0.1))
                )
                .scaleEffect(isCurrentlyPlaying ? 1.1 : 1.0)
                .animation(.easeInOut(duration: 0.2), value: isCurrentlyPlaying)
        }
        .disabled(audioURL == nil)
    }
    
    private var isCurrentlyPlaying: Bool {
        audioService.isPlaying && audioService.currentAudioURL == audioURL
    }
}
