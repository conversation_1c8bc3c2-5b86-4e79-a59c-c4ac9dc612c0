//
//  AuthenticationService.swift
//  NIRA
//
//  Created by NIRA Team on 27/05/2025.
//

import Foundation
import SwiftUI
import Supabase
import Combine

// MARK: - Authentication Service

@MainActor
class AuthenticationService: ObservableObject {
    static let shared = AuthenticationService()
    
    // MARK: - Published Properties
    @Published var isAuthenticated = false
    @Published var currentUser: Auth.User?
    @Published var userProfile: SupabaseUserProfile?
    @Published var isLoading = false
    @Published var errorMessage: String?
    @Published var authState: AuthState = .unauthenticated
    
    // MARK: - Private Properties
    private let supabaseClient = NIRASupabaseClient.shared
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Initialization
    private init() {
        setupAuthStateListener()
        checkInitialAuthState()
    }
    
    // MARK: - Public Methods
    
    /// Sign up a new user with email and password
    func signUp(email: String, password: String, firstName: String? = nil, lastName: String? = nil) async throws {
        isLoading = true
        errorMessage = nil
        
        do {
            try await supabaseClient.signUp(email: email, password: password)
            
            // Create user profile if sign up successful
            if let firstName = firstName, let lastName = lastName {
                try await createUserProfile(firstName: firstName, lastName: lastName)
            }
            
            authState = .emailVerificationRequired
            
        } catch {
            errorMessage = handleAuthError(error)
            throw AuthenticationError.signUpFailed(error.localizedDescription)
        }
        
        isLoading = false
    }
    
    /// Sign in with email and password
    func signIn(email: String, password: String) async throws {
        isLoading = true
        errorMessage = nil
        
        do {
            try await supabaseClient.signIn(email: email, password: password)
            authState = .authenticated
            
            // Load user profile
            await loadUserProfile()
            
        } catch {
            errorMessage = handleAuthError(error)
            throw AuthenticationError.signInFailed(error.localizedDescription)
        }
        
        isLoading = false
    }
    
    /// Sign out the current user
    func signOut() async throws {
        isLoading = true
        errorMessage = nil
        
        do {
            try await supabaseClient.signOut()
            
            // Clear local state
            currentUser = nil
            userProfile = nil
            isAuthenticated = false
            authState = .unauthenticated
            
        } catch {
            errorMessage = handleAuthError(error)
            throw AuthenticationError.signOutFailed(error.localizedDescription)
        }
        
        isLoading = false
    }
    
    /// Reset password for email
    func resetPassword(email: String) async throws {
        isLoading = true
        errorMessage = nil
        
        do {
            try await supabaseClient.resetPassword(email: email)
            
        } catch {
            errorMessage = handleAuthError(error)
            throw AuthenticationError.passwordResetFailed(error.localizedDescription)
        }
        
        isLoading = false
    }
    
    /// Continue as guest (limited functionality)
    func continueAsGuest() async {
        authState = .guest
        isAuthenticated = true
        
        // Create a temporary guest profile
        userProfile = SupabaseUserProfile(
            id: UUID().uuidString,
            email: "<EMAIL>",
            firstName: "Guest",
            lastName: "User",
            preferredLanguages: ["english"],
            createdAt: Date(),
            lastActiveDate: Date(),
            isEmailVerified: false,
            subscriptionTier: "free",
            totalLessonsCompleted: 0,
            currentStreak: 0,
            longestStreak: 0,
            totalStudyTimeMinutes: 0
        )
    }
    
    /// Update user profile
    func updateProfile(firstName: String? = nil, lastName: String? = nil, preferredLanguages: [String]? = nil) async throws {
        guard authState != .guest else {
            throw AuthenticationError.guestLimitation
        }
        
        isLoading = true
        errorMessage = nil
        
        do {
            try await supabaseClient.updateUserProfile(
                firstName: firstName,
                lastName: lastName,
                preferredLanguages: preferredLanguages
            )
            
            // Reload user profile
            await loadUserProfile()
            
        } catch {
            errorMessage = handleAuthError(error)
            throw AuthenticationError.profileUpdateFailed(error.localizedDescription)
        }
        
        isLoading = false
    }
    
    /// Check if user has required permissions
    func hasPermission(for feature: AppFeature) -> Bool {
        switch authState {
        case .authenticated:
            return true
        case .guest:
            return feature.isAvailableForGuests
        case .unauthenticated, .emailVerificationRequired:
            return false
        }
    }
    
    /// Upgrade guest to full account
    func upgradeGuestAccount(email: String, password: String) async throws {
        guard authState == .guest else {
            throw AuthenticationError.notGuest
        }
        
        try await signUp(email: email, password: password)
    }
    
    // MARK: - Private Methods
    
    private func setupAuthStateListener() {
        // Listen to Supabase auth state changes
        supabaseClient.$currentUser
            .receive(on: DispatchQueue.main)
            .sink { [weak self] user in
                self?.currentUser = user
                self?.isAuthenticated = user != nil
                
                if user != nil && self?.authState == .unauthenticated {
                    self?.authState = .authenticated
                    Task { [weak self] in
                        await self?.loadUserProfile()
                    }
                }
            }
            .store(in: &cancellables)
        
        supabaseClient.$session
            .receive(on: DispatchQueue.main)
            .sink { [weak self] session in
                if session == nil {
                    self?.currentUser = nil
                    self?.userProfile = nil
                    self?.isAuthenticated = false
                    if self?.authState != .guest {
                        self?.authState = .unauthenticated
                    }
                }
            }
            .store(in: &cancellables)
    }
    
    private func checkInitialAuthState() {
        // Check if user is already authenticated
        if let user = supabaseClient.currentUser {
            currentUser = user
            isAuthenticated = true
            authState = .authenticated
            
            Task {
                await loadUserProfile()
            }
        }
    }
    
    private func createUserProfile(firstName: String, lastName: String) async throws {
        guard let userId = currentUser?.id else {
            throw AuthenticationError.noCurrentUser
        }
        
        let profile = SupabaseUserProfile(
            id: userId.uuidString,
            email: currentUser?.email ?? "",
            firstName: firstName,
            lastName: lastName,
            preferredLanguages: ["english"],
            createdAt: Date(),
            lastActiveDate: Date(),
            isEmailVerified: false,
            subscriptionTier: "free",
            totalLessonsCompleted: 0,
            currentStreak: 0,
            longestStreak: 0,
            totalStudyTimeMinutes: 0
        )
        
        // Save to database
        try await supabaseClient.createUserProfile(profile)
        userProfile = profile
    }
    
    private func loadUserProfile() async {
        guard authState == .authenticated else { return }
        
        do {
            userProfile = try await supabaseClient.getUserProfile()
        } catch {
            print("❌ Failed to load user profile: \(error)")
            // Don't throw error here, just log it
        }
    }
    
    private func handleAuthError(_ error: Error) -> String {
        if let authError = error as? AuthError {
            // Handle known Supabase AuthError cases
            let errorMessage = authError.localizedDescription.lowercased()
            
            if errorMessage.contains("invalid") && errorMessage.contains("credentials") {
                return "Invalid email or password. Please try again."
            } else if errorMessage.contains("email") && errorMessage.contains("confirm") {
                return "Please check your email and click the confirmation link."
            } else if errorMessage.contains("too many") || errorMessage.contains("rate limit") {
                return "Too many attempts. Please wait a moment and try again."
            } else if errorMessage.contains("weak") && errorMessage.contains("password") {
                return "Password is too weak. Please choose a stronger password."
            } else if errorMessage.contains("already") && errorMessage.contains("registered") {
                return "An account with this email already exists."
            } else {
                return "Authentication error: \(authError.localizedDescription)"
            }
        }
        
        return "An unexpected error occurred. Please try again."
    }
}

// MARK: - Supporting Types

enum AuthState {
    case unauthenticated
    case authenticated
    case guest
    case emailVerificationRequired
    
    var description: String {
        switch self {
        case .unauthenticated:
            return "Not signed in"
        case .authenticated:
            return "Signed in"
        case .guest:
            return "Guest mode"
        case .emailVerificationRequired:
            return "Email verification required"
        }
    }
}

enum AuthenticationError: LocalizedError {
    case signUpFailed(String)
    case signInFailed(String)
    case signOutFailed(String)
    case passwordResetFailed(String)
    case profileUpdateFailed(String)
    case noCurrentUser
    case guestLimitation
    case notGuest
    
    var errorDescription: String? {
        switch self {
        case .signUpFailed(let message):
            return "Sign up failed: \(message)"
        case .signInFailed(let message):
            return "Sign in failed: \(message)"
        case .signOutFailed(let message):
            return "Sign out failed: \(message)"
        case .passwordResetFailed(let message):
            return "Password reset failed: \(message)"
        case .profileUpdateFailed(let message):
            return "Profile update failed: \(message)"
        case .noCurrentUser:
            return "No current user found"
        case .guestLimitation:
            return "This feature requires a full account. Please sign up to continue."
        case .notGuest:
            return "This action is only available for guest users"
        }
    }
}

enum AppFeature {
    case aiChat
    case lessons
    case simulations
    case progress
    case profile
    case voiceRecording
    case fileUpload
    case socialFeatures
    case advancedAnalytics
    
    var isAvailableForGuests: Bool {
        switch self {
        case .aiChat, .lessons, .simulations:
            return true // Basic features available for guests
        case .progress, .profile, .voiceRecording, .fileUpload, .socialFeatures, .advancedAnalytics:
            return false // Advanced features require account
        }
    }
    
    var description: String {
        switch self {
        case .aiChat:
            return "AI Chat"
        case .lessons:
            return "Lessons"
        case .simulations:
            return "Simulations"
        case .progress:
            return "Progress Tracking"
        case .profile:
            return "Profile Management"
        case .voiceRecording:
            return "Voice Recording"
        case .fileUpload:
            return "File Upload"
        case .socialFeatures:
            return "Social Features"
        case .advancedAnalytics:
            return "Advanced Analytics"
        }
    }
}

// MARK: - Extensions

extension NIRASupabaseClient {
    func createUserProfile(_ profile: SupabaseUserProfile) async throws {
        let profileData: [String: AnyJSON] = [
            "id": AnyJSON.string(profile.id),
            "email": AnyJSON.string(profile.email),
            "first_name": AnyJSON.string(profile.firstName ?? ""),
            "last_name": AnyJSON.string(profile.lastName ?? ""),
            "preferred_languages": AnyJSON.string(profile.preferredLanguages.joined(separator: ",")),
            "created_at": AnyJSON.string(ISO8601DateFormatter().string(from: profile.createdAt)),
            "last_active_date": AnyJSON.string(ISO8601DateFormatter().string(from: profile.lastActiveDate)),
            "is_email_verified": AnyJSON.bool(profile.isEmailVerified),
            "subscription_tier": AnyJSON.string(profile.subscriptionTier),
            "total_lessons_completed": AnyJSON.integer(profile.totalLessonsCompleted),
            "current_streak": AnyJSON.integer(profile.currentStreak),
            "longest_streak": AnyJSON.integer(profile.longestStreak),
            "total_study_time_minutes": AnyJSON.integer(profile.totalStudyTimeMinutes)
        ]
        
        _ = try await client
            .from("users")
            .insert(profileData)
            .execute()
    }
} 