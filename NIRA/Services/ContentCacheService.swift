import Foundation
import SwiftData
import Combine
import Network
import UIKit

// MARK: - Intelligent Content Cache Service for iOS

@MainActor
class ContentCacheService: ObservableObject {
    static let shared = ContentCacheService()
    
    @Published var isDownloading = false
    @Published var downloadProgress: Double = 0.0
    @Published var cacheStatus: CacheStatus = .unknown
    @Published var availableOfflineContent: [String] = []
    @Published var lastError: Error?
    
    private let networkMonitor = NWPathMonitor()
    private let backgroundQueue = DispatchQueue(label: "content-cache", qos: .background)
    private let apiClient = APIClient.shared
    private let fileManager = FileManager.default
    private let maxCacheSize: Int = 100 * 1024 * 1024 // 100MB
    
    private var modelContext: ModelContext?
    private var isNetworkAvailable = false
    private var cacheDirectory: URL
    
    private init() {
        cacheDirectory = fileManager.urls(for: .cachesDirectory, in: .userDomainMask).first!
            .appendingPathComponent("NIRA_ContentCache")
        
        createCacheDirectories()
        loadCacheStatus()
        setupNetworkMonitoring()
    }
    
    func configure(with modelContext: ModelContext) {
        self.modelContext = modelContext
    }

    // MARK: - Public Methods
    
    func getCachedLesson(id: String) async throws -> CachedLesson? {
        let lessonPath = cacheDirectory.appendingPathComponent("lessons/\(id).json")
        
        guard fileManager.fileExists(atPath: lessonPath.path) else {
            return nil
        }
        
        let data = try Data(contentsOf: lessonPath)
        var lesson = try JSONDecoder().decode(CachedLesson.self, from: data)
        
        // Update access time
        lesson.lastAccessedAt = Date()
        let updatedData = try JSONEncoder().encode(lesson)
        try updatedData.write(to: lessonPath)
        
        return lesson
    }
    
    func downloadLesson(id: String) async throws -> CachedLesson {
        guard isNetworkAvailable else {
            throw CacheError.networkUnavailable
        }
        
        // Check if already cached
        if let cached = try await getCachedLesson(id: id) {
            return cached
        }
        
        await MainActor.run {
            isDownloading = true
        }
        
        defer {
            Task { @MainActor in
                isDownloading = false
            }
        }
        
        do {
            let lesson = try await apiClient.getLesson(id: id)
            
            let cachedLesson = CachedLesson(
                id: id,
                content: lesson.lesson,
                downloadedAt: Date(),
                lastAccessedAt: Date(),
                size: estimateLessonSize(lesson.lesson)
            )
            
            try storeCachedLesson(cachedLesson)
            
            await MainActor.run {
                availableOfflineContent.append(id)
                updateCacheStatus()
            }
            
            return cachedLesson
            
        } catch {
            await MainActor.run {
                lastError = error
            }
            throw error
        }
    }
    
    func requestLessonBundle(
        language: String,
        bundleSize: Int = 10,
        preferences: UserPreferences? = nil
    ) async throws -> LessonBundle {
        guard isNetworkAvailable else {
            throw CacheError.networkUnavailable
        }
        
        await MainActor.run {
            isDownloading = true
            downloadProgress = 0.0
        }
        
        defer {
            Task { @MainActor in
                isDownloading = false
                downloadProgress = 0.0
            }
        }
        
        let _ = LessonBundleRequest(
            language: language,
            bundleSize: bundleSize,
            preferences: preferences
        )
        
        let response = try await apiClient.requestLessonBundle(
            language: language,
            bundleSize: bundleSize,
            preferences: preferences
        )
        
        let bundle = LessonBundle(
            id: response.bundleId,
            language: language,
            lessonIds: response.lessonIds,
            downloadedLessons: [],
            createdAt: Date(),
            expiresAt: response.expiresAt
        )
        
        // Download priority lessons immediately
        try await downloadPriorityLessons(response.priorityLessons, bundle: bundle)
        
        // Schedule background downloads for remaining lessons
        scheduleBackgroundDownload(for: bundle, recommendations: response.cacheRecommendations)
        
        return bundle
    }
    
    func getOfflineContent(for language: String) async throws -> [OfflineContent] {
        let allCached = try getAllCachedLessons()
        let languageContent = allCached.filter { $0.content.language == language }
        
        return languageContent.map { cached in
            OfflineContent(
                id: cached.id,
                title: cached.content.title,
                description: cached.content.description,
                difficulty: cached.content.difficulty,
                estimatedDuration: cached.content.estimatedDuration,
                downloadedAt: cached.downloadedAt,
                size: cached.size
            )
        }
    }
    
    func clearCache() async throws {
        let lessonsDir = cacheDirectory.appendingPathComponent("lessons")
        
        if fileManager.fileExists(atPath: lessonsDir.path) {
            try fileManager.removeItem(at: lessonsDir)
        }
        
        try fileManager.createDirectory(at: lessonsDir, withIntermediateDirectories: true)
        
        await MainActor.run {
            availableOfflineContent.removeAll()
            updateCacheStatus()
        }
    }
    
    func performCacheMaintenance() async {
        do {
            try await cleanupExpiredContent()
            try await optimizeCacheSize()
        } catch {
            await MainActor.run {
                lastError = error
            }
        }
    }
    
    func prefetchRecommendedContent(for user: User) async throws {
        guard isNetworkAvailable else { return }
        
        let shouldPrefetchContent = await shouldPrefetch()
        guard shouldPrefetchContent else { return }
        
        let response = try await apiClient.prefetchContent(
            language: user.preferredLanguages.first?.rawValue ?? "english",
            lessonCount: 5,
            deviceCapacity: nil
        )
        
        let highPriorityRecommendations = response.data.lessonIds.prefix(3)
        
        for lessonId in highPriorityRecommendations {
            _ = try? await downloadLesson(id: lessonId)
        }
    }
    
    func getCacheRecommendations() async throws -> [CacheRecommendation] {
        guard isNetworkAvailable else {
            throw CacheError.networkUnavailable
        }
        
        let response = try await apiClient.getCacheRecommendations(
            language: "english",
            currentCacheSize: getCurrentCacheSize()
        )
        return response.recommendations
    }
    
    // MARK: - Private Methods
    
    private func createCacheDirectories() {
        let directories = [
            cacheDirectory,
            cacheDirectory.appendingPathComponent("lessons"),
            cacheDirectory.appendingPathComponent("temp")
        ]
        
        for directory in directories {
            if !fileManager.fileExists(atPath: directory.path) {
                _ = try? fileManager.createDirectory(at: directory, withIntermediateDirectories: true)
            }
        }
    }
    
    private func setupNetworkMonitoring() {
        networkMonitor.pathUpdateHandler = { [weak self] path in
            Task { @MainActor in
                self?.isNetworkAvailable = path.status == .satisfied
            }
        }
        networkMonitor.start(queue: backgroundQueue)
    }
    
    private func cleanupExpiredContent() async throws {
        let allCached = try getAllCachedLessons()
        let expiredLessons = allCached.filter { lesson in
            let daysSinceDownload = Calendar.current.dateComponents([.day], from: lesson.downloadedAt, to: Date()).day ?? 0
            return daysSinceDownload > 7
        }
        
        for lesson in expiredLessons {
            try deleteCachedLesson(id: lesson.id)
        }
        
        await MainActor.run {
            updateCacheStatus()
        }
    }
    
    private func optimizeCacheSize() async throws {
        let currentSize = getCurrentCacheSize()
        
        guard currentSize > maxCacheSize else { return }
        
        let allCached = try getAllCachedLessons()
        let sortedByAccess = allCached.sorted { $0.lastAccessedAt < $1.lastAccessedAt }
        
        var sizeToFree = currentSize - maxCacheSize
        
        for lesson in sortedByAccess {
            guard sizeToFree > 0 else { break }
            
            try deleteCachedLesson(id: lesson.id)
            sizeToFree -= lesson.size
        }
        
        await MainActor.run {
            updateCacheStatus()
        }
    }
    
    private func shouldPrefetch() async -> Bool {
        // Check if device is charging or has sufficient battery
        UIDevice.current.isBatteryMonitoringEnabled = true
        
        if UIDevice.current.batteryState == .charging || UIDevice.current.batteryState == .full {
            return true
        }
        
        // Check battery level
        let minBatteryLevel: Float = 0.3 // 30%
        let currentBatteryLevel = UIDevice.current.batteryLevel
        
        return currentBatteryLevel >= minBatteryLevel || currentBatteryLevel == -1 // -1 means unknown (simulator)
    }
    
    private func storeCachedLesson(_ lesson: CachedLesson) throws {
        let lessonPath = cacheDirectory.appendingPathComponent("lessons/\(lesson.id).json")
        let data = try JSONEncoder().encode(lesson)
        try data.write(to: lessonPath)
    }
    
    private func deleteCachedLesson(id: String) throws {
        let lessonPath = cacheDirectory.appendingPathComponent("lessons/\(id).json")
        if fileManager.fileExists(atPath: lessonPath.path) {
            try fileManager.removeItem(at: lessonPath)
        }
    }
    
    private func updateLessonAccessTime(id: String) throws {
        // Remove the async Task since this method is not async
        // This would need to be refactored to be async if needed
    }
    
    private func getAllCachedLessons() throws -> [CachedLesson] {
        let lessonsDir = cacheDirectory.appendingPathComponent("lessons")
        let lessonFiles = try fileManager.contentsOfDirectory(at: lessonsDir, includingPropertiesForKeys: nil)
        
        return lessonFiles.compactMap { url in
            guard let data = try? Data(contentsOf: url),
                  let lesson = try? JSONDecoder().decode(CachedLesson.self, from: data) else {
                return nil
            }
            return lesson
        }
    }
    
    private func getCurrentCacheSize() -> Int {
        do {
            let lessonsDir = cacheDirectory.appendingPathComponent("lessons")
            let lessonFiles = try fileManager.contentsOfDirectory(at: lessonsDir, includingPropertiesForKeys: [.fileSizeKey])
            
            return lessonFiles.reduce(0) { total, url in
                let size = (try? url.resourceValues(forKeys: [.fileSizeKey]).fileSize) ?? 0
                return total + size
            }
        } catch {
            return 0
        }
    }
    
    private func downloadPriorityLessons(
        _ lessons: [APIClientLessonContent],
        bundle: LessonBundle
    ) async throws {
        for (index, lesson) in lessons.enumerated() {
            let cachedLesson = CachedLesson(
                id: lesson.id,
                content: lesson,
                downloadedAt: Date(),
                lastAccessedAt: Date(),
                size: estimateLessonSize(lesson)
            )
            
            try storeCachedLesson(cachedLesson)
            
            await MainActor.run {
                availableOfflineContent.append(lesson.id)
                downloadProgress = Double(index + 1) / Double(lessons.count)
            }
        }
    }
    
    private func scheduleBackgroundDownload(
        for bundle: LessonBundle,
        recommendations: [CacheRecommendation]
    ) {
        // Schedule background downloads for remaining lessons
        Task.detached(priority: .background) { [weak self] in
            guard let self = self else { return }
            
            for recommendation in recommendations {
                let networkAvailable = await self.isNetworkAvailable
                guard networkAvailable else { break }
                
                _ = try? await self.downloadLesson(id: recommendation.lessonId)
                
                // Wait between downloads to avoid overwhelming the server
                try? await Task.sleep(nanoseconds: 2_000_000_000) // 2 seconds
            }
        }
    }
    
    private func scheduleDelayedDownloads(_ recommendations: [CacheRecommendation]) {
        // Schedule medium priority downloads for later
        Task.detached(priority: .background) { [weak self] in
            // Wait 1 hour before starting medium priority downloads
            try? await Task.sleep(nanoseconds: 3600_000_000_000)
            
            guard let self = self else { return }
            
            for recommendation in recommendations {
                let networkAvailable = await self.isNetworkAvailable
                let shouldPrefetchContent = await self.shouldPrefetch()
                guard networkAvailable && shouldPrefetchContent else { break }
                
                _ = try? await self.downloadLesson(id: recommendation.lessonId)
                try? await Task.sleep(nanoseconds: 5_000_000_000) // 5 seconds between downloads
            }
        }
    }
    
    private func cacheLesson(_ lesson: APIClientLessonContent) throws {
        let cachedLesson = CachedLesson(
            id: lesson.id,
            content: lesson,
            downloadedAt: Date(),
            lastAccessedAt: Date(),
            size: estimateLessonSize(lesson)
        )
        
        try storeCachedLesson(cachedLesson)
        
        // Update available content list
        availableOfflineContent.append(lesson.id)
    }
    
    private func estimateLessonSize(_ lesson: APIClientLessonContent) -> Int {
        // Estimate lesson size based on content
        let baseSize = 1024 * 1024 // 1MB base
        let exerciseSize = lesson.exercises.count * 100 * 1024 // 100KB per exercise
        let vocabularySize = lesson.vocabulary.count * 50 * 1024 // 50KB per vocabulary item
        
        return baseSize + exerciseSize + vocabularySize
    }
    
    private func loadCacheStatus() {
        let currentSize = getCurrentCacheSize()
        let maxSize = maxCacheSize
        
        cacheStatus = CacheStatus(
            currentSize: currentSize,
            maxSize: maxSize,
            utilizationPercentage: Double(currentSize) / Double(maxSize),
            lessonCount: (try? getAllCachedLessons().count) ?? 0
        )
        
        availableOfflineContent = (try? getAllCachedLessons().map { $0.id }) ?? []
    }
    
    private func updateCacheStatus() {
        loadCacheStatus()
    }
}

// MARK: - Supporting Types

struct LessonBundle: Codable {
    let id: String
    let language: String
    let lessonIds: [String]
    var downloadedLessons: [String]
    let createdAt: Date
    let expiresAt: Date
}

struct CachedLesson: Codable {
    let id: String
    let content: APIClientLessonContent
    let downloadedAt: Date
    var lastAccessedAt: Date
    let size: Int
}

struct OfflineContent: Identifiable {
    let id: String
    let title: String
    let description: String
    let difficulty: String
    let estimatedDuration: Int
    let downloadedAt: Date
    let size: Int
}

extension CacheStatus {
    static let unknown = CacheStatus(currentSize: 0, maxSize: 100 * 1024 * 1024, utilizationPercentage: 0.0, lessonCount: 0)
}

struct CacheStatus {
    let currentSize: Int
    let maxSize: Int
    let utilizationPercentage: Double
    let lessonCount: Int
    
    var formattedCurrentSize: String {
        ByteCountFormatter.string(fromByteCount: Int64(currentSize), countStyle: .file)
    }
    
    var formattedMaxSize: String {
        ByteCountFormatter.string(fromByteCount: Int64(maxSize), countStyle: .file)
    }
}

enum CacheError: LocalizedError {
    case networkUnavailable
    case diskSpaceFull
    case lessonNotFound
    
    var errorDescription: String? {
        switch self {
        case .networkUnavailable:
            return "Network connection required"
        case .diskSpaceFull:
            return "Insufficient storage space"
        case .lessonNotFound:
            return "Lesson not found in cache"
        }
    }
}

// Mock UserSession for demo
struct UserSession {
    static var current: User? = nil
}

// Mock Config for demo
struct Config {
    static let wifiOnlyDownloads = true
    static let minBatteryLevel: Float = 0.3
} 