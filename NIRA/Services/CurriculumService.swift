import Foundation
import SwiftUI
import Combine

@MainActor
class CurriculumService: ObservableObject {
    static let shared = CurriculumService()

    @Published var currentCurriculum: LanguageCurriculum?
    @Published var userSkillLevels: [SkillArea: SkillLevel] = [:]
    @Published var unlockedLessons: Set<UUID> = []
    @Published var recommendedNextLessons: [LessonRecommendation] = []
    @Published var learningPath: [CurriculumNode] = []

    private let userPreferencesService = UserPreferencesService.shared
    private let analyticsService = LearningAnalyticsService.shared
    private var cancellables = Set<AnyCancellable>()

    private init() {
        setupSubscriptions()
        loadUserProgress()
    }

    // MARK: - Public Methods

    func loadCurriculum(for language: Language) async {
        let curriculum = await generateCurriculum(for: language)
        await MainActor.run {
            self.currentCurriculum = curriculum
            self.updateLearningPath()
            self.updateRecommendations()
        }
    }

    func getNextRecommendedLessons(count: Int = 3) -> [LessonRecommendation] {
        guard let curriculum = currentCurriculum else { return [] }

        var recommendations: [LessonRecommendation] = []
        let _ = userPreferencesService.selectedLanguage

        // Get lessons based on current skill levels and progress
        for skillArea in SkillArea.allCases {
            let currentLevel = userSkillLevels[skillArea] ?? .beginner
            let nextLessons = curriculum.getNextLessons(
                for: skillArea,
                currentLevel: currentLevel,
                maxCount: count
            )

            for lesson in nextLessons {
                let recommendation = LessonRecommendation(
                    lessonId: lesson.id,
                    title: lesson.title,
                    skillArea: skillArea,
                    difficulty: lesson.difficulty,
                    estimatedDuration: lesson.estimatedDuration,
                    prerequisitesMet: checkPrerequisites(lesson),
                    confidenceScore: calculateConfidenceScore(lesson, skillArea: skillArea),
                    recommendationReason: generateRecommendationReason(lesson, skillArea: skillArea)
                )
                recommendations.append(recommendation)
            }
        }

        // Sort by confidence score and return top recommendations
        return Array(recommendations.sorted { $0.confidenceScore > $1.confidenceScore }.prefix(count))
    }

    func updateSkillLevel(_ skillArea: SkillArea, newLevel: SkillLevel) {
        userSkillLevels[skillArea] = newLevel
        updateLearningPath()
        updateRecommendations()
        saveUserProgress()
    }

    func markLessonCompleted(_ lessonId: UUID, performance: LessonPerformance) {
        unlockedLessons.insert(lessonId)

        // Update skill levels based on performance
        updateSkillLevelsFromPerformance(performance)

        // Unlock new lessons based on prerequisites
        unlockNewLessons()

        // Update recommendations
        updateRecommendations()

        saveUserProgress()
    }

    func getSkillProgress(for skillArea: SkillArea) -> SkillProgress {
        let currentLevel = userSkillLevels[skillArea] ?? .beginner
        let completedLessons = getCompletedLessonsCount(for: skillArea)
        let totalLessons = getTotalLessonsCount(for: skillArea)
        let nextMilestone = getNextMilestone(for: skillArea, currentLevel: currentLevel)

        return SkillProgress(
            skillArea: skillArea,
            currentLevel: currentLevel,
            completedLessons: completedLessons,
            totalLessons: totalLessons,
            progressPercentage: Double(completedLessons) / Double(max(totalLessons, 1)),
            nextMilestone: nextMilestone,
            estimatedTimeToNextLevel: estimateTimeToNextLevel(skillArea: skillArea)
        )
    }

    func getLanguageOverallProgress() -> LanguageProgress {
        let language = userPreferencesService.selectedLanguage
        let skillProgresses = SkillArea.allCases.map { getSkillProgress(for: $0) }

        let totalCompleted = skillProgresses.reduce(0) { $0 + $1.completedLessons }
        let totalLessons = skillProgresses.reduce(0) { $0 + $1.totalLessons }
        let averageLevel = calculateAverageSkillLevel()

        return LanguageProgress(
            language: language,
            completedLessons: totalCompleted,
            totalLessons: totalLessons,
            progressPercentage: Double(totalCompleted) / Double(max(totalLessons, 1)),
            averageSkillLevel: averageLevel,
            skillBreakdown: Dictionary(uniqueKeysWithValues: SkillArea.allCases.map { ($0, getSkillProgress(for: $0)) })
        )
    }

    func suggestStudyPlan(timeAvailable: Int) -> StudyPlan {
        let recommendations = getNextRecommendedLessons(count: 10)
        var plan = StudyPlan(totalTime: timeAvailable, sessions: [])

        var remainingTime = timeAvailable

        for recommendation in recommendations {
            if remainingTime >= recommendation.estimatedDuration {
                let session = CurriculumStudySession(
                    lessonId: recommendation.lessonId,
                    title: recommendation.title,
                    skillArea: recommendation.skillArea,
                    duration: recommendation.estimatedDuration,
                    priority: recommendation.confidenceScore
                )
                plan.sessions.append(session)
                remainingTime -= recommendation.estimatedDuration
            }
        }

        return plan
    }

    // MARK: - Private Methods

    private func setupSubscriptions() {
        userPreferencesService.$selectedLanguage
            .debounce(for: .milliseconds(300), scheduler: RunLoop.main)
            .sink { [weak self] language in
                Task {
                    await self?.loadCurriculum(for: language)
                }
            }
            .store(in: &cancellables)
    }

    private func generateCurriculum(for language: Language) async -> LanguageCurriculum {
        // Generate a structured curriculum based on language and CEFR levels
        let curriculum = LanguageCurriculum(
            language: language,
            levels: generateLevelsForLanguage(language),
            skillAreas: SkillArea.allCases,
            totalLessons: calculateTotalLessons(for: language)
        )

        return curriculum
    }

    private func generateLevelsForLanguage(_ language: Language) -> [CurriculumLevel] {
        return CEFRLevel.allCases.map { cefrLevel in
            CurriculumLevel(
                cefrLevel: cefrLevel,
                skillRequirements: generateSkillRequirements(for: cefrLevel),
                estimatedHours: estimateHoursForLevel(cefrLevel),
                milestones: generateMilestones(for: cefrLevel, language: language)
            )
        }
    }

    private func generateSkillRequirements(for level: CEFRLevel) -> [SkillArea: SkillLevel] {
        // Define skill requirements for each CEFR level
        switch level {
        case .a1:
            return [
                .vocabulary: .beginner,
                .grammar: .beginner,
                .listening: .beginner,
                .speaking: .beginner,
                .reading: .beginner,
                .writing: .beginner,
                .culture: .beginner
            ]
        case .a2:
            return [
                .vocabulary: .elementary,
                .grammar: .elementary,
                .listening: .elementary,
                .speaking: .beginner,
                .reading: .elementary,
                .writing: .beginner,
                .culture: .elementary
            ]
        case .b1:
            return [
                .vocabulary: .intermediate,
                .grammar: .intermediate,
                .listening: .intermediate,
                .speaking: .elementary,
                .reading: .intermediate,
                .writing: .elementary,
                .culture: .intermediate
            ]
        case .b2:
            return [
                .vocabulary: .upperIntermediate,
                .grammar: .upperIntermediate,
                .listening: .upperIntermediate,
                .speaking: .intermediate,
                .reading: .upperIntermediate,
                .writing: .intermediate,
                .culture: .upperIntermediate
            ]
        case .c1:
            return [
                .vocabulary: .advanced,
                .grammar: .advanced,
                .listening: .advanced,
                .speaking: .upperIntermediate,
                .reading: .advanced,
                .writing: .upperIntermediate,
                .culture: .advanced
            ]
        case .c2:
            return [
                .vocabulary: .mastery,
                .grammar: .mastery,
                .listening: .mastery,
                .speaking: .advanced,
                .reading: .mastery,
                .writing: .advanced,
                .culture: .mastery
            ]
        }
    }

    private func generateMilestones(for level: CEFRLevel, language: Language) -> [LearningMilestone] {
        // Generate specific milestones for each level and language
        switch level {
        case .a1:
            return [
                LearningMilestone(
                    title: "Basic Greetings & Introductions",
                    description: "Learn to greet people and introduce yourself",
                    skillArea: .speaking,
                    requiredLessons: 3,
                    rewardPoints: 100
                ),
                LearningMilestone(
                    title: "Essential Vocabulary (100 words)",
                    description: "Master the most common 100 words",
                    skillArea: .vocabulary,
                    requiredLessons: 5,
                    rewardPoints: 200
                ),
                LearningMilestone(
                    title: "Basic Grammar Structures",
                    description: "Understand present tense and basic sentence structure",
                    skillArea: .grammar,
                    requiredLessons: 4,
                    rewardPoints: 150
                )
            ]
        case .a2:
            return [
                LearningMilestone(
                    title: "Everyday Conversations",
                    description: "Handle simple daily interactions",
                    skillArea: .speaking,
                    requiredLessons: 6,
                    rewardPoints: 250
                ),
                LearningMilestone(
                    title: "Past & Future Tenses",
                    description: "Express actions in different time frames",
                    skillArea: .grammar,
                    requiredLessons: 5,
                    rewardPoints: 200
                )
            ]
        default:
            return [] // Add more levels as needed
        }
    }

    private func updateLearningPath() {
        guard let curriculum = currentCurriculum else { return }

        // Generate a personalized learning path based on current progress
        var path: [CurriculumNode] = []

        for skillArea in SkillArea.allCases {
            let currentLevel = userSkillLevels[skillArea] ?? .beginner
            let nextLessons = curriculum.getNextLessons(for: skillArea, currentLevel: currentLevel, maxCount: 2)

            for lesson in nextLessons {
                let node = CurriculumNode(
                    id: lesson.id,
                    title: lesson.title,
                    skillArea: skillArea,
                    difficulty: lesson.difficulty,
                    prerequisites: lesson.prerequisites,
                    isUnlocked: checkPrerequisites(lesson),
                    estimatedDuration: lesson.estimatedDuration
                )
                path.append(node)
            }
        }

        // Sort by priority and difficulty
        learningPath = path.sorted { node1, node2 in
            if node1.isUnlocked != node2.isUnlocked {
                return node1.isUnlocked && !node2.isUnlocked
            }
            return node1.difficulty.rawValue < node2.difficulty.rawValue
        }
    }

    private func updateRecommendations() {
        recommendedNextLessons = getNextRecommendedLessons(count: 5)
    }

    private func checkPrerequisites(_ lesson: CurriculumLesson) -> Bool {
        return lesson.prerequisites.allSatisfy { prerequisiteId in
            unlockedLessons.contains(prerequisiteId)
        }
    }

    private func calculateConfidenceScore(_ lesson: CurriculumLesson, skillArea: SkillArea) -> Double {
        let currentLevel = userSkillLevels[skillArea] ?? .beginner
        let levelMatch = abs(currentLevel.rawValue - lesson.difficulty.rawValue)
        let prerequisiteScore = checkPrerequisites(lesson) ? 1.0 : 0.3
        let recentActivityBonus = getRecentActivityBonus(skillArea: skillArea)

        return max(0.1, min(1.0, (1.0 - Double(levelMatch) * 0.2) * prerequisiteScore + recentActivityBonus))
    }

    private func generateRecommendationReason(_ lesson: CurriculumLesson, skillArea: SkillArea) -> String {
        let currentLevel = userSkillLevels[skillArea] ?? .beginner

        if lesson.difficulty.rawValue == currentLevel.rawValue {
            return "Perfect match for your current \(skillArea.displayName) level"
        } else if lesson.difficulty.rawValue == currentLevel.rawValue + 1 {
            return "Next step in your \(skillArea.displayName) journey"
        } else if lesson.difficulty.rawValue < currentLevel.rawValue {
            return "Review and strengthen your \(skillArea.displayName) foundation"
        } else {
            return "Challenge yourself with advanced \(skillArea.displayName)"
        }
    }

    private func updateSkillLevelsFromPerformance(_ performance: LessonPerformance) {
        for (skillArea, score) in performance.skillScores {
            let currentLevel = userSkillLevels[skillArea] ?? .beginner

            // Adjust skill level based on performance
            if score >= 0.9 && currentLevel.rawValue < SkillLevel.mastery.rawValue {
                // Excellent performance - consider level up
                let newLevel = SkillLevel(rawValue: currentLevel.rawValue + 1) ?? currentLevel
                userSkillLevels[skillArea] = newLevel
            } else if score < 0.6 && currentLevel.rawValue > SkillLevel.beginner.rawValue {
                // Poor performance - consider level down
                let newLevel = SkillLevel(rawValue: currentLevel.rawValue - 1) ?? currentLevel
                userSkillLevels[skillArea] = newLevel
            }
        }
    }

    private func unlockNewLessons() {
        guard let curriculum = currentCurriculum else { return }

        // Check all lessons to see if new ones can be unlocked
        for lesson in curriculum.getAllLessons() {
            if !unlockedLessons.contains(lesson.id) && checkPrerequisites(lesson) {
                unlockedLessons.insert(lesson.id)
            }
        }
    }

    private func getCompletedLessonsCount(for skillArea: SkillArea) -> Int {
        // This would query the actual lesson completion data
        // For now, return a placeholder based on skill level
        let currentLevel = userSkillLevels[skillArea] ?? .beginner
        return currentLevel.rawValue * 5 // Rough estimate
    }

    private func getTotalLessonsCount(for skillArea: SkillArea) -> Int {
        // This would return the actual total lessons for the skill area
        return 30 // Placeholder
    }

    private func getNextMilestone(for skillArea: SkillArea, currentLevel: SkillLevel) -> LearningMilestone? {
        guard let curriculum = currentCurriculum else { return nil }

        // Find the next milestone for this skill area
        return curriculum.levels.first { level in
            level.milestones.contains { milestone in
                milestone.skillArea == skillArea &&
                milestone.requiredLessons > getCompletedLessonsCount(for: skillArea)
            }
        }?.milestones.first { $0.skillArea == skillArea }
    }

    private func estimateTimeToNextLevel(skillArea: SkillArea) -> Int {
        let currentLevel = userSkillLevels[skillArea] ?? .beginner
        let lessonsNeeded = (currentLevel.rawValue + 1) * 5 - getCompletedLessonsCount(for: skillArea)
        return max(0, lessonsNeeded * 20) // 20 minutes per lesson estimate
    }

    private func calculateAverageSkillLevel() -> SkillLevel {
        let totalLevels = userSkillLevels.values.reduce(0) { $0 + $1.rawValue }
        let averageValue = totalLevels / max(userSkillLevels.count, 1)
        return SkillLevel(rawValue: averageValue) ?? .beginner
    }

    private func calculateTotalLessons(for language: Language) -> Int {
        // Calculate based on language complexity and available content
        switch language {
        case .french, .spanish: return 200
        case .english: return 250
        case .japanese: return 300
        case .tamil: return 150
        case .korean: return 280
        case .italian: return 190
        case .german: return 220
        case .hindi: return 180
        case .chinese: return 320
        case .portuguese: return 185
        case .telugu: return 160
        case .vietnamese: return 200
        case .indonesian: return 175
        case .arabic: return 280
        // Previous 10 languages
        case .kannada: return 160
        case .malayalam: return 165
        case .bengali: return 190
        case .marathi: return 170
        case .punjabi: return 175
        case .dutch: return 210
        case .swedish: return 205
        case .thai: return 250
        case .russian: return 300
        case .norwegian: return 200
        // Additional 25 languages
        case .gujarati: return 180
        case .odia: return 155
        case .assamese: return 145
        case .konkani: return 120
        case .sindhi: return 140
        case .bhojpuri: return 135
        case .maithili: return 130
        case .swahili: return 195
        case .hebrew: return 240
        case .greek: return 230
        case .turkish: return 260
        case .farsi: return 270
        case .tagalog: return 185
        case .ukrainian: return 250
        case .danish: return 195
        case .xhosa: return 110
        case .zulu: return 115
        case .amharic: return 160
        case .quechua: return 100
        case .maori: return 90
        case .cherokee: return 80
        case .navajo: return 85
        case .hawaiian: return 75
        case .inuktitut: return 70
        case .yoruba: return 150
        // Additional languages to complete the 50-language expansion
        case .urdu: return 200
        case .polish: return 220
        case .czech: return 210
        case .hungarian: return 240
        case .romanian: return 200
        case .bulgarian: return 190
        case .croatian: return 185
        case .serbian: return 180
        case .slovak: return 175
        case .slovenian: return 170
        case .estonian: return 160
        case .latvian: return 165
        case .lithuanian: return 170
        case .maltese: return 140
        case .irish: return 150
        case .welsh: return 145
        case .scots: return 130
        case .manx: return 100
        case .cornish: return 95
        case .breton: return 120
        case .basque: return 180
        case .catalan: return 190
        case .galician: return 175
        }
    }

    private func estimateHoursForLevel(_ level: CEFRLevel) -> Int {
        switch level {
        case .a1: return 80
        case .a2: return 120
        case .b1: return 180
        case .b2: return 250
        case .c1: return 350
        case .c2: return 500
        }
    }

    private func getRecentActivityBonus(skillArea: SkillArea) -> Double {
        // Boost recommendations for skill areas that haven't been practiced recently
        // This would check actual recent activity data
        return 0.1 // Placeholder
    }

    private func loadUserProgress() {
        // Load saved progress from UserDefaults or database
        // For now, initialize with beginner levels
        for skillArea in SkillArea.allCases {
            userSkillLevels[skillArea] = .beginner
        }
    }

    private func saveUserProgress() {
        // Save progress to persistent storage
        // Implementation would depend on chosen storage method
    }
}

// MARK: - Supporting Models

enum SkillArea: Int, CaseIterable {
    case vocabulary = 0
    case grammar = 1
    case listening = 2
    case speaking = 3
    case reading = 4
    case writing = 5
    case culture = 6

    var displayName: String {
        switch self {
        case .vocabulary: return "Vocabulary"
        case .grammar: return "Grammar"
        case .listening: return "Listening"
        case .speaking: return "Speaking"
        case .reading: return "Reading"
        case .writing: return "Writing"
        case .culture: return "Culture"
        }
    }

    var icon: String {
        switch self {
        case .vocabulary: return "textformat.abc"
        case .grammar: return "text.book.closed"
        case .listening: return "ear"
        case .speaking: return "mic"
        case .reading: return "book"
        case .writing: return "pencil"
        case .culture: return "globe"
        }
    }

    var color: Color {
        switch self {
        case .vocabulary: return .blue
        case .grammar: return .green
        case .listening: return .purple
        case .speaking: return .orange
        case .reading: return .red
        case .writing: return .yellow
        case .culture: return .pink
        }
    }
}

enum SkillLevel: Int, CaseIterable {
    case beginner = 0
    case elementary = 1
    case intermediate = 2
    case upperIntermediate = 3
    case advanced = 4
    case mastery = 5

    var displayName: String {
        switch self {
        case .beginner: return "Beginner"
        case .elementary: return "Elementary"
        case .intermediate: return "Intermediate"
        case .upperIntermediate: return "Upper Intermediate"
        case .advanced: return "Advanced"
        case .mastery: return "Mastery"
        }
    }

    var cefrEquivalent: CEFRLevel? {
        switch self {
        case .beginner: return .a1
        case .elementary: return .a2
        case .intermediate: return .b1
        case .upperIntermediate: return .b2
        case .advanced: return .c1
        case .mastery: return .c2
        }
    }
}

enum CEFRLevel: Int, CaseIterable {
    case a1 = 0
    case a2 = 1
    case b1 = 2
    case b2 = 3
    case c1 = 4
    case c2 = 5

    var displayName: String {
        switch self {
        case .a1: return "A1 - Beginner"
        case .a2: return "A2 - Elementary"
        case .b1: return "B1 - Intermediate"
        case .b2: return "B2 - Upper Intermediate"
        case .c1: return "C1 - Advanced"
        case .c2: return "C2 - Mastery"
        }
    }
}

struct LanguageCurriculum {
    let language: Language
    let levels: [CurriculumLevel]
    let skillAreas: [SkillArea]
    let totalLessons: Int

    func getNextLessons(for skillArea: SkillArea, currentLevel: SkillLevel, maxCount: Int) -> [CurriculumLesson] {
        // Return appropriate lessons for the skill area and level
        // This would query the actual lesson database
        return [] // Placeholder
    }

    func getAllLessons() -> [CurriculumLesson] {
        return levels.flatMap { $0.lessons }
    }
}

struct CurriculumLevel {
    let cefrLevel: CEFRLevel
    let skillRequirements: [SkillArea: SkillLevel]
    let estimatedHours: Int
    let milestones: [LearningMilestone]
    let lessons: [CurriculumLesson] = [] // Would be populated from database
}

struct CurriculumLesson {
    let id: UUID
    let title: String
    let difficulty: SkillLevel
    let skillArea: SkillArea
    let prerequisites: [UUID]
    let estimatedDuration: Int
    let topics: [String]
    let learningObjectives: [String]
}

struct LessonRecommendation: Identifiable {
    let id = UUID()
    let lessonId: UUID
    let title: String
    let skillArea: SkillArea
    let difficulty: SkillLevel
    let estimatedDuration: Int
    let prerequisitesMet: Bool
    let confidenceScore: Double
    let recommendationReason: String
}

struct SkillProgress {
    let skillArea: SkillArea
    let currentLevel: SkillLevel
    let completedLessons: Int
    let totalLessons: Int
    let progressPercentage: Double
    let nextMilestone: LearningMilestone?
    let estimatedTimeToNextLevel: Int
}

struct LearningMilestone: Identifiable {
    let id = UUID()
    let title: String
    let description: String
    let skillArea: SkillArea
    let requiredLessons: Int
    let rewardPoints: Int
}

struct CurriculumNode: Identifiable {
    let id: UUID
    let title: String
    let skillArea: SkillArea
    let difficulty: SkillLevel
    let prerequisites: [UUID]
    let isUnlocked: Bool
    let estimatedDuration: Int
}

struct LessonPerformance {
    let lessonId: UUID
    let overallScore: Double
    let skillScores: [SkillArea: Double]
    let timeSpent: Int
    let hintsUsed: Int
    let mistakesMade: Int
}

struct StudyPlan {
    let totalTime: Int
    var sessions: [CurriculumStudySession]

    var remainingTime: Int {
        totalTime - sessions.reduce(0) { $0 + $1.duration }
    }
}

struct CurriculumStudySession: Identifiable {
    let id = UUID()
    let lessonId: UUID
    let title: String
    let skillArea: SkillArea
    let duration: Int
    let priority: Double
}