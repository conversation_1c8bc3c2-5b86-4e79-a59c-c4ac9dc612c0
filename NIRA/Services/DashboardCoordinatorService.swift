import Foundation
import SwiftUI
import Combine

@MainActor
class DashboardCoordinatorService: ObservableObject {
    static let shared = DashboardCoordinatorService()
    
    // Published properties for dashboard sections
    @Published var todaysGoalData: TodaysGoalData = TodaysGoalData()
    @Published var recentActivities: [DashboardActivity] = []
    @Published var recommendations: [DashboardRecommendation] = []
    @Published var achievements: [DashboardAchievement] = []
    @Published var isLoading: Bool = false
    
    // Phase 3 AI-powered properties
    @Published var aiInsights: [AIInsight] = []
    @Published var personalizedContent: [PersonalizedContent] = []
    @Published var performanceForecast: PerformanceForecast?
    @Published var learningStyleRecommendations: [LearningStyleRecommendation] = []
    @Published var motivationForecast: MotivationForecast?
    @Published var optimalStudyTime: OptimalStudyTime?
    
    // Dependencies
    private let userPreferencesService = UserPreferencesService.shared
    private let analyticsService = LearningAnalyticsService.shared
    private let lessonService = LessonService.shared
    private let curriculumService = CurriculumService.shared
    private let recommendationEngine = EnhancedRecommendationEngine.shared
    private let socialFeaturesService = SocialFeaturesService.shared
    
    // Phase 3 AI Services
    private let aiPersonalizationService = AIPersonalizationService.shared
    private let predictiveAnalyticsService = PredictiveAnalyticsService.shared
    private let smartNotificationService = SmartNotificationService.shared
    private let intelligentTutoringService = IntelligentTutoringService.shared
    
    private var cancellables = Set<AnyCancellable>()
    
    private init() {
        setupSubscriptions()
        Task {
            await loadDashboardData()
        }
    }
    
    // MARK: - Public Methods
    
    func refreshDashboard() async {
        isLoading = true
        await loadDashboardData()
        isLoading = false
    }
    
    func navigateToLessons(for language: Language) {
        // This will be used by the UI to navigate to filtered lessons
        userPreferencesService.updateLanguage(language)
        // Navigation logic will be handled by the UI layer
    }
    
    func startLearningSession() {
        let userId = UUID() // TODO: Get from actual user service
        let languageId = UUID() // TODO: Map from selected language
        
        analyticsService.startSession(
            userId: userId,
            languageId: languageId,
            sessionType: .practice
        )
    }
    
    func getTodaysLessons() -> [LessonSummary] {
        return userPreferencesService.getTodaysLessons()
    }
    
    func getLanguageProgress(for language: Language) -> LanguageProgress {
        let availableCount = userPreferencesService.getAvailableLessonsCount(for: language)
        let progressPercentage = userPreferencesService.getUserProgressForLanguage(language)
        let completedCount = Int(Double(availableCount) * progressPercentage)
        
        return LanguageProgress(
            language: language,
            completedLessons: completedCount,
            totalLessons: availableCount,
            progressPercentage: progressPercentage
        )
    }
    
    // MARK: - Phase 3 AI Methods
    
    func generateAIInsights() async {
        let insights = await generatePersonalizedInsights()
        await MainActor.run {
            self.aiInsights = insights
        }
    }
    
    func getPersonalizedLearningPath() -> OptimalLearningPath? {
        return aiPersonalizationService.getOptimalLearningPath()
    }
    
    func scheduleSmartReminder(type: NotificationType) async {
        await smartNotificationService.scheduleSmartReminder(type: type)
    }
    
    func getMotivationalMessage() -> MotivationalMessage {
        return smartNotificationService.getMotivationalMessage()
    }
    
    func predictLearningOutcome(for lesson: CurriculumLesson) -> LearningOutcomePrediction {
        return predictiveAnalyticsService.predictLearningOutcome(for: lesson)
    }
    
    func startIntelligentTutoring(for lesson: CurriculumLesson) async -> TutoringSession {
        return await intelligentTutoringService.startTutoringSession(for: lesson)
    }
    
    // MARK: - Private Methods
    
    private func setupSubscriptions() {
        // Listen to language changes and refresh data
        userPreferencesService.$selectedLanguage
            .debounce(for: .milliseconds(300), scheduler: RunLoop.main)
            .sink { [weak self] _ in
                Task {
                    await self?.loadDashboardData()
                }
            }
            .store(in: &cancellables)
        
        // Listen to goal changes
        Publishers.CombineLatest(
            userPreferencesService.$dailyGoal,
            userPreferencesService.$weeklyGoal
        )
        .debounce(for: .milliseconds(300), scheduler: RunLoop.main)
        .sink { [weak self] _, _ in
            Task {
                await self?.updateGoalData()
            }
        }
        .store(in: &cancellables)
    }
    
    private func loadDashboardData() async {
        await withTaskGroup(of: Void.self) { group in
            group.addTask { await self.updateGoalData() }
            group.addTask { await self.updateRecentActivities() }
            group.addTask { await self.updateRecommendations() }
            group.addTask { await self.updateAchievements() }
            
            // Phase 3 AI data loading
            group.addTask { await self.updateAIInsights() }
            group.addTask { await self.updatePersonalizedContent() }
            group.addTask { await self.updatePerformanceForecast() }
            group.addTask { await self.updateLearningStyleRecommendations() }
            group.addTask { await self.updateMotivationForecast() }
            group.addTask { await self.updateOptimalStudyTime() }
        }
    }
    
    private func updateGoalData() async {
        let todaysProgress = userPreferencesService.getTodaysProgress()
        let weeklyProgress = userPreferencesService.getWeeklyProgress()
        let languageProgress = userPreferencesService.getLanguageSpecificProgress()
        
        let goalData = TodaysGoalData(
            dailyCompleted: todaysProgress.completed,
            dailyGoal: todaysProgress.goal,
            weeklyCompleted: weeklyProgress.completed,
            weeklyGoal: weeklyProgress.goal,
            languageCompleted: languageProgress.completed,
            languageTotal: languageProgress.total,
            selectedLanguage: userPreferencesService.selectedLanguage
        )
        
        await MainActor.run {
            self.todaysGoalData = goalData
        }
    }
    
    private func updateRecentActivities() async {
        // Get recent activities from analytics service
        let userProgressItems = Array(analyticsService.userProgress.values.suffix(5))
        
        let activities = userProgressItems.map { progress in
            DashboardActivity(
                id: progress.id,
                title: "Lesson Completed", // TODO: Get actual lesson title
                subtitle: "\(progress.completionStatus.displayName) • \(formatTime(progress.timeSpentSeconds))",
                icon: progress.completionStatus.icon,
                color: progress.completionStatus.color,
                timestamp: progress.completedAt ?? progress.createdAt,
                accuracyScore: progress.accuracyScore,
                canRetry: progress.completionStatus != .mastered
            )
        }
        
        await MainActor.run {
            self.recentActivities = activities
        }
    }
    
    private func updateRecommendations() async {
        // Get enhanced recommendations from the new recommendation engine
        let enhancedRecommendations = recommendationEngine.getTopRecommendations(count: 5)
        
        let dashboardRecommendations = enhancedRecommendations.map { recommendation in
            DashboardRecommendation(
                id: recommendation.id,
                title: recommendation.title,
                description: getRecommendationDescription(for: recommendation),
                confidenceScore: recommendation.score,
                type: mapEnhancedRecommendationType(recommendation),
                estimatedDuration: recommendation.estimatedDuration
            )
        }
        
        await MainActor.run {
            self.recommendations = Array(dashboardRecommendations)
        }
    }
    
    private func updateAchievements() async {
        // Get recent achievements from analytics service
        let userAchievements = analyticsService.userAchievements.prefix(5)
        
        let dashboardAchievements = userAchievements.map { userAchievement in
            // Find the corresponding achievement details
            let achievement = analyticsService.achievements.first { $0.id == userAchievement.achievementId }
            
            return DashboardAchievement(
                id: userAchievement.id,
                title: achievement?.name ?? "Achievement",
                description: achievement?.description ?? "Great job!",
                icon: achievement?.iconName ?? "🏆",
                earnedAt: userAchievement.earnedAt,
                pointsReward: achievement?.pointsReward ?? 0,
                difficulty: achievement?.difficulty ?? .bronze
            )
        }
        
        await MainActor.run {
            self.achievements = Array(dashboardAchievements)
        }
    }
    
    private func mapRecommendationType(_ contentType: String) -> DashboardRecommendationType {
        switch contentType.lowercased() {
        case "lesson": return .lesson
        case "vocabulary": return .vocabulary
        case "grammar": return .grammar
        case "conversation": return .conversation
        default: return .lesson
        }
    }
    
    private func mapEnhancedRecommendationType(_ recommendation: AnyRecommendation) -> DashboardRecommendationType {
        switch recommendation {
        case .personalized(let rec):
            switch rec.skillArea {
            case .vocabulary: return .vocabulary
            case .grammar: return .grammar
            case .speaking, .listening: return .conversation
            default: return .lesson
            }
        case .review(_): return .lesson
        case .challenge(_): return .lesson
        case .social(_): return .conversation
        }
    }
    
    private func getRecommendationDescription(for recommendation: AnyRecommendation) -> String {
        switch recommendation {
        case .personalized(let rec):
            return rec.recommendationReason
        case .review(let rec):
            return "Review \(rec.title) - \(rec.reviewType.displayName)"
        case .challenge(let rec):
            return "Challenge: \(rec.challengeType.displayName)"
        case .social(let rec):
            return "Social: \(rec.socialType.displayName)"
        }
    }
    
    private func formatTime(_ seconds: Int) -> String {
        let minutes = seconds / 60
        return "\(minutes)m"
    }
    
    // MARK: - Phase 3 Private Methods
    
    private func updateAIInsights() async {
        let insights = await generatePersonalizedInsights()
        await MainActor.run {
            self.aiInsights = insights
        }
    }
    
    private func updatePersonalizedContent() async {
        await MainActor.run {
            self.personalizedContent = self.aiPersonalizationService.personalizedContent
        }
    }
    
    private func updatePerformanceForecast() async {
        await MainActor.run {
            self.performanceForecast = self.predictiveAnalyticsService.performanceForecast
        }
    }
    
    private func updateLearningStyleRecommendations() async {
        let recommendations = aiPersonalizationService.getLearningStyleRecommendations()
        await MainActor.run {
            self.learningStyleRecommendations = recommendations
        }
    }
    
    private func updateMotivationForecast() async {
        let forecast = predictiveAnalyticsService.getMotivationForecast()
        await MainActor.run {
            self.motivationForecast = forecast
        }
    }
    
    private func updateOptimalStudyTime() async {
        let optimalTime = predictiveAnalyticsService.getOptimalStudyTime()
        await MainActor.run {
            self.optimalStudyTime = optimalTime
        }
    }
    
    private func generatePersonalizedInsights() async -> [AIInsight] {
        var insights: [AIInsight] = []
        
        // Learning style insights
        if let learningStyle = aiPersonalizationService.learningStyleProfile {
            insights.append(AIInsight(
                id: UUID(),
                type: .learningStyle,
                title: "Your Learning Style: \(learningStyle.primaryStyle.displayName)",
                description: "You learn best through \(learningStyle.primaryStyle.displayName.lowercased()) methods",
                confidence: learningStyle.confidenceLevel,
                actionable: true,
                recommendations: ["Focus on \(learningStyle.primaryStyle.displayName.lowercased()) content"],
                createdAt: Date()
            ))
        }
        
        // Performance insights
        if let forecast = predictiveAnalyticsService.performanceForecast {
            insights.append(AIInsight(
                id: UUID(),
                type: .performance,
                title: "Performance Trend: \(forecast.overallTrend.displayName)",
                description: "Your learning performance is \(forecast.overallTrend.displayName.lowercased())",
                confidence: forecast.confidenceLevel,
                actionable: true,
                recommendations: forecast.keyInsights,
                createdAt: Date()
            ))
        }
        
        // Motivation insights
        let motivationForecast = predictiveAnalyticsService.getMotivationForecast()
        insights.append(AIInsight(
            id: UUID(),
            type: .motivation,
            title: "Motivation Level: \(Int(motivationForecast.currentMotivationLevel * 100))%",
            description: "Your motivation is trending \(motivationForecast.trendDirection.displayName.lowercased())",
            confidence: 0.8,
            actionable: true,
            recommendations: motivationForecast.recommendedActions.map { $0.title },
            createdAt: Date()
        ))
        
        return insights
    }
}

// MARK: - Dashboard Data Models

struct TodaysGoalData {
    let dailyCompleted: Int
    let dailyGoal: Int
    let weeklyCompleted: Int
    let weeklyGoal: Int
    let languageCompleted: Int
    let languageTotal: Int
    let selectedLanguage: Language
    
    init(
        dailyCompleted: Int = 0,
        dailyGoal: Int = 3,
        weeklyCompleted: Int = 0,
        weeklyGoal: Int = 21,
        languageCompleted: Int = 0,
        languageTotal: Int = 0,
        selectedLanguage: Language = .french
    ) {
        self.dailyCompleted = dailyCompleted
        self.dailyGoal = dailyGoal
        self.weeklyCompleted = weeklyCompleted
        self.weeklyGoal = weeklyGoal
        self.languageCompleted = languageCompleted
        self.languageTotal = languageTotal
        self.selectedLanguage = selectedLanguage
    }
    
    var dailyProgress: Double {
        guard dailyGoal > 0 else { return 0.0 }
        return min(Double(dailyCompleted) / Double(dailyGoal), 1.0)
    }
    
    var weeklyProgress: Double {
        guard weeklyGoal > 0 else { return 0.0 }
        return min(Double(weeklyCompleted) / Double(weeklyGoal), 1.0)
    }
    
    var languageProgress: Double {
        guard languageTotal > 0 else { return 0.0 }
        return Double(languageCompleted) / Double(languageTotal)
    }
    
    var isDailyGoalComplete: Bool {
        return dailyCompleted >= dailyGoal
    }
    
    var isWeeklyGoalComplete: Bool {
        return weeklyCompleted >= weeklyGoal
    }
}

struct DashboardActivity: Identifiable {
    let id: UUID
    let title: String
    let subtitle: String
    let icon: String
    let color: Color
    let timestamp: Date
    let accuracyScore: Double?
    let canRetry: Bool
}

struct DashboardRecommendation: Identifiable {
    let id: UUID
    let title: String
    let description: String
    let confidenceScore: Double
    let type: DashboardRecommendationType
    let estimatedDuration: Int // minutes
}

enum DashboardRecommendationType {
    case lesson
    case vocabulary
    case grammar
    case conversation
    
    var icon: String {
        switch self {
        case .lesson: return "book.fill"
        case .vocabulary: return "textformat.abc"
        case .grammar: return "text.book.closed.fill"
        case .conversation: return "bubble.left.and.bubble.right.fill"
        }
    }
    
    var color: Color {
        switch self {
        case .lesson: return Color("NiraPrimary")
        case .vocabulary: return Color("NiraSecondary")
        case .grammar: return Color("NiraSuccess")
        case .conversation: return Color("NiraInfo")
        }
    }
}



struct DashboardAchievement: Identifiable {
    let id: UUID
    let title: String
    let description: String
    let icon: String
    let earnedAt: Date
    let pointsReward: Int
    let difficulty: LearningAchievement.AchievementDifficulty
}

struct LanguageProgress {
    let language: Language
    let completedLessons: Int
    let totalLessons: Int
    let progressPercentage: Double
    let averageSkillLevel: SkillLevel?
    let skillBreakdown: [SkillArea: SkillProgress]?
    
    init(language: Language, completedLessons: Int, totalLessons: Int, progressPercentage: Double, averageSkillLevel: SkillLevel? = nil, skillBreakdown: [SkillArea: SkillProgress]? = nil) {
        self.language = language
        self.completedLessons = completedLessons
        self.totalLessons = totalLessons
        self.progressPercentage = progressPercentage
        self.averageSkillLevel = averageSkillLevel
        self.skillBreakdown = skillBreakdown
    }
    
    var progressText: String {
        return "\(completedLessons)/\(totalLessons) lessons"
    }
    
    var percentageText: String {
        return "\(Int(progressPercentage * 100))%"
    }
}

// MARK: - Extensions



// MARK: - Enhanced Recommendation Extensions

extension ReviewType {
    var displayName: String {
        switch self {
        case .spacedRepetition: return "Spaced Repetition"
        case .weakConcept: return "Weak Concept Review"
        case .forgettingCurve: return "Memory Refresh"
        case .comprehensive: return "Comprehensive Review"
        }
    }
}

extension ChallengeType {
    var displayName: String {
        switch self {
        case .speed: return "Speed Challenge"
        case .accuracy: return "Accuracy Challenge"
        case .endurance: return "Endurance Challenge"
        case .skill: return "Skill Challenge"
        case .mixed: return "Mixed Challenge"
        }
    }
}

extension SocialType {
    var displayName: String {
        switch self {
        case .friendActivity: return "Friend Activity"
        case .groupChallenge: return "Group Challenge"
        case .communityTrend: return "Community Trend"
        case .leaderboard: return "Leaderboard"
        }
    }
}

// MARK: - Phase 3 AI Models

struct AIInsight: Identifiable {
    let id: UUID
    let type: AIInsightType
    let title: String
    let description: String
    let confidence: Double
    let actionable: Bool
    let recommendations: [String]
    let createdAt: Date
}

enum AIInsightType {
    case learningStyle
    case performance
    case motivation
    case prediction
    case optimization
    
    var displayName: String {
        switch self {
        case .learningStyle: return "Learning Style"
        case .performance: return "Performance"
        case .motivation: return "Motivation"
        case .prediction: return "Prediction"
        case .optimization: return "Optimization"
        }
    }
    
    var icon: String {
        switch self {
        case .learningStyle: return "brain.head.profile"
        case .performance: return "chart.line.uptrend.xyaxis"
        case .motivation: return "heart.fill"
        case .prediction: return "crystal.ball.fill"
        case .optimization: return "gearshape.fill"
        }
    }
    
    var color: Color {
        switch self {
        case .learningStyle: return .purple
        case .performance: return .blue
        case .motivation: return .red
        case .prediction: return .orange
        case .optimization: return .green
        }
    }
} 