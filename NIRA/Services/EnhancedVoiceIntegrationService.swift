import Foundation
import AVFoundation
import Speech
import Combine

// MARK: - Enhanced Voice Integration Service for Agentic AI

@MainActor
class EnhancedVoiceIntegrationService: NSObject, ObservableObject {
    static let shared = EnhancedVoiceIntegrationService()

    // MARK: - Published Properties
    @Published var isRecording = false
    @Published var isPlaying = false
    @Published var recordingLevel: Float = 0.0
    @Published var transcriptionText = ""
    @Published var voiceAnalysis: VoiceAnalysis?
    @Published var lastError: Error?

    // MARK: - Voice Recognition
    private var speechRecognizer: SFSpeechRecognizer?
    private var recognitionRequest: SFSpeechAudioBufferRecognitionRequest?
    private var recognitionTask: SFSpeechRecognitionTask?
    private var audioEngine = AVAudioEngine()

    // MARK: - Voice Synthesis
    private var speechSynthesizer = AVSpeechSynthesizer()
    private var currentUtterance: AVSpeechUtterance?

    // MARK: - Audio Recording
    private var audioRecorder: AVAudioRecorder?
    private var audioPlayer: AVAudioPlayer?
    private var recordingSession = AVAudioSession.sharedInstance()

    // MARK: - Multi-Agent Integration
    private let agentOrchestrationService = AgentOrchestrationService.shared

    // MARK: - Voice Profiles
    private var agentVoiceProfiles: [AgentType: VoiceProfile] = [:]

    override init() {
        super.init()
        setupVoiceIntegration()
        setupAgentVoiceProfiles()
    }

    // MARK: - Public Methods

    func startVoiceRecognition(for language: Language) async throws {
        guard !isRecording else { return }

        // Request permissions
        try await requestPermissions()

        // Setup speech recognizer for the target language
        setupSpeechRecognizer(for: language)

        // Start recognition
        try startRecognition()

        await MainActor.run {
            isRecording = true
        }
    }

    func stopVoiceRecognition() async {
        audioEngine.stop()
        recognitionRequest?.endAudio()
        recognitionTask?.cancel()

        await MainActor.run {
            isRecording = false
            recordingLevel = 0.0
        }
    }

    func speakWithAgentVoice(
        text: String,
        agentType: AgentType,
        language: Language
    ) async throws {
        guard !isPlaying else { return }

        let voiceProfile = agentVoiceProfiles[agentType] ?? createDefaultVoiceProfile(for: agentType)

        await MainActor.run {
            isPlaying = true
        }

        // Create utterance with agent-specific voice characteristics
        let utterance = AVSpeechUtterance(string: text)
        utterance.voice = selectVoice(for: language, profile: voiceProfile)
        utterance.rate = voiceProfile.speechRate
        utterance.pitchMultiplier = voiceProfile.pitchMultiplier
        utterance.volume = voiceProfile.volume

        currentUtterance = utterance
        speechSynthesizer.speak(utterance)
    }

    func analyzeVoiceInput(_ audioData: Data) async throws -> VoiceAnalysis {
        // Analyze voice characteristics for pronunciation assessment
        let analysis = VoiceAnalysis(
            confidence: calculateConfidence(from: audioData),
            pronunciationScore: assessPronunciation(from: audioData),
            fluencyScore: assessFluency(from: audioData),
            intonationScore: assessIntonation(from: audioData),
            suggestions: generatePronunciationSuggestions(from: audioData)
        )

        await MainActor.run {
            voiceAnalysis = analysis
        }

        return analysis
    }

    func processVoiceInputWithAgents(
        audioData: Data,
        session: MultiAgentSession
    ) async throws -> MultiAgentVoiceResponse {
        // Transcribe audio
        let transcription = try await transcribeAudio(audioData)

        // Analyze voice characteristics
        let voiceAnalysis = try await analyzeVoiceInput(audioData)

        // Create user input for agent processing
        let userInput = UserInput(
            text: transcription,
            type: .voice,
            timestamp: Date(),
            metadata: [
                "voice_analysis": voiceAnalysis,
                "audio_duration": audioData.count
            ]
        )

        // Process with multi-agent system
        let agentResponse = try await agentOrchestrationService.processUserInput(userInput, session: session)

        // Generate voice responses from different agents
        var agentVoiceResponses: [AgentType: Data] = [:]

        for (agentType, response) in agentResponse.agentResponses {
            let audioData = try await generateVoiceResponse(
                text: response.content,
                agentType: agentType,
                language: session.context.language
            )
            agentVoiceResponses[agentType] = audioData
        }

        return MultiAgentVoiceResponse(
            transcription: transcription,
            voiceAnalysis: voiceAnalysis,
            agentResponse: agentResponse,
            agentVoiceResponses: agentVoiceResponses
        )
    }

    // MARK: - Private Methods

    private func setupVoiceIntegration() {
        speechSynthesizer.delegate = self

        // Configure audio session
        do {
            try recordingSession.setCategory(.playAndRecord, mode: .default)
            try recordingSession.setActive(true)
        } catch {
            print("Failed to setup audio session: \(error)")
        }
    }

    private func setupAgentVoiceProfiles() {
        agentVoiceProfiles = [
            .tutor: VoiceProfile(
                speechRate: 0.5,
                pitchMultiplier: 1.0,
                volume: 0.8,
                voiceQuality: .clear,
                personality: .professional
            ),
            .conversationPartner: VoiceProfile(
                speechRate: 0.6,
                pitchMultiplier: 1.1,
                volume: 0.9,
                voiceQuality: .natural,
                personality: .friendly
            ),
            .culturalGuide: VoiceProfile(
                speechRate: 0.45,
                pitchMultiplier: 0.95,
                volume: 0.85,
                voiceQuality: .warm,
                personality: .wise
            ),
            .progressCoach: VoiceProfile(
                speechRate: 0.55,
                pitchMultiplier: 1.15,
                volume: 0.9,
                voiceQuality: .energetic,
                personality: .encouraging
            ),
            .scenarioDirector: VoiceProfile(
                speechRate: 0.5,
                pitchMultiplier: 1.05,
                volume: 0.8,
                voiceQuality: .dramatic,
                personality: .engaging
            ),
            .speechCoach: VoiceProfile(
                speechRate: 0.4,
                pitchMultiplier: 0.9,
                volume: 0.75,
                voiceQuality: .precise,
                personality: .patient
            ),
            .assessmentAgent: VoiceProfile(
                speechRate: 0.5,
                pitchMultiplier: 1.0,
                volume: 0.8,
                voiceQuality: .neutral,
                personality: .analytical
            )
        ]
    }

    private func requestPermissions() async throws {
        // Request speech recognition permission
        let speechStatus = await withCheckedContinuation { continuation in
            SFSpeechRecognizer.requestAuthorization { status in
                continuation.resume(returning: status)
            }
        }
        guard speechStatus == .authorized else {
            throw VoiceError.speechPermissionDenied
        }

        // Request microphone permission
        let microphoneStatus = await withCheckedContinuation { continuation in
            if #available(iOS 17.0, *) {
                AVAudioApplication.requestRecordPermission { granted in
                    continuation.resume(returning: granted)
                }
            } else {
                recordingSession.requestRecordPermission { granted in
                    continuation.resume(returning: granted)
                }
            }
        }
        guard microphoneStatus else {
            throw VoiceError.microphonePermissionDenied
        }
    }

    private func setupSpeechRecognizer(for language: Language) {
        let locale = Locale(identifier: language.localeIdentifier)
        speechRecognizer = SFSpeechRecognizer(locale: locale)

        guard speechRecognizer?.isAvailable == true else {
            lastError = VoiceError.speechRecognitionUnavailable
            return
        }
    }

    private func startRecognition() throws {
        // Cancel any previous task
        recognitionTask?.cancel()
        recognitionTask = nil

        // Create recognition request
        recognitionRequest = SFSpeechAudioBufferRecognitionRequest()
        guard let recognitionRequest = recognitionRequest else {
            throw VoiceError.recognitionRequestFailed
        }

        recognitionRequest.shouldReportPartialResults = true

        // Start audio engine
        let inputNode = audioEngine.inputNode
        let recordingFormat = inputNode.outputFormat(forBus: 0)

        inputNode.installTap(onBus: 0, bufferSize: 1024, format: recordingFormat) { buffer, _ in
            recognitionRequest.append(buffer)

            // Update recording level
            let level = self.calculateAudioLevel(from: buffer)
            DispatchQueue.main.async {
                self.recordingLevel = level
            }
        }

        audioEngine.prepare()
        try audioEngine.start()

        // Start recognition task
        recognitionTask = speechRecognizer?.recognitionTask(with: recognitionRequest) { result, error in
            if let result = result {
                DispatchQueue.main.async {
                    self.transcriptionText = result.bestTranscription.formattedString
                }
            }

            if let error = error {
                DispatchQueue.main.async {
                    self.lastError = error
                }
            }
        }
    }

    private func calculateAudioLevel(from buffer: AVAudioPCMBuffer) -> Float {
        guard let channelData = buffer.floatChannelData?[0] else { return 0.0 }

        let frames = buffer.frameLength
        var sum: Float = 0.0

        for i in 0..<Int(frames) {
            sum += abs(channelData[i])
        }

        let average = sum / Float(frames)
        return min(average * 10, 1.0) // Normalize to 0-1 range
    }

    private func selectVoice(for language: Language, profile: VoiceProfile) -> AVSpeechSynthesisVoice? {
        let voices = AVSpeechSynthesisVoice.speechVoices()

        // Try to find a voice that matches the language and quality preferences
        let languageVoices = voices.filter { $0.language.hasPrefix(language.localeIdentifier) }

        // Prefer enhanced quality voices
        if let enhancedVoice = languageVoices.first(where: { $0.quality == .enhanced }) {
            return enhancedVoice
        }

        // Fallback to default voice for the language
        return languageVoices.first ?? AVSpeechSynthesisVoice(language: language.localeIdentifier)
    }

    private func createDefaultVoiceProfile(for agentType: AgentType) -> VoiceProfile {
        return VoiceProfile(
            speechRate: 0.5,
            pitchMultiplier: 1.0,
            volume: 0.8,
            voiceQuality: .natural,
            personality: .neutral
        )
    }

    private func calculateConfidence(from audioData: Data) -> Double {
        // Analyze audio quality and clarity
        // This would use more sophisticated audio analysis in production
        return 0.85
    }

    private func assessPronunciation(from audioData: Data) -> Double {
        // Analyze pronunciation accuracy
        // This would use phonetic analysis in production
        return 0.8
    }

    private func assessFluency(from audioData: Data) -> Double {
        // Analyze speech fluency and rhythm
        return 0.75
    }

    private func assessIntonation(from audioData: Data) -> Double {
        // Analyze intonation patterns
        return 0.8
    }

    private func generatePronunciationSuggestions(from audioData: Data) -> [String] {
        // Generate specific pronunciation improvement suggestions
        return [
            "Focus on vowel clarity",
            "Practice consonant endings",
            "Work on rhythm and stress patterns"
        ]
    }

    private func transcribeAudio(_ audioData: Data) async throws -> String {
        // Use speech recognition to transcribe audio
        // This would integrate with the speech recognition system
        return transcriptionText
    }

    private func generateVoiceResponse(
        text: String,
        agentType: AgentType,
        language: Language
    ) async throws -> Data {
        // Generate audio data for agent voice response
        // This would use text-to-speech synthesis
        return Data()
    }
}

// MARK: - AVSpeechSynthesizerDelegate

extension EnhancedVoiceIntegrationService: AVSpeechSynthesizerDelegate {
    nonisolated func speechSynthesizer(_ synthesizer: AVSpeechSynthesizer, didFinish utterance: AVSpeechUtterance) {
        DispatchQueue.main.async {
            self.isPlaying = false
            self.currentUtterance = nil
        }
    }

    nonisolated func speechSynthesizer(_ synthesizer: AVSpeechSynthesizer, didCancel utterance: AVSpeechUtterance) {
        DispatchQueue.main.async {
            self.isPlaying = false
            self.currentUtterance = nil
        }
    }
}

// MARK: - Supporting Types

struct VoiceProfile {
    let speechRate: Float
    let pitchMultiplier: Float
    let volume: Float
    let voiceQuality: VoiceQuality
    let personality: VoicePersonality

    enum VoiceQuality {
        case clear, natural, warm, energetic, dramatic, precise, neutral
    }

    enum VoicePersonality {
        case professional, friendly, wise, encouraging, engaging, patient, analytical, neutral
    }
}

struct VoiceAnalysis {
    let confidence: Double
    let pronunciationScore: Double
    let fluencyScore: Double
    let intonationScore: Double
    let suggestions: [String]
}

struct MultiAgentVoiceResponse {
    let transcription: String
    let voiceAnalysis: VoiceAnalysis
    let agentResponse: MultiAgentResponse
    let agentVoiceResponses: [AgentType: Data]
}

// VoiceError is already defined in EnhancedAIService.swift

// MARK: - Extensions

extension Language {
    var localeIdentifier: String {
        switch self {
        case .english: return "en-US"
        case .spanish: return "es-ES"
        case .french: return "fr-FR"
        case .german: return "de-DE"
        case .italian: return "it-IT"
        case .portuguese: return "pt-PT"
        case .chinese: return "zh-CN"
        case .japanese: return "ja-JP"
        case .korean: return "ko-KR"
        case .arabic: return "ar-SA"
        case .hindi: return "hi-IN"
        case .tamil: return "ta-IN"
        case .telugu: return "te-IN"
        case .vietnamese: return "vi-VN"
        case .indonesian: return "id-ID"
        // New 10 languages
        case .kannada: return "kn-IN"
        case .malayalam: return "ml-IN"
        case .bengali: return "bn-BD"
        case .marathi: return "mr-IN"
        case .punjabi: return "pa-IN"
        case .dutch: return "nl-NL"
        case .swedish: return "sv-SE"
        case .thai: return "th-TH"
        case .russian: return "ru-RU"
        case .norwegian: return "nb-NO"
        // Additional 25 languages
        case .gujarati: return "gu-IN"
        case .odia: return "or-IN"
        case .assamese: return "as-IN"
        case .konkani: return "kok-IN"
        case .sindhi: return "sd-IN"
        case .bhojpuri: return "bho-IN"
        case .maithili: return "mai-IN"
        case .swahili: return "sw-TZ"
        case .hebrew: return "he-IL"
        case .greek: return "el-GR"
        case .turkish: return "tr-TR"
        case .farsi: return "fa-IR"
        case .tagalog: return "tl-PH"
        case .ukrainian: return "uk-UA"
        case .danish: return "da-DK"
        case .xhosa: return "xh-ZA"
        case .zulu: return "zu-ZA"
        case .amharic: return "am-ET"
        case .quechua: return "qu-PE"
        case .maori: return "mi-NZ"
        case .cherokee: return "chr-US"
        case .navajo: return "nv-US"
        case .hawaiian: return "haw-US"
        case .inuktitut: return "iu-CA"
        case .yoruba: return "yo-NG"
        // Additional languages to complete the 50-language expansion
        case .urdu: return "ur-PK"
        case .polish: return "pl-PL"
        case .czech: return "cs-CZ"
        case .hungarian: return "hu-HU"
        case .romanian: return "ro-RO"
        case .bulgarian: return "bg-BG"
        case .croatian: return "hr-HR"
        case .serbian: return "sr-RS"
        case .slovak: return "sk-SK"
        case .slovenian: return "sl-SI"
        case .estonian: return "et-EE"
        case .latvian: return "lv-LV"
        case .lithuanian: return "lt-LT"
        case .maltese: return "mt-MT"
        case .irish: return "ga-IE"
        case .welsh: return "cy-GB"
        case .scots: return "gd-GB"
        case .manx: return "gv-IM"
        case .cornish: return "kw-GB"
        case .breton: return "br-FR"
        case .basque: return "eu-ES"
        case .catalan: return "ca-ES"
        case .galician: return "gl-ES"
        }
    }
}
