//
//  ErrorHandlingService.swift
//  NIRA
//
//  Created by NIRA Team on 27/05/2025.
//

import Foundation
import SwiftUI
import Combine

// MARK: - Error Handling Service

@MainActor
class ErrorHandlingService: ObservableObject {
    static let shared = ErrorHandlingService()

    // MARK: - Published Properties
    @Published var currentError: AppError?
    @Published var isShowingError = false
    @Published var errorHistory: [ErrorLogEntry] = []

    // MARK: - Private Properties
    private var cancellables = Set<AnyCancellable>()
    private let maxErrorHistoryCount = 100
    private let retryDelays: [TimeInterval] = [1.0, 2.0, 5.0] // Progressive retry delays

    // MARK: - Initialization
    private init() {
        setupErrorObserver()
    }

    // MARK: - Public Methods

    /// Handle an error with automatic retry logic and user-friendly messaging
    func handleError(_ error: Error, context: ErrorContext = .general, canRetry: Bool = true) {
        let appError = AppError.from(error, context: context)

        // Log the error
        logError(appError, context: context)

        // Show user-friendly error if appropriate
        if appError.shouldShowToUser {
            currentError = appError
            isShowingError = true
        }

        // Attempt automatic retry for network errors
        if canRetry && appError.isRetryable {
            scheduleRetry(for: appError, context: context)
        }
    }

    /// Handle errors with custom retry logic
    func handleError<T>(_ error: Error,
                       context: ErrorContext = .general,
                       retryAction: @escaping () async throws -> T,
                       onSuccess: @escaping (T) -> Void = { _ in },
                       maxRetries: Int = 3) {
        let appError = AppError.from(error, context: context)
        logError(appError, context: context)

        if appError.isRetryable && maxRetries > 0 {
            Task {
                do {
                    try await Task.sleep(nanoseconds: UInt64(retryDelays.first ?? 1.0) * 1_000_000_000)
                    let result = try await retryAction()
                    onSuccess(result)
                } catch {
                    handleError(error, context: context, retryAction: retryAction, onSuccess: onSuccess, maxRetries: maxRetries - 1)
                }
            }
        } else {
            currentError = appError
            isShowingError = true
        }
    }

    /// Dismiss current error
    func dismissError() {
        currentError = nil
        isShowingError = false
    }

    /// Clear error history
    func clearErrorHistory() {
        errorHistory.removeAll()
    }

    /// Get error statistics for debugging
    func getErrorStatistics() -> ErrorStatistics {
        let totalErrors = errorHistory.count
        let networkErrors = errorHistory.filter { $0.error.category == .network }.count
        let authErrors = errorHistory.filter { $0.error.category == .authentication }.count
        let apiErrors = errorHistory.filter { $0.error.category == .api }.count
        let dataErrors = errorHistory.filter { $0.error.category == .data }.count

        let recentErrors = errorHistory.filter {
            Date().timeIntervalSince($0.timestamp) < 3600 // Last hour
        }.count

        return ErrorStatistics(
            totalErrors: totalErrors,
            networkErrors: networkErrors,
            authenticationErrors: authErrors,
            apiErrors: apiErrors,
            dataErrors: dataErrors,
            recentErrors: recentErrors,
            lastErrorTime: errorHistory.last?.timestamp
        )
    }

    /// Export error logs for debugging
    func exportErrorLogs() -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .medium

        var logString = "NIRA Error Log Export\n"
        logString += "Generated: \(formatter.string(from: Date()))\n"
        logString += "Total Errors: \(errorHistory.count)\n\n"

        for entry in errorHistory.reversed() { // Most recent first
            logString += "[\(formatter.string(from: entry.timestamp))] "
            logString += "\(entry.error.category.rawValue.uppercased()): "
            logString += "\(entry.error.title)\n"
            logString += "Context: \(entry.context.rawValue)\n"
            logString += "Details: \(entry.error.message)\n"
            if let technicalDetails = entry.error.technicalDetails {
                logString += "Technical: \(technicalDetails)\n"
            }
            logString += "Retryable: \(entry.error.isRetryable)\n\n"
        }

        return logString
    }

    // MARK: - Private Methods

    private func setupErrorObserver() {
        // Observe network connectivity changes
        NotificationCenter.default.publisher(for: .networkConnectivityChanged)
            .sink { [weak self] _ in
                self?.handleNetworkConnectivityChange()
            }
            .store(in: &cancellables)
    }

    private func logError(_ error: AppError, context: ErrorContext) {
        let entry = ErrorLogEntry(
            error: error,
            context: context,
            timestamp: Date()
        )

        errorHistory.append(entry)

        // Limit history size
        if errorHistory.count > maxErrorHistoryCount {
            errorHistory.removeFirst(errorHistory.count - maxErrorHistoryCount)
        }

        // Log to console in debug mode
        #if DEBUG
        print("🚨 NIRA Error [\(context.rawValue)]: \(error.title)")
        print("   Message: \(error.message)")
        if let technical = error.technicalDetails {
            print("   Technical: \(technical)")
        }
        print("   Retryable: \(error.isRetryable)")
        #endif

        // Send to analytics in production
        #if !DEBUG
        sendErrorToAnalytics(error, context: context)
        #endif
    }

    private func scheduleRetry(for error: AppError, context: ErrorContext) {
        guard let delay = retryDelays.first else { return }

        Task {
            try await Task.sleep(nanoseconds: UInt64(delay) * 1_000_000_000)

            // Retry logic would be implemented here based on context
            switch context {
            case .networkRequest:
                // Retry network request
                break
            case .authentication:
                // Retry authentication
                break
            case .dataSync:
                // Retry data synchronization
                break
            default:
                break
            }
        }
    }

    private func handleNetworkConnectivityChange() {
        // Handle network connectivity changes
        // This could trigger retries for failed network operations
    }

    private func sendErrorToAnalytics(_ error: AppError, context: ErrorContext) {
        // Send error data to analytics service
        // Implementation would depend on chosen analytics provider
    }
}

// MARK: - Supporting Types

struct AppError: Identifiable, Equatable {
    let id = UUID()
    let category: ErrorCategory
    let title: String
    let message: String
    let technicalDetails: String?
    let isRetryable: Bool
    let shouldShowToUser: Bool
    let severity: ErrorSeverity

    static func from(_ error: Error, context: ErrorContext) -> AppError {
        // Convert various error types to AppError
        if let authError = error as? AuthenticationError {
            return AppError(
                category: .authentication,
                title: "Authentication Error",
                message: authError.localizedDescription,
                technicalDetails: "\(authError)",
                isRetryable: false,
                shouldShowToUser: true,
                severity: .medium
            )
        }

        // Handle Supabase-related errors by checking error description
        if error.localizedDescription.contains("Supabase") || error.localizedDescription.contains("supabase") {
            return AppError(
                category: .api,
                title: "Service Error",
                message: "Database service error occurred. Please try again.",
                technicalDetails: error.localizedDescription,
                isRetryable: true,
                shouldShowToUser: true,
                severity: .medium
            )
        }

        if let urlError = error as? URLError {
            return AppError(
                category: .network,
                title: "Connection Error",
                message: getNetworkErrorMessage(urlError),
                technicalDetails: "\(urlError)",
                isRetryable: true,
                shouldShowToUser: true,
                severity: .medium
            )
        }

        // Generic error handling
        return AppError(
            category: .general,
            title: "Unexpected Error",
            message: "Something went wrong. Please try again.",
            technicalDetails: error.localizedDescription,
            isRetryable: true,
            shouldShowToUser: true,
            severity: .low
        )
    }

    private static func getNetworkErrorMessage(_ error: URLError) -> String {
        switch error.code {
        case .notConnectedToInternet:
            return "No internet connection. Please check your network settings."
        case .timedOut:
            return "Request timed out. Please try again."
        case .cannotFindHost:
            return "Cannot connect to server. Please try again later."
        case .networkConnectionLost:
            return "Network connection lost. Please try again."
        default:
            return "Network error occurred. Please check your connection."
        }
    }
}

enum ErrorCategory: String, CaseIterable {
    case network = "network"
    case authentication = "authentication"
    case api = "api"
    case data = "data"
    case ui = "ui"
    case general = "general"

    var icon: String {
        switch self {
        case .network:
            return "wifi.exclamationmark"
        case .authentication:
            return "person.crop.circle.badge.exclamationmark"
        case .api:
            return "server.rack"
        case .data:
            return "externaldrive.badge.exclamationmark"
        case .ui:
            return "exclamationmark.triangle"
        case .general:
            return "exclamationmark.circle"
        }
    }

    var color: Color {
        switch self {
        case .network:
            return .orange
        case .authentication:
            return .red
        case .api:
            return .blue
        case .data:
            return .purple
        case .ui:
            return .yellow
        case .general:
            return .gray
        }
    }
}

enum ErrorContext: String, CaseIterable {
    case general = "general"
    case authentication = "authentication"
    case networkRequest = "network_request"
    case dataSync = "data_sync"
    case fileUpload = "file_upload"
    case voiceRecording = "voice_recording"
    case aiChat = "ai_chat"
    case lessonLoading = "lesson_loading"
    case simulationLoading = "simulation_loading"
    case profileUpdate = "profile_update"
}

enum ErrorSeverity: String, CaseIterable {
    case low = "low"
    case medium = "medium"
    case high = "high"
    case critical = "critical"

    var color: Color {
        switch self {
        case .low:
            return .green
        case .medium:
            return .yellow
        case .high:
            return .orange
        case .critical:
            return .red
        }
    }
}

struct ErrorLogEntry: Identifiable {
    let id = UUID()
    let error: AppError
    let context: ErrorContext
    let timestamp: Date
}

struct ErrorStatistics {
    let totalErrors: Int
    let networkErrors: Int
    let authenticationErrors: Int
    let apiErrors: Int
    let dataErrors: Int
    let recentErrors: Int
    let lastErrorTime: Date?
}

// MARK: - Error Display Views

struct ErrorDisplayView: View {
    let error: AppError
    let onDismiss: () -> Void
    let onRetry: (() -> Void)?

    var body: some View {
        VStack(spacing: 20) {
            // Error Icon
            Image(systemName: error.category.icon)
                .font(.system(size: 50))
                .foregroundColor(error.category.color)

            // Error Title
            Text(error.title)
                .font(.title2)
                .fontWeight(.bold)
                .multilineTextAlignment(.center)

            // Error Message
            Text(error.message)
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal)

            // Action Buttons
            VStack(spacing: 12) {
                if let onRetry = onRetry, error.isRetryable {
                    Button("Try Again") {
                        onRetry()
                    }
                    .buttonStyle(PrimaryErrorButtonStyle())
                }

                Button("Dismiss") {
                    onDismiss()
                }
                .buttonStyle(SecondaryErrorButtonStyle())
            }
        }
        .padding(30)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(.regularMaterial)
        )
        .overlay(
            RoundedRectangle(cornerRadius: 20)
                .stroke(error.severity.color.opacity(0.3), lineWidth: 2)
        )
        .shadow(color: .black.opacity(0.1), radius: 10, x: 0, y: 5)
    }
}

struct PrimaryErrorButtonStyle: ButtonStyle {
    func makeBody(configuration: Self.Configuration) -> some View {
        configuration.label
            .font(.headline)
            .fontWeight(.semibold)
            .foregroundColor(.white)
            .frame(maxWidth: .infinity)
            .padding()
            .background(Color.blue)
            .cornerRadius(12)
            .scaleEffect(configuration.isPressed ? 0.98 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}

struct SecondaryErrorButtonStyle: ButtonStyle {
    func makeBody(configuration: Self.Configuration) -> some View {
        configuration.label
            .font(.subheadline)
            .fontWeight(.medium)
            .foregroundColor(.secondary)
            .frame(maxWidth: .infinity)
            .padding()
            .background(Color.gray.opacity(0.1))
            .cornerRadius(12)
            .scaleEffect(configuration.isPressed ? 0.98 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}

// MARK: - Notification Extensions

extension Notification.Name {
    static let networkConnectivityChanged = Notification.Name("networkConnectivityChanged")
}

// MARK: - Error Handling View Modifier

struct ErrorHandlingViewModifier: ViewModifier {
    @StateObject private var errorService = ErrorHandlingService.shared

    func body(content: Content) -> some View {
        content
            .alert("Error", isPresented: $errorService.isShowingError) {
                if let error = errorService.currentError {
                    if error.isRetryable {
                        Button("Try Again") {
                            // Retry action would be implemented here
                            errorService.dismissError()
                        }
                    }

                    Button("OK") {
                        errorService.dismissError()
                    }
                }
            } message: {
                if let error = errorService.currentError {
                    Text(error.message)
                }
            }
    }
}

extension View {
    func withErrorHandling() -> some View {
        modifier(ErrorHandlingViewModifier())
    }
}