import Foundation
import SwiftData
import Combine

// MARK: - Hybrid Lesson Service for Server + Cache Architecture

@MainActor
class LessonService: ObservableObject {
    static let shared = LessonService()
    
    @Published var isLoading = false
    @Published var currentLesson: Lesson?
    @Published var availableLessons: [Lesson] = []
    @Published var userProgress: [Progress] = []
    @Published var achievements: [Achievement] = []
    @Published var lastError: Error?
    @Published var offlineMode = false
    
    private let contentCacheService = ContentCacheService.shared
    private let apiClient = APIClient.shared
    private var modelContext: ModelContext?
    private var cancellables = Set<AnyCancellable>()
    
    private init() {}
    
    func configure(with modelContext: ModelContext) {
        self.modelContext = modelContext
        contentCacheService.configure(with: modelContext)
        loadInitialData()
        
        // Set up offline mode detection
        observeNetworkStatus()
    }
    
    // MARK: - Hybrid Lesson Management
    
    func getPersonalizedLessons(for user: User, count: Int = 10) async throws -> [Lesson] {
        await MainActor.run {
            isLoading = true
        }
        
        defer {
            Task { @MainActor in
                isLoading = false
            }
        }
        
        do {
            // Try to get from cache first if offline
            if offlineMode {
                return try await getOfflineLessons(for: user, count: count)
            }
            
            // Online: Get personalized recommendations from server
            let personalizedResponse = try await apiClient.getPersonalizedLessons(
                language: user.preferredLanguages.first?.rawValue ?? "english",
                count: count
            )
            
            // Convert server response to local Lesson objects
            var lessons: [Lesson] = []
            
            for metadata in personalizedResponse.lessons {
                // Check if we have this lesson cached
                if let cachedLesson = try await contentCacheService.getCachedLesson(id: metadata.id) {
                    let lesson = convertCachedLessonToLesson(cachedLesson.content, user: user)
                    lessons.append(lesson)
                } else {
                    // Create a placeholder lesson that will be loaded on demand
                    let lesson = createPlaceholderLesson(from: metadata, user: user)
                    lessons.append(lesson)
                }
            }
            
            // Cache the recommendations for future offline access
            scheduleRecommendationCaching(personalizedResponse.cacheRecommendations)
            
            await MainActor.run {
                availableLessons = lessons
            }
            
            return lessons
            
        } catch {
            await MainActor.run {
                lastError = error
            }
            
            // Fallback to offline content if server fails
            if !offlineMode {
                return try await getOfflineLessons(for: user, count: count)
            }
            
            throw error
        }
    }
    
    func getLesson(id: String, for user: User) async throws -> Lesson {
        await MainActor.run {
            isLoading = true
        }
        
        defer {
            Task { @MainActor in
                isLoading = false
            }
        }
        
        // Check cache first
        if let cachedLesson = try await contentCacheService.getCachedLesson(id: id) {
            return convertCachedLessonToLesson(cachedLesson.content, user: user)
        }
        
        // If not cached and online, download from server
        if !offlineMode {
            let cachedLesson = try await contentCacheService.downloadLesson(id: id)
            return convertCachedLessonToLesson(cachedLesson.content, user: user)
        }
        
        // Offline and not cached - throw error
        throw LessonServiceError.lessonNotAvailableOffline
    }
    
    func requestLessonBundle(
        language: String,
        bundleSize: Int = 10,
        preferences: UserPreferences? = nil
    ) async throws -> LessonBundle {
        guard !offlineMode else {
            throw LessonServiceError.offlineModeActive
        }
        
        await MainActor.run {
            isLoading = true
        }
        
        defer {
            Task { @MainActor in
                isLoading = false
            }
        }
        
        return try await contentCacheService.requestLessonBundle(
            language: language,
            bundleSize: bundleSize,
            preferences: preferences
        )
    }
    
    func startLesson(_ lesson: Lesson, for user: User) async throws -> ServiceLearningSession {
        // If lesson has no exercises, load the full content first
        if lesson.exercises.isEmpty {
            let fullLesson = try await getLesson(id: lesson.id.uuidString, for: user)
            await MainActor.run {
                currentLesson = fullLesson
            }
        } else {
            await MainActor.run {
                currentLesson = lesson
            }
        }
        
        let session = ServiceLearningSession(
            userID: user.id,
            startTime: Date(),
            language: lesson.language,
            sessionType: .practice
        )
        
        try saveLearningSession(session)
        
        return session
    }
    
    func completeExercise(
        _ exercise: Exercise,
        userAnswer: String,
        timeSpent: Int,
        for user: User,
        in lesson: Lesson
    ) async throws -> ExerciseResult {
        let isCorrect = validateAnswer(exercise: exercise, userAnswer: userAnswer)
        let points = calculatePoints(exercise: exercise, isCorrect: isCorrect, timeSpent: timeSpent)
        
        let result = ExerciseResult(
            exerciseID: exercise.id,
            exerciseType: exercise.type,
            userAnswer: userAnswer,
            isCorrect: isCorrect,
            score: points,
            timeSpent: timeSpent
        )
        
        try saveExerciseResult(result)
        
        // Update progress and sync to server when online
        let progress = try getOrCreateProgress(for: user, lesson: lesson)
        try saveProgress(progress)
        
        // Sync to server if online
        if !offlineMode {
            try await syncProgressToServer(progress)
        }
        
        return result
    }
    
    func completeLesson(
        _ lesson: Lesson,
        exerciseResults: [ExerciseResult],
        for user: User
    ) async throws -> LessonCompletion {
        let totalScore = exerciseResults.reduce(0) { $0 + $1.score }
        let totalTimeSpent = exerciseResults.reduce(0) { $0 + $1.timeSpent }
        let accuracy = exerciseResults.isEmpty ? 0.0 : Double(exerciseResults.filter { $0.isCorrect }.count) / Double(exerciseResults.count)
        
        // Update progress
        let progress = try getOrCreateProgress(for: user, lesson: lesson)
        progress.score = totalScore
        progress.timeSpent += totalTimeSpent
        progress.isCompleted = true
        progress.completedAt = Date()
        
        try saveProgress(progress)
        
        // Update user stats
        user.totalLessonsCompleted += 1
        user.totalPointsEarned += totalScore
        updateUserStreak(user)
        
        try saveUser(user)
        
        // Check for achievements
        let newAchievements = try await checkAndAwardAchievements(for: user, lesson: lesson, accuracy: accuracy)
        
        // Sync to server if online
        if !offlineMode {
            try await syncCompletionToServer(lesson, progress: progress, user: user)
        }
        
        // Create lesson completion
        let completion = LessonCompletion(
            lesson: lesson,
            user: user,
            score: totalScore,
            accuracy: accuracy,
            timeSpent: totalTimeSpent,
            newAchievements: newAchievements,
            completedAt: Date()
        )
        
        await MainActor.run {
            currentLesson = nil
            if let index = availableLessons.firstIndex(where: { $0.id == lesson.id }) {
                availableLessons.remove(at: index)
            }
        }
        
        // Trigger prefetch of new content based on completion
        if !offlineMode {
            Task {
                do {
                    try await triggerAdaptivePrefetch(for: user, basedOn: completion)
                } catch {
                    // Log error but don't fail the completion
                    print("Warning: Failed to trigger adaptive prefetch: \(error)")
                }
            }
        }
        
        return completion
    }
    
    // MARK: - Lesson Generation (for backwards compatibility)
    
    func generateLesson(
        language: Language,
        difficulty: Difficulty,
        category: LessonCategory,
        for user: User
    ) async throws -> Lesson {
        // For the hybrid architecture, we request lessons from the server instead of generating locally
        let personalizedLessons = try await getPersonalizedLessons(for: user, count: 1)
        
        if let lesson = personalizedLessons.first {
            return lesson
        } else {
            // Fallback: create a basic lesson structure
            return Lesson(
                title: "\(language.displayName) \(category.displayName) Lesson",
                lessonDescription: "A \(difficulty.displayName) level \(category.displayName) lesson for \(language.displayName)",
                language: language,
                difficulty: difficulty,
                category: category,
                estimatedDuration: 15,
                exercises: [],
                culturalContext: nil,
                tags: [category.displayName.lowercased()],
                totalPoints: 100,
                isAIGenerated: true
            )
        }
    }
    
    // MARK: - Offline Support
    
    private func getOfflineLessons(for user: User, count: Int) async throws -> [Lesson] {
        let targetLanguage = user.preferredLanguages.first?.rawValue ?? "english"
        let offlineContent = try await contentCacheService.getOfflineContent(for: targetLanguage)
        
        return offlineContent.prefix(count).map { content in
            Lesson(
                title: content.title,
                lessonDescription: content.description,
                language: Language(rawValue: targetLanguage) ?? .english,
                difficulty: Difficulty(rawValue: content.difficulty) ?? .intermediate,
                category: .conversation,
                estimatedDuration: content.estimatedDuration,
                exercises: [],
                culturalContext: nil,
                tags: [],
                totalPoints: 0,
                isAIGenerated: true
            )
        }
    }
    
    private func observeNetworkStatus() {
        // Observe ContentCacheService network status to update offline mode
        contentCacheService.$cacheStatus
            .receive(on: DispatchQueue.main)
            .sink { [weak self] status in
                // Update offline mode based on network availability
                self?.offlineMode = status.lessonCount == 0
            }
            .store(in: &cancellables)
    }
    
    // MARK: - Conversion Helpers
    
    private func convertCachedLessonToLesson(_ content: APIClientLessonContent, user: User) -> Lesson {
        let exercises = content.exercises.map { convertAPIClientExercise($0) }
        
        let lesson = Lesson(
            title: content.title,
            lessonDescription: content.description,
            language: Language(rawValue: content.language) ?? .english,
            difficulty: Difficulty(rawValue: content.difficulty) ?? .intermediate,
            category: LessonCategory(rawValue: content.category) ?? .conversation,
            estimatedDuration: content.estimatedDuration,
            exercises: exercises,
            culturalContext: convertCulturalContext(content.culturalContext),
            tags: content.topics,
            totalPoints: content.exercises.reduce(0) { total, exercise in
                total + exercise.points
            },
            isAIGenerated: true
        )
        
        return lesson
    }
    
    private func convertSupabaseExercise(_ exercise: GeneratedExercise) -> Exercise {
        let correctAnswerIndex: Int?
        if let correctAnswer = exercise.correctAnswer {
            if let index = Int(correctAnswer) {
                correctAnswerIndex = index
            } else if let options = exercise.options {
                correctAnswerIndex = options.firstIndex(of: correctAnswer)
            } else {
                correctAnswerIndex = nil
            }
        } else {
            correctAnswerIndex = nil
        }
        
        return Exercise(
            type: ExerciseType(rawValue: exercise.type) ?? .multipleChoice,
            question: exercise.question,
            options: exercise.options ?? [],
            correctAnswer: correctAnswerIndex,
            explanation: "Complete this exercise to continue",
            points: 100 // Default points
        )
    }
    
    private func mapSupabaseDifficultyToLocal(_ level: Int) -> Difficulty {
        switch level {
        case 1: return .beginner
        case 2: return .elementary
        case 3: return .intermediate
        case 4: return .upperIntermediate
        case 5: return .advanced
        case 6: return .expert
        default: return .beginner
        }
    }
    
    private func createPlaceholderLesson(from metadata: APIClientLessonMetadata, user: User) -> Lesson {
        let targetLanguage = user.preferredLanguages.first?.rawValue ?? "english"
        return Lesson(
            title: metadata.title,
            lessonDescription: metadata.description,
            language: Language(rawValue: targetLanguage) ?? .english,
            difficulty: Difficulty(rawValue: metadata.difficulty) ?? .intermediate,
            category: LessonCategory(rawValue: metadata.category) ?? .conversation,
            estimatedDuration: metadata.estimatedDuration,
            exercises: [],
            culturalContext: nil,
            tags: [],
            totalPoints: 0,
            isAIGenerated: true
        )
    }
    
    private func convertAPIClientExercise(_ exercise: APIClientExercise) -> Exercise {
        return Exercise(
            type: ExerciseType(rawValue: exercise.type) ?? .multipleChoice,
            question: exercise.question,
            options: exercise.options ?? [],
            correctAnswer: convertCorrectAnswer(exercise.correctAnswer, from: exercise.options ?? []),
            explanation: exercise.explanation,
            points: exercise.points
        )
    }
    
    private func convertGeneratedExercise(_ exercise: APIClientExercise) -> Exercise {
        return Exercise(
            type: ExerciseType(rawValue: exercise.type) ?? .multipleChoice,
            question: exercise.question,
            options: exercise.options ?? [],
            correctAnswer: convertCorrectAnswer(exercise.correctAnswer, from: exercise.options ?? []),
            explanation: exercise.explanation,
            points: exercise.points
        )
    }
    
    private func convertCorrectAnswer(_ correctAnswer: String, from options: [String]) -> Int? {
        return options.firstIndex(of: correctAnswer)
    }
    
    private func convertCulturalContext(_ context: CulturalContentDTO) -> CulturalContext {
        let scenario = CulturalScenario(rawValue: context.topic.lowercased().replacingOccurrences(of: " ", with: "_")) ?? .frenchCafe
        
        return CulturalContext(
            scenario: scenario,
            setting: context.region,
            participants: [],
            tips: context.tags
        )
    }
    
    // MARK: - Server Synchronization
    
    private func syncProgressToServer(_ progress: Progress) async throws {
        // Implementation would sync progress to server
        // This includes exercise results, time spent, completion status
    }
    
    private func syncCompletionToServer(_ lesson: Lesson, progress: Progress, user: User) async throws {
        // Implementation would sync lesson completion to server
        // This updates user statistics and triggers new recommendations
    }
    
    private func triggerAdaptivePrefetch(for user: User, basedOn completion: LessonCompletion) async throws {
        // Trigger server to generate new personalized content based on completion
        // This helps maintain a fresh pipeline of relevant lessons
        try await contentCacheService.prefetchRecommendedContent(for: user)
    }
    
    // MARK: - Background Tasks
    
    private func scheduleRecommendationCaching(_ recommendations: [String]) {
        Task.detached(priority: .background) { [weak self] in
            guard let self = self else { return }
            
            for recommendation in recommendations {
                do {
                    _ = try await self.contentCacheService.downloadLesson(id: recommendation)
                } catch {
                    print("Warning: Failed to cache recommendation \(recommendation): \(error)")
                }
                
                // Small delay between downloads
                do {
                    try await Task.sleep(nanoseconds: 1_000_000_000)
                } catch {
                    // Sleep failure is not critical
                    break
                }
            }
        }
    }
    
    // MARK: - Existing Helper Methods (simplified for space)
    
    private func loadInitialData() {
        // Load cached data and sync with server if online
    }
    
    private func validateAnswer(exercise: Exercise, userAnswer: String) -> Bool {
        guard let correctAnswer = exercise.correctAnswer else {
            // For exercise types without correct answers (like pronunciation), always return true for now
            return true
        }
        
        return correctAnswer == Int(userAnswer) || 
               exercise.options[correctAnswer].lowercased() == userAnswer.lowercased()
    }
    
    private func calculatePoints(exercise: Exercise, isCorrect: Bool, timeSpent: Int) -> Int {
        guard isCorrect else { return 0 }
        
        let basePoints = exercise.points
        let timeLimit = exercise.timeLimit ?? 60
        let timeBonus = max(0, (timeLimit - timeSpent) / 10)
        
        return basePoints + timeBonus
    }
    
    private func getOrCreateProgress(for user: User, lesson: Lesson) throws -> Progress {
        // Implementation to get or create progress record
        return Progress(
            userID: user.id,
            lessonID: lesson.id
        )
    }
    
    private func saveProgress(_ progress: Progress) throws {
        // Save progress to local database
    }
    
    private func saveUser(_ user: User) throws {
        // Save user to local database
    }
    
    private func saveLearningSession(_ session: ServiceLearningSession) throws {
        // Save learning session to local database
    }
    
    private func saveExerciseResult(_ result: ExerciseResult) throws {
        // Save exercise result to local database
    }
    
    private func updateUserStreak(_ user: User) {
        // Update user's learning streak
    }
    
    private func checkAndAwardAchievements(for user: User, lesson: Lesson, accuracy: Double) async throws -> [Achievement] {
        // Check for new achievements and award them
        return []
    }
}

// MARK: - Supporting Types

enum LessonServiceError: LocalizedError {
    case lessonNotAvailableOffline
    case offlineModeActive
    case syncFailed
    
    var errorDescription: String? {
        switch self {
        case .lessonNotAvailableOffline:
            return "This lesson is not available offline. Please connect to the internet to download it."
        case .offlineModeActive:
            return "This feature requires an internet connection."
        case .syncFailed:
            return "Failed to sync data with server. Changes will be synced when connection is restored."
        }
    }
}

struct LessonCompletion {
    let lesson: Lesson
    let user: User
    let score: Int
    let accuracy: Double
    let timeSpent: Int
    let newAchievements: [Achievement]
    let completedAt: Date
}

// MARK: - Type disambiguation for local LearningSession
struct ServiceLearningSession {
    let userID: UUID
    let startTime: Date
    let language: Language
    let sessionType: ServiceSessionType
}

enum ServiceSessionType: String, CaseIterable {
    case practice = "practice"
    case review = "review"
    case test = "test"
}

// Mock extensions for missing properties
extension User {
    var targetLanguage: String { "english" }
} 