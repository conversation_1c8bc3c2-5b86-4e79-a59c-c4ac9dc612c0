import Foundation
import Combine

// MARK: - Multi-Model AI Service for Enhanced Agent Capabilities

@MainActor
class MultiModelAIService: ObservableObject {
    static let shared = MultiModelAIService()
    
    // MARK: - Published Properties
    @Published var isProcessing = false
    @Published var lastError: Error?
    @Published var modelStatus: [AIModel: ModelStatus] = [:]
    
    // MARK: - Private Properties
    private let geminiService = GeminiService.shared
    private let enhancedAIService = EnhancedAIService()
    
    enum AIModel: String, CaseIterable {
        case gemini = "gemini-2.0-flash"
        case openai = "gpt-4o"
        case grok = "grok-beta"
        case claude = "claude-3-5-sonnet"
        
        var displayName: String {
            switch self {
            case .gemini: return "Gemini 2.0 Flash"
            case .openai: return "GPT-4o"
            case .grok: return "Grok"
            case .claude: return "Claude 3.5 Sonnet"
            }
        }
        
        var capabilities: [AICapability] {
            switch self {
            case .gemini:
                return [.textGeneration, .voiceInteraction, .imageAnalysis, .codeGeneration, .multimodal]
            case .openai:
                return [.textGeneration, .imageAnalysis, .codeGeneration, .reasoning]
            case .grok:
                return [.textGeneration, .realTimeData, .reasoning]
            case .claude:
                return [.textGeneration, .reasoning, .codeGeneration, .longContext]
            }
        }
    }
    
    enum AICapability {
        case textGeneration, voiceInteraction, imageAnalysis, codeGeneration, multimodal, reasoning, realTimeData, longContext
    }
    
    enum ModelStatus {
        case available, unavailable, limited, error
    }
    
    enum AIError: LocalizedError {
        case modelUnavailable
        case invalidResponse
        case rateLimitExceeded
        case authenticationFailed
        case networkError
        
        var errorDescription: String? {
            switch self {
            case .modelUnavailable:
                return "AI model is currently unavailable"
            case .invalidResponse:
                return "Invalid response from AI service"
            case .rateLimitExceeded:
                return "Rate limit exceeded. Please try again later"
            case .authenticationFailed:
                return "Authentication failed. Please check API keys"
            case .networkError:
                return "Network connection error"
            }
        }
    }
    
    // MARK: - Initialization
    private init() {
        checkModelAvailability()
    }
    
    // MARK: - Public Methods
    
    func generateResponse(
        message: String,
        agent: LanguageTutor,
        conversationHistory: [SupabaseEnhancedMessage] = [],
        preferredModel: AIModel? = nil,
        capabilities: [AICapability] = [.textGeneration]
    ) async throws -> EnhancedAIResponse {
        
        isProcessing = true
        defer { isProcessing = false }
        
        let model = selectBestModel(for: capabilities, preferred: preferredModel)
        
        do {
            let response = try await generateWithModel(
                model: model,
                message: message,
                agent: agent,
                conversationHistory: conversationHistory
            )
            
            return response
        } catch {
            // Fallback to alternative model
            if let fallbackModel = getFallbackModel(for: model, capabilities: capabilities) {
                return try await generateWithModel(
                    model: fallbackModel,
                    message: message,
                    agent: agent,
                    conversationHistory: conversationHistory
                )
            }
            
            throw error
        }
    }
    
    func analyzeImage(
        imageData: Data,
        prompt: String,
        agent: LanguageTutor
    ) async throws -> String {
        
        let model = selectBestModel(for: [.imageAnalysis])
        
        switch model {
        case .gemini:
            return try await analyzeImageWithGemini(imageData: imageData, prompt: prompt, agent: agent)
        case .openai:
            return try await analyzeImageWithOpenAI(imageData: imageData, prompt: prompt, agent: agent)
        default:
            throw AIError.modelUnavailable
        }
    }
    
    func generateCode(
        prompt: String,
        language: String,
        agent: LanguageTutor
    ) async throws -> String {
        
        let model = selectBestModel(for: [.codeGeneration])
        
        switch model {
        case .gemini:
            return try await generateCodeWithGemini(prompt: prompt, language: language, agent: agent)
        case .openai:
            return try await generateCodeWithOpenAI(prompt: prompt, language: language, agent: agent)
        case .claude:
            return try await generateCodeWithClaude(prompt: prompt, language: language, agent: agent)
        default:
            throw AIError.modelUnavailable
        }
    }
    
    func getRealTimeData(
        query: String,
        agent: LanguageTutor
    ) async throws -> String {
        
        // Use Grok for real-time data queries
        guard modelStatus[.grok] == .available else {
            throw AIError.modelUnavailable
        }
        
        return try await getRealTimeDataWithGrok(query: query, agent: agent)
    }
    
    // MARK: - Model Selection
    
    private func selectBestModel(for capabilities: [AICapability], preferred: AIModel? = nil) -> AIModel {
        // If preferred model is specified and available, use it
        if let preferred = preferred,
           modelStatus[preferred] == .available,
           capabilities.allSatisfy({ preferred.capabilities.contains($0) }) {
            return preferred
        }
        
        // Find best available model for required capabilities
        let availableModels = AIModel.allCases.filter { model in
            modelStatus[model] == .available &&
            capabilities.allSatisfy { model.capabilities.contains($0) }
        }
        
        // Priority order: Gemini -> OpenAI -> Claude -> Grok
        let priorityOrder: [AIModel] = [.gemini, .openai, .claude, .grok]
        
        for model in priorityOrder {
            if availableModels.contains(model) {
                return model
            }
        }
        
        // Fallback to first available model
        return availableModels.first ?? .gemini
    }
    
    private func getFallbackModel(for model: AIModel, capabilities: [AICapability]) -> AIModel? {
        let alternatives = AIModel.allCases.filter { alternative in
            alternative != model &&
            modelStatus[alternative] == .available &&
            capabilities.allSatisfy { alternative.capabilities.contains($0) }
        }
        
        return alternatives.first
    }
    
    // MARK: - Model-Specific Implementations
    
    private func generateWithModel(
        model: AIModel,
        message: String,
        agent: LanguageTutor,
        conversationHistory: [SupabaseEnhancedMessage]
    ) async throws -> EnhancedAIResponse {
        
        switch model {
        case .gemini:
            return try await generateWithGemini(message: message, agent: agent, conversationHistory: conversationHistory)
        case .openai:
            return try await generateWithOpenAI(message: message, agent: agent, conversationHistory: conversationHistory)
        case .grok:
            return try await generateWithGrok(message: message, agent: agent, conversationHistory: conversationHistory)
        case .claude:
            return try await generateWithClaude(message: message, agent: agent, conversationHistory: conversationHistory)
        }
    }
    
    private func generateWithGemini(
        message: String,
        agent: LanguageTutor,
        conversationHistory: [SupabaseEnhancedMessage]
    ) async throws -> EnhancedAIResponse {
        
        // Use existing enhanced AI service which uses Gemini
        return try await enhancedAIService.generateEnhancedResponse(
            for: message,
            agent: agent,
            conversationId: UUID(),
            includeKnowledgeBase: true
        )
    }
    
    private func generateWithOpenAI(
        message: String,
        agent: LanguageTutor,
        conversationHistory: [SupabaseEnhancedMessage]
    ) async throws -> EnhancedAIResponse {
        
        let prompt = buildPrompt(message: message, agent: agent, conversationHistory: conversationHistory)
        
        let request = OpenAIRequest(
            model: "gpt-4o",
            messages: [
                OpenAIMessage(role: "system", content: getSystemPrompt(for: agent)),
                OpenAIMessage(role: "user", content: prompt)
            ],
            temperature: 0.7,
            maxTokens: 1000
        )
        
        let response = try await makeOpenAIRequest(request)
        
        return EnhancedAIResponse(
            content: response.choices.first?.message.content ?? "",
            responseTime: 0.0,
            confidence: 0.9,
            grammarCorrections: [],
            culturalNotes: [],
            vocabularyHighlights: [],
            aiMetadata: SupabaseAIMetadata(
                model: "gpt-4o",
                temperature: 0.7,
                maxTokens: 1000,
                promptTokens: nil,
                completionTokens: nil,
                totalTokens: nil,
                finishReason: "stop",
                processingTime: 0.0
            )
        )
    }
    
    private func generateWithGrok(
        message: String,
        agent: LanguageTutor,
        conversationHistory: [SupabaseEnhancedMessage]
    ) async throws -> EnhancedAIResponse {
        
        // Grok API implementation (similar to OpenAI)
        let prompt = buildPrompt(message: message, agent: agent, conversationHistory: conversationHistory)
        
        let request = GrokRequest(
            model: "grok-beta",
            messages: [
                GrokMessage(role: "system", content: getSystemPrompt(for: agent)),
                GrokMessage(role: "user", content: prompt)
            ],
            temperature: 0.7,
            maxTokens: 1000
        )
        
        let response = try await makeGrokRequest(request)
        
        return EnhancedAIResponse(
            content: response.choices.first?.message.content ?? "",
            responseTime: 0.0,
            confidence: 0.85,
            grammarCorrections: [],
            culturalNotes: [],
            vocabularyHighlights: [],
            aiMetadata: SupabaseAIMetadata(
                model: "grok-beta",
                temperature: 0.7,
                maxTokens: 1000,
                promptTokens: nil,
                completionTokens: nil,
                totalTokens: nil,
                finishReason: "stop",
                processingTime: 0.0
            )
        )
    }
    
    private func generateWithClaude(
        message: String,
        agent: LanguageTutor,
        conversationHistory: [SupabaseEnhancedMessage]
    ) async throws -> EnhancedAIResponse {
        
        // Claude API implementation
        let prompt = buildPrompt(message: message, agent: agent, conversationHistory: conversationHistory)
        
        let request = ClaudeRequest(
            model: "claude-3-5-sonnet-20241022",
            messages: [
                ClaudeMessage(role: "user", content: prompt)
            ],
            system: getSystemPrompt(for: agent),
            maxTokens: 1000,
            temperature: 0.7
        )
        
        let response = try await makeClaudeRequest(request)
        
        return EnhancedAIResponse(
            content: response.content.first?.text ?? "",
            responseTime: 0.0,
            confidence: 0.95,
            grammarCorrections: [],
            culturalNotes: [],
            vocabularyHighlights: [],
            aiMetadata: SupabaseAIMetadata(
                model: "claude-3-5-sonnet",
                temperature: 0.7,
                maxTokens: 1000,
                promptTokens: nil,
                completionTokens: nil,
                totalTokens: nil,
                finishReason: "stop",
                processingTime: 0.0
            )
        )
    }
    
    // MARK: - Image Analysis
    
    private func analyzeImageWithGemini(imageData: Data, prompt: String, agent: LanguageTutor) async throws -> String {
        // Use existing Gemini service for image analysis
        _ = imageData.base64EncodedString()
        
        _ = """
        As \(agent.name), a \(agent.personality) \(agent.language.displayName) tutor, analyze this image and respond to: \(prompt)
        
        Provide your response in \(agent.language.displayName) with English explanations when helpful.
        """
        
        // Implementation would use Gemini Vision API
        return "Image analysis result from Gemini"
    }
    
    private func analyzeImageWithOpenAI(imageData: Data, prompt: String, agent: LanguageTutor) async throws -> String {
        let base64Image = imageData.base64EncodedString()
        
        let request = OpenAIVisionRequest(
            model: "gpt-4o",
            messages: [
                OpenAIVisionMessage(
                    role: "user",
                    content: [
                        OpenAIVisionContent(type: "text", text: prompt, imageUrl: nil),
                        OpenAIVisionContent(type: "image_url", text: nil, imageUrl: OpenAIImageUrl(url: "data:image/jpeg;base64,\(base64Image)"))
                    ]
                )
            ],
            maxTokens: 1000
        )
        
        let response = try await makeOpenAIVisionRequest(request)
        return response.choices.first?.message.content ?? ""
    }
    
    // MARK: - Code Generation
    
    private func generateCodeWithGemini(prompt: String, language: String, agent: LanguageTutor) async throws -> String {
        _ = """
        Generate \(language) code for: \(prompt)
        
        Provide clean, well-commented code with explanations.
        """
        
        // Use Gemini for code generation
        return "Generated code from Gemini"
    }
    
    private func generateCodeWithOpenAI(prompt: String, language: String, agent: LanguageTutor) async throws -> String {
        let request = OpenAIRequest(
            model: "gpt-4o",
            messages: [
                OpenAIMessage(role: "system", content: "You are an expert programmer. Generate clean, efficient code."),
                OpenAIMessage(role: "user", content: "Generate \(language) code for: \(prompt)")
            ],
            temperature: 0.3,
            maxTokens: 2000
        )
        
        let response = try await makeOpenAIRequest(request)
        return response.choices.first?.message.content ?? ""
    }
    
    private func generateCodeWithClaude(prompt: String, language: String, agent: LanguageTutor) async throws -> String {
        let request = ClaudeRequest(
            model: "claude-3-5-sonnet-20241022",
            messages: [
                ClaudeMessage(role: "user", content: "Generate \(language) code for: \(prompt)")
            ],
            system: "You are an expert programmer. Generate clean, efficient, well-documented code.",
            maxTokens: 2000,
            temperature: 0.3
        )
        
        let response = try await makeClaudeRequest(request)
        return response.content.first?.text ?? ""
    }
    
    // MARK: - Real-time Data
    
    private func getRealTimeDataWithGrok(query: String, agent: LanguageTutor) async throws -> String {
        let request = GrokRequest(
            model: "grok-beta",
            messages: [
                GrokMessage(role: "system", content: "You have access to real-time information. Provide current, accurate data."),
                GrokMessage(role: "user", content: query)
            ],
            temperature: 0.5,
            maxTokens: 1000
        )
        
        let response = try await makeGrokRequest(request)
        return response.choices.first?.message.content ?? ""
    }
    
    // MARK: - Helper Methods
    
    private func buildPrompt(message: String, agent: LanguageTutor, conversationHistory: [SupabaseEnhancedMessage]) -> String {
        var prompt = message
        
        if !conversationHistory.isEmpty {
            prompt += "\n\nConversation History:\n"
            for msg in conversationHistory.suffix(5) {
                let role = msg.senderType == .user ? "Student" : agent.name
                prompt += "\(role): \(msg.content)\n"
            }
        }
        
        return prompt
    }
    
    private func getSystemPrompt(for agent: LanguageTutor) -> String {
        return """
        You are \(agent.name), a \(agent.personality) language tutor specializing in \(agent.language.displayName).
        
        Your teaching approach:
        - Be encouraging and patient
        - Provide cultural context when relevant
        - Correct mistakes gently with explanations
        - Ask follow-up questions to keep conversation flowing
        - Adapt to the student's level and interests
        """
    }
    
    private func checkModelAvailability() {
        Task {
            for model in AIModel.allCases {
                modelStatus[model] = await checkModelStatus(model)
            }
        }
    }
    
    private func checkModelStatus(_ model: AIModel) async -> ModelStatus {
        // Simple availability check - in production, this would ping each service
        switch model {
        case .gemini:
            return APIKeys.geminiAPIKey.isEmpty ? .unavailable : .available
        case .openai:
            return APIKeys.openAIAPIKey.isEmpty ? .unavailable : .available
        case .grok, .claude:
            return .limited // Would need actual API keys
        }
    }
    
    // MARK: - API Request Methods
    
    private func makeOpenAIRequest(_ request: OpenAIRequest) async throws -> OpenAIResponse {
        guard let url = URL(string: "https://api.openai.com/v1/chat/completions") else {
            throw AIError.networkError
        }
        
        var urlRequest = URLRequest(url: url)
        urlRequest.httpMethod = "POST"
        urlRequest.setValue("Bearer \(APIKeys.openAIAPIKey)", forHTTPHeaderField: "Authorization")
        urlRequest.setValue("application/json", forHTTPHeaderField: "Content-Type")
        urlRequest.httpBody = try JSONEncoder().encode(request)
        
        let (data, _) = try await URLSession.shared.data(for: urlRequest)
        return try JSONDecoder().decode(OpenAIResponse.self, from: data)
    }
    
    private func makeOpenAIVisionRequest(_ request: OpenAIVisionRequest) async throws -> OpenAIResponse {
        guard let url = URL(string: "https://api.openai.com/v1/chat/completions") else {
            throw AIError.networkError
        }
        
        var urlRequest = URLRequest(url: url)
        urlRequest.httpMethod = "POST"
        urlRequest.setValue("Bearer \(APIKeys.openAIAPIKey)", forHTTPHeaderField: "Authorization")
        urlRequest.setValue("application/json", forHTTPHeaderField: "Content-Type")
        urlRequest.httpBody = try JSONEncoder().encode(request)
        
        let (data, _) = try await URLSession.shared.data(for: urlRequest)
        return try JSONDecoder().decode(OpenAIResponse.self, from: data)
    }
    
    private func makeGrokRequest(_ request: GrokRequest) async throws -> GrokResponse {
        // Grok API endpoint (hypothetical - would need actual endpoint)
        guard let url = URL(string: "https://api.x.ai/v1/chat/completions") else {
            throw AIError.networkError
        }
        
        var urlRequest = URLRequest(url: url)
        urlRequest.httpMethod = "POST"
        urlRequest.setValue("Bearer YOUR_GROK_API_KEY", forHTTPHeaderField: "Authorization")
        urlRequest.setValue("application/json", forHTTPHeaderField: "Content-Type")
        urlRequest.httpBody = try JSONEncoder().encode(request)
        
        let (data, _) = try await URLSession.shared.data(for: urlRequest)
        return try JSONDecoder().decode(GrokResponse.self, from: data)
    }
    
    private func makeClaudeRequest(_ request: ClaudeRequest) async throws -> ClaudeResponse {
        guard let url = URL(string: "https://api.anthropic.com/v1/messages") else {
            throw AIError.networkError
        }
        
        var urlRequest = URLRequest(url: url)
        urlRequest.httpMethod = "POST"
        urlRequest.setValue("YOUR_CLAUDE_API_KEY", forHTTPHeaderField: "x-api-key")
        urlRequest.setValue("application/json", forHTTPHeaderField: "Content-Type")
        urlRequest.setValue("2023-06-01", forHTTPHeaderField: "anthropic-version")
        urlRequest.httpBody = try JSONEncoder().encode(request)
        
        let (data, _) = try await URLSession.shared.data(for: urlRequest)
        return try JSONDecoder().decode(ClaudeResponse.self, from: data)
    }
}

// MARK: - API Models

// OpenAI Models
struct OpenAIRequest: Codable {
    let model: String
    let messages: [OpenAIMessage]
    let temperature: Double
    let maxTokens: Int
    
    enum CodingKeys: String, CodingKey {
        case model, messages, temperature
        case maxTokens = "max_tokens"
    }
}

struct OpenAIMessage: Codable {
    let role: String
    let content: String
}

struct OpenAIResponse: Codable {
    let choices: [OpenAIChoice]
}

struct OpenAIChoice: Codable {
    let message: OpenAIMessage
}

// OpenAI Vision Models
struct OpenAIVisionRequest: Codable {
    let model: String
    let messages: [OpenAIVisionMessage]
    let maxTokens: Int
    
    enum CodingKeys: String, CodingKey {
        case model, messages
        case maxTokens = "max_tokens"
    }
}

struct OpenAIVisionMessage: Codable {
    let role: String
    let content: [OpenAIVisionContent]
}

struct OpenAIVisionContent: Codable {
    let type: String
    let text: String?
    let imageUrl: OpenAIImageUrl?
    
    enum CodingKeys: String, CodingKey {
        case type, text
        case imageUrl = "image_url"
    }
}

struct OpenAIImageUrl: Codable {
    let url: String
}

// Grok Models
struct GrokRequest: Codable {
    let model: String
    let messages: [GrokMessage]
    let temperature: Double
    let maxTokens: Int
    
    enum CodingKeys: String, CodingKey {
        case model, messages, temperature
        case maxTokens = "max_tokens"
    }
}

struct GrokMessage: Codable {
    let role: String
    let content: String
}

struct GrokResponse: Codable {
    let choices: [GrokChoice]
}

struct GrokChoice: Codable {
    let message: GrokMessage
}

// Claude Models
struct ClaudeRequest: Codable {
    let model: String
    let messages: [ClaudeMessage]
    let system: String
    let maxTokens: Int
    let temperature: Double
    
    enum CodingKeys: String, CodingKey {
        case model, messages, system, temperature
        case maxTokens = "max_tokens"
    }
}

struct ClaudeMessage: Codable {
    let role: String
    let content: String
}

struct ClaudeResponse: Codable {
    let content: [ClaudeContent]
}

struct ClaudeContent: Codable {
    let text: String
} 