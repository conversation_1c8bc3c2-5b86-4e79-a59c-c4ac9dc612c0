import Foundation
import Combine

// MARK: - Pinecone Vector Database Service for Personalized Learning

@MainActor
class PineconeService: ObservableObject {
    static let shared = PineconeService()

    private let baseURL: String
    private let apiKey: String
    private let useMockData: Bool
    private let indexName: String = "nira-user-embeddings"

    @Published var isProcessing = false
    @Published var lastError: Error?

    private init() {
        self.baseURL = ProcessInfo.processInfo.environment["PINECONE_URL"] ?? "https://your-index.pinecone.io"
        self.apiKey = ProcessInfo.processInfo.environment["PINECONE_API_KEY"] ?? "your-pinecone-api-key"
        self.useMockData = apiKey == "your-pinecone-api-key" || ProcessInfo.processInfo.environment["USE_MOCK_DATA"] == "true"
    }

    // MARK: - User Vector Management

    func createUserEmbedding(for user: User, learningData: UserLearningData) async throws {
        await MainActor.run {
            isProcessing = true
        }

        defer {
            Task { @MainActor in
                isProcessing = false
            }
        }

        let embedding = try await generateUserEmbedding(from: learningData)
        let vector = PineconeVector(
            id: user.id.uuidString,
            values: embedding,
            metadata: createUserMetadata(for: user, learningData: learningData)
        )

        try await upsertVector(vector)
    }

    func updateUserEmbedding(for user: User, newProgress: Progress) async throws {
        await MainActor.run {
            isProcessing = true
        }

        defer {
            Task { @MainActor in
                isProcessing = false
            }
        }

        // Retrieve current user data and update with new progress
        let updatedLearningData = try await getUserLearningData(for: user, including: newProgress)
        let embedding = try await generateUserEmbedding(from: updatedLearningData)

        let vector = PineconeVector(
            id: user.id.uuidString,
            values: embedding,
            metadata: createUserMetadata(for: user, learningData: updatedLearningData)
        )

        try await upsertVector(vector)
    }

    // MARK: - Personalized Recommendations

    func findSimilarLearners(for user: User, limit: Int = 10) async throws -> [SimilarLearner] {
        await MainActor.run {
            isProcessing = true
        }

        defer {
            Task { @MainActor in
                isProcessing = false
            }
        }

        let userVector = try await fetchUserVector(userId: user.id.uuidString)
        let similarVectors = try await queryVectors(
            vector: userVector.values,
            topK: limit + 1, // +1 to exclude self
            includeMetadata: true
        )

        return similarVectors
            .filter { $0.id != user.id.uuidString } // Exclude self
            .prefix(limit)
            .map { vector in
                SimilarLearner(
                    id: vector.id,
                    similarity: vector.score,
                    metadata: vector.metadata
                )
            }
    }

    func getPersonalizedLessonRecommendations(for user: User, count: Int = 5) async throws -> [LessonRecommendation] {
        await MainActor.run {
            isProcessing = true
        }

        defer {
            Task { @MainActor in
                isProcessing = false
            }
        }

        // Find similar users
        let similarLearners = try await findSimilarLearners(for: user, limit: 20)

        // Analyze their successful lessons
        let recommendations = try await analyzeSuccessfulLessons(
            for: similarLearners,
            excluding: try await getCompletedLessonIds(for: user),
            limit: count
        )

        return recommendations
    }

    func getAdaptiveDifficultyRecommendation(for user: User, category: LessonCategory) async throws -> Difficulty {
        await MainActor.run {
            isProcessing = true
        }

        defer {
            Task { @MainActor in
                isProcessing = false
            }
        }

        let userVector = try await fetchUserVector(userId: user.id.uuidString)
        let performance = extractPerformanceMetrics(from: userVector.metadata)

        return calculateOptimalDifficulty(
            category: category,
            performance: performance,
            userLevel: "intermediate" // Default level since currentLevel doesn't exist on User
        )
    }

    // MARK: - Learning Pattern Analysis

    func analyzeLearningPatterns(for user: User) async throws -> LearningPatterns {
        await MainActor.run {
            isProcessing = true
        }

        defer {
            Task { @MainActor in
                isProcessing = false
            }
        }

        if useMockData {
            try await Task.sleep(nanoseconds: 500_000_000)
            return createMockLearningPatterns(for: user)
        }

        let userVector = try await fetchUserVector(userId: user.id.uuidString)
        let metadata = userVector.metadata

        return LearningPatterns(
            learningVelocity: extractLearningVelocity(from: metadata),
            retentionRate: extractRetentionRate(from: metadata),
            consistencyScore: Double(user.currentStreak) / 30.0,
            adaptabilityScore: 0.6,
            motivationLevel: 0.8,
            challengePreference: 0.5,
            socialLearningPreference: 0.3,
            gamificationResponse: 0.7,
            preferredTimeOfDay: extractPreferredTime(from: metadata),
            strongCategories: extractStrongCategories(from: metadata),
            weakCategories: extractWeakCategories(from: metadata),
            preferredExerciseTypes: extractPreferredExerciseTypes(from: metadata)
        )
    }

    func predictUserEngagement(for lesson: Lesson, user: User) async throws -> Double {
        await MainActor.run {
            isProcessing = true
        }

        defer {
            Task { @MainActor in
                isProcessing = false
            }
        }

        let userVector = try await fetchUserVector(userId: user.id.uuidString)
        let lessonEmbedding = try await generateLessonEmbedding(from: lesson)

        // Calculate cosine similarity between user preferences and lesson characteristics
        let similarity = cosineSimilarity(userVector.values, lessonEmbedding)

        // Adjust based on user's historical engagement patterns
        let engagementMultiplier = extractEngagementMultiplier(from: userVector.metadata)

        return min(similarity * engagementMultiplier, 1.0)
    }

    // MARK: - Content Recommendation

    func getPersonalizedRecommendations(
        for user: User,
        count: Int = 5
    ) async throws -> [LessonRecommendation] {
        await MainActor.run {
            isProcessing = true
        }

        defer {
            Task { @MainActor in
                isProcessing = false
            }
        }

        if useMockData {
            try await Task.sleep(nanoseconds: 300_000_000)
            return createMockRecommendations(for: user, count: count)
        }

        // Real implementation would query Pinecone here
        return createMockRecommendations(for: user, count: count)
    }

    // MARK: - Learning Progress Storage

    func storeProgress(_ progress: Progress, for user: User) async throws {
        await MainActor.run {
            isProcessing = true
        }

        defer {
            Task { @MainActor in
                isProcessing = false
            }
        }

        if useMockData {
            try await Task.sleep(nanoseconds: 200_000_000)
            print("Mock: Stored progress for user \(user.id)")
            return
        }

        // Real implementation would store in Pinecone here
        print("Stored progress for user \(user.id)")
    }

    // MARK: - Mock Data Methods

    private func createMockLearningPatterns(for user: User) -> LearningPatterns {
        return LearningPatterns(
            learningVelocity: 0.7,
            retentionRate: 0.78,
            consistencyScore: 0.7,
            adaptabilityScore: 0.6,
            motivationLevel: 0.8,
            challengePreference: 0.5,
            socialLearningPreference: 0.3,
            gamificationResponse: 0.7,
            preferredTimeOfDay: "afternoon",
            strongCategories: [.vocabulary, .grammar],
            weakCategories: [.pronunciation, .listening],
            preferredExerciseTypes: [.multipleChoice, .fillInBlank]
        )
    }

    private func createMockRecommendations(for user: User, count: Int) -> [LessonRecommendation] {
        let mockRecommendations = [
            LessonRecommendation(
                lessonID: UUID(),
                title: "French Café Conversations",
                category: .conversation,
                difficulty: .intermediate,
                relevanceScore: 0.95,
                reason: "Based on your interest in cultural contexts and strong vocabulary",
                estimatedDuration: 15
            ),
            LessonRecommendation(
                lessonID: UUID(),
                title: "Spanish Pronunciation Basics",
                category: .pronunciation,
                difficulty: .beginner,
                relevanceScore: 0.88,
                reason: "To improve your pronunciation skills",
                estimatedDuration: 10
            ),
            LessonRecommendation(
                lessonID: UUID(),
                title: "Japanese Grammar Patterns",
                category: .grammar,
                difficulty: .advanced,
                relevanceScore: 0.82,
                reason: "Building on your strong grammar foundation",
                estimatedDuration: 25
            ),
            LessonRecommendation(
                lessonID: UUID(),
                title: "Tamil Cultural Expressions",
                category: .culture,
                difficulty: .intermediate,
                relevanceScore: 0.79,
                reason: "Perfect for expanding cultural knowledge",
                estimatedDuration: 20
            ),
            LessonRecommendation(
                lessonID: UUID(),
                title: "English Listening Comprehension",
                category: .listening,
                difficulty: .intermediate,
                relevanceScore: 0.75,
                reason: "To strengthen your listening skills",
                estimatedDuration: 18
            )
        ]

        return Array(mockRecommendations.prefix(count))
    }

    // MARK: - Helper methods that need implementation

    private func getUserLearningData(for user: User, including progress: Progress) async throws -> UserLearningData {
        // Mock implementation for now
        return UserLearningData(
            preferredLanguages: user.preferredLanguages,
            categoryPerformance: [:],
            averageSessionDuration: 15.0,
            learningVelocity: 0.7,
            retentionRate: 0.8,
            preferredTimeOfDay: "afternoon",
            strongestCategory: "vocabulary",
            weakestCategory: "pronunciation"
        )
    }

    private func analyzeSuccessfulLessons(for learners: [SimilarLearner], excluding lessonIds: [UUID], limit: Int) async throws -> [LessonRecommendation] {
        // Mock implementation
        return createMockRecommendations(for: User(), count: limit)
    }

    private func getCompletedLessonIds(for user: User) async throws -> [UUID] {
        // Mock implementation
        return []
    }

    private func extractPerformanceMetrics(from metadata: [String: Any]) -> [String: Double] {
        // Mock implementation
        return [:]
    }

    private func calculateOptimalDifficulty(category: LessonCategory, performance: [String: Double], userLevel: String) -> Difficulty {
        // Mock implementation
        return .intermediate
    }

    private func extractLearningVelocity(from metadata: [String: Any]) -> Double {
        return metadata["learningVelocity"] as? Double ?? 0.7
    }

    private func extractRetentionRate(from metadata: [String: Any]) -> Double {
        return metadata["retentionRate"] as? Double ?? 0.8
    }

    private func extractPreferredTime(from metadata: [String: Any]) -> String {
        return metadata["preferredTimeOfDay"] as? String ?? "afternoon"
    }

    private func extractStrongCategories(from metadata: [String: Any]) -> [LessonCategory] {
        return [.vocabulary, .grammar]
    }

    private func extractWeakCategories(from metadata: [String: Any]) -> [LessonCategory] {
        return [.pronunciation, .listening]
    }

    private func extractPreferredExerciseTypes(from metadata: [String: Any]) -> [ExerciseType] {
        return [.multipleChoice, .fillInBlank]
    }

    private func extractEngagementMultiplier(from metadata: [String: Any]) -> Double {
        return metadata["engagementMultiplier"] as? Double ?? 1.0
    }

    // Add missing helper methods for vector operations
    private func createCategoryVector(_ category: LessonCategory, difficulty: Difficulty) -> [Float] {
        return [Float](repeating: 0.5, count: 15)
    }

    private func createLessonCulturalVector(from context: CulturalContext?) -> [Float] {
        guard let context = context else {
            return [Float](repeating: 0.0, count: 10)
        }

        // Create vector based on actual CulturalContext properties
        return [
            Float(context.scenario.rawValue.hash % 10) / 10.0, // Scenario type
            context.setting.isEmpty ? 0.0 : 1.0,
            Float(context.participants.count) / 10.0, // Normalize participant count
            Float(context.socialNorms.count) / 10.0, // Normalize social norms count
            Float(context.etiquette.count) / 10.0, // Normalize etiquette count
            Float(context.commonPhrases.count) / 20.0, // Normalize phrases count
            context.backgroundInfo.isEmpty ? 0.0 : 1.0,
            Float(context.tips.count) / 10.0, // Normalize tips count
            context.historicalContext?.isEmpty == false ? 1.0 : 0.0,
            context.modernUsage?.isEmpty == false ? 1.0 : 0.0
        ]
    }

    private func createLessonExerciseVector(from exercises: [Exercise]) -> [Float] {
        return [Float](repeating: 0.5, count: 15)
    }

    private func createComplexityVector(duration: Int, exerciseCount: Int, totalPoints: Int) -> [Float] {
        return [
            Float(duration) / 60.0,
            Float(exerciseCount) / 20.0,
            Float(totalPoints) / 1000.0
        ] + Array(repeating: Float(0.5), count: 9)
    }
}

// MARK: - Private Methods

private extension PineconeService {
    func generateUserEmbedding(from learningData: UserLearningData) async throws -> [Float] {
        // Create a vector representation of user's learning profile
        var embedding: [Float] = []

        // Language preferences (5 dimensions)
        let languageVector = createLanguageVector(from: learningData.preferredLanguages)
        embedding.append(contentsOf: languageVector)

        // Skill levels per category (12 dimensions) - using string keys now
        let categoryPerformance = learningData.categoryPerformance.mapValues { $0 }
        let skillVector = createSkillVectorFromStrings(from: categoryPerformance)
        embedding.append(contentsOf: skillVector)

        // Learning patterns (8 dimensions) - simplified since no patterns struct
        let patternVector = createPatternVectorSimplified(
            velocity: learningData.learningVelocity,
            retention: learningData.retentionRate
        )
        embedding.append(contentsOf: patternVector)

        // Cultural and temporal preferences (simplified to 15 dimensions)
        let preferencesVector = createPreferencesVectorSimplified()
        embedding.append(contentsOf: preferencesVector)

        // Exercise type preferences (15 dimensions) - simplified
        let exerciseVector = createExerciseVectorSimplified()
        embedding.append(contentsOf: exerciseVector)

        // Normalize the embedding
        return normalizeVector(embedding)
    }

    func generateLessonEmbedding(from lesson: Lesson) async throws -> [Float] {
        var embedding: [Float] = []

        // Language (5 dimensions)
        let languageVector = createLanguageVector(from: [lesson.language])
        embedding.append(contentsOf: languageVector)

        // Category and difficulty (15 dimensions)
        let categoryVector = createCategoryVector(lesson.category, difficulty: lesson.difficulty)
        embedding.append(contentsOf: categoryVector)

        // Cultural content (10 dimensions)
        let culturalVector = createLessonCulturalVector(from: lesson.culturalContext)
        embedding.append(contentsOf: culturalVector)

        // Exercise types (15 dimensions)
        let exerciseTypesVector = createLessonExerciseVector(from: lesson.exercises)
        embedding.append(contentsOf: exerciseTypesVector)

        // Duration and complexity (12 dimensions)
        let complexityVector = createComplexityVector(
            duration: lesson.estimatedDuration,
            exerciseCount: lesson.exercises.count,
            totalPoints: lesson.totalPoints
        )
        embedding.append(contentsOf: complexityVector)

        return normalizeVector(embedding)
    }

    func createUserMetadata(for user: User, learningData: UserLearningData) -> [String: Any] {
        return [
            "userId": user.id.uuidString,
            "currentStreak": user.currentStreak,
            "totalLessonsCompleted": user.totalLessonsCompleted,
            "totalPointsEarned": user.totalPointsEarned,
            "joinDate": user.joinDate.timeIntervalSince1970,
            "preferredLanguages": user.preferredLanguages,
            "averageSessionDuration": learningData.averageSessionDuration,
            "preferredTimeOfDay": learningData.preferredTimeOfDay,
            "strongestCategory": learningData.strongestCategory,
            "weakestCategory": learningData.weakestCategory,
            "learningVelocity": learningData.learningVelocity,
            "retentionRate": learningData.retentionRate,
            "lastUpdated": Date().timeIntervalSince1970
        ]
    }

    func upsertVector(_ vector: PineconeVector) async throws {
        guard let url = URL(string: "\(baseURL)/vectors/upsert") else {
            throw PineconeError.invalidURL
        }

        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue(apiKey, forHTTPHeaderField: "Api-Key")

        let requestBody = PineconeUpsertRequest(vectors: [vector])
        request.httpBody = try JSONEncoder().encode(requestBody)

        let (_, response) = try await URLSession.shared.data(for: request)

        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw PineconeError.upsertFailed
        }
    }

    func queryVectors(vector: [Float], topK: Int, includeMetadata: Bool = true) async throws -> [PineconeMatch] {
        guard let url = URL(string: "\(baseURL)/query") else {
            throw PineconeError.invalidURL
        }

        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue(apiKey, forHTTPHeaderField: "Api-Key")

        let queryRequest = PineconeQueryRequest(
            vector: vector,
            topK: topK,
            includeMetadata: includeMetadata
        )

        request.httpBody = try JSONEncoder().encode(queryRequest)

        let (data, response) = try await URLSession.shared.data(for: request)

        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw PineconeError.queryFailed
        }

        let queryResponse = try JSONDecoder().decode(PineconeQueryResponse.self, from: data)
        return queryResponse.matches
    }

    func fetchUserVector(userId: String) async throws -> PineconeVector {
        guard let url = URL(string: "\(baseURL)/vectors/fetch?ids=\(userId)") else {
            throw PineconeError.invalidURL
        }

        var request = URLRequest(url: url)
        request.setValue(apiKey, forHTTPHeaderField: "Api-Key")

        let (data, response) = try await URLSession.shared.data(for: request)

        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw PineconeError.fetchFailed
        }

        let fetchResponse = try JSONDecoder().decode(PineconeFetchResponse.self, from: data)

        guard let vector = fetchResponse.vectors[userId] else {
            throw PineconeError.vectorNotFound
        }

        return vector
    }

    // Vector creation helper methods
    func createLanguageVector(from languages: [Language]) -> [Float] {
        var vector = [Float](repeating: 0.0, count: 50) // Updated to support 50 languages
        for language in languages {
            switch language {
            case .french: vector[0] = 1.0
            case .english: vector[1] = 1.0
            case .spanish: vector[2] = 1.0
            case .japanese: vector[3] = 1.0
            case .tamil: vector[4] = 1.0
            case .korean: vector[5] = 1.0
            case .italian: vector[6] = 1.0
            case .german: vector[7] = 1.0
            case .hindi: vector[8] = 1.0
            case .chinese: vector[9] = 1.0
            case .portuguese: vector[10] = 1.0
            case .telugu: vector[11] = 1.0
            case .vietnamese: vector[12] = 1.0
            case .indonesian: vector[13] = 1.0
            case .arabic: vector[14] = 1.0
            // Previous 10 languages
            case .kannada: vector[15] = 1.0
            case .malayalam: vector[16] = 1.0
            case .bengali: vector[17] = 1.0
            case .marathi: vector[18] = 1.0
            case .punjabi: vector[19] = 1.0
            case .dutch: vector[20] = 1.0
            case .swedish: vector[21] = 1.0
            case .thai: vector[22] = 1.0
            case .russian: vector[23] = 1.0
            case .norwegian: vector[24] = 1.0
            // Additional 25 languages
            case .gujarati: vector[25] = 1.0
            case .odia: vector[26] = 1.0
            case .assamese: vector[27] = 1.0
            case .konkani: vector[28] = 1.0
            case .sindhi: vector[29] = 1.0
            case .bhojpuri: vector[30] = 1.0
            case .maithili: vector[31] = 1.0
            case .swahili: vector[32] = 1.0
            case .hebrew: vector[33] = 1.0
            case .greek: vector[34] = 1.0
            case .turkish: vector[35] = 1.0
            case .farsi: vector[36] = 1.0
            case .tagalog: vector[37] = 1.0
            case .ukrainian: vector[38] = 1.0
            case .danish: vector[39] = 1.0
            case .xhosa: vector[40] = 1.0
            case .zulu: vector[41] = 1.0
            case .amharic: vector[42] = 1.0
            case .quechua: vector[43] = 1.0
            case .maori: vector[44] = 1.0
            case .cherokee: vector[45] = 1.0
            case .navajo: vector[46] = 1.0
            case .hawaiian: vector[47] = 1.0
            case .inuktitut: vector[48] = 1.0
            case .yoruba: vector[49] = 1.0
            default:
                // Handle new languages by assigning them to available vector positions
                // This is a simplified approach - in production, you'd want to expand the vector size
                break
            }
        }
        return vector
    }

    func createSkillVector(from performance: [LessonCategory: Double]) -> [Float] {
        let categories = LessonCategory.allCases
        return categories.map { Float(performance[$0] ?? 0.5) }
    }

    func createPatternVector(from patterns: PineconeService.LearningPatterns) -> [Float] {
        return [
            Float(patterns.learningVelocity),
            Float(patterns.retentionRate),
            Float(patterns.consistencyScore),
            Float(patterns.adaptabilityScore),
            Float(patterns.motivationLevel),
            Float(patterns.challengePreference),
            Float(patterns.socialLearningPreference),
            Float(patterns.gamificationResponse)
        ]
    }

    func createCulturalVector(from preferences: CulturalPreferences) -> [Float] {
        return [
            preferences.formality ? 1.0 : 0.0,
            preferences.traditionalVsModern,
            preferences.businessOriented ? 1.0 : 0.0,
            preferences.casualOriented ? 1.0 : 0.0,
            preferences.regionalVariations ? 1.0 : 0.0,
            preferences.historicalContext ? 1.0 : 0.0,
            preferences.slangAndColloquialisms ? 1.0 : 0.0,
            preferences.professionalContext ? 1.0 : 0.0,
            preferences.socialContext ? 1.0 : 0.0,
            preferences.academicContext ? 1.0 : 0.0
        ]
    }

    func createTemporalVector(from preferences: TimePreferences) -> [Float] {
        return [
            preferences.morningLearner ? 1.0 : 0.0,
            preferences.afternoonLearner ? 1.0 : 0.0,
            preferences.eveningLearner ? 1.0 : 0.0,
            Float(preferences.preferredSessionLength),
            Float(preferences.frequencyScore),
            Float(preferences.consistencyScore),
            Float(preferences.weekendLearner ? 1.0 : 0.0)
        ]
    }

    func createExerciseVector(from preferences: [ExerciseType: Double]) -> [Float] {
        let exerciseTypes = ExerciseType.allCases
        return exerciseTypes.map { Float(preferences[$0] ?? 0.5) }
    }

    func normalizeVector(_ vector: [Float]) -> [Float] {
        let magnitude = sqrt(vector.reduce(0) { $0 + $1 * $1 })
        guard magnitude > 0 else { return vector }
        return vector.map { $0 / magnitude }
    }

    func cosineSimilarity(_ a: [Float], _ b: [Float]) -> Double {
        guard a.count == b.count else { return 0.0 }

        let dotProduct = zip(a, b).reduce(0) { $0 + ($1.0 * $1.1) }
        let magnitudeA = sqrt(a.reduce(0) { $0 + $1 * $1 })
        let magnitudeB = sqrt(b.reduce(0) { $0 + $1 * $1 })

        guard magnitudeA > 0 && magnitudeB > 0 else { return 0.0 }

        return Double(dotProduct / (magnitudeA * magnitudeB))
    }

    func createSkillVectorFromStrings(from performance: [String: Double]) -> [Float] {
        let categories = LessonCategory.allCases
        return categories.map { Float(performance[$0.rawValue] ?? 0.5) }
    }

    func createPatternVectorSimplified(velocity: Double, retention: Double) -> [Float] {
        return [
            Float(velocity),
            Float(retention),
            0.6, // consistencyScore
            0.6, // adaptabilityScore
            0.8, // motivationLevel
            0.5, // challengePreference
            0.3, // socialLearningPreference
            0.7  // gamificationResponse
        ]
    }

    func createPreferencesVectorSimplified() -> [Float] {
        // Simplified cultural and temporal preferences
        return [Float](repeating: 0.5, count: 15)
    }

    func createExerciseVectorSimplified() -> [Float] {
        // Simplified exercise preferences
        return [Float](repeating: 0.5, count: 15)
    }
}

// MARK: - PineconeService-specific Data Models

extension PineconeService {
    struct UserLearningData {
        let preferredLanguages: [Language]
        let categoryPerformance: [String: Double]
        let averageSessionDuration: Double
        let learningVelocity: Double
        let retentionRate: Double
        let preferredTimeOfDay: String
        let strongestCategory: String
        let weakestCategory: String
    }

    struct LearningPatterns {
        let learningVelocity: Double
        let retentionRate: Double
        let consistencyScore: Double
        let adaptabilityScore: Double
        let motivationLevel: Double
        let challengePreference: Double
        let socialLearningPreference: Double
        let gamificationResponse: Double
        let preferredTimeOfDay: String
        let strongCategories: [LessonCategory]
        let weakCategories: [LessonCategory]
        let preferredExerciseTypes: [ExerciseType]
    }

    struct CulturalPreferences {
        let formality: Bool
        let traditionalVsModern: Float
        let businessOriented: Bool
        let casualOriented: Bool
        let regionalVariations: Bool
        let historicalContext: Bool
        let slangAndColloquialisms: Bool
        let professionalContext: Bool
        let socialContext: Bool
        let academicContext: Bool
    }

    struct TimePreferences {
        let morningLearner: Bool
        let afternoonLearner: Bool
        let eveningLearner: Bool
        let preferredSessionLength: Double
        let frequencyScore: Double
        let consistencyScore: Double
        let weekendLearner: Bool
    }

    struct SimilarLearner {
        let id: String
        let similarity: Double
        let metadata: [String: Any]
    }

    struct LessonRecommendation {
        let lessonID: UUID
        let title: String
        let category: LessonCategory
        let difficulty: Difficulty
        let relevanceScore: Double
        let reason: String
        let estimatedDuration: Double
    }
}

// MARK: - Pinecone API Models

struct PineconeVector: Codable {
    let id: String
    let values: [Float]
    let metadata: [String: Any]

    enum CodingKeys: String, CodingKey {
        case id, values, metadata
    }

    init(id: String, values: [Float], metadata: [String: Any]) {
        self.id = id
        self.values = values
        self.metadata = metadata
    }

    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(id, forKey: .id)
        try container.encode(values, forKey: .values)

        // Convert metadata to JSON-compatible format
        let jsonData = try JSONSerialization.data(withJSONObject: metadata)
        let jsonObject = try JSONSerialization.jsonObject(with: jsonData)
        try container.encode(SupabaseAnyCodable(jsonObject), forKey: .metadata)
    }

    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        id = try container.decode(String.self, forKey: .id)
        values = try container.decode([Float].self, forKey: .values)

        let metadataValue = try container.decode(SupabaseAnyCodable.self, forKey: .metadata)
        metadata = metadataValue.value as? [String: Any] ?? [:]
    }
}



struct PineconeUpsertRequest: Codable {
    let vectors: [PineconeVector]
}

struct PineconeQueryRequest: Codable {
    let vector: [Float]
    let topK: Int
    let includeMetadata: Bool
}

struct PineconeQueryResponse: Codable {
    let matches: [PineconeMatch]
}

struct PineconeMatch: Codable {
    let id: String
    let score: Double
    let metadata: [String: Any]

    enum CodingKeys: String, CodingKey {
        case id, score, metadata
    }

    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        id = try container.decode(String.self, forKey: .id)
        score = try container.decode(Double.self, forKey: .score)

        let metadataValue = try container.decode(SupabaseAnyCodable.self, forKey: .metadata)
        metadata = metadataValue.value as? [String: Any] ?? [:]
    }

    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(id, forKey: .id)
        try container.encode(score, forKey: .score)

        let jsonData = try JSONSerialization.data(withJSONObject: metadata)
        let jsonObject = try JSONSerialization.jsonObject(with: jsonData)
        try container.encode(SupabaseAnyCodable(jsonObject), forKey: .metadata)
    }
}

struct PineconeFetchResponse: Codable {
    let vectors: [String: PineconeVector]
}

enum PineconeError: Error, LocalizedError {
    case invalidURL
    case upsertFailed
    case queryFailed
    case fetchFailed
    case vectorNotFound
    case invalidResponse

    var errorDescription: String? {
        switch self {
        case .invalidURL: return "Invalid Pinecone URL"
        case .upsertFailed: return "Failed to upsert vector"
        case .queryFailed: return "Failed to query vectors"
        case .fetchFailed: return "Failed to fetch vector"
        case .vectorNotFound: return "Vector not found"
        case .invalidResponse: return "Invalid response from Pinecone"
        }
    }
}