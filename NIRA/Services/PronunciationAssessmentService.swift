import Foundation
import SwiftUI
import Combine
import AVFoundation
import Speech

// MARK: - Pronunciation Assessment Service with AI-Powered Analysis

@MainActor
class PronunciationAssessmentService: ObservableObject {
    static let shared = PronunciationAssessmentService()

    // MARK: - Published Properties
    @Published var isRecording = false
    @Published var isAnalyzing = false
    @Published var currentAssessment: PronunciationAssessment?
    @Published var assessmentHistory: [PronunciationAssessment] = []
    @Published var pronunciationProgress: [Language: PronunciationProgress] = [:]
    @Published var lastError: Error?
    @Published var recordingLevel: Float = 0.0

    // MARK: - Private Properties
    private var audioEngine: AVAudioEngine?
    private var audioRecorder: AVAudioRecorder?
    private var speechRecognizer: SFSpeechRecognizer?
    private var recognitionRequest: SFSpeechAudioBufferRecognitionRequest?
    private var recognitionTask: SFSpeechRecognitionTask?
    private var audioFile: AVAudioFile?
    private var recordingURL: URL?
    private var levelTimer: Timer?

    // Service Dependencies
    private let geminiService = GeminiService.shared
    private let analyticsService = LearningAnalyticsService.shared
    private let userPreferencesService = UserPreferencesService.shared

    private var cancellables = Set<AnyCancellable>()

    // Audio Configuration
    private let sampleRate: Double = 44100.0
    private let channels: UInt32 = 1
    private let bitDepth: UInt32 = 16

    private init() {
        setupAudioSession()
        loadAssessmentHistory()
    }

    // MARK: - Public Methods

    func startPronunciationAssessment(
        targetPhrase: String,
        language: Language,
        difficulty: PronunciationDifficulty = .intermediate
    ) async throws {

        guard !isRecording else {
            throw PronunciationError.alreadyRecording
        }

        // Request permissions
        try await requestPermissions()

        // Setup speech recognizer for the target language
        setupSpeechRecognizer(for: language)

        // Start recording
        try await startRecording()

        await MainActor.run {
            self.isRecording = true
            self.currentAssessment = PronunciationAssessment(
                id: UUID(),
                targetPhrase: targetPhrase,
                language: language,
                difficulty: difficulty,
                startTime: Date(),
                userAudio: nil,
                transcription: nil,
                overallScore: nil,
                detailedScores: nil,
                feedback: nil,
                improvements: [],
                createdAt: Date()
            )
        }

        // Start level monitoring
        startLevelMonitoring()
    }

    func stopPronunciationAssessment() async throws -> PronunciationAssessment? {
        guard isRecording, let assessment = currentAssessment else {
            throw PronunciationError.notRecording
        }

        // Stop recording
        await stopRecording()

        await MainActor.run {
            self.isRecording = false
            self.isAnalyzing = true
        }

        // Analyze the recorded audio
        let analyzedAssessment = try await analyzeRecording(assessment)

        // Save to history
        await MainActor.run {
            self.assessmentHistory.append(analyzedAssessment)
            self.currentAssessment = analyzedAssessment
            self.isAnalyzing = false
        }

        // Update progress tracking
        updatePronunciationProgress(for: analyzedAssessment)

        // Track analytics
        trackAssessmentAnalytics(analyzedAssessment)

        return analyzedAssessment
    }

    func getPersonalizedExercises(for language: Language, count: Int = 5) -> [PronunciationExercise] {
        let progress = pronunciationProgress[language] ?? PronunciationProgress(
            language: language,
            overallScore: 0.0,
            phonemeScores: [:],
            weakAreas: [],
            strongAreas: [],
            totalAssessments: 0,
            lastAssessmentDate: nil
        )

        return generatePersonalizedExercises(based: progress, count: count)
    }

    func getPronunciationTips(for language: Language, phoneme: String? = nil) -> [PronunciationTip] {
        return generatePronunciationTips(language: language, specificPhoneme: phoneme)
    }

    func compareWithNativeSpeaker(
        userAudio: Data,
        nativeAudio: Data,
        targetPhrase: String,
        language: Language
    ) async throws -> ComparisonResult {

        isAnalyzing = true
        defer { isAnalyzing = false }

        // Analyze both audio samples
        let userAnalysis = try await analyzeAudioData(userAudio, targetPhrase: targetPhrase, language: language)
        let nativeAnalysis = try await analyzeAudioData(nativeAudio, targetPhrase: targetPhrase, language: language)

        // Compare the analyses
        let comparison = ComparisonResult(
            userScore: userAnalysis.overallScore ?? 0.0,
            nativeScore: 100.0, // Native speaker baseline
            similarities: calculateSimilarities(user: userAnalysis, native: nativeAnalysis),
            differences: calculateDifferences(user: userAnalysis, native: nativeAnalysis),
            recommendations: generateComparisonRecommendations(user: userAnalysis, native: nativeAnalysis)
        )

        return comparison
    }

    // MARK: - Recording Management

    private func startRecording() async throws {
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
        recordingURL = documentsPath.appendingPathComponent("pronunciation_\(UUID().uuidString).wav")

        guard let url = recordingURL else {
            throw PronunciationError.recordingSetupFailed
        }

        let settings: [String: Any] = [
            AVFormatIDKey: Int(kAudioFormatLinearPCM),
            AVSampleRateKey: sampleRate,
            AVNumberOfChannelsKey: channels,
            AVLinearPCMBitDepthKey: bitDepth,
            AVLinearPCMIsBigEndianKey: false,
            AVLinearPCMIsFloatKey: false
        ]

        do {
            audioRecorder = try AVAudioRecorder(url: url, settings: settings)
            audioRecorder?.isMeteringEnabled = true
            audioRecorder?.record()

            // Also setup real-time speech recognition
            try setupRealTimeRecognition()

        } catch {
            throw PronunciationError.recordingSetupFailed
        }
    }

    private func stopRecording() async {
        audioRecorder?.stop()
        audioRecorder = nil

        // Stop speech recognition
        recognitionTask?.cancel()
        recognitionRequest?.endAudio()
        audioEngine?.stop()
        audioEngine?.inputNode.removeTap(onBus: 0)

        // Stop level monitoring
        levelTimer?.invalidate()
        levelTimer = nil

        await MainActor.run {
            self.recordingLevel = 0.0
        }
    }

    private func setupRealTimeRecognition() throws {
        guard let speechRecognizer = speechRecognizer,
              speechRecognizer.isAvailable else {
            throw PronunciationError.speechRecognitionUnavailable
        }

        audioEngine = AVAudioEngine()
        recognitionRequest = SFSpeechAudioBufferRecognitionRequest()

        guard let audioEngine = audioEngine,
              let recognitionRequest = recognitionRequest else {
            throw PronunciationError.speechRecognitionSetupFailed
        }

        recognitionRequest.shouldReportPartialResults = true

        let inputNode = audioEngine.inputNode
        let recordingFormat = inputNode.outputFormat(forBus: 0)

        inputNode.installTap(onBus: 0, bufferSize: 1024, format: recordingFormat) { buffer, _ in
            recognitionRequest.append(buffer)
        }

        recognitionTask = speechRecognizer.recognitionTask(with: recognitionRequest) { [weak self] result, error in
            if let result = result {
                Task { @MainActor in
                    // Update real-time transcription
                    self?.updateRealTimeTranscription(result.bestTranscription.formattedString)
                }
            }
        }

        audioEngine.prepare()
        try audioEngine.start()
    }

    private func updateRealTimeTranscription(_ transcription: String) {
        guard var assessment = currentAssessment else { return }
        assessment.transcription = transcription
        currentAssessment = assessment
    }

    // MARK: - Audio Analysis

    private func analyzeRecording(_ assessment: PronunciationAssessment) async throws -> PronunciationAssessment {
        guard let recordingURL = recordingURL else {
            throw PronunciationError.noRecordingFound
        }

        // Load audio data
        let audioData = try Data(contentsOf: recordingURL)

        // Perform comprehensive analysis
        let analysis = try await analyzeAudioData(
            audioData,
            targetPhrase: assessment.targetPhrase,
            language: assessment.language
        )

        // Create updated assessment
        var updatedAssessment = assessment
        updatedAssessment.userAudio = audioData
        updatedAssessment.transcription = analysis.transcription
        updatedAssessment.overallScore = analysis.overallScore
        updatedAssessment.detailedScores = analysis.detailedScores
        updatedAssessment.feedback = analysis.feedback
        updatedAssessment.improvements = analysis.improvements
        updatedAssessment.endTime = Date()

        return updatedAssessment
    }

    private func analyzeAudioData(
        _ audioData: Data,
        targetPhrase: String,
        language: Language
    ) async throws -> PronunciationAnalysis {

        // Use Gemini for pronunciation analysis
        let prompt = buildAnalysisPrompt(
            targetPhrase: targetPhrase,
            language: language,
            audioData: audioData
        )

        let response = try await GeminiService.shared.makeGeminiRequest(prompt: prompt)

        // Parse the AI response into structured analysis
        return try parseAnalysisResponse(response, targetPhrase: targetPhrase, language: language)
    }

    private func buildAnalysisPrompt(targetPhrase: String, language: Language, audioData: Data) -> String {
        return """
        Analyze this pronunciation recording for language learning assessment.

        Target Language: \(language.displayName)
        Target Phrase: "\(targetPhrase)"

        Please provide a comprehensive pronunciation analysis including:

        1. Overall Pronunciation Score (0-100)
        2. Phoneme-level accuracy scores
        3. Rhythm and stress pattern analysis
        4. Intonation assessment
        5. Clarity and fluency evaluation
        6. Specific areas for improvement
        7. Positive feedback on strengths
        8. Personalized practice recommendations

        Format your response as JSON with the following structure:
        {
            "overallScore": number,
            "transcription": "what was actually said",
            "phonemeScores": {
                "phoneme": score
            },
            "rhythmScore": number,
            "stressScore": number,
            "intonationScore": number,
            "clarityScore": number,
            "fluencyScore": number,
            "strengths": ["strength1", "strength2"],
            "improvements": ["improvement1", "improvement2"],
            "feedback": "detailed feedback message",
            "recommendations": ["recommendation1", "recommendation2"]
        }
        """
    }

    private func parseAnalysisResponse(
        _ response: String,
        targetPhrase: String,
        language: Language
    ) throws -> PronunciationAnalysis {

        // Extract JSON from response
        guard let jsonData = extractJSON(from: response)?.data(using: .utf8),
              let analysisDict = try? JSONSerialization.jsonObject(with: jsonData) as? [String: Any] else {
            throw PronunciationError.analysisParsingFailed
        }

        let overallScore = analysisDict["overallScore"] as? Double ?? 0.0
        let transcription = analysisDict["transcription"] as? String ?? ""
        let phonemeScores = analysisDict["phonemeScores"] as? [String: Double] ?? [:]
        let strengths = analysisDict["strengths"] as? [String] ?? []
        let improvements = analysisDict["improvements"] as? [String] ?? []
        let feedback = analysisDict["feedback"] as? String ?? ""
        let recommendations = analysisDict["recommendations"] as? [String] ?? []

        let detailedScores = DetailedPronunciationScores(
            phonemeAccuracy: phonemeScores,
            rhythmScore: analysisDict["rhythmScore"] as? Double ?? 0.0,
            stressScore: analysisDict["stressScore"] as? Double ?? 0.0,
            intonationScore: analysisDict["intonationScore"] as? Double ?? 0.0,
            clarityScore: analysisDict["clarityScore"] as? Double ?? 0.0,
            fluencyScore: analysisDict["fluencyScore"] as? Double ?? 0.0
        )

        return PronunciationAnalysis(
            transcription: transcription,
            overallScore: overallScore,
            detailedScores: detailedScores,
            feedback: feedback,
            improvements: improvements.map { PronunciationImprovement(area: $0, suggestion: "", priority: .medium) },
            strengths: strengths,
            recommendations: recommendations
        )
    }

    private func extractJSON(from text: String) -> String? {
        // Extract JSON from AI response
        let pattern = #"\{[\s\S]*\}"#
        let regex = try? NSRegularExpression(pattern: pattern)
        let range = NSRange(text.startIndex..., in: text)

        if let match = regex?.firstMatch(in: text, range: range) {
            return String(text[Range(match.range, in: text)!])
        }

        return nil
    }

    // MARK: - Progress Tracking

    private func updatePronunciationProgress(for assessment: PronunciationAssessment) {
        let language = assessment.language
        var progress = pronunciationProgress[language] ?? PronunciationProgress(
            language: language,
            overallScore: 0.0,
            phonemeScores: [:],
            weakAreas: [],
            strongAreas: [],
            totalAssessments: 0,
            lastAssessmentDate: nil
        )

        // Update overall score (weighted average)
        let newScore = assessment.overallScore ?? 0.0
        progress.overallScore = (progress.overallScore * Double(progress.totalAssessments) + newScore) / Double(progress.totalAssessments + 1)

        // Update phoneme scores
        if let detailedScores = assessment.detailedScores {
            for (phoneme, score) in detailedScores.phonemeAccuracy {
                let currentScore = progress.phonemeScores[phoneme] ?? 0.0
                progress.phonemeScores[phoneme] = (currentScore + score) / 2.0
            }
        }

        // Update weak and strong areas
        progress.weakAreas = identifyWeakAreas(from: progress.phonemeScores)
        progress.strongAreas = identifyStrongAreas(from: progress.phonemeScores)

        progress.totalAssessments += 1
        progress.lastAssessmentDate = Date()

        pronunciationProgress[language] = progress
    }

    private func identifyWeakAreas(from phonemeScores: [String: Double]) -> [String] {
        return phonemeScores.filter { $0.value < 70.0 }.map { $0.key }
    }

    private func identifyStrongAreas(from phonemeScores: [String: Double]) -> [String] {
        return phonemeScores.filter { $0.value >= 85.0 }.map { $0.key }
    }

    // MARK: - Exercise Generation

    private func generatePersonalizedExercises(
        based progress: PronunciationProgress,
        count: Int
    ) -> [PronunciationExercise] {

        var exercises: [PronunciationExercise] = []

        // Focus on weak areas
        for weakArea in progress.weakAreas.prefix(count) {
            let exercise = PronunciationExercise(
                id: UUID(),
                title: "Improve \(weakArea) pronunciation",
                description: "Practice words and phrases containing the \(weakArea) sound",
                language: progress.language,
                targetPhoneme: weakArea,
                difficulty: determineDifficulty(for: weakArea, in: progress),
                phrases: generatePhrasesFor(phoneme: weakArea, language: progress.language),
                tips: generateTipsFor(phoneme: weakArea, language: progress.language)
            )
            exercises.append(exercise)
        }

        // Add general exercises if needed
        while exercises.count < count {
            exercises.append(generateGeneralExercise(for: progress.language))
        }

        return exercises
    }

    private func generatePhrasesFor(phoneme: String, language: Language) -> [String] {
        // This would contain a comprehensive database of phrases for each phoneme
        switch language {
        case .english:
            return ["Practice phrase 1", "Practice phrase 2", "Practice phrase 3"]
        case .french:
            return ["Phrase de pratique 1", "Phrase de pratique 2", "Phrase de pratique 3"]
        case .spanish:
            return ["Frase de práctica 1", "Frase de práctica 2", "Frase de práctica 3"]
        case .japanese:
            return ["練習フレーズ1", "練習フレーズ2", "練習フレーズ3"]
        case .tamil:
            return ["பயிற்சி வாக்கியம் 1", "பயிற்சி வாக்கியம் 2", "பயிற்சி வாக்கியம் 3"]
        case .korean:
            return ["연습 문장 1", "연습 문장 2", "연습 문장 3"]
        case .italian:
            return ["Frase di pratica 1", "Frase di pratica 2", "Frase di pratica 3"]
        case .german:
            return ["Übungssatz 1", "Übungssatz 2", "Übungssatz 3"]
        case .hindi:
            return ["अभ्यास वाक्य 1", "अभ्यास वाक्य 2", "अभ्यास वाक्य 3"]
        case .chinese:
            return ["练习句子 1", "练习句子 2", "练习句子 3"]
        case .portuguese:
            return ["Frase de prática 1", "Frase de prática 2", "Frase de prática 3"]
        case .telugu:
            return ["అభ్యాస వాక్యం 1", "అభ్యాస వాక్యం 2", "అభ్యాస వాక్యం 3"]
        case .vietnamese:
            return ["Câu luyện tập 1", "Câu luyện tập 2", "Câu luyện tập 3"]
        case .indonesian:
            return ["Kalimat latihan 1", "Kalimat latihan 2", "Kalimat latihan 3"]
        case .arabic:
            return ["جملة التدريب 1", "جملة التدريب 2", "جملة التدريب 3"]
        // New 10 languages
        case .kannada:
            return ["ಅಭ್ಯಾಸ ವಾಕ್ಯ 1", "ಅಭ್ಯಾಸ ವಾಕ್ಯ 2", "ಅಭ್ಯಾಸ ವಾಕ್ಯ 3"]
        case .malayalam:
            return ["പരിശീലന വാക്യം 1", "പരിശീലന വാക്യം 2", "പരിശീലന വാക്യം 3"]
        case .bengali:
            return ["অনুশীলন বাক্য 1", "অনুশীলন বাক্য 2", "অনুশীলন বাক্য 3"]
        case .marathi:
            return ["सराव वाक्य 1", "सराव वाक्य 2", "सराव वाक्य 3"]
        case .punjabi:
            return ["ਅਭਿਆਸ ਵਾਕ 1", "ਅਭਿਆਸ ਵਾਕ 2", "ਅਭਿਆਸ ਵਾਕ 3"]
        case .dutch:
            return ["Oefenzin 1", "Oefenzin 2", "Oefenzin 3"]
        case .swedish:
            return ["Övningsfras 1", "Övningsfras 2", "Övningsfras 3"]
        case .thai:
            return ["ประโยคฝึก 1", "ประโยคฝึก 2", "ประโยคฝึก 3"]
        case .russian:
            return ["Практическое предложение 1", "Практическое предложение 2", "Практическое предложение 3"]
        case .norwegian:
            return ["Øvelsessetning 1", "Øvelsessetning 2", "Øvelsessetning 3"]
        // Additional 25 languages
        case .gujarati:
            return ["અભ્યાસ વાક્ય 1", "અભ્યાસ વાક્ય 2", "અભ્યાસ વાક્ય 3"]
        case .odia:
            return ["ଅଭ୍ୟାସ ବାକ୍ୟ 1", "ଅଭ୍ୟାସ ବାକ୍ୟ 2", "ଅଭ୍ୟାସ ବାକ୍ୟ 3"]
        case .assamese:
            return ["অনুশীলন বাক্য 1", "অনুশীলন বাক্য 2", "অনুশীলন বাক্য 3"]
        case .konkani:
            return ["अभ्यास वाक्य 1", "अभ्यास वाक्य 2", "अभ्यास वाक्य 3"]
        case .sindhi:
            return ["مشق جو جملو 1", "مشق جو جملو 2", "مشق جو جملو 3"]
        case .bhojpuri:
            return ["अभ्यास वाक्य 1", "अभ्यास वाक्य 2", "अभ्यास वाक्य 3"]
        case .maithili:
            return ["अभ्यास वाक्य 1", "अभ्यास वाक्य 2", "अभ्यास वाक्य 3"]
        case .swahili:
            return ["Sentensi ya mazoezi 1", "Sentensi ya mazoezi 2", "Sentensi ya mazoezi 3"]
        case .hebrew:
            return ["משפט תרגול 1", "משפט תרגול 2", "משפט תרגול 3"]
        case .greek:
            return ["Πρόταση εξάσκησης 1", "Πρόταση εξάσκησης 2", "Πρόταση εξάσκησης 3"]
        case .turkish:
            return ["Alıştırma cümlesi 1", "Alıştırma cümlesi 2", "Alıştırma cümlesi 3"]
        case .farsi:
            return ["جمله تمرینی 1", "جمله تمرینی 2", "جمله تمرینی 3"]
        case .tagalog:
            return ["Pangungusap na pagsasanay 1", "Pangungusap na pagsasanay 2", "Pangungusap na pagsasanay 3"]
        case .ukrainian:
            return ["Тренувальне речення 1", "Тренувальне речення 2", "Тренувальне речення 3"]
        case .danish:
            return ["Øvelsessætning 1", "Øvelsessætning 2", "Øvelsessætning 3"]
        case .xhosa:
            return ["Isivakalisi sokuziqhelanisa 1", "Isivakalisi sokuziqhelanisa 2", "Isivakalisi sokuziqhelanisa 3"]
        case .zulu:
            return ["Umusho wokuzivocavoca 1", "Umusho wokuzivocavoca 2", "Umusho wokuzivocavoca 3"]
        case .amharic:
            return ["የልምምድ ዓረፍተ ነገር 1", "የልምምድ ዓረፍተ ነገር 2", "የልምምድ ዓረፍተ ነገር 3"]
        case .quechua:
            return ["Yachay rimay 1", "Yachay rimay 2", "Yachay rimay 3"]
        case .maori:
            return ["Rerenga kōrero ako 1", "Rerenga kōrero ako 2", "Rerenga kōrero ako 3"]
        case .cherokee:
            return ["ᏗᏕᎶᏆᏍᏗ ᎠᏂᏓᏪᏒ 1", "ᏗᏕᎶᏆᏍᏗ ᎠᏂᏓᏪᏒ 2", "ᏗᏕᎶᏆᏍᏗ ᎠᏂᏓᏪᏒ 3"]
        case .navajo:
            return ["Naaltsoos bee ííłʼíní 1", "Naaltsoos bee ííłʼíní 2", "Naaltsoos bee ííłʼíní 3"]
        case .hawaiian:
            return ["ʻŌlelo hoʻomaʻamaʻa 1", "ʻŌlelo hoʻomaʻamaʻa 2", "ʻŌlelo hoʻomaʻamaʻa 3"]
        case .inuktitut:
            return ["ᐃᓕᓐᓂᐊᖅᑐᖅ ᐅᖃᐅᓯᖅ 1", "ᐃᓕᓐᓂᐊᖅᑐᖅ ᐅᖃᐅᓯᖅ 2", "ᐃᓕᓐᓂᐊᖅᑐᖅ ᐅᖃᐅᓯᖅ 3"]
        case .yoruba:
            return ["Gbólóhùn ìdánwò 1", "Gbólóhùn ìdánwò 2", "Gbólóhùn ìdánwò 3"]
        default:
            // Default practice phrases for new languages
            return ["Practice phrase 1", "Practice phrase 2", "Practice phrase 3"]
        }
    }

    private func generateTipsFor(phoneme: String, language: Language) -> [String] {
        return [
            "Focus on tongue placement",
            "Practice breath control",
            "Listen to native speakers"
        ]
    }

    private func generateGeneralExercise(for language: Language) -> PronunciationExercise {
        return PronunciationExercise(
            id: UUID(),
            title: "General pronunciation practice",
            description: "Practice common phrases in \(language.displayName)",
            language: language,
            targetPhoneme: nil,
            difficulty: .intermediate,
            phrases: ["Hello, how are you?", "Thank you very much", "Have a great day"],
            tips: ["Speak slowly and clearly", "Focus on rhythm", "Practice daily"]
        )
    }

    private func determineDifficulty(for phoneme: String, in progress: PronunciationProgress) -> PronunciationDifficulty {
        let score = progress.phonemeScores[phoneme] ?? 0.0

        if score < 50.0 {
            return .beginner
        } else if score < 75.0 {
            return .intermediate
        } else {
            return .advanced
        }
    }

    // MARK: - Tips and Recommendations

    private func generatePronunciationTips(language: Language, specificPhoneme: String?) -> [PronunciationTip] {
        var tips: [PronunciationTip] = []

        if let phoneme = specificPhoneme {
            tips.append(PronunciationTip(
                id: UUID(),
                title: "Master the \(phoneme) sound",
                description: "Specific techniques for improving \(phoneme) pronunciation",
                language: language,
                targetPhoneme: phoneme,
                technique: "Focus on tongue and lip position",
                examples: generatePhrasesFor(phoneme: phoneme, language: language),
                difficulty: .intermediate
            ))
        }

        // Add general tips
        tips.append(contentsOf: getGeneralTips(for: language))

        return tips
    }

    private func getGeneralTips(for language: Language) -> [PronunciationTip] {
        return [
            PronunciationTip(
                id: UUID(),
                title: "Daily practice routine",
                description: "Establish a consistent pronunciation practice schedule",
                language: language,
                targetPhoneme: nil,
                technique: "Practice 10-15 minutes daily",
                examples: ["Morning warm-up exercises", "Evening review sessions"],
                difficulty: .beginner
            ),
            PronunciationTip(
                id: UUID(),
                title: "Listen and repeat",
                description: "Improve by mimicking native speakers",
                language: language,
                targetPhoneme: nil,
                technique: "Shadow native speaker audio",
                examples: ["Podcasts", "Movies", "Music"],
                difficulty: .intermediate
            )
        ]
    }

    // MARK: - Comparison Analysis

    private func calculateSimilarities(user: PronunciationAnalysis, native: PronunciationAnalysis) -> [String] {
        var similarities: [String] = []

        if let userScores = user.detailedScores,
           let nativeScores = native.detailedScores {

            for (phoneme, userScore) in userScores.phonemeAccuracy {
                if let nativeScore = nativeScores.phonemeAccuracy[phoneme],
                   abs(userScore - nativeScore) < 10.0 {
                    similarities.append("Good \(phoneme) pronunciation")
                }
            }
        }

        return similarities
    }

    private func calculateDifferences(user: PronunciationAnalysis, native: PronunciationAnalysis) -> [String] {
        var differences: [String] = []

        if let userScores = user.detailedScores,
           let nativeScores = native.detailedScores {

            for (phoneme, userScore) in userScores.phonemeAccuracy {
                if let nativeScore = nativeScores.phonemeAccuracy[phoneme],
                   nativeScore - userScore > 20.0 {
                    differences.append("Improve \(phoneme) pronunciation")
                }
            }
        }

        return differences
    }

    private func generateComparisonRecommendations(user: PronunciationAnalysis, native: PronunciationAnalysis) -> [String] {
        return [
            "Focus on rhythm and stress patterns",
            "Practice with native speaker audio",
            "Record yourself regularly to track progress"
        ]
    }

    // MARK: - Helper Methods

    private func setupAudioSession() {
        do {
            let audioSession = AVAudioSession.sharedInstance()
            try audioSession.setCategory(.playAndRecord, mode: .measurement, options: [.duckOthers])
            try audioSession.setActive(true)
        } catch {
            print("❌ Failed to setup audio session: \(error)")
        }
    }

    private func setupSpeechRecognizer(for language: Language) {
        let locale = getLocale(for: language)
        speechRecognizer = SFSpeechRecognizer(locale: locale)
    }

    private func getLocale(for language: Language) -> Locale {
        switch language {
        case .english: return Locale(identifier: "en-US")
        case .french: return Locale(identifier: "fr-FR")
        case .spanish: return Locale(identifier: "es-ES")
        case .japanese: return Locale(identifier: "ja-JP")
        case .tamil: return Locale(identifier: "ta-IN")
        case .korean: return Locale(identifier: "ko-KR")
        case .italian: return Locale(identifier: "it-IT")
        case .german: return Locale(identifier: "de-DE")
        case .hindi: return Locale(identifier: "hi-IN")
        case .chinese: return Locale(identifier: "zh-CN")
        case .portuguese: return Locale(identifier: "pt-BR")
        case .telugu: return Locale(identifier: "te-IN")
        case .vietnamese: return Locale(identifier: "vi-VN")
        case .indonesian: return Locale(identifier: "id-ID")
        case .arabic: return Locale(identifier: "ar-SA")
        // New 10 languages
        case .kannada: return Locale(identifier: "kn-IN")
        case .malayalam: return Locale(identifier: "ml-IN")
        case .bengali: return Locale(identifier: "bn-BD")
        case .marathi: return Locale(identifier: "mr-IN")
        case .punjabi: return Locale(identifier: "pa-IN")
        case .dutch: return Locale(identifier: "nl-NL")
        case .swedish: return Locale(identifier: "sv-SE")
        case .thai: return Locale(identifier: "th-TH")
        case .russian: return Locale(identifier: "ru-RU")
        case .norwegian: return Locale(identifier: "nb-NO")
        // Additional 25 languages
        case .gujarati: return Locale(identifier: "gu-IN")
        case .odia: return Locale(identifier: "or-IN")
        case .assamese: return Locale(identifier: "as-IN")
        case .konkani: return Locale(identifier: "kok-IN")
        case .sindhi: return Locale(identifier: "sd-IN")
        case .bhojpuri: return Locale(identifier: "bho-IN")
        case .maithili: return Locale(identifier: "mai-IN")
        case .swahili: return Locale(identifier: "sw-TZ")
        case .hebrew: return Locale(identifier: "he-IL")
        case .greek: return Locale(identifier: "el-GR")
        case .turkish: return Locale(identifier: "tr-TR")
        case .farsi: return Locale(identifier: "fa-IR")
        case .tagalog: return Locale(identifier: "tl-PH")
        case .ukrainian: return Locale(identifier: "uk-UA")
        case .danish: return Locale(identifier: "da-DK")
        case .xhosa: return Locale(identifier: "xh-ZA")
        case .zulu: return Locale(identifier: "zu-ZA")
        case .amharic: return Locale(identifier: "am-ET")
        case .quechua: return Locale(identifier: "qu-PE")
        case .maori: return Locale(identifier: "mi-NZ")
        case .cherokee: return Locale(identifier: "chr-US")
        case .navajo: return Locale(identifier: "nv-US")
        case .hawaiian: return Locale(identifier: "haw-US")
        case .inuktitut: return Locale(identifier: "iu-CA")
        case .yoruba: return Locale(identifier: "yo-NG")
        // Additional languages to complete the 50-language expansion
        case .urdu: return Locale(identifier: "ur-PK")
        case .polish: return Locale(identifier: "pl-PL")
        case .czech: return Locale(identifier: "cs-CZ")
        case .hungarian: return Locale(identifier: "hu-HU")
        case .romanian: return Locale(identifier: "ro-RO")
        case .bulgarian: return Locale(identifier: "bg-BG")
        case .croatian: return Locale(identifier: "hr-HR")
        case .serbian: return Locale(identifier: "sr-RS")
        case .slovak: return Locale(identifier: "sk-SK")
        case .slovenian: return Locale(identifier: "sl-SI")
        case .estonian: return Locale(identifier: "et-EE")
        case .latvian: return Locale(identifier: "lv-LV")
        case .lithuanian: return Locale(identifier: "lt-LT")
        case .maltese: return Locale(identifier: "mt-MT")
        case .irish: return Locale(identifier: "ga-IE")
        case .welsh: return Locale(identifier: "cy-GB")
        case .scots: return Locale(identifier: "gd-GB")
        case .manx: return Locale(identifier: "gv-IM")
        case .cornish: return Locale(identifier: "kw-GB")
        case .breton: return Locale(identifier: "br-FR")
        case .basque: return Locale(identifier: "eu-ES")
        case .catalan: return Locale(identifier: "ca-ES")
        case .galician: return Locale(identifier: "gl-ES")
        }
    }

    private func requestPermissions() async throws {
        // Request microphone permission
        let micPermission = await withCheckedContinuation { continuation in
            AVAudioApplication.requestRecordPermission { granted in
                continuation.resume(returning: granted)
            }
        }

        guard micPermission else {
            throw PronunciationError.microphonePermissionDenied
        }

        // Request speech recognition permission
        let speechPermission = await withCheckedContinuation { continuation in
            SFSpeechRecognizer.requestAuthorization { status in
                continuation.resume(returning: status == .authorized)
            }
        }

        guard speechPermission else {
            throw PronunciationError.speechRecognitionPermissionDenied
        }
    }

    private func startLevelMonitoring() {
        levelTimer = Timer.scheduledTimer(withTimeInterval: 0.1, repeats: true) { [weak self] _ in
            Task { @MainActor in
                self?.updateRecordingLevel()
            }
        }
    }

    @MainActor
    private func updateRecordingLevel() {
        audioRecorder?.updateMeters()
        let level = audioRecorder?.averagePower(forChannel: 0) ?? -160.0
        let normalizedLevel = max(0.0, (level + 160.0) / 160.0)

        recordingLevel = Float(normalizedLevel)
    }

    private func loadAssessmentHistory() {
        // Load from UserDefaults or Core Data
        // For now, initialize empty
        assessmentHistory = []
        pronunciationProgress = [:]
    }

    private func trackAssessmentAnalytics(_ assessment: PronunciationAssessment) {
        analyticsService.trackInteraction(
            userId: UUID(), // Get current user ID
            interactionType: .exerciseAttempt,
            contentType: .vocabulary,
            contentId: assessment.id.uuidString,
            isCorrect: (assessment.overallScore ?? 0.0) >= 70.0,
            responseTime: Int(assessment.endTime?.timeIntervalSince(assessment.startTime) ?? 0),
            metadata: [
                "language": SupabaseAnyCodable(assessment.language.rawValue),
                "target_phrase": SupabaseAnyCodable(assessment.targetPhrase),
                "overall_score": SupabaseAnyCodable(assessment.overallScore ?? 0.0),
                "difficulty": SupabaseAnyCodable(assessment.difficulty.rawValue)
            ]
        )
    }
}

// MARK: - Supporting Models

struct PronunciationAssessment: Codable, Identifiable {
    let id: UUID
    let targetPhrase: String
    let language: Language
    let difficulty: PronunciationDifficulty
    let startTime: Date
    var endTime: Date?
    var userAudio: Data?
    var transcription: String?
    var overallScore: Double?
    var detailedScores: DetailedPronunciationScores?
    var feedback: String?
    var improvements: [PronunciationImprovement]
    let createdAt: Date
}

struct DetailedPronunciationScores: Codable {
    let phonemeAccuracy: [String: Double]
    let rhythmScore: Double
    let stressScore: Double
    let intonationScore: Double
    let clarityScore: Double
    let fluencyScore: Double
}

struct PronunciationImprovement: Codable, Identifiable {
    let id: UUID
    let area: String
    let suggestion: String
    let priority: ImprovementPriority

    init(area: String, suggestion: String, priority: ImprovementPriority) {
        self.id = UUID()
        self.area = area
        self.suggestion = suggestion
        self.priority = priority
    }

    enum ImprovementPriority: String, Codable {
        case low, medium, high

        var displayName: String {
            switch self {
            case .low: return "Low Priority"
            case .medium: return "Medium Priority"
            case .high: return "High Priority"
            }
        }
    }
}

struct PronunciationProgress: Codable {
    let language: Language
    var overallScore: Double
    var phonemeScores: [String: Double]
    var weakAreas: [String]
    var strongAreas: [String]
    var totalAssessments: Int
    var lastAssessmentDate: Date?
}

struct PronunciationExercise: Codable, Identifiable {
    let id: UUID
    let title: String
    let description: String
    let language: Language
    let targetPhoneme: String?
    let difficulty: PronunciationDifficulty
    let phrases: [String]
    let tips: [String]
}

struct PronunciationTip: Codable, Identifiable {
    let id: UUID
    let title: String
    let description: String
    let language: Language
    let targetPhoneme: String?
    let technique: String
    let examples: [String]
    let difficulty: PronunciationDifficulty
}

struct PronunciationAnalysis {
    let transcription: String
    let overallScore: Double?
    let detailedScores: DetailedPronunciationScores?
    let feedback: String
    let improvements: [PronunciationImprovement]
    let strengths: [String]
    let recommendations: [String]
}

struct ComparisonResult {
    let userScore: Double
    let nativeScore: Double
    let similarities: [String]
    let differences: [String]
    let recommendations: [String]
}

enum PronunciationDifficulty: String, Codable, CaseIterable {
    case beginner = "beginner"
    case intermediate = "intermediate"
    case advanced = "advanced"

    var displayName: String {
        switch self {
        case .beginner: return "Beginner"
        case .intermediate: return "Intermediate"
        case .advanced: return "Advanced"
        }
    }
}

enum PronunciationError: LocalizedError {
    case alreadyRecording
    case notRecording
    case recordingSetupFailed
    case noRecordingFound
    case analysisParsingFailed
    case speechRecognitionUnavailable
    case speechRecognitionSetupFailed
    case microphonePermissionDenied
    case speechRecognitionPermissionDenied

    var errorDescription: String? {
        switch self {
        case .alreadyRecording:
            return "Already recording audio"
        case .notRecording:
            return "Not currently recording"
        case .recordingSetupFailed:
            return "Failed to setup audio recording"
        case .noRecordingFound:
            return "No audio recording found"
        case .analysisParsingFailed:
            return "Failed to parse pronunciation analysis"
        case .speechRecognitionUnavailable:
            return "Speech recognition is not available"
        case .speechRecognitionSetupFailed:
            return "Failed to setup speech recognition"
        case .microphonePermissionDenied:
            return "Microphone permission denied"
        case .speechRecognitionPermissionDenied:
            return "Speech recognition permission denied"
        }
    }
}