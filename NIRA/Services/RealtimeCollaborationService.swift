import Foundation
import SwiftUI
import Combine
import Network

// MARK: - Real-time Collaboration Service for Multiplayer Learning

@MainActor
class RealtimeCollaborationService: NSObject, ObservableObject {
    static let shared = RealtimeCollaborationService()

    // MARK: - Published Properties
    @Published var isConnected = false
    @Published var currentSession: CollaborationSession?
    @Published var activeParticipants: [SessionParticipant] = []
    @Published var sessionActivities: [SessionActivity] = []
    @Published var connectionStatus: ConnectionStatus = .disconnected
    @Published var lastError: Error?
    @Published var isHosting = false
    @Published var sessionInvitations: [SessionInvitation] = []

    // MARK: - Private Properties
    private var webSocketTask: URLSessionWebSocketTask?
    private var sessionId: UUID?
    private var userId: UUID?
    private var heartbeatTimer: Timer?
    private let networkMonitor = NWPathMonitor()
    private let networkQueue = DispatchQueue(label: "NetworkMonitor")

    // Service Dependencies
    private let supabaseClient = NIRASupabaseClient.shared
    private let voiceService = GeminiLiveVoiceService.shared
    private let analyticsService = LearningAnalyticsService.shared
    private let socialService = SocialFeaturesService.shared

    private var cancellables = Set<AnyCancellable>()

    enum ConnectionStatus {
        case disconnected, connecting, connected, error, reconnecting

        var displayName: String {
            switch self {
            case .disconnected: return "Disconnected"
            case .connecting: return "Connecting..."
            case .connected: return "Connected"
            case .error: return "Connection Error"
            case .reconnecting: return "Reconnecting..."
            }
        }
    }

    private override init() {
        super.init()
        setupNetworkMonitoring()
        setupSubscriptions()
    }

    // MARK: - Session Management

    func createSession(
        title: String,
        language: Language,
        maxParticipants: Int = 4,
        sessionType: CollaborationSessionType = .conversationPractice,
        isPrivate: Bool = false
    ) async throws -> CollaborationSession {

        connectionStatus = .connecting

        let session = CollaborationSession(
            id: UUID(),
            title: title,
            hostId: getCurrentUserId(),
            language: language,
            sessionType: sessionType,
            maxParticipants: maxParticipants,
            isPrivate: isPrivate,
            status: .waiting,
            createdAt: Date(),
            participants: [
                SessionParticipant(
                    id: UUID(),
                    userId: getCurrentUserId(),
                    displayName: getCurrentUserName(),
                    role: .host,
                    joinedAt: Date(),
                    isActive: true,
                    currentScore: 0
                )
            ]
        )

        // Save session to Supabase
        try await saveSessionToDatabase(session)

        // Connect to WebSocket
        try await connectToSession(session.id)

        await MainActor.run {
            self.currentSession = session
            self.isHosting = true
            self.sessionId = session.id
            self.connectionStatus = .connected
            self.isConnected = true
        }

        return session
    }

    func joinSession(_ sessionId: UUID) async throws {
        connectionStatus = .connecting

        // Fetch session details from Supabase
        guard let session = try await fetchSessionFromDatabase(sessionId) else {
            throw CollaborationError.sessionNotFound
        }

        // Check if session is full
        if session.participants.count >= session.maxParticipants {
            throw CollaborationError.sessionFull
        }

        // Connect to WebSocket
        try await connectToSession(sessionId)

        // Add participant to session
        let participant = SessionParticipant(
            id: UUID(),
            userId: getCurrentUserId(),
            displayName: getCurrentUserName(),
            role: .participant,
            joinedAt: Date(),
            isActive: true,
            currentScore: 0
        )

        try await addParticipantToSession(sessionId: sessionId, participant: participant)

        await MainActor.run {
            self.currentSession = session
            self.sessionId = sessionId
            self.connectionStatus = .connected
            self.isConnected = true
        }
    }

    func leaveSession() async {
        guard let sessionId = sessionId else { return }

        // Notify other participants
        try? await sendMessage(SessionMessage(
            type: .participantLeft,
            senderId: getCurrentUserId(),
            sessionId: sessionId,
            content: "User left the session",
            timestamp: Date()
        ))

        // Disconnect WebSocket
        await disconnectFromSession()

        // Update session in database
        try? await removeParticipantFromSession(sessionId: sessionId, userId: getCurrentUserId())

        await MainActor.run {
            self.currentSession = nil
            self.sessionId = nil
            self.isHosting = false
            self.activeParticipants = []
            self.sessionActivities = []
            self.connectionStatus = .disconnected
            self.isConnected = false
        }
    }

    // MARK: - Real-time Activities

    func startActivity(_ activity: SessionActivity) async throws {
        guard let sessionId = sessionId, isHosting else {
            throw CollaborationError.notAuthorized
        }

        let message = SessionMessage(
            type: .activityStarted,
            senderId: getCurrentUserId(),
            sessionId: sessionId,
            content: try JSONEncoder().encode(activity).base64EncodedString(),
            timestamp: Date()
        )

        try await sendMessage(message)

        await MainActor.run {
            self.sessionActivities.append(activity)
        }

        // Track analytics
        analyticsService.trackInteraction(
            userId: getCurrentUserId(),
            interactionType: .exerciseAttempt,
            contentType: .exercise,
            contentId: activity.id.uuidString,
            isCorrect: nil,
            responseTime: nil,
            metadata: [
                "activity_type": SupabaseAnyCodable(activity.type.rawValue),
                "session_id": SupabaseAnyCodable(sessionId.uuidString)
            ]
        )
    }

    func submitActivityResponse(_ response: ActivityResponse) async throws {
        guard let sessionId = sessionId else {
            throw CollaborationError.notConnected
        }

        let message = SessionMessage(
            type: .activityResponse,
            senderId: getCurrentUserId(),
            sessionId: sessionId,
            content: try JSONEncoder().encode(response).base64EncodedString(),
            timestamp: Date()
        )

        try await sendMessage(message)

        // Update local score
        if let index = activeParticipants.firstIndex(where: { $0.userId == getCurrentUserId() }) {
            await MainActor.run {
                self.activeParticipants[index].currentScore += response.points
            }
        }
    }

    func startVoiceChat() async throws {
        guard let session = currentSession else {
            throw CollaborationError.notConnected
        }

        // Create a group voice session with the language tutor
        let agent = getLanguageTutorForSession(session)
        try await voiceService.startLiveConversation(
            with: agent,
            conversationId: session.id,
            responseModality: .audio,
            enableTranscription: true
        )

        // Notify other participants
        let message = SessionMessage(
            type: .voiceChatStarted,
            senderId: getCurrentUserId(),
            sessionId: session.id,
            content: "Voice chat started",
            timestamp: Date()
        )

        try await sendMessage(message)
    }

    func stopVoiceChat() async {
        await voiceService.stopLiveConversation()

        guard let sessionId = sessionId else { return }

        let message = SessionMessage(
            type: .voiceChatEnded,
            senderId: getCurrentUserId(),
            sessionId: sessionId,
            content: "Voice chat ended",
            timestamp: Date()
        )

        try? await sendMessage(message)
    }

    // MARK: - Invitations

    func sendInvitation(to userId: UUID, sessionId: UUID) async throws {
        let invitation = SessionInvitation(
            id: UUID(),
            sessionId: sessionId,
            fromUserId: getCurrentUserId(),
            toUserId: userId,
            status: .pending,
            createdAt: Date(),
            expiresAt: Date().addingTimeInterval(3600) // 1 hour
        )

        try await saveInvitationToDatabase(invitation)

        // Send push notification
        try await sendInvitationNotification(invitation)
    }

    func respondToInvitation(_ invitationId: UUID, accept: Bool) async throws {
        guard let invitation = try await fetchInvitationFromDatabase(invitationId) else {
            throw CollaborationError.invitationNotFound
        }

        let newStatus: InvitationStatus = accept ? .accepted : .declined
        try await updateInvitationStatus(invitationId, status: newStatus)

        if accept {
            try await joinSession(invitation.sessionId)
        }

        // Remove from local invitations
        await MainActor.run {
            self.sessionInvitations.removeAll { $0.id == invitationId }
        }
    }

    // MARK: - WebSocket Communication

    private func connectToSession(_ sessionId: UUID) async throws {
        let urlString = "\(APIKeys.supabaseURL.replacingOccurrences(of: "https://", with: "wss://"))/realtime/v1/websocket?apikey=\(APIKeys.supabaseAnonKey)&vsn=1.0.0"

        guard let url = URL(string: urlString) else {
            throw CollaborationError.invalidURL
        }

        var request = URLRequest(url: url)
        request.setValue("websocket", forHTTPHeaderField: "Upgrade")
        request.setValue("Upgrade", forHTTPHeaderField: "Connection")

        let session = URLSession(configuration: .default)
        webSocketTask = session.webSocketTask(with: request)
        webSocketTask?.resume()

        // Start listening for messages
        startListening()

        // Start heartbeat
        startHeartbeat()

        // Join the session channel
        try await joinSessionChannel(sessionId)
    }

    private func disconnectFromSession() async {
        heartbeatTimer?.invalidate()
        heartbeatTimer = nil

        webSocketTask?.cancel(with: .goingAway, reason: nil)
        webSocketTask = nil
    }

    private func startListening() {
        guard let webSocketTask = webSocketTask else { return }

        webSocketTask.receive { [weak self] result in
            switch result {
            case .success(let message):
                Task {
                    await self?.handleWebSocketMessage(message)
                    await self?.startListening() // Continue listening
                }
            case .failure(let error):
                Task { @MainActor in
                    self?.handleWebSocketError(error)
                }
            }
        }
    }

    private func handleWebSocketMessage(_ message: URLSessionWebSocketTask.Message) async {
        switch message {
        case .string(let text):
            if let data = text.data(using: .utf8),
               let sessionMessage = try? JSONDecoder().decode(SessionMessage.self, from: data) {
                await processSessionMessage(sessionMessage)
            }
        case .data(let data):
            if let sessionMessage = try? JSONDecoder().decode(SessionMessage.self, from: data) {
                await processSessionMessage(sessionMessage)
            }
        @unknown default:
            break
        }
    }

    private func processSessionMessage(_ message: SessionMessage) async {
        await MainActor.run {
            switch message.type {
            case .participantJoined:
                if let data = Data(base64Encoded: message.content),
                   let participant = try? JSONDecoder().decode(SessionParticipant.self, from: data) {
                    self.activeParticipants.append(participant)
                }

            case .participantLeft:
                self.activeParticipants.removeAll { $0.userId == message.senderId }

            case .activityStarted:
                if let data = Data(base64Encoded: message.content),
                   let activity = try? JSONDecoder().decode(SessionActivity.self, from: data) {
                    self.sessionActivities.append(activity)
                }

            case .activityResponse:
                if let data = Data(base64Encoded: message.content),
                   let response = try? JSONDecoder().decode(ActivityResponse.self, from: data) {
                    self.handleActivityResponse(response)
                }

            case .voiceChatStarted:
                // Handle voice chat started
                break

            case .voiceChatEnded:
                // Handle voice chat ended
                break

            case .chatMessage:
                // Handle text chat message
                break
            }
        }
    }

    private func handleActivityResponse(_ response: ActivityResponse) {
        // Update participant score
        if let index = activeParticipants.firstIndex(where: { $0.userId == response.userId }) {
            activeParticipants[index].currentScore += response.points
        }
    }

    private func sendMessage(_ message: SessionMessage) async throws {
        guard let webSocketTask = webSocketTask else {
            throw CollaborationError.notConnected
        }

        let data = try JSONEncoder().encode(message)
        let webSocketMessage = URLSessionWebSocketTask.Message.data(data)

        try await webSocketTask.send(webSocketMessage)
    }

    private func joinSessionChannel(_ sessionId: UUID) async throws {
        let joinMessage = [
            "topic": "collaboration:\(sessionId.uuidString)",
            "event": "phx_join",
            "payload": [:],
            "ref": UUID().uuidString
        ] as [String: Any]

        let data = try JSONSerialization.data(withJSONObject: joinMessage)
        let message = URLSessionWebSocketTask.Message.data(data)

        try await webSocketTask?.send(message)
    }

    private func startHeartbeat() {
        heartbeatTimer = Timer.scheduledTimer(withTimeInterval: 30.0, repeats: true) { [weak self] _ in
            Task {
                await self?.sendHeartbeat()
            }
        }
    }

    private func sendHeartbeat() async {
        let heartbeatMessage = [
            "topic": "phoenix",
            "event": "heartbeat",
            "payload": [:],
            "ref": UUID().uuidString
        ] as [String: Any]

        do {
            let data = try JSONSerialization.data(withJSONObject: heartbeatMessage)
            let message = URLSessionWebSocketTask.Message.data(data)
            try await webSocketTask?.send(message)
        } catch {
            print("❌ Failed to send heartbeat: \(error)")
        }
    }

    private func handleWebSocketError(_ error: Error) {
        lastError = error
        connectionStatus = .error

        // Attempt to reconnect
        Task {
            await attemptReconnection()
        }
    }

    private func attemptReconnection() async {
        guard let sessionId = sessionId else { return }

        await MainActor.run {
            self.connectionStatus = .reconnecting
        }

        try? await Task.sleep(nanoseconds: 2_000_000_000) // Wait 2 seconds

        do {
            try await connectToSession(sessionId)
            await MainActor.run {
                self.connectionStatus = .connected
            }
        } catch {
            await MainActor.run {
                self.lastError = error
                self.connectionStatus = .error
            }
        }
    }

    // MARK: - Database Operations

    private func saveSessionToDatabase(_ session: CollaborationSession) async throws {
        // Implementation would save to Supabase
        print("💾 Saving session to database: \(session.id)")
    }

    private func fetchSessionFromDatabase(_ sessionId: UUID) async throws -> CollaborationSession? {
        // Implementation would fetch from Supabase
        print("📥 Fetching session from database: \(sessionId)")
        return nil
    }

    private func addParticipantToSession(sessionId: UUID, participant: SessionParticipant) async throws {
        // Implementation would update Supabase
        await MainActor.run {
            self.activeParticipants.append(participant)
        }
    }

    private func removeParticipantFromSession(sessionId: UUID, userId: UUID) async throws {
        // Implementation would update Supabase
        await MainActor.run {
            self.activeParticipants.removeAll { $0.userId == userId }
        }
    }

    private func saveInvitationToDatabase(_ invitation: SessionInvitation) async throws {
        // Implementation would save to Supabase
        print("💾 Saving invitation to database: \(invitation.id)")
    }

    private func fetchInvitationFromDatabase(_ invitationId: UUID) async throws -> SessionInvitation? {
        // Implementation would fetch from Supabase
        print("📥 Fetching invitation from database: \(invitationId)")
        return nil
    }

    private func updateInvitationStatus(_ invitationId: UUID, status: InvitationStatus) async throws {
        // Implementation would update Supabase
        print("🔄 Updating invitation status: \(invitationId) -> \(status)")
    }

    private func sendInvitationNotification(_ invitation: SessionInvitation) async throws {
        // Implementation would send push notification
        print("📱 Sending invitation notification: \(invitation.id)")
    }

    // MARK: - Helper Methods

    private func setupNetworkMonitoring() {
        networkMonitor.pathUpdateHandler = { [weak self] path in
            guard let self = self else { return }
            DispatchQueue.main.async {
                if path.status != .satisfied && self.isConnected == true {
                    Task { @MainActor [weak self] in
                        await self?.handleNetworkDisconnection()
                    }
                }
            }
        }
        networkMonitor.start(queue: networkQueue)
    }

    private func setupSubscriptions() {
        // Listen for social service updates
        socialService.$friends
            .sink { [weak self] friends in
                // Update available participants for invitations
                guard let _ = self else { return }
            }
            .store(in: &cancellables)
    }

    private func handleNetworkDisconnection() async {
        await MainActor.run {
            self.connectionStatus = .error
            self.isConnected = false
        }

        // Attempt to reconnect when network is restored
        try? await Task.sleep(nanoseconds: 5_000_000_000) // Wait 5 seconds

        if let sessionId = sessionId {
            try? await connectToSession(sessionId)
        }
    }

    private func getCurrentUserId() -> UUID {
        // Implementation would get current user ID
        return UUID()
    }

    private func getCurrentUserName() -> String {
        // Implementation would get current user name
        return "User"
    }

    private func getLanguageTutorForSession(_ session: CollaborationSession) -> LanguageTutor {
        // Return appropriate tutor for the session language
        let mockAgents = LanguageTutor.mockAgents

        switch session.language {
        case .french:
            return mockAgents.first { $0.language == .french } ?? mockAgents[0]
        case .spanish:
            return mockAgents.first { $0.language == .spanish } ?? mockAgents[0]
        case .japanese:
            return mockAgents.first { $0.language == .japanese } ?? mockAgents[0]
        case .tamil:
            return mockAgents.first { $0.language == .tamil } ?? mockAgents[0]
        case .english:
            return mockAgents.first { $0.language == .english } ?? mockAgents[0]
        case .korean:
            return mockAgents.first { $0.language == .korean } ?? mockAgents[0]
        case .italian:
            return mockAgents.first { $0.language == .italian } ?? mockAgents[0]
        case .german:
            return mockAgents.first { $0.language == .german } ?? mockAgents[0]
        case .hindi:
            return mockAgents.first { $0.language == .hindi } ?? mockAgents[0]
        case .chinese:
            return mockAgents.first { $0.language == .chinese } ?? mockAgents[0]
        case .portuguese:
            return mockAgents.first { $0.language == .portuguese } ?? mockAgents[0]
        case .telugu:
            return mockAgents.first { $0.language == .telugu } ?? mockAgents[0]
        case .vietnamese:
            return mockAgents.first { $0.language == .vietnamese } ?? mockAgents[0]
        case .indonesian:
            return mockAgents.first { $0.language == .indonesian } ?? mockAgents[0]
        case .arabic:
            return mockAgents.first { $0.language == .arabic } ?? mockAgents[0]
        // New 10 languages
        case .kannada:
            return mockAgents.first { $0.language == .kannada } ?? mockAgents[0]
        case .malayalam:
            return mockAgents.first { $0.language == .malayalam } ?? mockAgents[0]
        case .bengali:
            return mockAgents.first { $0.language == .bengali } ?? mockAgents[0]
        case .marathi:
            return mockAgents.first { $0.language == .marathi } ?? mockAgents[0]
        case .punjabi:
            return mockAgents.first { $0.language == .punjabi } ?? mockAgents[0]
        case .dutch:
            return mockAgents.first { $0.language == .dutch } ?? mockAgents[0]
        case .swedish:
            return mockAgents.first { $0.language == .swedish } ?? mockAgents[0]
        case .thai:
            return mockAgents.first { $0.language == .thai } ?? mockAgents[0]
        case .russian:
            return mockAgents.first { $0.language == .russian } ?? mockAgents[0]
        case .norwegian:
            return mockAgents.first { $0.language == .norwegian } ?? mockAgents[0]
        // Additional 25 languages
        case .gujarati:
            return mockAgents.first { $0.language == .gujarati } ?? mockAgents[0]
        case .odia:
            return mockAgents.first { $0.language == .odia } ?? mockAgents[0]
        case .assamese:
            return mockAgents.first { $0.language == .assamese } ?? mockAgents[0]
        case .konkani:
            return mockAgents.first { $0.language == .konkani } ?? mockAgents[0]
        case .sindhi:
            return mockAgents.first { $0.language == .sindhi } ?? mockAgents[0]
        case .bhojpuri:
            return mockAgents.first { $0.language == .bhojpuri } ?? mockAgents[0]
        case .maithili:
            return mockAgents.first { $0.language == .maithili } ?? mockAgents[0]
        case .swahili:
            return mockAgents.first { $0.language == .swahili } ?? mockAgents[0]
        case .hebrew:
            return mockAgents.first { $0.language == .hebrew } ?? mockAgents[0]
        case .greek:
            return mockAgents.first { $0.language == .greek } ?? mockAgents[0]
        case .turkish:
            return mockAgents.first { $0.language == .turkish } ?? mockAgents[0]
        case .farsi:
            return mockAgents.first { $0.language == .farsi } ?? mockAgents[0]
        case .tagalog:
            return mockAgents.first { $0.language == .tagalog } ?? mockAgents[0]
        case .ukrainian:
            return mockAgents.first { $0.language == .ukrainian } ?? mockAgents[0]
        case .danish:
            return mockAgents.first { $0.language == .danish } ?? mockAgents[0]
        case .xhosa:
            return mockAgents.first { $0.language == .xhosa } ?? mockAgents[0]
        case .zulu:
            return mockAgents.first { $0.language == .zulu } ?? mockAgents[0]
        case .amharic:
            return mockAgents.first { $0.language == .amharic } ?? mockAgents[0]
        case .quechua:
            return mockAgents.first { $0.language == .quechua } ?? mockAgents[0]
        case .maori:
            return mockAgents.first { $0.language == .maori } ?? mockAgents[0]
        case .cherokee:
            return mockAgents.first { $0.language == .cherokee } ?? mockAgents[0]
        case .navajo:
            return mockAgents.first { $0.language == .navajo } ?? mockAgents[0]
        case .hawaiian:
            return mockAgents.first { $0.language == .hawaiian } ?? mockAgents[0]
        case .inuktitut:
            return mockAgents.first { $0.language == .inuktitut } ?? mockAgents[0]
        case .yoruba:
            return mockAgents.first { $0.language == .yoruba } ?? mockAgents[0]
        // Additional languages to complete the 50-language expansion
        case .urdu:
            return mockAgents.first { $0.language == .urdu } ?? mockAgents[0]
        case .polish:
            return mockAgents.first { $0.language == .polish } ?? mockAgents[0]
        case .czech:
            return mockAgents.first { $0.language == .czech } ?? mockAgents[0]
        case .hungarian:
            return mockAgents.first { $0.language == .hungarian } ?? mockAgents[0]
        case .romanian:
            return mockAgents.first { $0.language == .romanian } ?? mockAgents[0]
        case .bulgarian:
            return mockAgents.first { $0.language == .bulgarian } ?? mockAgents[0]
        case .croatian:
            return mockAgents.first { $0.language == .croatian } ?? mockAgents[0]
        case .serbian:
            return mockAgents.first { $0.language == .serbian } ?? mockAgents[0]
        case .slovak:
            return mockAgents.first { $0.language == .slovak } ?? mockAgents[0]
        case .slovenian:
            return mockAgents.first { $0.language == .slovenian } ?? mockAgents[0]
        case .estonian:
            return mockAgents.first { $0.language == .estonian } ?? mockAgents[0]
        case .latvian:
            return mockAgents.first { $0.language == .latvian } ?? mockAgents[0]
        case .lithuanian:
            return mockAgents.first { $0.language == .lithuanian } ?? mockAgents[0]
        case .maltese:
            return mockAgents.first { $0.language == .maltese } ?? mockAgents[0]
        case .irish:
            return mockAgents.first { $0.language == .irish } ?? mockAgents[0]
        case .welsh:
            return mockAgents.first { $0.language == .welsh } ?? mockAgents[0]
        case .scots:
            return mockAgents.first { $0.language == .scots } ?? mockAgents[0]
        case .manx:
            return mockAgents.first { $0.language == .manx } ?? mockAgents[0]
        case .cornish:
            return mockAgents.first { $0.language == .cornish } ?? mockAgents[0]
        case .breton:
            return mockAgents.first { $0.language == .breton } ?? mockAgents[0]
        case .basque:
            return mockAgents.first { $0.language == .basque } ?? mockAgents[0]
        case .catalan:
            return mockAgents.first { $0.language == .catalan } ?? mockAgents[0]
        case .galician:
            return mockAgents.first { $0.language == .galician } ?? mockAgents[0]
        }
    }
}

// MARK: - Supporting Models

struct CollaborationSession: Codable, Identifiable {
    let id: UUID
    let title: String
    let hostId: UUID
    let language: Language
    let sessionType: CollaborationSessionType
    let maxParticipants: Int
    let isPrivate: Bool
    var status: SessionStatus
    let createdAt: Date
    var participants: [SessionParticipant]
    var endedAt: Date?
}

struct SessionParticipant: Codable, Identifiable {
    let id: UUID
    let userId: UUID
    let displayName: String
    let role: ParticipantRole
    let joinedAt: Date
    var isActive: Bool
    var currentScore: Int
    var leftAt: Date?
}

struct SessionActivity: Codable, Identifiable {
    let id: UUID
    let type: ActivityType
    let title: String
    let description: String
    let instructions: String
    let timeLimit: TimeInterval?
    let maxPoints: Int
    let createdAt: Date
    var responses: [ActivityResponse]
    var isCompleted: Bool
}

struct ActivityResponse: Codable, Identifiable {
    let id: UUID
    let activityId: UUID
    let userId: UUID
    let content: String
    let submittedAt: Date
    let points: Int
    let isCorrect: Bool?
    let feedback: String?
}

struct SessionMessage: Codable {
    let type: MessageType
    let senderId: UUID
    let sessionId: UUID
    let content: String
    let timestamp: Date

    enum MessageType: String, Codable {
        case participantJoined = "participant_joined"
        case participantLeft = "participant_left"
        case activityStarted = "activity_started"
        case activityResponse = "activity_response"
        case voiceChatStarted = "voice_chat_started"
        case voiceChatEnded = "voice_chat_ended"
        case chatMessage = "chat_message"
    }
}

struct SessionInvitation: Codable, Identifiable {
    let id: UUID
    let sessionId: UUID
    let fromUserId: UUID
    let toUserId: UUID
    var status: InvitationStatus
    let createdAt: Date
    let expiresAt: Date
    var respondedAt: Date?
}

enum CollaborationSessionType: String, Codable, CaseIterable {
    case conversationPractice = "conversation_practice"
    case vocabularyChallenge = "vocabulary_challenge"
    case grammarQuiz = "grammar_quiz"
    case pronunciationContest = "pronunciation_contest"
    case culturalScenario = "cultural_scenario"
    case freeConversation = "free_conversation"

    var displayName: String {
        switch self {
        case .conversationPractice: return "Conversation Practice"
        case .vocabularyChallenge: return "Vocabulary Challenge"
        case .grammarQuiz: return "Grammar Quiz"
        case .pronunciationContest: return "Pronunciation Contest"
        case .culturalScenario: return "Cultural Scenario"
        case .freeConversation: return "Free Conversation"
        }
    }
}

enum SessionStatus: String, Codable {
    case waiting = "waiting"
    case active = "active"
    case completed = "completed"
    case cancelled = "cancelled"

    var displayName: String {
        switch self {
        case .waiting: return "Waiting for participants"
        case .active: return "Active"
        case .completed: return "Completed"
        case .cancelled: return "Cancelled"
        }
    }
}

enum ParticipantRole: String, Codable {
    case host = "host"
    case participant = "participant"
    case observer = "observer"

    var displayName: String {
        switch self {
        case .host: return "Host"
        case .participant: return "Participant"
        case .observer: return "Observer"
        }
    }
}

enum ActivityType: String, Codable, CaseIterable {
    case multipleChoice = "multiple_choice"
    case fillInBlank = "fill_in_blank"
    case translation = "translation"
    case pronunciation = "pronunciation"
    case conversation = "conversation"
    case rolePlay = "role_play"
    case wordAssociation = "word_association"
    case storytelling = "storytelling"

    var displayName: String {
        switch self {
        case .multipleChoice: return "Multiple Choice"
        case .fillInBlank: return "Fill in the Blank"
        case .translation: return "Translation"
        case .pronunciation: return "Pronunciation"
        case .conversation: return "Conversation"
        case .rolePlay: return "Role Play"
        case .wordAssociation: return "Word Association"
        case .storytelling: return "Storytelling"
        }
    }
}

enum InvitationStatus: String, Codable {
    case pending = "pending"
    case accepted = "accepted"
    case declined = "declined"
    case expired = "expired"

    var displayName: String {
        switch self {
        case .pending: return "Pending"
        case .accepted: return "Accepted"
        case .declined: return "Declined"
        case .expired: return "Expired"
        }
    }
}

enum CollaborationError: LocalizedError {
    case notConnected
    case notAuthorized
    case sessionNotFound
    case sessionFull
    case invitationNotFound
    case invalidURL
    case networkError

    var errorDescription: String? {
        switch self {
        case .notConnected:
            return "Not connected to collaboration session"
        case .notAuthorized:
            return "Not authorized to perform this action"
        case .sessionNotFound:
            return "Session not found"
        case .sessionFull:
            return "Session is full"
        case .invitationNotFound:
            return "Invitation not found"
        case .invalidURL:
            return "Invalid WebSocket URL"
        case .networkError:
            return "Network connection error"
        }
    }
}