//
//  SecureAuthenticationService.swift
//  NIRA
//
//  Created by Security Audit on 28/05/2025.
//

import Foundation
import SwiftUI
import Supabase
import Combine
import CryptoKit

// MARK: - Secure Authentication Service

@MainActor
class SecureAuthenticationService: ObservableObject {
    static let shared = SecureAuthenticationService()

    // MARK: - Published Properties
    @Published var isAuthenticated = false
    @Published var currentUser: Auth.User?
    @Published var userProfile: SupabaseUserProfile?
    @Published var isLoading = false
    @Published var errorMessage: String?
    @Published var authState: AuthState = .unauthenticated
    @Published var remainingAttempts: Int = 5

    // MARK: - Private Properties
    private let supabaseClient = NIRASupabaseClient.shared
    private let inputValidator = InputValidationService.shared
    private let secureStorage = SecureStorageService.shared
    private var cancellables = Set<AnyCancellable>()

    // Rate limiting and security
    private var failedAttempts: [String: (count: Int, lastAttempt: Date)] = [:]
    private let maxAttempts = 5
    private let lockoutDuration: TimeInterval = 900 // 15 minutes
    private let sessionTimeout: TimeInterval = 3600 // 1 hour

    // Security monitoring
    private var securityEvents: [SecurityEvent] = []

    // MARK: - Initialization
    private init() {
        setupAuthStateListener()
        checkInitialAuthState()
        startSessionMonitoring()
    }

    // MARK: - Public Methods

    /// Secure sign up with comprehensive validation
    func signUp(email: String, password: String, firstName: String? = nil, lastName: String? = nil) async throws {
        // Check rate limiting
        try checkRateLimit(for: email)

        isLoading = true
        errorMessage = nil

        do {
            // Validate inputs
            let validatedEmail = try await inputValidator.validateEmail(email)
            let validatedPassword = try inputValidator.validatePassword(password)

            var validatedFirstName: String?
            var validatedLastName: String?

            if let firstName = firstName {
                validatedFirstName = try inputValidator.validateName(firstName)
            }

            if let lastName = lastName {
                validatedLastName = try inputValidator.validateName(lastName)
            }

            // Log security event
            logSecurityEvent(.signUpAttempt(email: validatedEmail))

            try await supabaseClient.signUp(email: validatedEmail, password: validatedPassword)

            // Create user profile if sign up successful
            if let firstName = validatedFirstName, let lastName = validatedLastName {
                try await createUserProfile(firstName: firstName, lastName: lastName)
            }

            authState = .emailVerificationRequired

            // Clear failed attempts on success
            clearFailedAttempts(for: validatedEmail)

            // Log successful sign up
            logSecurityEvent(.signUpSuccess(email: validatedEmail))

        } catch let validationError as ValidationError {
            recordFailedAttempt(for: email)
            errorMessage = validationError.localizedDescription
            logSecurityEvent(.signUpFailed(email: email, reason: validationError.localizedDescription))
            throw AuthenticationError.signUpFailed(validationError.localizedDescription)
        } catch {
            recordFailedAttempt(for: email)
            errorMessage = handleAuthError(error)
            logSecurityEvent(.signUpFailed(email: email, reason: error.localizedDescription))
            throw AuthenticationError.signUpFailed(error.localizedDescription)
        }

        isLoading = false
    }

    /// Secure sign in with rate limiting and monitoring
    func signIn(email: String, password: String) async throws {
        // Check rate limiting
        try checkRateLimit(for: email)

        isLoading = true
        errorMessage = nil

        do {
            // Validate inputs
            let validatedEmail = try await inputValidator.validateEmail(email)
            let validatedPassword = try inputValidator.validatePassword(password)

            // Log security event
            logSecurityEvent(.signInAttempt(email: validatedEmail))

            try await supabaseClient.signIn(email: validatedEmail, password: validatedPassword)
            authState = .authenticated

            // Load user profile
            await loadUserProfile()

            // Clear failed attempts on success
            clearFailedAttempts(for: validatedEmail)

            // Start session monitoring
            startSessionTimeout()

            // Log successful sign in
            logSecurityEvent(.signInSuccess(email: validatedEmail))

        } catch let validationError as ValidationError {
            recordFailedAttempt(for: email)
            errorMessage = validationError.localizedDescription
            logSecurityEvent(.signInFailed(email: email, reason: validationError.localizedDescription))
            throw AuthenticationError.signInFailed(validationError.localizedDescription)
        } catch {
            recordFailedAttempt(for: email)
            errorMessage = handleAuthError(error)
            logSecurityEvent(.signInFailed(email: email, reason: error.localizedDescription))
            throw AuthenticationError.signInFailed(error.localizedDescription)
        }

        isLoading = false
    }

    /// Secure sign out with session cleanup
    func signOut() async throws {
        isLoading = true
        errorMessage = nil

        do {
            // Log security event
            if let email = currentUser?.email {
                logSecurityEvent(.signOutAttempt(email: email))
            }

            try await supabaseClient.signOut()

            // Clear local state securely
            await clearSecureSession()

            // Log successful sign out
            logSecurityEvent(.signOutSuccess)

        } catch {
            errorMessage = handleAuthError(error)
            logSecurityEvent(.signOutFailed(reason: error.localizedDescription))
            throw AuthenticationError.signOutFailed(error.localizedDescription)
        }

        isLoading = false
    }

    /// Secure password reset with validation
    func resetPassword(email: String) async throws {
        // Check rate limiting
        try checkRateLimit(for: email)

        isLoading = true
        errorMessage = nil

        do {
            // Validate email
            let validatedEmail = try await inputValidator.validateEmail(email)

            // Log security event
            logSecurityEvent(.passwordResetAttempt(email: validatedEmail))

            try await supabaseClient.resetPassword(email: validatedEmail)

            // Log successful password reset
            logSecurityEvent(.passwordResetSuccess(email: validatedEmail))

        } catch let validationError as ValidationError {
            recordFailedAttempt(for: email)
            errorMessage = validationError.localizedDescription
            logSecurityEvent(.passwordResetFailed(email: email, reason: validationError.localizedDescription))
            throw AuthenticationError.passwordResetFailed(validationError.localizedDescription)
        } catch {
            recordFailedAttempt(for: email)
            errorMessage = handleAuthError(error)
            logSecurityEvent(.passwordResetFailed(email: email, reason: error.localizedDescription))
            throw AuthenticationError.passwordResetFailed(error.localizedDescription)
        }

        isLoading = false
    }

    /// Continue as guest with limited functionality
    func continueAsGuest() async {
        authState = .guest
        isAuthenticated = true

        // Log security event
        logSecurityEvent(.guestModeActivated)

        // Create a temporary guest profile with minimal data
        userProfile = SupabaseUserProfile(
            id: "guest_\(UUID().uuidString)",
            email: "",
            firstName: "Guest",
            lastName: "User",
            preferredLanguages: ["english"],
            createdAt: Date(),
            lastActiveDate: Date(),
            isEmailVerified: false,
            subscriptionTier: "guest",
            totalLessonsCompleted: 0,
            currentStreak: 0,
            longestStreak: 0,
            totalStudyTimeMinutes: 0
        )

        // Set guest session timeout (shorter than authenticated users)
        startGuestSessionTimeout()
    }

    // MARK: - Security Methods

    private func checkRateLimit(for email: String) throws {
        let now = Date()

        if let attempts = failedAttempts[email] {
            // Check if still in lockout period
            if attempts.count >= maxAttempts {
                let timeSinceLast = now.timeIntervalSince(attempts.lastAttempt)
                if timeSinceLast < lockoutDuration {
                    let _ = Int(lockoutDuration - timeSinceLast)
                    throw AuthenticationError.signInFailed("Rate limit exceeded")
                } else {
                    // Lockout period expired, reset attempts
                    failedAttempts.removeValue(forKey: email)
                }
            }
        }

        // Update remaining attempts for UI
        let currentAttempts = failedAttempts[email]?.count ?? 0
        remainingAttempts = max(0, maxAttempts - currentAttempts)
    }

    private func recordFailedAttempt(for email: String) {
        let now = Date()

        if var attempts = failedAttempts[email] {
            attempts.count += 1
            attempts.lastAttempt = now
            failedAttempts[email] = attempts
        } else {
            failedAttempts[email] = (count: 1, lastAttempt: now)
        }

        // Update remaining attempts for UI
        remainingAttempts = max(0, maxAttempts - failedAttempts[email]!.count)

        // Log security event for monitoring
        logSecurityEvent(.failedAttemptRecorded(email: email, count: failedAttempts[email]!.count))
    }

    private func clearFailedAttempts(for email: String) {
        failedAttempts.removeValue(forKey: email)
        remainingAttempts = maxAttempts
    }

    private func logSecurityEvent(_ event: SecurityEvent) {
        securityEvents.append(event)

        // Keep only last 100 events to prevent memory issues
        if securityEvents.count > 100 {
            securityEvents.removeFirst(securityEvents.count - 100)
        }

        // In production, send critical events to security monitoring service
        #if DEBUG
        print("🔒 Security Event: \(event)")
        #endif
    }

    private func startSessionMonitoring() {
        Timer.scheduledTimer(withTimeInterval: 60, repeats: true) { [weak self] _ in
            Task { @MainActor in
                await self?.checkSessionValidity()
            }
        }
    }

    private func checkSessionValidity() async {
        guard authState == .authenticated else { return }

        // Check if session has expired
        if let lastActivity = userProfile?.lastActiveDate {
            let timeSinceActivity = Date().timeIntervalSince(lastActivity)
            if timeSinceActivity > sessionTimeout {
                await forceSignOut(reason: "Session timeout")
            }
        }
    }

    private func startSessionTimeout() {
        // Update last activity
        userProfile?.lastActiveDate = Date()
    }

    private func startGuestSessionTimeout() {
        // Guest sessions expire after 30 minutes
        DispatchQueue.main.asyncAfter(deadline: .now() + 1800) { [weak self] in
            if self?.authState == .guest {
                Task { @MainActor in
                    await self?.forceSignOut(reason: "Guest session timeout")
                }
            }
        }
    }

    private func forceSignOut(reason: String) async {
        logSecurityEvent(.forcedSignOut(reason: reason))

        do {
            try await signOut()
        } catch {
            // Force local cleanup even if remote signout fails
            await clearSecureSession()
        }
    }

    private func clearSecureSession() async {
        currentUser = nil
        userProfile = nil
        isAuthenticated = false
        authState = .unauthenticated

        // Clear any cached sensitive data
        await secureStorage.clearUserData()
    }

    // MARK: - Missing Method Implementations

    private func setupAuthStateListener() {
        // Set up Supabase auth state listener
        Task {
            for await state in supabaseClient.client.auth.authStateChanges {
                await MainActor.run {
                    switch state.event {
                    case .signedIn:
                        self.currentUser = state.session?.user
                        self.isAuthenticated = true
                        self.authState = .authenticated
                    case .signedOut:
                        self.currentUser = nil
                        self.isAuthenticated = false
                        self.authState = .unauthenticated
                    case .tokenRefreshed:
                        self.currentUser = state.session?.user
                    default:
                        break
                    }
                }
            }
        }
    }

    private func checkInitialAuthState() {
        Task {
            do {
                let session = try await supabaseClient.client.auth.session
                await MainActor.run {
                    let user = session.user
                    self.currentUser = user
                    self.isAuthenticated = true
                    self.authState = .authenticated
                    Task {
                        await self.loadUserProfile()
                    }
                }
            } catch {
                await MainActor.run {
                    self.authState = .unauthenticated
                }
            }
        }
    }

    private func createUserProfile(firstName: String, lastName: String) async throws {
        guard let user = currentUser else {
            throw AuthenticationError.signUpFailed("No authenticated user found")
        }

        let profile = SupabaseUserProfile(
            id: user.id.uuidString,
            email: user.email ?? "",
            firstName: firstName,
            lastName: lastName,
            preferredLanguages: ["english"],
            createdAt: Date(),
            lastActiveDate: Date(),
            isEmailVerified: user.emailConfirmedAt != nil,
            subscriptionTier: "free",
            totalLessonsCompleted: 0,
            currentStreak: 0,
            longestStreak: 0,
            totalStudyTimeMinutes: 0
        )

        // Store profile in Supabase
        try await supabaseClient.client
            .from("user_profiles")
            .insert(profile)
            .execute()

        userProfile = profile
    }

    private func handleAuthError(_ error: Error) -> String {
        // Convert technical errors to user-friendly messages
        let errorMessage = error.localizedDescription.lowercased()

        if errorMessage.contains("invalid") && errorMessage.contains("credentials") {
            return "Invalid email or password. Please check your credentials and try again."
        } else if errorMessage.contains("email") && errorMessage.contains("already") {
            return "An account with this email already exists. Please sign in instead."
        } else if errorMessage.contains("network") || errorMessage.contains("connection") {
            return "Network error. Please check your internet connection and try again."
        } else if errorMessage.contains("rate") && errorMessage.contains("limit") {
            return "Too many attempts. Please wait a few minutes before trying again."
        } else if errorMessage.contains("email") && errorMessage.contains("verification") {
            return "Please verify your email address before signing in."
        } else {
            return "Authentication failed. Please try again."
        }
    }

    private func loadUserProfile() async {
        guard let user = currentUser else { return }

        do {
            let response = try await supabaseClient.client
                .from("user_profiles")
                .select()
                .eq("id", value: user.id.uuidString)
                .single()
                .execute()

            let profile = try JSONDecoder().decode(SupabaseUserProfile.self, from: response.data)

            await MainActor.run {
                self.userProfile = profile
            }
        } catch {
            print("Failed to load user profile: \(error)")
            // Create a basic profile if none exists
            await createBasicProfile(for: user)
        }
    }

    private func createBasicProfile(for user: Auth.User) async {
        let profile = SupabaseUserProfile(
            id: user.id.uuidString,
            email: user.email ?? "",
            firstName: "",
            lastName: "",
            preferredLanguages: ["english"],
            createdAt: Date(),
            lastActiveDate: Date(),
            isEmailVerified: user.emailConfirmedAt != nil,
            subscriptionTier: "free",
            totalLessonsCompleted: 0,
            currentStreak: 0,
            longestStreak: 0,
            totalStudyTimeMinutes: 0
        )

        do {
            try await supabaseClient.client
                .from("user_profiles")
                .insert(profile)
                .execute()

            await MainActor.run {
                self.userProfile = profile
            }
        } catch {
            print("Failed to create basic profile: \(error)")
        }
    }
}
