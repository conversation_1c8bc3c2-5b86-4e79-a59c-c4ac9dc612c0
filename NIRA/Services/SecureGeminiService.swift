//
//  SecureGeminiService.swift
//  NIRA
//
//  Created by Security Audit on 28/05/2025.
//

import Foundation
import Combine

// MARK: - Secure Gemini AI Service with OWASP LLM Top 10 Compliance

@MainActor
class SecureGeminiService: ObservableObject {
    static let shared = SecureGeminiService()

    private let inputValidator = InputValidationService.shared
    private let secureNetwork = SecureNetworkService.shared
    private let secureStorage = SecureStorageService.shared

    @Published var isGenerating = false
    @Published var lastError: Error?
    @Published var lastGenerated: Date?

    // Security configuration
    private let maxPromptLength = 4000
    private let maxResponseLength = 8000
    private let rateLimitDelay: TimeInterval = 2.0
    private var lastRequestTime: Date = Date.distantPast
    private let maxRequestsPerMinute = 10
    private var requestCount: [(date: Date, count: Int)] = []

    // Content filtering and security
    private let blockedPatterns = [
        "system prompt", "ignore instructions", "training data", "model weights",
        "bypass safety", "jailbreak", "pretend you are", "roleplay as",
        "forget previous", "new instructions", "override", "admin mode"
    ]

    private let sensitiveDataPatterns = [
        "password", "api key", "secret", "token", "credit card",
        "ssn", "social security", "bank account", "personal information"
    ]

    private init() {
        // Validate API configuration on initialization
        do {
            try SecureAPIKeys.validateConfiguration()
        } catch {
            print("⚠️ API configuration validation failed: \(error)")
        }
    }

    // MARK: - Secure Lesson Generation

    func generateLesson(
        language: Language,
        difficulty: Difficulty,
        category: LessonCategory,
        culturalContext: String? = nil,
        userLevel: String = "beginner"
    ) async throws -> GeneratedLesson {
        // Security checks
        try await performSecurityChecks()

        await MainActor.run {
            isGenerating = true
        }

        defer {
            Task { @MainActor in
                isGenerating = false
            }
        }

        // Validate and sanitize inputs
        let sanitizedContext = try await validateAndSanitizeCulturalContext(culturalContext)
        let sanitizedUserLevel = try await inputValidator.validateTextContent(userLevel, maxLength: 50)

        // Create secure prompt
        let prompt = try createSecureLessonPrompt(
            language: language,
            difficulty: difficulty,
            category: category,
            culturalContext: sanitizedContext,
            userLevel: sanitizedUserLevel
        )

        // Make secure API request
        let response = try await makeSecureGeminiRequest(prompt: prompt)

        // Validate and parse response
        let lesson = try parseAndValidateLessonResponse(response)

        await MainActor.run {
            lastGenerated = Date()
        }

        return lesson
    }

    // MARK: - Secure Exercise Generation

    func generateExercises(
        for lesson: Lesson,
        count: Int = 5,
        types: [ExerciseType] = ExerciseType.allCases
    ) async throws -> [GeneratedExercise] {
        // Security checks
        try await performSecurityChecks()

        // Validate count
        guard count > 0 && count <= 20 else {
            throw SecureGeminiError.invalidExerciseCount
        }

        await MainActor.run {
            isGenerating = true
        }

        defer {
            Task { @MainActor in
                isGenerating = false
            }
        }

        // Create secure prompt
        let prompt = try createSecureExercisePrompt(for: lesson, count: count, types: types)

        // Make secure API request
        let response = try await makeSecureGeminiRequest(prompt: prompt)

        // Validate and parse response
        let exercises = try parseAndValidateExerciseResponse(response)

        await MainActor.run {
            lastGenerated = Date()
        }

        return exercises
    }

    // MARK: - Secure Cultural Context Generation

    func generateCulturalContext(
        language: Language,
        scenario: String,
        difficulty: Difficulty
    ) async throws -> GeneratedCulturalContext {
        // Security checks
        try await performSecurityChecks()

        await MainActor.run {
            isGenerating = true
        }

        defer {
            Task { @MainActor in
                isGenerating = false
            }
        }

        // Validate and sanitize scenario
        let sanitizedScenario = try await inputValidator.validateTextContent(scenario, maxLength: 200)

        // Create secure prompt
        let prompt = try createSecureCulturalPrompt(
            language: language,
            scenario: sanitizedScenario,
            difficulty: difficulty
        )

        // Make secure API request
        let response = try await makeSecureGeminiRequest(prompt: prompt)

        // Validate and parse response
        let culturalContext = try parseAndValidateCulturalResponse(response)

        await MainActor.run {
            lastGenerated = Date()
        }

        return culturalContext
    }

    // MARK: - Security Methods

    private func performSecurityChecks() async throws {
        // Rate limiting check
        try checkRateLimit()

        // API key validation
        guard !SecureAPIKeys.geminiAPIKey.isEmpty else {
            throw SecureGeminiError.missingAPIKey
        }

        // Network connectivity check
        guard secureNetwork.isConnected else {
            throw SecureGeminiError.noNetworkConnection
        }
    }

    private func checkRateLimit() throws {
        let now = Date()

        // Clean old requests (older than 1 minute)
        requestCount = requestCount.filter { now.timeIntervalSince($0.date) < 60 }

        // Check if we've exceeded rate limit
        let totalRequests = requestCount.reduce(0) { $0 + $1.count }
        guard totalRequests < maxRequestsPerMinute else {
            throw SecureGeminiError.rateLimitExceeded
        }

        // Check minimum delay between requests
        let timeSinceLastRequest = now.timeIntervalSince(lastRequestTime)
        guard timeSinceLastRequest >= rateLimitDelay else {
            throw SecureGeminiError.requestTooSoon
        }

        // Record this request
        requestCount.append((date: now, count: 1))
        lastRequestTime = now
    }

    private func validateAndSanitizeCulturalContext(_ context: String?) async throws -> String? {
        guard let context = context else { return nil }

        // Validate input
        let sanitized = try await inputValidator.validateTextContent(context, maxLength: 500)

        // Check for sensitive content
        try checkForSensitiveContent(sanitized)

        return sanitized
    }

    private func checkForSensitiveContent(_ text: String) throws {
        let lowercaseText = text.lowercased()

        // Check for blocked patterns
        for pattern in blockedPatterns {
            if lowercaseText.contains(pattern) {
                throw SecureGeminiError.blockedContent(pattern: pattern)
            }
        }

        // Check for sensitive data patterns
        for pattern in sensitiveDataPatterns {
            if lowercaseText.contains(pattern) {
                throw SecureGeminiError.sensitiveDataDetected(pattern: pattern)
            }
        }
    }

    private func createSecureLessonPrompt(
        language: Language,
        difficulty: Difficulty,
        category: LessonCategory,
        culturalContext: String?,
        userLevel: String
    ) throws -> String {

        // Create base prompt with security constraints
        var prompt = """
        You are a language learning content generator. Create educational content only.

        CONSTRAINTS:
        - Only generate educational language learning content
        - Do not include personal information, sensitive data, or harmful content
        - Keep content appropriate for all ages
        - Focus on cultural accuracy and respect

        TASK: Create a language lesson with these parameters:
        - Language: \(language.displayName)
        - Difficulty: \(difficulty.displayName)
        - Category: \(category.displayName)
        - User Level: \(userLevel)
        """

        if let context = culturalContext {
            prompt += "\n- Cultural Context: \(context)"
        }

        prompt += """

        Return ONLY a valid JSON object with this exact structure:
        {
            "title": "Lesson title (max 100 chars)",
            "description": "Lesson description (max 500 chars)",
            "estimatedDuration": 15,
            "vocabulary": [
                {
                    "word": "target word",
                    "translation": "translation",
                    "pronunciation": "phonetic pronunciation",
                    "partOfSpeech": "noun/verb/adjective/etc",
                    "example": "example sentence",
                    "exampleTranslation": "translated example"
                }
            ],
            "grammarPoints": [
                {
                    "rule": "Grammar rule name",
                    "explanation": "Clear explanation",
                    "examples": ["Example 1", "Example 2"],
                    "tips": "Learning tip"
                }
            ]
        }

        Ensure all content is educational, culturally respectful, and appropriate.
        """

        // Validate prompt length
        guard prompt.count <= maxPromptLength else {
            throw SecureGeminiError.promptTooLong
        }

        // Final security check
        try checkForSensitiveContent(prompt)

        return prompt
    }

    private func createSecureExercisePrompt(for lesson: Lesson, count: Int, types: [ExerciseType]) throws -> String {
        let typeNames = types.map { $0.displayName }.joined(separator: ", ")

        let prompt = """
        You are a language learning exercise generator. Create educational exercises only.

        CONSTRAINTS:
        - Only generate educational language exercises
        - Keep content appropriate for all ages
        - Ensure exercises are fair and unbiased
        - Focus on language learning objectives

        TASK: Create \(count) exercises for:
        - Lesson: \(lesson.title)
        - Language: \(lesson.language.displayName)
        - Difficulty: \(lesson.difficulty.displayName)
        - Types: \(typeNames)

        Return ONLY a valid JSON array with this structure:
        [
            {
                "type": "multiple_choice",
                "question": "Exercise question (max 200 chars)",
                "options": ["Option 1", "Option 2", "Option 3", "Option 4"],
                "correctAnswer": 0,
                "explanation": "Why this answer is correct",
                "points": 10,
                "timeLimit": 30,
                "hints": ["Hint 1", "Hint 2"]
            }
        ]

        Ensure all exercises are educational and appropriate.
        """

        // Validate prompt length
        guard prompt.count <= maxPromptLength else {
            throw SecureGeminiError.promptTooLong
        }

        // Security check
        try checkForSensitiveContent(prompt)

        return prompt
    }

    private func createSecureCulturalPrompt(language: Language, scenario: String, difficulty: Difficulty) throws -> String {
        let prompt = """
        You are a cultural education content generator. Create respectful cultural information only.

        CONSTRAINTS:
        - Only generate respectful, accurate cultural information
        - Avoid stereotypes and generalizations
        - Keep content educational and appropriate
        - Focus on cultural understanding and respect

        TASK: Create cultural context for:
        - Language: \(language.displayName)
        - Scenario: \(scenario)
        - Difficulty: \(difficulty.displayName)

        Return ONLY a valid JSON object with this structure:
        {
            "scenario": "\(scenario)",
            "setting": "Setting description",
            "participants": ["Role 1", "Role 2"],
            "socialNorms": ["Norm 1", "Norm 2"],
            "etiquette": ["Etiquette rule 1", "Etiquette rule 2"],
            "backgroundInfo": "Cultural background",
            "tips": ["Tip 1", "Tip 2"],
            "doAndDonts": ["Do: Action", "Don't: Action"]
        }

        Ensure all content is culturally respectful and accurate.
        """

        // Validate prompt length
        guard prompt.count <= maxPromptLength else {
            throw SecureGeminiError.promptTooLong
        }

        // Security check
        try checkForSensitiveContent(prompt)

        return prompt
    }

    // MARK: - Secure API Communication

    private func makeSecureGeminiRequest(prompt: String) async throws -> String {
        // Validate API key
        let apiKey = SecureAPIKeys.geminiAPIKey
        guard !apiKey.isEmpty else {
            throw SecureGeminiError.missingAPIKey
        }

        // Create secure URL
        guard let url = try? inputValidator.validateURL("https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=\(apiKey)") else {
            throw SecureGeminiError.invalidURL
        }

        // Create request body with security constraints
        let requestBody = SecureGeminiRequest(
            contents: [
                SecureGeminiContent(
                    parts: [SecureGeminiPart(text: prompt)]
                )
            ],
            safetySettings: [
                SafetySetting(category: "HARM_CATEGORY_HARASSMENT", threshold: "BLOCK_MEDIUM_AND_ABOVE"),
                SafetySetting(category: "HARM_CATEGORY_HATE_SPEECH", threshold: "BLOCK_MEDIUM_AND_ABOVE"),
                SafetySetting(category: "HARM_CATEGORY_SEXUALLY_EXPLICIT", threshold: "BLOCK_MEDIUM_AND_ABOVE"),
                SafetySetting(category: "HARM_CATEGORY_DANGEROUS_CONTENT", threshold: "BLOCK_MEDIUM_AND_ABOVE")
            ],
            generationConfig: GenerationConfig(
                temperature: 0.7,
                topK: 40,
                topP: 0.95,
                maxOutputTokens: 2048,
                stopSequences: ["END", "STOP", "###"]
            )
        )

        // Make secure request
        let responseData = try await secureNetwork.secureRequest(
            url: url,
            method: HTTPMethod.POST,
            body: try JSONEncoder().encode(requestBody),
            headers: ["Content-Type": "application/json"],
            responseType: SecureGeminiResponse.self
        )

        // Validate response
        guard let text = responseData.candidates.first?.content.parts.first?.text else {
            throw SecureGeminiError.noContent
        }

        // Validate response length
        guard text.count <= maxResponseLength else {
            throw SecureGeminiError.responseTooLong
        }

        // Security check on response
        try checkForSensitiveContent(text)

        return text
    }

    // MARK: - Response Parsing and Validation

    private func parseAndValidateLessonResponse(_ response: String) throws -> GeneratedLesson {
        // Extract and validate JSON
        let jsonString = extractSecureJSON(from: response)

        guard let data = jsonString.data(using: .utf8) else {
            throw SecureGeminiError.invalidJSON
        }

        // Parse with security validation
        let decoder = JSONDecoder()
        let lesson = try decoder.decode(GeneratedLesson.self, from: data)

        // Validate lesson content
        try validateLessonContent(lesson)

        return lesson
    }

    private func parseAndValidateExerciseResponse(_ response: String) throws -> [GeneratedExercise] {
        // Extract and validate JSON
        let jsonString = extractSecureJSON(from: response)

        guard let data = jsonString.data(using: .utf8) else {
            throw SecureGeminiError.invalidJSON
        }

        // Parse with security validation
        let decoder = JSONDecoder()
        let exercises = try decoder.decode([GeneratedExercise].self, from: data)

        // Validate exercise content
        for exercise in exercises {
            try validateExerciseContent(exercise)
        }

        return exercises
    }

    private func parseAndValidateCulturalResponse(_ response: String) throws -> GeneratedCulturalContext {
        // Extract and validate JSON
        let jsonString = extractSecureJSON(from: response)

        guard let data = jsonString.data(using: .utf8) else {
            throw SecureGeminiError.invalidJSON
        }

        // Parse with security validation
        let decoder = JSONDecoder()
        let context = try decoder.decode(GeneratedCulturalContext.self, from: data)

        // Validate cultural content
        try validateCulturalContent(context)

        return context
    }

    // MARK: - Content Validation

    private func validateLessonContent(_ lesson: GeneratedLesson) throws {
        // Validate title
        guard lesson.title.count <= 100 else {
            throw SecureGeminiError.contentTooLong("title")
        }

        // Validate description
        guard lesson.description.count <= 500 else {
            throw SecureGeminiError.contentTooLong("description")
        }

        // Check for sensitive content in all text fields
        try checkForSensitiveContent(lesson.title)
        try checkForSensitiveContent(lesson.description)

        // Validate vocabulary entries
        for vocab in lesson.vocabulary {
            try checkForSensitiveContent(vocab.word)
            try checkForSensitiveContent(vocab.translation)
            try checkForSensitiveContent(vocab.example)
        }
    }

    private func validateExerciseContent(_ exercise: GeneratedExercise) throws {
        // Validate question length
        guard exercise.question.count <= 200 else {
            throw SecureGeminiError.contentTooLong("question")
        }

        // Check for sensitive content
        try checkForSensitiveContent(exercise.question)

        // Validate options
        if let options = exercise.options {
            for option in options {
                try checkForSensitiveContent(option)
            }
        }
    }

    private func validateCulturalContent(_ context: GeneratedCulturalContext) throws {
        // Check for sensitive content in all fields
        try checkForSensitiveContent(context.scenario)
        try checkForSensitiveContent(context.setting)
        try checkForSensitiveContent(context.backgroundInfo)

        // Validate arrays
        for norm in context.socialNorms {
            try checkForSensitiveContent(norm)
        }

        for tip in context.tips {
            try checkForSensitiveContent(tip)
        }
    }

    private func extractSecureJSON(from response: String) -> String {
        // Remove markdown formatting and extract JSON safely
        let lines = response.components(separatedBy: .newlines)
        var jsonLines: [String] = []
        var inJsonBlock = false

        for line in lines {
            if line.trimmingCharacters(in: .whitespaces).hasPrefix("```json") {
                inJsonBlock = true
                continue
            } else if line.trimmingCharacters(in: .whitespaces).hasPrefix("```") && inJsonBlock {
                break
            } else if inJsonBlock {
                jsonLines.append(line)
            } else if line.trimmingCharacters(in: .whitespaces).hasPrefix("{") {
                // Direct JSON without markdown
                return response
            }
        }

        return jsonLines.joined(separator: "\n")
    }
}

// MARK: - Secure Gemini Types

struct SecureGeminiRequest: Codable {
    let contents: [SecureGeminiContent]
    let safetySettings: [SafetySetting]
    let generationConfig: GenerationConfig
}

struct SecureGeminiContent: Codable {
    let parts: [SecureGeminiPart]
}

struct SecureGeminiPart: Codable {
    let text: String
}

struct SafetySetting: Codable {
    let category: String
    let threshold: String
}

struct GenerationConfig: Codable {
    let temperature: Double
    let topK: Int
    let topP: Double
    let maxOutputTokens: Int
    let stopSequences: [String]
}

struct SecureGeminiResponse: Codable {
    let candidates: [SecureGeminiCandidate]
}

struct SecureGeminiCandidate: Codable {
    let content: SecureGeminiContent
    let safetyRatings: [SafetyRating]?
}

struct SafetyRating: Codable {
    let category: String
    let probability: String
}

// MARK: - Secure Gemini Errors

enum SecureGeminiError: LocalizedError {
    case missingAPIKey
    case invalidURL
    case noNetworkConnection
    case rateLimitExceeded
    case requestTooSoon
    case promptTooLong
    case responseTooLong
    case invalidExerciseCount
    case blockedContent(pattern: String)
    case sensitiveDataDetected(pattern: String)
    case noContent
    case invalidJSON
    case contentTooLong(String)

    var errorDescription: String? {
        switch self {
        case .missingAPIKey:
            return "API key not configured. Please check your settings."
        case .invalidURL:
            return "Invalid API endpoint URL."
        case .noNetworkConnection:
            return "No network connection available."
        case .rateLimitExceeded:
            return "Rate limit exceeded. Please wait before making another request."
        case .requestTooSoon:
            return "Please wait before making another request."
        case .promptTooLong:
            return "Request is too long. Please shorten your input."
        case .responseTooLong:
            return "Response is too long and may contain unsafe content."
        case .invalidExerciseCount:
            return "Invalid number of exercises requested."
        case .blockedContent(let pattern):
            return "Content contains blocked pattern: \(pattern)"
        case .sensitiveDataDetected(let pattern):
            return "Sensitive data detected: \(pattern)"
        case .noContent:
            return "No content received from AI service."
        case .invalidJSON:
            return "Invalid response format received."
        case .contentTooLong(let field):
            return "Content in \(field) is too long."
        }
    }
}
