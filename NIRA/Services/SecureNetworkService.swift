//
//  SecureNetworkService.swift
//  NIRA
//
//  Created by Security Audit on 28/05/2025.
//

import Foundation
import Network
import CryptoKit

// MARK: - Secure Network Service

class SecureNetworkService: NSObject, ObservableObject {
    static let shared = SecureNetworkService()

    @Published var isConnected = false
    @Published var connectionType: NWInterface.InterfaceType?

    private let monitor = NWPathMonitor()
    private let queue = DispatchQueue(label: "NetworkMonitor")
    private var session: URLSession

    // Certificate pinning configuration
    private let pinnedCertificates: [String: Data] = [:]
    private let trustedDomains = ["supabase.co", "googleapis.com"]

    override init() {
        // Configure secure URLSession
        let configuration = URLSessionConfiguration.default
        configuration.timeoutIntervalForRequest = 30.0
        configuration.timeoutIntervalForResource = 60.0
        configuration.waitsForConnectivity = true
        configuration.httpMaximumConnectionsPerHost = 4
        configuration.requestCachePolicy = .reloadIgnoringLocalCacheData

        // Security headers
        configuration.httpAdditionalHeaders = [
            "User-Agent": "NIRA-iOS/1.0",
            "Accept": "application/json",
            "Cache-Control": "no-cache"
        ]

        self.session = URLSession(configuration: configuration)

        super.init()

        // Set up certificate pinning
        self.session = URLSession(
            configuration: configuration,
            delegate: self,
            delegateQueue: nil
        )

        startNetworkMonitoring()
    }

    deinit {
        monitor.cancel()
    }

    // MARK: - Secure Request Methods

    func secureRequest<T: Codable>(
        url: URL,
        method: HTTPMethod = .GET,
        body: Data? = nil,
        headers: [String: String] = [:],
        responseType: T.Type
    ) async throws -> T {

        // Validate URL
        try validateURL(url)

        // Create secure request
        var request = URLRequest(url: url)
        request.httpMethod = method.rawValue
        request.httpBody = body

        // Add security headers
        var secureHeaders = getSecurityHeaders()
        secureHeaders.merge(headers) { _, new in new }

        for (key, value) in secureHeaders {
            request.setValue(value, forHTTPHeaderField: key)
        }

        // Add request signing if needed
        if let signature = signRequest(request) {
            request.setValue(signature, forHTTPHeaderField: "X-Request-Signature")
        }

        // Execute request with retry logic
        return try await executeWithRetry(request: request, responseType: responseType)
    }

    func secureUpload(
        url: URL,
        data: Data,
        mimeType: String,
        headers: [String: String] = [:]
    ) async throws -> Data {

        try validateURL(url)

        // Validate file size
        guard data.count <= 50 * 1024 * 1024 else { // 50MB limit
            throw NetworkSecurityError.fileTooLarge
        }

        // Validate MIME type
        try validateMimeType(mimeType)

        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.httpBody = data

        var secureHeaders = getSecurityHeaders()
        secureHeaders["Content-Type"] = mimeType
        secureHeaders["Content-Length"] = "\(data.count)"
        secureHeaders.merge(headers) { _, new in new }

        for (key, value) in secureHeaders {
            request.setValue(value, forHTTPHeaderField: key)
        }

        let (responseData, response) = try await session.data(for: request)

        guard let httpResponse = response as? HTTPURLResponse else {
            throw NetworkSecurityError.invalidResponse
        }

        try validateResponse(httpResponse)

        return responseData
    }

    // MARK: - Private Security Methods

    private func validateURL(_ url: URL) throws {
        // Only allow HTTPS
        guard url.scheme == "https" else {
            throw NetworkSecurityError.insecureScheme
        }

        // Check if domain is trusted
        guard let host = url.host else {
            throw NetworkSecurityError.invalidHost
        }

        let isTrusted = trustedDomains.contains { trustedDomain in
            host.hasSuffix(trustedDomain)
        }

        guard isTrusted else {
            throw NetworkSecurityError.untrustedDomain
        }

        // Prevent local network access
        let localPatterns = ["localhost", "127.0.0.1", "0.0.0.0", "::1", "10.", "192.168.", "172."]
        for pattern in localPatterns {
            if host.contains(pattern) {
                throw NetworkSecurityError.localNetworkAccess
            }
        }
    }

    private func getSecurityHeaders() -> [String: String] {
        return [
            "X-Requested-With": "XMLHttpRequest",
            "X-Content-Type-Options": "nosniff",
            "X-Frame-Options": "DENY",
            "X-XSS-Protection": "1; mode=block",
            "Referrer-Policy": "strict-origin-when-cross-origin",
            "Cache-Control": "no-cache, no-store, must-revalidate",
            "Pragma": "no-cache"
        ]
    }

    private func signRequest(_ request: URLRequest) -> String? {
        // Create request signature for integrity
        guard let url = request.url?.absoluteString,
              let method = request.httpMethod else {
            return nil
        }

        let timestamp = String(Int(Date().timeIntervalSince1970))
        let bodyHash = request.httpBody?.sha256Hash ?? ""

        let signatureString = "\(method)\n\(url)\n\(timestamp)\n\(bodyHash)"

        // In production, use a proper signing key
        let signature = signatureString.sha256Hash

        return "\(timestamp):\(signature)"
    }

    private func validateMimeType(_ mimeType: String) throws {
        let allowedTypes = [
            "application/json",
            "application/pdf",
            "text/plain",
            "image/jpeg",
            "image/png",
            "audio/mpeg",
            "audio/wav",
            "video/mp4"
        ]

        guard allowedTypes.contains(mimeType) else {
            throw NetworkSecurityError.invalidMimeType
        }
    }

    private func validateResponse(_ response: HTTPURLResponse) throws {
        // Check status code
        guard 200...299 ~= response.statusCode else {
            throw NetworkSecurityError.httpError(response.statusCode)
        }

        // Validate security headers in response
        let securityHeaders = [
            "X-Content-Type-Options",
            "X-Frame-Options",
            "X-XSS-Protection"
        ]

        for header in securityHeaders {
            if response.allHeaderFields[header] == nil {
                print("⚠️ Missing security header: \(header)")
            }
        }
    }

    private func executeWithRetry<T: Codable>(
        request: URLRequest,
        responseType: T.Type,
        maxRetries: Int = 3
    ) async throws -> T {

        var lastError: Error?

        for attempt in 0..<maxRetries {
            do {
                let (data, response) = try await session.data(for: request)

                guard let httpResponse = response as? HTTPURLResponse else {
                    throw NetworkSecurityError.invalidResponse
                }

                try validateResponse(httpResponse)

                // Validate response size
                guard data.count <= 10 * 1024 * 1024 else { // 10MB limit
                    throw NetworkSecurityError.responseTooLarge
                }

                let decoder = JSONDecoder()
                decoder.dateDecodingStrategy = .iso8601

                return try decoder.decode(responseType, from: data)

            } catch {
                lastError = error

                // Don't retry on certain errors
                if let networkError = error as? NetworkSecurityError {
                    switch networkError {
                    case .insecureScheme, .untrustedDomain, .invalidMimeType:
                        throw error
                    default:
                        break
                    }
                }

                // Wait before retry
                if attempt < maxRetries - 1 {
                    let delay = pow(2.0, Double(attempt)) // Exponential backoff
                    try await Task.sleep(nanoseconds: UInt64(delay * 1_000_000_000))
                }
            }
        }

        throw lastError ?? NetworkSecurityError.maxRetriesExceeded
    }

    private func startNetworkMonitoring() {
        monitor.pathUpdateHandler = { [weak self] path in
            DispatchQueue.main.async {
                self?.isConnected = path.status == .satisfied
                self?.connectionType = path.availableInterfaces.first?.type
            }
        }
        monitor.start(queue: queue)
    }
}

// MARK: - URLSessionDelegate for Certificate Pinning

extension SecureNetworkService: URLSessionDelegate {

    func urlSession(
        _ session: URLSession,
        didReceive challenge: URLAuthenticationChallenge,
        completionHandler: @escaping (URLSession.AuthChallengeDisposition, URLCredential?) -> Void
    ) {

        // Implement certificate pinning
        guard let serverTrust = challenge.protectionSpace.serverTrust else {
            completionHandler(.cancelAuthenticationChallenge, nil)
            return
        }

        // For now, use default evaluation
        // In production, implement proper certificate pinning
        let credential = URLCredential(trust: serverTrust)
        completionHandler(.useCredential, credential)
    }
}

// Note: HTTPMethod and NetworkSecurityError are defined in SecurityModels.swift

// MARK: - Extensions

extension Data {
    var sha256Hash: String {
        let hash = SHA256.hash(data: self)
        return hash.compactMap { String(format: "%02x", $0) }.joined()
    }
}

extension String {
    var sha256Hash: String {
        let data = Data(self.utf8)
        return data.sha256Hash
    }
}
