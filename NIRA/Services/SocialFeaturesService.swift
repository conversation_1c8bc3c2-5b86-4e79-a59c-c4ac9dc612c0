import Foundation
import SwiftUI
import Combine
import Social

@MainActor
class SocialFeaturesService: ObservableObject {
    static let shared = SocialFeaturesService()
    
    @Published var friends: [Friend] = []
    @Published var friendRequests: [FriendRequest] = []
    @Published var leaderboards: [Leaderboard] = []
    @Published var studyGroups: [StudyGroup] = []
    @Published var achievements: [SharedAchievement] = []
    @Published var socialFeed: [SocialFeedItem] = []
    @Published var isLoading: Bool = false
    
    private let userPreferencesService = UserPreferencesService.shared
    private let analyticsService = LearningAnalyticsService.shared
    private let curriculumService = CurriculumService.shared
    private var cancellables = Set<AnyCancellable>()
    
    private init() {
        setupSubscriptions()
        Task {
            await loadSocialData()
        }
    }
    
    // MARK: - Public Methods
    
    func refreshSocialData() async {
        isLoading = true
        await loadSocialData()
        isLoading = false
    }
    
    // MARK: - Friend Management
    
    func sendFriendRequest(to username: String) async -> Bool {
        // Send friend request to another user
        // This would integrate with backend API
        return true // Placeholder
    }
    
    func acceptFriendRequest(_ requestId: UUID) async -> Bool {
        // Accept incoming friend request
        if let index = friendRequests.firstIndex(where: { $0.id == requestId }) {
            let request = friendRequests[index]
            friendRequests.remove(at: index)
            
            // Add to friends list
            let newFriend = Friend(
                id: request.fromUserId,
                username: request.fromUsername,
                displayName: request.fromDisplayName,
                profileImageURL: request.profileImageURL,
                currentLanguage: request.currentLanguage,
                totalPoints: 0,
                currentStreak: 0,
                lastActive: Date(),
                mutualFriends: 0,
                isOnline: false
            )
            friends.append(newFriend)
            
            return true
        }
        return false
    }
    
    func declineFriendRequest(_ requestId: UUID) {
        friendRequests.removeAll { $0.id == requestId }
    }
    
    func removeFriend(_ friendId: UUID) {
        friends.removeAll { $0.id == friendId }
    }
    
    func getFriendProgress(_ friendId: UUID) -> FriendProgress? {
        guard let friend = friends.first(where: { $0.id == friendId }) else { return nil }
        
        // This would fetch actual progress data from backend
        return FriendProgress(
            friend: friend,
            languageProgress: [:], // Placeholder
            recentAchievements: [],
            weeklyActivity: [],
            compareWithUser: generateComparison(with: friend)
        )
    }
    
    // MARK: - Achievement Sharing
    
    func shareAchievement(_ achievement: LearningAchievement, to platforms: [SocialPlatform]) async -> Bool {
        let sharedAchievement = SharedAchievement(
            achievementId: achievement.id,
            title: achievement.name,
            description: achievement.description,
            iconName: achievement.iconName ?? "🏆",
            earnedAt: Date(),
            pointsReward: achievement.pointsReward,
            difficulty: achievement.difficulty,
            language: userPreferencesService.selectedLanguage,
            shareText: generateShareText(for: achievement)
        )
        
        achievements.append(sharedAchievement)
        
        // Share to social platforms
        for platform in platforms {
            await shareToSocialPlatform(achievement: sharedAchievement, platform: platform)
        }
        
        // Add to social feed
        let feedItem = SocialFeedItem(
            type: .achievement,
            userId: UUID(), // Current user ID
            username: "You",
            content: "Earned \(achievement.name)!",
            timestamp: Date(),
            language: userPreferencesService.selectedLanguage,
            metadata: ["achievementId": achievement.id.uuidString]
        )
        socialFeed.insert(feedItem, at: 0)
        
        return true
    }
    
    func shareProgress(message: String, to platforms: [SocialPlatform]) async -> Bool {
        let progress = curriculumService.getLanguageOverallProgress()
        let shareText = generateProgressShareText(progress: progress, customMessage: message)
        
        for platform in platforms {
            await shareProgressToSocialPlatform(text: shareText, platform: platform)
        }
        
        // Add to social feed
        let feedItem = SocialFeedItem(
            type: .progress,
            userId: UUID(), // Current user ID
            username: "You",
            content: message,
            timestamp: Date(),
            language: userPreferencesService.selectedLanguage,
            metadata: [
                "completedLessons": "\(progress.completedLessons)",
                "totalLessons": "\(progress.totalLessons)",
                "percentage": "\(Int(progress.progressPercentage * 100))"
            ]
        )
        socialFeed.insert(feedItem, at: 0)
        
        return true
    }
    
    // MARK: - Leaderboards
    
    func getLeaderboard(type: LeaderboardType, timeframe: LeaderboardTimeframe) -> Leaderboard? {
        return leaderboards.first { $0.type == type && $0.timeframe == timeframe }
    }
    
    func getUserRanking(in leaderboard: Leaderboard) -> LeaderboardEntry? {
        return leaderboard.entries.first { $0.isCurrentUser }
    }
    
    func refreshLeaderboards() async {
        // Fetch latest leaderboard data
        let newLeaderboards = await fetchLeaderboards()
        await MainActor.run {
            self.leaderboards = newLeaderboards
        }
    }
    
    // MARK: - Study Groups
    
    func createStudyGroup(name: String, description: String, language: Language, isPrivate: Bool) async -> StudyGroup? {
        let studyGroup = StudyGroup(
            name: name,
            description: description,
            language: language,
            createdBy: UUID(), // Current user ID
            memberCount: 1,
            isPrivate: isPrivate,
            createdAt: Date(),
            lastActivity: Date(),
            weeklyGoal: 50, // Default weekly goal
            currentWeekProgress: 0,
            tags: []
        )
        
        studyGroups.append(studyGroup)
        return studyGroup
    }
    
    func joinStudyGroup(_ groupId: UUID) async -> Bool {
        if let index = studyGroups.firstIndex(where: { $0.id == groupId }) {
            studyGroups[index].memberCount += 1
            return true
        }
        return false
    }
    
    func leaveStudyGroup(_ groupId: UUID) async -> Bool {
        if let index = studyGroups.firstIndex(where: { $0.id == groupId }) {
            studyGroups[index].memberCount -= 1
            if studyGroups[index].memberCount <= 0 {
                studyGroups.remove(at: index)
            }
            return true
        }
        return false
    }
    
    func getStudyGroupProgress(_ groupId: UUID) -> StudyGroupProgress? {
        guard let group = studyGroups.first(where: { $0.id == groupId }) else { return nil }
        
        // This would fetch actual group progress data
        return StudyGroupProgress(
            group: group,
            memberProgress: [], // Placeholder
            weeklyStats: StudyGroupWeeklyStats(
                totalLessonsCompleted: 45,
                totalTimeSpent: 1200,
                averageAccuracy: 0.85,
                mostActiveDay: .monday,
                topPerformer: "Alice"
            ),
            challenges: [],
            recentActivity: []
        )
    }
    
    // MARK: - Social Feed
    
    func loadSocialFeed() async {
        let feedItems = await fetchSocialFeedItems()
        await MainActor.run {
            self.socialFeed = feedItems
        }
    }
    
    func likeFeedItem(_ itemId: UUID) {
        if let index = socialFeed.firstIndex(where: { $0.id == itemId }) {
            socialFeed[index].likeCount += 1
            socialFeed[index].isLikedByUser = true
        }
    }
    
    func unlikeFeedItem(_ itemId: UUID) {
        if let index = socialFeed.firstIndex(where: { $0.id == itemId }) {
            socialFeed[index].likeCount = max(0, socialFeed[index].likeCount - 1)
            socialFeed[index].isLikedByUser = false
        }
    }
    
    func commentOnFeedItem(_ itemId: UUID, comment: String) {
        if let index = socialFeed.firstIndex(where: { $0.id == itemId }) {
            let newComment = SocialComment(
                userId: UUID(), // Current user ID
                username: "You",
                content: comment,
                timestamp: Date()
            )
            socialFeed[index].comments.append(newComment)
        }
    }
    
    // MARK: - Challenges
    
    func createChallenge(name: String, description: String, type: ChallengeType, duration: Int, participants: [UUID]) async -> SocialChallenge? {
        let challenge = SocialChallenge(
            name: name,
            description: description,
            type: type,
            createdBy: UUID(), // Current user ID
            participants: participants,
            startDate: Date(),
            endDate: Date().addingTimeInterval(TimeInterval(duration * 86400)),
            isActive: true,
            prize: "Achievement Badge",
            rules: generateChallengeRules(for: type)
        )
        
        return challenge
    }
    
    func joinChallenge(_ challengeId: UUID) async -> Bool {
        // Join an existing challenge
        return true // Placeholder
    }
    
    func getChallengeLeaderboard(_ challengeId: UUID) -> [ChallengeParticipant] {
        // Return challenge-specific leaderboard
        return [] // Placeholder
    }
    
    // MARK: - Private Methods
    
    private func setupSubscriptions() {
        // Listen for achievement updates to potentially share
        analyticsService.$userAchievements
            .sink { [weak self] achievements in
                // Check for new achievements that could be shared
                self?.checkForNewAchievements(achievements)
            }
            .store(in: &cancellables)
    }
    
    private func loadSocialData() async {
        await withTaskGroup(of: Void.self) { group in
            group.addTask { await self.loadFriends() }
            group.addTask { await self.loadFriendRequests() }
            group.addTask { await self.loadLeaderboards() }
            group.addTask { await self.loadStudyGroups() }
            group.addTask { await self.loadSocialFeed() }
        }
    }
    
    private func loadFriends() async {
        // Load friends from backend
        let mockFriends = [
            Friend(
                id: UUID(),
                username: "alice_learns",
                displayName: "Alice",
                profileImageURL: nil,
                currentLanguage: .french,
                totalPoints: 1250,
                currentStreak: 15,
                lastActive: Date().addingTimeInterval(-3600),
                mutualFriends: 3,
                isOnline: true
            ),
            Friend(
                id: UUID(),
                username: "bob_polyglot",
                displayName: "Bob",
                profileImageURL: nil,
                currentLanguage: .spanish,
                totalPoints: 890,
                currentStreak: 7,
                lastActive: Date().addingTimeInterval(-7200),
                mutualFriends: 1,
                isOnline: false
            )
        ]
        
        await MainActor.run {
            self.friends = mockFriends
        }
    }
    
    private func loadFriendRequests() async {
        // Load pending friend requests
        let mockRequests = [
            FriendRequest(
                fromUserId: UUID(),
                fromUsername: "charlie_student",
                fromDisplayName: "Charlie",
                profileImageURL: nil,
                currentLanguage: .japanese,
                message: "Let's learn together!",
                sentAt: Date().addingTimeInterval(-86400)
            )
        ]
        
        await MainActor.run {
            self.friendRequests = mockRequests
        }
    }
    
    private func loadLeaderboards() async {
        let mockLeaderboards = await fetchLeaderboards()
        await MainActor.run {
            self.leaderboards = mockLeaderboards
        }
    }
    
    private func loadStudyGroups() async {
        // Load user's study groups
        let mockGroups = [
            StudyGroup(
                name: "French Beginners",
                description: "Learning French together from scratch",
                language: .french,
                createdBy: UUID(),
                memberCount: 12,
                isPrivate: false,
                createdAt: Date().addingTimeInterval(-86400 * 30),
                lastActivity: Date().addingTimeInterval(-3600),
                weeklyGoal: 100,
                currentWeekProgress: 67,
                tags: ["beginner", "conversation", "grammar"]
            )
        ]
        
        await MainActor.run {
            self.studyGroups = mockGroups
        }
    }
    
    private func fetchLeaderboards() async -> [Leaderboard] {
        // Generate mock leaderboard data
        let weeklyPoints = Leaderboard(
            type: .points,
            timeframe: .weekly,
            title: "Weekly Points",
            entries: [
                LeaderboardEntry(
                    rank: 1,
                    userId: UUID(),
                    username: "alice_learns",
                    displayName: "Alice",
                    score: 1250,
                    change: .up(5),
                    isCurrentUser: false
                ),
                LeaderboardEntry(
                    rank: 2,
                    userId: UUID(),
                    username: "you",
                    displayName: "You",
                    score: 1100,
                    change: .up(2),
                    isCurrentUser: true
                ),
                LeaderboardEntry(
                    rank: 3,
                    userId: UUID(),
                    username: "bob_polyglot",
                    displayName: "Bob",
                    score: 890,
                    change: .down(1),
                    isCurrentUser: false
                )
            ]
        )
        
        return [weeklyPoints]
    }
    
    private func fetchSocialFeedItems() async -> [SocialFeedItem] {
        // Generate mock social feed
        return [
            SocialFeedItem(
                type: .achievement,
                userId: UUID(),
                username: "alice_learns",
                content: "Just earned the 'Grammar Master' achievement! 🎉",
                timestamp: Date().addingTimeInterval(-3600),
                language: .french,
                likeCount: 5,
                isLikedByUser: false,
                comments: [
                    SocialComment(
                        userId: UUID(),
                        username: "bob_polyglot",
                        content: "Congratulations! 👏",
                        timestamp: Date().addingTimeInterval(-1800)
                    )
                ],
                metadata: ["achievementId": UUID().uuidString]
            ),
            SocialFeedItem(
                type: .progress,
                userId: UUID(),
                username: "charlie_student",
                content: "Completed 50 lessons in Japanese! Halfway to my goal 📚",
                timestamp: Date().addingTimeInterval(-7200),
                language: .japanese,
                likeCount: 8,
                isLikedByUser: true,
                comments: [],
                metadata: ["completedLessons": "50", "totalLessons": "100"]
            )
        ]
    }
    
    private func generateComparison(with friend: Friend) -> UserComparison {
        // Generate comparison data between current user and friend
        return UserComparison(
            pointsDifference: friend.totalPoints - 1100, // Assuming user has 1100 points
            streakComparison: .behind(friend.currentStreak - 10), // Assuming user has 10 day streak
            languageComparison: friend.currentLanguage == userPreferencesService.selectedLanguage ? .same : .different,
            weeklyLessonsComparison: .ahead(3) // User completed 3 more lessons this week
        )
    }
    
    private func generateShareText(for achievement: LearningAchievement) -> String {
        let language = userPreferencesService.selectedLanguage.displayName
        return "🎉 Just earned '\(achievement.name)' in \(language) on NIRA! \(achievement.description) #LanguageLearning #NIRA"
    }
    
    private func generateProgressShareText(progress: LanguageProgress, customMessage: String) -> String {
        let percentage = Int(progress.progressPercentage * 100)
        return "\(customMessage) \n\n📊 \(percentage)% complete in \(progress.language.displayName) (\(progress.completedLessons)/\(progress.totalLessons) lessons) #LanguageLearning #NIRA"
    }
    
    private func shareToSocialPlatform(achievement: SharedAchievement, platform: SocialPlatform) async {
        switch platform {
        case .twitter:
            await shareToTwitter(text: achievement.shareText)
        case .facebook:
            await shareToFacebook(text: achievement.shareText)
        case .instagram:
            await shareToInstagram(text: achievement.shareText)
        case .linkedin:
            await shareToLinkedIn(text: achievement.shareText)
        }
    }
    
    private func shareProgressToSocialPlatform(text: String, platform: SocialPlatform) async {
        switch platform {
        case .twitter:
            await shareToTwitter(text: text)
        case .facebook:
            await shareToFacebook(text: text)
        case .instagram:
            await shareToInstagram(text: text)
        case .linkedin:
            await shareToLinkedIn(text: text)
        }
    }
    
    private func shareToTwitter(text: String) async {
        // Implement Twitter sharing
        // Note: SLServiceTypeTwitter is deprecated, use modern sharing APIs
        // if SLComposeViewController.isAvailable(forServiceType: SLServiceTypeTwitter) {
        //     // Use SLComposeViewController for Twitter sharing
        // }
    }
    
    private func shareToFacebook(text: String) async {
        // Implement Facebook sharing
        // Note: SLServiceTypeFacebook is deprecated, use modern sharing APIs
        // if SLComposeViewController.isAvailable(forServiceType: SLServiceTypeFacebook) {
        //     // Use SLComposeViewController for Facebook sharing
        // }
    }
    
    private func shareToInstagram(text: String) async {
        // Implement Instagram sharing (typically requires custom integration)
    }
    
    private func shareToLinkedIn(text: String) async {
        // Implement LinkedIn sharing
    }
    
    private func checkForNewAchievements(_ achievements: [UserLearningAchievement]) {
        // Check if there are new achievements that could be auto-shared
        // This could trigger notifications asking if user wants to share
    }
    
    private func generateChallengeRules(for type: ChallengeType) -> [String] {
        switch type {
        case .speed:
            return [
                "Complete lessons as quickly as possible",
                "Accuracy must be above 80%",
                "No hints allowed",
                "Duration: 1 week"
            ]
        case .accuracy:
            return [
                "Achieve highest accuracy score",
                "Minimum 10 lessons required",
                "Time is not a factor",
                "Duration: 2 weeks"
            ]
        case .endurance:
            return [
                "Complete the most lessons",
                "Quality over quantity",
                "Daily participation required",
                "Duration: 1 month"
            ]
        case .skill:
            return [
                "Focus on specific skill area",
                "Demonstrate improvement",
                "Peer evaluation included",
                "Duration: 2 weeks"
            ]
        case .mixed:
            return [
                "Combination of all skills",
                "Balanced scoring system",
                "Weekly milestones",
                "Duration: 1 month"
            ]
        }
    }
}

// MARK: - Supporting Models

struct Friend: Identifiable {
    let id: UUID
    let username: String
    let displayName: String
    let profileImageURL: URL?
    let currentLanguage: Language
    let totalPoints: Int
    let currentStreak: Int
    let lastActive: Date
    let mutualFriends: Int
    let isOnline: Bool
}

struct FriendRequest: Identifiable {
    let id = UUID()
    let fromUserId: UUID
    let fromUsername: String
    let fromDisplayName: String
    let profileImageURL: URL?
    let currentLanguage: Language
    let message: String?
    let sentAt: Date
}

struct FriendProgress {
    let friend: Friend
    let languageProgress: [Language: Double]
    let recentAchievements: [LearningAchievement]
    let weeklyActivity: [DayActivity]
    let compareWithUser: UserComparison
}

struct UserComparison {
    let pointsDifference: Int
    let streakComparison: StreakComparison
    let languageComparison: LanguageComparison
    let weeklyLessonsComparison: LessonComparison
}

enum StreakComparison {
    case ahead(Int)
    case behind(Int)
    case tied
}

enum LanguageComparison {
    case same
    case different
}

enum LessonComparison {
    case ahead(Int)
    case behind(Int)
    case tied
}

struct DayActivity {
    let date: Date
    let lessonsCompleted: Int
    let timeSpent: Int
    let pointsEarned: Int
}

struct Leaderboard: Identifiable {
    let id = UUID()
    let type: LeaderboardType
    let timeframe: LeaderboardTimeframe
    let title: String
    let entries: [LeaderboardEntry]
}

enum LeaderboardType {
    case points
    case streak
    case accuracy
    case lessons
    case timeSpent
}

enum LeaderboardTimeframe {
    case daily
    case weekly
    case monthly
    case allTime
}

struct LeaderboardEntry: Identifiable {
    let id = UUID()
    let rank: Int
    let userId: UUID
    let username: String
    let displayName: String
    let score: Int
    let change: RankChange
    let isCurrentUser: Bool
}

enum RankChange {
    case up(Int)
    case down(Int)
    case same
    case new
}

struct StudyGroup: Identifiable {
    let id = UUID()
    let name: String
    let description: String
    let language: Language
    let createdBy: UUID
    var memberCount: Int
    let isPrivate: Bool
    let createdAt: Date
    let lastActivity: Date
    let weeklyGoal: Int
    let currentWeekProgress: Int
    let tags: [String]
}

struct StudyGroupProgress {
    let group: StudyGroup
    let memberProgress: [StudyGroupMember]
    let weeklyStats: StudyGroupWeeklyStats
    let challenges: [StudyGroupChallenge]
    let recentActivity: [StudyGroupActivity]
}

struct StudyGroupMember {
    let userId: UUID
    let username: String
    let displayName: String
    let weeklyLessons: Int
    let weeklyPoints: Int
    let contributionScore: Double
}

struct StudyGroupWeeklyStats {
    let totalLessonsCompleted: Int
    let totalTimeSpent: Int
    let averageAccuracy: Double
    let mostActiveDay: DayOfWeek
    let topPerformer: String
}

enum DayOfWeek: CaseIterable {
    case monday, tuesday, wednesday, thursday, friday, saturday, sunday
}

struct StudyGroupChallenge {
    let id: UUID
    let name: String
    let description: String
    let startDate: Date
    let endDate: Date
    let participants: [UUID]
    let isActive: Bool
}

struct StudyGroupActivity {
    let userId: UUID
    let username: String
    let action: String
    let timestamp: Date
}

struct SharedAchievement: Identifiable {
    let id = UUID()
    let achievementId: UUID
    let title: String
    let description: String
    let iconName: String
    let earnedAt: Date
    let pointsReward: Int
    let difficulty: LearningAchievement.AchievementDifficulty
    let language: Language
    let shareText: String
}

struct SocialFeedItem: Identifiable {
    let id = UUID()
    let type: SocialFeedType
    let userId: UUID
    let username: String
    let content: String
    let timestamp: Date
    let language: Language
    var likeCount: Int = 0
    var isLikedByUser: Bool = false
    var comments: [SocialComment] = []
    let metadata: [String: String]
}

enum SocialFeedType {
    case achievement
    case progress
    case challenge
    case milestone
    case groupActivity
}

struct SocialComment: Identifiable {
    let id = UUID()
    let userId: UUID
    let username: String
    let content: String
    let timestamp: Date
}

struct SocialChallenge: Identifiable {
    let id = UUID()
    let name: String
    let description: String
    let type: ChallengeType
    let createdBy: UUID
    let participants: [UUID]
    let startDate: Date
    let endDate: Date
    let isActive: Bool
    let prize: String
    let rules: [String]
}

struct ChallengeParticipant: Identifiable {
    let id = UUID()
    let userId: UUID
    let username: String
    let displayName: String
    let score: Int
    let rank: Int
    let progress: Double
}

enum SocialPlatform: CaseIterable {
    case twitter
    case facebook
    case instagram
    case linkedin
    
    var displayName: String {
        switch self {
        case .twitter: return "Twitter"
        case .facebook: return "Facebook"
        case .instagram: return "Instagram"
        case .linkedin: return "LinkedIn"
        }
    }
    
    var icon: String {
        switch self {
        case .twitter: return "twitter"
        case .facebook: return "facebook"
        case .instagram: return "instagram"
        case .linkedin: return "linkedin"
        }
    }
} 