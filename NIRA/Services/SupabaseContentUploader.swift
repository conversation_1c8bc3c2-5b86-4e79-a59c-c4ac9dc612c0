import Foundation
import Supabase

@MainActor
class SupabaseContentUploader: ObservableObject {
    static let shared = SupabaseContentUploader()

    @Published var isUploading = false
    @Published var uploadProgress = 0.0
    @Published var uploadStatus = "Ready"
    @Published var uploadedCount = 0

    private let supabaseClient = NIRASupabaseClient.shared

    private init() {}

    // MARK: - Main Upload Methods

    func uploadGeneratedCourse(_ course: GeneratedCourse) async throws {
        isUploading = true
        uploadProgress = 0.0
        uploadStatus = "Starting upload..."
        uploadedCount = 0

        let totalLessons = course.totalLessons
        var uploadedLessons = 0

        // First, ensure language exists in database
        let languageId = try await ensureLanguageExists(course.language)

        for level in course.levels {
            uploadStatus = "Uploading \(level.level) level content..."

            for lesson in level.lessons {
                try await uploadLesson(lesson, languageId: languageId, level: level.level)
                uploadedLessons += 1
                uploadedCount = uploadedLessons
                uploadProgress = Double(uploadedLessons) / Double(totalLessons)

                // Small delay to avoid overwhelming the database
                try await Task.sleep(nanoseconds: 250_000_000)
            }
        }

        uploadStatus = "Upload complete! \(uploadedLessons) lessons uploaded."
        isUploading = false
    }

    func uploadLesson(_ lesson: GeneratedLesson, languageId: UUID, level: String) async throws {
        // Create content metadata from the lesson
        let _: [String: SupabaseAnyCodable] = [
            "vocabulary": SupabaseAnyCodable(lesson.vocabulary.map { vocab in
                [
                    "word": vocab.word,
                    "translation": vocab.translation,
                    "pronunciation": vocab.pronunciation,
                    "partOfSpeech": determinePartOfSpeech(vocab.word),
                    "example": generateExampleSentence(vocab.word),
                    "exampleTranslation": generateExampleTranslation(vocab.word, vocab.translation)
                ]
            }),
            "grammarPoints": SupabaseAnyCodable(lesson.grammarPoints.map { grammar in
                [
                    "rule": grammar.rule,
                    "explanation": grammar.explanation,
                    "examples": grammar.examples,
                    "tips": generateGrammarTips(grammar.rule)
                ]
            }),
            "dialogues": SupabaseAnyCodable(lesson.dialogues.map { dialogue in
                [
                    "speaker": dialogue.speaker,
                    "text": dialogue.text,
                    "translation": dialogue.translation,
                    "culturalNote": dialogue.culturalNote ?? ""
                ]
            }),
            "exercises": SupabaseAnyCodable([]),
            "culturalContext": SupabaseAnyCodable([
                "setting": extractScenarioFromTitle(lesson.title),
                "scenario": lesson.title,
                "doAndDonts": extractDoAndDonts(lesson.culturalContext.culturalNotes),
                "culturalNotes": lesson.culturalContext.culturalNotes
            ])
        ]

        // Create the SupabaseLesson
        let supabaseLesson = SupabaseLesson(
            id: UUID(),
            pathId: UUID(), // TODO: Get actual path ID from database
            title: lesson.title,
            description: lesson.description,
            lessonType: determineLessonType(from: lesson),
            difficultyLevel: getDifficultyNumber(from: level),
            estimatedDuration: 25,
            sequenceOrder: nil,
            learningObjectives: extractLearningObjectives(from: lesson),
            vocabularyFocus: lesson.vocabulary.map { $0.word },
            grammarConcepts: lesson.grammarPoints.map { $0.rule },
            culturalNotes: lesson.culturalContext.culturalNotes,
            prerequisiteLessons: [],
            contentMetadata: SupabaseAnyCodable([
                "vocabulary": lesson.vocabulary.map { vocab in
                    [
                        "word": vocab.word,
                        "translation": vocab.translation,
                        "partOfSpeech": vocab.partOfSpeech,
                        "context": vocab.example,
                        "difficulty": level.lowercased(),
                        "pronunciation": vocab.pronunciation,
                        "example": vocab.example,
                        "exampleTranslation": vocab.exampleTranslation
                    ]
                },
                "exercises": lesson.exercises.map { exercise in
                    [
                        "type": exercise.type,
                        "question": exercise.question,
                        "options": exercise.options ?? [],
                        "correctAnswer": exercise.correctAnswer ?? "",
                        "explanation": exercise.explanation ?? "",
                        "points": exercise.points
                    ]
                },
                "grammarPoints": lesson.grammarPoints.map { grammar in
                    [
                        "rule": grammar.rule,
                        "explanation": grammar.explanation,
                        "examples": grammar.examples,
                        "tips": grammar.tips
                    ]
                },
                "dialogues": lesson.dialogues.map { dialogue in
                    [
                        "speaker": dialogue.speaker,
                        "text": dialogue.text,
                        "translation": dialogue.translation,
                        "culturalNote": dialogue.culturalNote ?? ""
                    ]
                }
            ]),
            isActive: true,
            createdAt: Date(),
            updatedAt: Date(),
            audioUrl: nil,
            hasAudio: false,
            audioMetadata: nil
        )

        // Upload to Supabase
        try await uploadLessonToSupabase(supabaseLesson)
    }

    // MARK: - Database Helpers

    private func ensureLanguageExists(_ language: String) async throws -> UUID {
        // Check if language exists, create if not
        // This would query your languages table and return/create the ID
        return UUID() // Placeholder - implement based on your schema
    }

    private func ensureLevelExists(_ level: String) async throws -> UUID {
        // Check if level exists, create if not
        return UUID() // Placeholder - implement based on your schema
    }

    private func ensureTopicExists(_ topic: String) async throws -> UUID {
        // Check if topic exists, create if not
        return UUID() // Placeholder - implement based on your schema
    }

    private func uploadLessonToSupabase(_ lesson: SupabaseLesson) async throws {
        // Insert directly using the SupabaseLesson struct
        try await supabaseClient.client
            .from("lessons")
            .insert(lesson)
            .execute()
    }

    // MARK: - Conversion Helpers

    private func convertCorrectAnswer(_ exercise: GeneratedExercise) -> Int {
        guard let correctAnswer = exercise.correctAnswer else {
            // For exercise types without correctAnswer (matching, pronunciation), return 0
            return 0
        }
        return convertCorrectAnswerFromString(correctAnswer, options: exercise.options ?? [])
    }

    private func convertCorrectAnswerFromString(_ correctAnswer: String, options: [String]) -> Int {
        // If correctAnswer is an index, use it directly
        if let index = Int(correctAnswer) {
            return index
        }

        // Otherwise find the index of the correct answer string
        return options.firstIndex(of: correctAnswer) ?? 0
    }

    private func extractSkillFocus(from lesson: GeneratedLesson) -> [String] {
        var skills: [String] = []

        if !lesson.vocabulary.isEmpty {
            skills.append("vocabulary")
        }
        if !lesson.grammarPoints.isEmpty {
            skills.append("grammar")
        }
        if !lesson.dialogues.isEmpty {
            skills.append("conversation")
        }
        if !lesson.culturalContext.culturalNotes.isEmpty {
            skills.append("cultural_awareness")
        }

        return skills.isEmpty ? ["general"] : skills
    }

    private func extractLearningObjectives(from lesson: GeneratedLesson) -> [String] {
        var objectives: [String] = []

        if !lesson.vocabulary.isEmpty {
            objectives.append("Learn \(lesson.vocabulary.count) new vocabulary words")
        }
        if !lesson.grammarPoints.isEmpty {
            objectives.append("Master \(lesson.grammarPoints.count) grammar concepts")
        }
        if !lesson.dialogues.isEmpty {
            objectives.append("Practice conversation skills through dialogues")
        }
        if !lesson.culturalContext.culturalNotes.isEmpty {
            objectives.append("Understand cultural context and etiquette")
        }

        return objectives.isEmpty ? ["Complete the lesson activities"] : objectives
    }

    private func extractMainTopic(from lesson: GeneratedLesson) -> String {
        // Extract main topic from lesson title or content
        let title = lesson.title.lowercased()

        if title.contains("food") || title.contains("dining") || title.contains("restaurant") {
            return "Food & Dining"
        } else if title.contains("travel") || title.contains("transport") {
            return "Travel"
        } else if title.contains("family") || title.contains("personal") {
            return "Family & Personal"
        } else if title.contains("work") || title.contains("business") || title.contains("profession") {
            return "Work & Business"
        } else if title.contains("health") || title.contains("body") {
            return "Health & Wellness"
        } else if title.contains("greeting") || title.contains("introduction") {
            return "Basic Communication"
        } else {
            return "General Topics"
        }
    }

    private func getDifficultyNumber(from level: String) -> Int {
        switch level {
        case "A1": return 1
        case "A2": return 2
        case "B1": return 3
        case "B2": return 4
        case "C1": return 5
        case "C2": return 6
        default: return 1
        }
    }

    private func determineLessonType(from lesson: GeneratedLesson) -> String {
        let hasVocab = !lesson.vocabulary.isEmpty
        let hasGrammar = !lesson.grammarPoints.isEmpty
        let hasConversation = !lesson.dialogues.isEmpty
        let hasCultural = !lesson.culturalContext.culturalNotes.isEmpty

        if hasVocab && hasConversation && hasCultural {
            return "vocabulary_culture"
        } else if hasGrammar && hasConversation {
            return "grammar_conversation"
        } else if hasConversation {
            return "conversation"
        } else if hasVocab {
            return "vocabulary"
        } else if hasGrammar {
            return "grammar"
        } else {
            return "general"
        }
    }

    // MARK: - Helper Functions for Scalable Content Generation

    private func extractScenarioFromTitle(_ title: String) -> String {
        let lowercaseTitle = title.lowercased()
        if lowercaseTitle.contains("café") || lowercaseTitle.contains("restaurant") {
            return "dining"
        } else if lowercaseTitle.contains("travel") || lowercaseTitle.contains("airport") {
            return "travel"
        } else if lowercaseTitle.contains("work") || lowercaseTitle.contains("business") {
            return "business"
        } else if lowercaseTitle.contains("family") || lowercaseTitle.contains("home") {
            return "family"
        } else {
            return "general"
        }
    }

    private func extractDoAndDonts(_ culturalNotes: String) -> [String] {
        // Extract do's and don'ts from cultural notes
        let sentences = culturalNotes.components(separatedBy: ".")
        return sentences.compactMap { sentence in
            let trimmed = sentence.trimmingCharacters(in: .whitespaces)
            return trimmed.isEmpty ? nil : "Do: \(trimmed)"
        }.prefix(3).map { String($0) }
    }

    private func generateExampleSentence(_ word: String) -> String {
        // Generate a simple example sentence
        return "I use \(word) in my daily life."
    }

    private func generateExampleTranslation(_ word: String, _ translation: String) -> String {
        // Generate a simple example translation
        return "I use \(translation) in my daily life."
    }

    private func determinePartOfSpeech(_ word: String) -> String {
        // Simple heuristic for part of speech
        let lowercaseWord = word.lowercased()
        if lowercaseWord.hasSuffix("ing") || lowercaseWord.hasSuffix("er") || lowercaseWord.hasSuffix("ed") {
            return "verb"
        } else if lowercaseWord.hasSuffix("ly") {
            return "adverb"
        } else if lowercaseWord.hasSuffix("tion") || lowercaseWord.hasSuffix("ness") {
            return "noun"
        } else {
            return "noun" // default
        }
    }

    private func generateGrammarTips(_ rule: String) -> String {
        // Generate helpful tips for grammar rules
        let lowercaseRule = rule.lowercased()
        if lowercaseRule.contains("tense") {
            return "Practice with time expressions to master this tense"
        } else if lowercaseRule.contains("article") {
            return "Remember the gender and number agreement"
        } else if lowercaseRule.contains("verb") {
            return "Pay attention to subject-verb agreement"
        } else {
            return "Practice this rule in different contexts"
        }
    }

    private func generateCulturalNote(_ text: String) -> String? {
        // Generate cultural notes for dialogues
        let lowercaseText = text.lowercased()
        if lowercaseText.contains("bonjour") || lowercaseText.contains("hello") {
            return "Formal greetings are important in this culture"
        } else if lowercaseText.contains("please") || lowercaseText.contains("thank") {
            return "Politeness is highly valued"
        } else if lowercaseText.contains("excuse") || lowercaseText.contains("sorry") {
            return "Apologizing shows respect and courtesy"
        } else {
            return nil
        }
    }

    private func encodeContentMetadata(_ metadata: ContentMetadata?) throws -> [String: Any] {
        guard let metadata = metadata else { return [:] }

        let encoder = JSONEncoder()
        let data = try encoder.encode(metadata)
        guard let dict = try JSONSerialization.jsonObject(with: data) as? [String: Any] else {
            throw NSError(domain: "EncodingError", code: 1, userInfo: [NSLocalizedDescriptionKey: "Failed to encode content metadata"])
        }
        return dict
    }

    private func encodeLessonContent(_ content: SupabaseLessonContentData) throws -> [String: Any] {
        let encoder = JSONEncoder()
        let data = try encoder.encode(content)
        let json = try JSONSerialization.jsonObject(with: data, options: [])
        return json as? [String: Any] ?? [:]
    }

    private func encodeMetadata(_ metadata: SupabaseLessonMetadata) throws -> [String: Any] {
        let encoder = JSONEncoder()
        let data = try encoder.encode(metadata)
        let json = try JSONSerialization.jsonObject(with: data, options: [])
        return json as? [String: Any] ?? [:]
    }

    // MARK: - Batch Operations

    func batchUploadLessons(_ lessons: [GeneratedLesson], languageId: UUID, level: String) async throws {
        uploadStatus = "Batch uploading \(lessons.count) lessons..."

        for (index, lesson) in lessons.enumerated() {
            try await uploadLesson(lesson, languageId: languageId, level: level)
            uploadProgress = Double(index + 1) / Double(lessons.count)
            uploadedCount = index + 1

            // Delay between uploads
            try await Task.sleep(nanoseconds: 300_000_000)
        }

        uploadStatus = "Batch upload complete!"
    }

    // MARK: - Content Management

    func deleteAllGeneratedContent() async throws {
        uploadStatus = "Deleting all generated content..."

        try await supabaseClient.client
            .from("lessons")
            .delete()
            .neq("id", value: UUID().uuidString) // Delete all
            .execute()

        uploadStatus = "Content deleted successfully"
    }

    func updateLessonContent(_ lessonId: UUID, with newContent: GeneratedLesson) async throws {
        uploadStatus = "Updating lesson content..."

        let contentDict = try encodeLessonContent(SupabaseLessonContentData(
            introduction: newContent.description,
            vocabulary: newContent.vocabulary.map { vocab in
                SupabaseVocabularyItem(
                    word: vocab.word,
                    translation: vocab.translation,
                    partOfSpeech: vocab.partOfSpeech,
                    context: vocab.example,
                    difficulty: "intermediate",
                    pronunciation: vocab.pronunciation,
                    example: vocab.example,
                    exampleTranslation: vocab.exampleTranslation
                )
            },
            dialogues: newContent.dialogues.map { dialogue in
                SupabaseDialogueItem(
                    speaker: dialogue.speaker,
                    text: dialogue.text,
                    translation: dialogue.translation,
                    culturalNote: nil
                )
            },
            exercises: [], // No exercises in GeminiService GeneratedLesson model
            grammarPoints: newContent.grammarPoints.map { grammar in
                SupabaseGrammarPoint(
                    rule: grammar.rule,
                    explanation: grammar.explanation,
                    examples: grammar.examples,
                    tips: ""
                )
            }
        ))

        // Convert contentDict to JSON string
        let contentData = try JSONSerialization.data(withJSONObject: contentDict)
        let contentString = String(data: contentData, encoding: .utf8) ?? "{}"

        try await supabaseClient.client
            .from("lessons")
            .update([
                "content": contentString,
                "updated_at": ISO8601DateFormatter().string(from: Date())
            ])
            .eq("id", value: lessonId.uuidString)
            .execute()

        uploadStatus = "Lesson updated successfully"
    }
}

// MARK: - Extended Models for Upload
// Note: SupabaseExercisePair is defined in SupabaseModels.swift

// Extensions removed - types are now properly defined in SupabaseModels.swift

// Helper struct for proper Supabase insertion
struct SupabaseLessonInsert: Encodable {
    let id: String
    let title: String
    let description: String
    let content: [String: Any]
    let languageId: String
    let levelId: String
    let topicId: String
    let difficultyLevel: Int
    let estimatedDurationMinutes: Int
    let lessonType: String
    let isPublished: Bool
    let metadata: [String: Any]
    let createdAt: String
    let updatedAt: String

    enum CodingKeys: String, CodingKey {
        case id
        case title
        case description
        case content
        case languageId = "language_id"
        case levelId = "level_id"
        case topicId = "topic_id"
        case difficultyLevel = "difficulty_level"
        case estimatedDurationMinutes = "estimated_duration_minutes"
        case lessonType = "lesson_type"
        case isPublished = "is_published"
        case metadata
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }

    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(id, forKey: .id)
        try container.encode(title, forKey: .title)
        try container.encode(description, forKey: .description)

        // Convert content dictionary to JSON data then to string
        let contentData = try JSONSerialization.data(withJSONObject: content)
        let contentString = String(data: contentData, encoding: .utf8) ?? "{}"
        try container.encode(contentString, forKey: .content)

        try container.encode(languageId, forKey: .languageId)
        try container.encode(levelId, forKey: .levelId)
        try container.encode(topicId, forKey: .topicId)
        try container.encode(difficultyLevel, forKey: .difficultyLevel)
        try container.encode(estimatedDurationMinutes, forKey: .estimatedDurationMinutes)
        try container.encode(lessonType, forKey: .lessonType)
        try container.encode(isPublished, forKey: .isPublished)

        // Convert metadata dictionary to JSON data then to string
        let metadataData = try JSONSerialization.data(withJSONObject: metadata)
        let metadataString = String(data: metadataData, encoding: .utf8) ?? "{}"
        try container.encode(metadataString, forKey: .metadata)

        try container.encode(createdAt, forKey: .createdAt)
        try container.encode(updatedAt, forKey: .updatedAt)
    }
}