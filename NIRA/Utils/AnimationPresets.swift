//
//  AnimationPresets.swift
//  NIRA
//
//  Created by NIRA Team on 2025-01-29.
//

import SwiftUI

struct AnimationPresets {
    
    // MARK: - Spring Animations
    
    static let gentleSpring = Animation.spring(
        response: 0.6,
        dampingFraction: 0.8,
        blendDuration: 0
    )
    
    static let bouncySpring = Animation.spring(
        response: 0.4,
        dampingFraction: 0.6,
        blendDuration: 0
    )
    
    static let snappySpring = Animation.spring(
        response: 0.3,
        dampingFraction: 0.9,
        blendDuration: 0
    )
    
    static let elasticSpring = Animation.spring(
        response: 0.8,
        dampingFraction: 0.5,
        blendDuration: 0
    )
    
    // MARK: - Easing Animations
    
    static let smoothEaseInOut = Animation.easeInOut(duration: 0.4)
    static let quickEaseOut = Animation.easeOut(duration: 0.2)
    static let slowEaseIn = Animation.easeIn(duration: 0.6)
    
    // MARK: - Custom Timing Curves
    
    static let cardFlip = Animation.timingCurve(0.25, 0.46, 0.45, 0.94, duration: 0.6)
    static let slideIn = Animation.timingCurve(0.16, 1, 0.3, 1, duration: 0.5)
    static let fadeScale = Animation.timingCurve(0.25, 0.1, 0.25, 1, duration: 0.4)
    
    // MARK: - Delayed Animations
    
    static func staggered(delay: Double) -> Animation {
        return gentleSpring.delay(delay)
    }
    
    static func cascading(index: Int, baseDelay: Double = 0.1) -> Animation {
        return gentleSpring.delay(Double(index) * baseDelay)
    }
    
    // MARK: - Repeating Animations
    
    static let pulse = Animation.easeInOut(duration: 1.0).repeatForever(autoreverses: true)
    static let rotate = Animation.linear(duration: 2.0).repeatForever(autoreverses: false)
    static let breathe = Animation.easeInOut(duration: 2.0).repeatForever(autoreverses: true)
    
    // MARK: - Context-Specific Animations
    
    static let buttonPress = Animation.easeInOut(duration: 0.1)
    static let cardAppear = bouncySpring.delay(0.1)
    static let tabSwitch = snappySpring
    static let modalPresent = slideIn
    static let alertShow = elasticSpring
    static let progressUpdate = smoothEaseInOut
    static let achievementPop = Animation.spring(response: 0.5, dampingFraction: 0.6).delay(0.2)
}

// MARK: - SwiftUI View Extensions

extension View {
    func animatedAppearance(delay: Double = 0) -> some View {
        self.opacity(0)
            .scaleEffect(0.8)
            .onAppear {
                withAnimation(AnimationPresets.cardAppear.delay(delay)) {
                    // Animation will be handled by state changes
                }
            }
    }
    
    func pulseEffect(isActive: Bool) -> some View {
        self.scaleEffect(isActive ? 1.05 : 1.0)
            .animation(AnimationPresets.pulse, value: isActive)
    }
    
    func springyTap() -> some View {
        self.scaleEffect(1.0)
            .onTapGesture {
                withAnimation(AnimationPresets.buttonPress) {
                    // Scale effect will be handled by state
                }
                HapticFeedbackManager.shared.buttonTap()
            }
    }
    
    func cascadeIn(index: Int) -> some View {
        self.opacity(0)
            .offset(y: 20)
            .onAppear {
                withAnimation(AnimationPresets.cascading(index: index)) {
                    // Animation will be handled by state changes
                }
            }
    }
}

// MARK: - Animation State Manager

@Observable
class AnimationStateManager {
    var isVisible: Bool = false
    var scale: CGFloat = 1.0
    var offset: CGSize = .zero
    var rotation: Double = 0
    var opacity: Double = 1.0
    
    func reset() {
        isVisible = false
        scale = 1.0
        offset = .zero
        rotation = 0
        opacity = 1.0
    }
    
    func animateIn() {
        withAnimation(AnimationPresets.cardAppear) {
            isVisible = true
            opacity = 1.0
            scale = 1.0
            offset = .zero
        }
    }
    
    func animateOut() {
        withAnimation(AnimationPresets.smoothEaseInOut) {
            isVisible = false
            opacity = 0
            scale = 0.8
        }
    }
    
    func bounce() {
        withAnimation(AnimationPresets.bouncySpring) {
            scale = 1.1
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            withAnimation(AnimationPresets.gentleSpring) {
                self.scale = 1.0
            }
        }
    }
}
