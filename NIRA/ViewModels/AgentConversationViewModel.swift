import SwiftUI
import Foundation
import Combine

@MainActor
class AgentConversationViewModel: ObservableObject {
    @Published var messages: [AgentMessage] = []
    @Published var isConnected: Bool = false
    @Published var currentLanguage: String = "french"
    @Published var isLoading: Bool = false
    
    private var sessionId: UUID?
    private var communicationService: AgentCommunicationService?
    private var cancellables = Set<AnyCancellable>()
    
    init() {
        self.communicationService = AgentCommunicationService()
        setupCommunicationService()
    }
    
    private func setupCommunicationService() {
        guard let service = communicationService else { return }
        
        // Listen for incoming messages
        service.$receivedMessage
            .compactMap { $0 }
            .sink { [weak self] agentResponse in
                self?.handleIncomingMessage(agentResponse)
            }
            .store(in: &cancellables)
        
        // Listen for connection status
        service.$isConnected
            .sink { [weak self] connected in
                self?.isConnected = connected
            }
            .store(in: &cancellables)
    }
    
    func startConversation(language: String) async {
        currentLanguage = language
        messages.removeAll()
        
        do {
            let session = try await communicationService?.startConversation(
                agentTypes: ["tutor_\(language)"],
                language: language,
                scenarioType: nil
            )
            
            if let session = session {
                sessionId = session.sessionId
                
                // Load initial conversation history
                for turn in session.conversationHistory {
                    let message = AgentMessage(
                        content: turn.message,
                        isFromUser: turn.speakerType == "user",
                        agentName: turn.speakerType == "agent" ? getAgentDisplayName(from: turn.speakerId) : nil,
                        responseTime: turn.metadata?.responseTime
                    )
                    messages.append(message)
                }
                
                // No need for WebSocket - using direct API calls
            }
        } catch {
            print("Failed to start conversation: \(error)")
            // Add error message to chat
            let errorMessage = AgentMessage(
                content: "Sorry, I'm having trouble connecting. Please check your internet connection and try again.",
                isFromUser: false,
                agentName: "System"
            )
            messages.append(errorMessage)
        }
    }
    
    func sendMessage(_ text: String) async {
        guard let sessionId = sessionId else {
            print("No active session")
            return
        }
        
        // Add user message to chat immediately
        let userMessage = AgentMessage(content: text, isFromUser: true)
        messages.append(userMessage)
        
        do {
            // Send message to backend
            let response = try await communicationService?.sendMessage(
                sessionId: sessionId,
                message: text
            )
            
            // Add agent response directly (no WebSocket needed)
            if let response = response {
                let agentMessage = AgentMessage(
                    content: response.agentResponse.message,
                    isFromUser: false,
                    agentName: getAgentDisplayName(from: response.agentResponse.agentId),
                    responseTime: response.agentResponse.metadata?.responseTime
                )
                messages.append(agentMessage)
            }
        } catch {
            print("Failed to send message: \(error)")
            // Add error message
            let errorMessage = AgentMessage(
                content: "Sorry, I couldn't process your message. Please try again.",
                isFromUser: false,
                agentName: "System"
            )
            messages.append(errorMessage)
        }
    }
    
    // WebSocket connection removed - using direct API calls only
    
    private func handleIncomingMessage(_ response: AgentMessageResponse) {
        let agentMessage = AgentMessage(
            content: response.agentResponse.message,
            isFromUser: false,
            agentName: getAgentDisplayName(from: response.agentResponse.agentId),
            responseTime: response.agentResponse.metadata?.responseTime
        )
        messages.append(agentMessage)
    }
    
    private func getAgentDisplayName(from agentId: String) -> String {
        switch agentId {
        case "tutor_french":
            return "Marie"
        case "tutor_spanish":
            return "Carlos"
        case "conversation_partner_french":
            return "Pierre"
        case "cultural_guide_french":
            return "Amélie"
        default:
            return "AI Tutor"
        }
    }
    
    deinit {
        // No cleanup needed - using direct API calls only
    }
}

// MARK: - Response Models

struct AgentConversationResponse: Codable {
    let sessionId: UUID
    let agents: [String]
    let language: String
    let scenarioType: String?
    let conversationHistory: [ConversationTurnResponse]
    let isActive: Bool
}

struct ConversationTurnResponse: Codable {
    let id: UUID
    let speakerType: String
    let speakerId: String
    let message: String
    let timestamp: Date
    let metadata: TurnMetadata?
}

struct TurnMetadata: Codable {
    let agentPersonality: String?
    let responseTime: Double?
    let confidence: Double?
    let culturalContext: String?
    let corrections: [String]?
    let learningPoints: [String]?
}

struct AgentMessageResponse: Codable {
    let sessionId: UUID
    let userMessage: String
    let agentResponse: AgentResponseData
}

struct AgentResponseData: Codable {
    let agentId: String
    let agentType: String
    let message: String
    let timestamp: Date
    let metadata: TurnMetadata?
} 