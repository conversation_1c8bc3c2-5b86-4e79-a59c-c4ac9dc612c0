import SwiftUI

struct ScenarioSelectorView: View {
    @Binding var selectedScenario: LearningScenario
    @Environment(\.dismiss) private var dismiss
    
    let language: Language
    
    @State private var selectedType: LearningScenario.ScenarioType = .conversation
    @State private var selectedDifficulty: LearningScenario.DifficultyLevel = .beginner
    @State private var customObjectives: [String] = []
    @State private var newObjective = ""
    @State private var culturalContext = ""
    
    var body: some View {
        NavigationView {
            Form {
                Section("Scenario Type") {
                    ForEach(LearningScenario.ScenarioType.allCases, id: \.self) { type in
                        ScenarioTypeRow(
                            type: type,
                            isSelected: selectedType == type
                        ) {
                            selectedType = type
                        }
                    }
                }
                
                Section("Difficulty Level") {
                    Picker("Difficulty", selection: $selectedDifficulty) {
                        ForEach(LearningScenario.DifficultyLevel.allCases, id: \.self) { level in
                            Text(level.displayName).tag(level)
                        }
                    }
                    .pickerStyle(SegmentedPickerStyle())
                }
                
                Section("Learning Objectives") {
                    ForEach(defaultObjectives, id: \.self) { objective in
                        Text(objective)
                            .foregroundColor(.secondary)
                    }
                    
                    ForEach(customObjectives.indices, id: \.self) { index in
                        HStack {
                            Text(customObjectives[index])
                            Spacer()
                            Button("Remove") {
                                customObjectives.remove(at: index)
                            }
                            .foregroundColor(.red)
                        }
                    }
                    
                    HStack {
                        TextField("Add custom objective", text: $newObjective)
                        Button("Add") {
                            if !newObjective.isEmpty {
                                customObjectives.append(newObjective)
                                newObjective = ""
                            }
                        }
                        .disabled(newObjective.isEmpty)
                    }
                }
                
                if selectedType == .culturalSimulation {
                    Section("Cultural Context") {
                        TextField("Describe the cultural setting", text: $culturalContext, axis: .vertical)
                            .lineLimit(3...6)
                    }
                }
                
                Section("Preview") {
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Scenario: \(selectedType.displayName)")
                            .fontWeight(.semibold)
                        
                        Text("Difficulty: \(selectedDifficulty.displayName)")
                            .foregroundColor(.secondary)
                        
                        Text("Language: \(language.displayName)")
                            .foregroundColor(.secondary)
                        
                        if !allObjectives.isEmpty {
                            Text("Objectives:")
                                .fontWeight(.medium)
                            ForEach(allObjectives.prefix(3), id: \.self) { objective in
                                Text("• \(objective)")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                            if allObjectives.count > 3 {
                                Text("... and \(allObjectives.count - 3) more")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }
                    }
                    .padding(.vertical, 4)
                }
            }
            .navigationTitle("Select Learning Scenario")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Start") {
                        applyScenario()
                        dismiss()
                    }
                    .fontWeight(.semibold)
                }
            }
        }
        .onAppear {
            loadCurrentScenario()
        }
    }
    
    // MARK: - Computed Properties
    
    private var defaultObjectives: [String] {
        switch selectedType {
        case .conversation:
            return [
                "Practice natural conversation flow",
                "Improve speaking confidence",
                "Learn common expressions"
            ]
        case .culturalSimulation:
            return [
                "Understand cultural context",
                "Practice appropriate responses",
                "Learn cultural etiquette"
            ]
        case .grammarFocus:
            return [
                "Master specific grammar rules",
                "Practice correct usage",
                "Identify common mistakes"
            ]
        case .vocabularyBuilding:
            return [
                "Learn new vocabulary",
                "Practice word usage",
                "Build topic-specific vocabulary"
            ]
        case .pronunciationPractice:
            return [
                "Improve pronunciation accuracy",
                "Practice difficult sounds",
                "Develop natural rhythm"
            ]
        case .comprehensiveAssessment:
            return [
                "Evaluate current level",
                "Identify strengths and weaknesses",
                "Get personalized recommendations"
            ]
        }
    }
    
    private var allObjectives: [String] {
        return defaultObjectives + customObjectives
    }
    
    // MARK: - Actions
    
    private func loadCurrentScenario() {
        selectedType = selectedScenario.type
        selectedDifficulty = selectedScenario.difficulty
        culturalContext = selectedScenario.culturalContext ?? ""
        
        // Extract custom objectives (those not in default list)
        customObjectives = selectedScenario.objectives.filter { objective in
            !defaultObjectives.contains(objective)
        }
    }
    
    private func applyScenario() {
        selectedScenario = LearningScenario(
            type: selectedType,
            difficulty: selectedDifficulty,
            objectives: allObjectives,
            culturalContext: culturalContext.isEmpty ? nil : culturalContext
        )
    }
}

// MARK: - Supporting Views

struct ScenarioTypeRow: View {
    let type: LearningScenario.ScenarioType
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    HStack {
                        Image(systemName: type.icon)
                            .foregroundColor(type.color)
                            .frame(width: 24)
                        
                        Text(type.displayName)
                            .fontWeight(.medium)
                            .foregroundColor(.primary)
                        
                        Spacer()
                        
                        if isSelected {
                            Image(systemName: "checkmark.circle.fill")
                                .foregroundColor(.blue)
                        }
                    }
                    
                    Text(type.description)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.leading)
                }
            }
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Extensions

extension LearningScenario.ScenarioType: CaseIterable {
    public static var allCases: [LearningScenario.ScenarioType] {
        return [.conversation, .culturalSimulation, .grammarFocus, .vocabularyBuilding, .pronunciationPractice, .comprehensiveAssessment]
    }
    
    var displayName: String {
        switch self {
        case .conversation: return "Conversation Practice"
        case .culturalSimulation: return "Cultural Simulation"
        case .grammarFocus: return "Grammar Focus"
        case .vocabularyBuilding: return "Vocabulary Building"
        case .pronunciationPractice: return "Pronunciation Practice"
        case .comprehensiveAssessment: return "Comprehensive Assessment"
        }
    }
    
    var description: String {
        switch self {
        case .conversation:
            return "Practice natural conversations with AI partners"
        case .culturalSimulation:
            return "Learn through immersive cultural scenarios"
        case .grammarFocus:
            return "Focus on specific grammar rules and patterns"
        case .vocabularyBuilding:
            return "Expand your vocabulary with targeted practice"
        case .pronunciationPractice:
            return "Improve pronunciation with speech coaching"
        case .comprehensiveAssessment:
            return "Get a complete evaluation of your skills"
        }
    }
    
    var icon: String {
        switch self {
        case .conversation: return "bubble.left.and.bubble.right"
        case .culturalSimulation: return "globe"
        case .grammarFocus: return "book"
        case .vocabularyBuilding: return "textbook"
        case .pronunciationPractice: return "waveform"
        case .comprehensiveAssessment: return "chart.bar.doc.horizontal"
        }
    }
    
    var color: Color {
        switch self {
        case .conversation: return .blue
        case .culturalSimulation: return .purple
        case .grammarFocus: return .green
        case .vocabularyBuilding: return .orange
        case .pronunciationPractice: return .pink
        case .comprehensiveAssessment: return .indigo
        }
    }
}

extension LearningScenario.DifficultyLevel: CaseIterable {
    public static var allCases: [LearningScenario.DifficultyLevel] {
        return [.beginner, .intermediate, .advanced]
    }
    
    var displayName: String {
        switch self {
        case .beginner: return "Beginner"
        case .intermediate: return "Intermediate"
        case .advanced: return "Advanced"
        }
    }
}

#Preview {
    ScenarioSelectorView(
        selectedScenario: .constant(LearningScenario(
            type: .conversation,
            difficulty: .beginner,
            objectives: ["Practice conversation"],
            culturalContext: nil
        )),
        language: .spanish
    )
}
