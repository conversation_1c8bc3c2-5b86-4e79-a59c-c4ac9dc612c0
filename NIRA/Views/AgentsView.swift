//
//  AgentsView.swift
//  NIRA
//
//  Created by NIRA Team on 2025-01-29.
//

import SwiftUI

struct AgentsView: View {
    @StateObject private var userPreferences = UserPreferencesService.shared
    @State private var selectedPersona: AgentPersona? = nil
    @State private var searchText = ""
    @State private var showingAgentDetail = false
    @State private var selectedAgent: LearningAgent?
    @State private var animateCards = false

    private var filteredAgents: [LearningAgent] {
        var agents = LearningAgent.getAgents(for: userPreferences.selectedLanguage)

        if let selectedPersona = selectedPersona {
            agents = agents.filter { $0.persona == selectedPersona }
        }

        if !searchText.isEmpty {
            agents = agents.filter {
                $0.name.localizedCaseInsensitiveContains(searchText) ||
                $0.persona.rawValue.localizedCaseInsensitiveContains(searchText)
            }
        }

        return agents
    }

    var body: some View {
        VStack(spacing: 0) {
            // Modern Header
            ModernAgentsHeader(
                selectedLanguage: userPreferences.selectedLanguage.displayName,
                searchText: $searchText
            )

            // Filter Controls
            filterControlsView

            // Main Content
            mainContentSection
        }
        .background(Color(.systemGroupedBackground))
        .navigationBarHidden(true)
        .sheet(isPresented: $showingAgentDetail) {
            if let agent = selectedAgent {
                AgentDetailView(agent: agent)
            }
        }
        .onAppear {
            withAnimation(.easeInOut(duration: 0.6).delay(0.2)) {
                animateCards = true
            }
        }
        .onChange(of: userPreferences.selectedLanguage) { oldValue, newValue in
            // Reset animation when language changes
            animateCards = false
            withAnimation(.easeInOut(duration: 0.6).delay(0.1)) {
                animateCards = true
            }
        }
    }

    // MARK: - Filter Controls

    private var filterControlsView: some View {
        VStack(spacing: 16) {
            // Persona Filters
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    FilterChip(
                        title: "All Companions",
                        isSelected: selectedPersona == nil,
                        color: .gray
                    ) {
                        selectedPersona = nil
                    }

                    ForEach(AgentPersona.allCases, id: \.self) { persona in
                        FilterChip(
                            title: persona.rawValue,
                            icon: persona.icon,
                            isSelected: selectedPersona == persona,
                            color: persona.color
                        ) {
                            selectedPersona = selectedPersona == persona ? nil : persona
                        }
                    }
                }
                .padding(.horizontal, 20)
            }
        }
        .padding(.vertical, 16)
        .background(
            LinearGradient(
                colors: [Color(.systemBackground), Color(.systemGray6).opacity(0.3)],
                startPoint: .top,
                endPoint: .bottom
            )
        )
    }

    // MARK: - Main Content Section
    private var mainContentSection: some View {
        ScrollView {
            if filteredAgents.isEmpty {
                emptyStateView
            } else {
                LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 16), count: 1), spacing: 16) {
                    ForEach(Array(filteredAgents.enumerated()), id: \.element.id) { index, agent in
                        ModernAgentCard(agent: agent) {
                            selectedAgent = agent
                            showingAgentDetail = true
                        }
                        .scaleEffect(animateCards ? 1.0 : 0.8)
                        .opacity(animateCards ? 1.0 : 0.0)
                        .animation(
                            .easeInOut(duration: 0.4)
                            .delay(Double(index) * 0.1),
                            value: animateCards
                        )
                    }
                }
                .padding()
            }
        }
    }

    // MARK: - Empty State
    private var emptyStateView: some View {
        VStack(spacing: 20) {
            Image(systemName: "person.3.sequence.fill")
                .font(.system(size: 60))
                .foregroundColor(.niraThemeIndigo.opacity(0.6))

            Text("No Agents Found")
                .font(.title2)
                .fontWeight(.semibold)
                .foregroundColor(.primary)

            Text("Try adjusting your filters or search terms")
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)

            Button("Clear Filters") {
                selectedPersona = nil
                searchText = ""
            }
            .font(.headline)
            .foregroundColor(.white)
            .padding(.horizontal, 24)
            .padding(.vertical, 12)
            .background(Color.niraThemeIndigo.gradient)
            .cornerRadius(12)
        }
        .padding()
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
}

// MARK: - Supporting Views



#Preview {
    AgentsView()
}
