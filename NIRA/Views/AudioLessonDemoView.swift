import SwiftUI

struct AudioLessonDemoView: View {
    @State private var lessons: [SupabaseLesson] = []
    @State private var isLoading = false
    @State private var errorMessage: String?
    
    private let supabaseClient = NIRASupabaseClient.shared
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // Header
                VStack(spacing: 8) {
                    Text("🎙️ Audio-Enhanced Lessons")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .foregroundColor(.niraPrimary)
                    
                    Text("Tamil A1 lessons with ElevenLabs audio")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                .padding(.top)
                
                if isLoading {
                    ProgressView("Loading lessons...")
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                } else if let errorMessage = errorMessage {
                    VStack(spacing: 16) {
                        Image(systemName: "exclamationmark.triangle")
                            .font(.largeTitle)
                            .foregroundColor(.orange)
                        
                        Text("Error Loading Lessons")
                            .font(.headline)
                        
                        Text(errorMessage)
                            .font(.body)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                        
                        Button("Retry") {
                            loadTamilLessons()
                        }
                        .buttonStyle(.borderedProminent)
                    }
                    .padding()
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                } else if lessons.isEmpty {
                    VStack(spacing: 16) {
                        Image(systemName: "book.closed")
                            .font(.largeTitle)
                            .foregroundColor(.gray)
                        
                        Text("No Audio Lessons Found")
                            .font(.headline)
                        
                        Text("Tamil A1 lessons with audio are not available yet.")
                            .font(.body)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                        
                        Button("Load Lessons") {
                            loadTamilLessons()
                        }
                        .buttonStyle(.borderedProminent)
                    }
                    .padding()
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                } else {
                    // Lessons list
                    ScrollView {
                        LazyVStack(spacing: 16) {
                            ForEach(lessons) { lesson in
                                AudioLessonCard(lesson: lesson)
                            }
                        }
                        .padding()
                    }
                }
            }
            .navigationBarHidden(true)
            .onAppear {
                loadTamilLessons()
            }
        }
    }
    
    private func loadTamilLessons() {
        isLoading = true
        errorMessage = nil
        
        Task {
            do {
                let allLessons = try await supabaseClient.getLessons(language: "Tamil")
                
                // Filter for lessons with audio
                let audioLessons = allLessons.filter { $0.hasAudio == true }
                
                await MainActor.run {
                    self.lessons = audioLessons
                    self.isLoading = false
                    
                    if audioLessons.isEmpty {
                        self.errorMessage = "No audio lessons found for Tamil"
                    }
                }
            } catch {
                await MainActor.run {
                    self.isLoading = false
                    self.errorMessage = "Failed to load lessons: \(error.localizedDescription)"
                }
            }
        }
    }
}

struct AudioLessonCard: View {
    let lesson: SupabaseLesson
    @State private var isExpanded = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Header
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(lesson.title)
                        .font(.headline)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)
                    
                    if let description = lesson.description {
                        Text(description)
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .lineLimit(isExpanded ? nil : 2)
                    }
                }
                
                Spacer()
                
                // Audio indicator
                if lesson.hasAudio == true {
                    VStack(spacing: 4) {
                        Image(systemName: "speaker.wave.2.fill")
                            .font(.title2)
                            .foregroundColor(.niraPrimary)
                        
                        Text("Audio")
                            .font(.caption)
                            .foregroundColor(.niraPrimary)
                    }
                }
            }
            
            // Learning objectives
            if let objectives = lesson.learningObjectives, !objectives.isEmpty {
                VStack(alignment: .leading, spacing: 8) {
                    Text("Learning Objectives:")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                    
                    ForEach(objectives.prefix(isExpanded ? objectives.count : 2), id: \.self) { objective in
                        HStack(alignment: .top, spacing: 8) {
                            Text("•")
                                .foregroundColor(.niraPrimary)
                                .fontWeight(.bold)
                            
                            Text(objective)
                                .font(.body)
                                .foregroundColor(.secondary)
                        }
                    }
                    
                    if objectives.count > 2 && !isExpanded {
                        Button("Show \(objectives.count - 2) more...") {
                            withAnimation(.easeInOut) {
                                isExpanded = true
                            }
                        }
                        .font(.caption)
                        .foregroundColor(.niraPrimary)
                    }
                }
            }
            
            // Cultural notes
            if let culturalNotes = lesson.culturalNotes, !culturalNotes.isEmpty {
                VStack(alignment: .leading, spacing: 8) {
                    Text("Cultural Context:")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                    
                    Text(culturalNotes)
                        .font(.body)
                        .foregroundColor(.secondary)
                        .lineLimit(isExpanded ? nil : 3)
                }
            }
            
            // Audio player
            if lesson.hasAudio == true, let audioUrl = lesson.audioUrl {
                LessonAudioPlayer(
                    audioUrl: audioUrl,
                    audioMetadata: lesson.audioMetadata?.value as? [String: Any]
                )
            }
            
            // Expand/Collapse button
            Button(action: {
                withAnimation(.easeInOut) {
                    isExpanded.toggle()
                }
            }) {
                HStack {
                    Text(isExpanded ? "Show Less" : "Show More")
                        .font(.caption)
                        .fontWeight(.medium)
                    
                    Image(systemName: isExpanded ? "chevron.up" : "chevron.down")
                        .font(.caption)
                }
                .foregroundColor(.niraPrimary)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
                .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
        )
    }
}

#Preview {
    AudioLessonDemoView()
}
