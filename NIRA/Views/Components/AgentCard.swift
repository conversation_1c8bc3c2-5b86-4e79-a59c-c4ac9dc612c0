//
//  AgentCard.swift
//  NIRA
//
//  Created by NIRA Team on 2025-01-29.
//

import SwiftUI

struct AgentCard: View {
    let agent: LearningAgent
    let action: () -> Void
    @State private var isPressed = false
    @State private var showFeatures = false

    var body: some View {
        Button(action: action) {
            VStack(alignment: .leading, spacing: 12) {
                // Header with Avatar and Tier
                headerSection

                // Agent Info
                agentInfoSection

                // Features Preview
                featuresSection

                // Action Button
                actionButton
            }
            .padding(16)
            .frame(height: 320)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(Color(.systemBackground))
                    .shadow(
                        color: agent.persona.color.opacity(isPressed ? 0.3 : 0.15),
                        radius: isPressed ? 12 : 8,
                        x: 0,
                        y: isPressed ? 6 : 4
                    )
            )
            .overlay(
                RoundedRectangle(cornerRadius: 20)
                    .stroke(
                        LinearGradient(
                            colors: [agent.persona.color.opacity(0.3), agent.tier.color.opacity(0.2)],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ),
                        lineWidth: 1
                    )
            )
            .scaleEffect(isPressed ? 0.95 : 1.0)
            .animation(.easeInOut(duration: 0.15), value: isPressed)
        }
        .buttonStyle(PlainButtonStyle())
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            isPressed = pressing
        }, perform: {})
    }

    // MARK: - Header Section
    private var headerSection: some View {
        HStack {
            // Avatar with Persona Color Background
            ZStack {
                Circle()
                    .fill(agent.persona.color.gradient)
                    .frame(width: 50, height: 50)

                Text(agent.avatar)
                    .font(.title2)
            }

            Spacer()
        }
    }

    // MARK: - Agent Info Section
    private var agentInfoSection: some View {
        VStack(alignment: .leading, spacing: 6) {
            // Name
            Text(agent.name)
                .font(.headline)
                .fontWeight(.bold)
                .foregroundColor(.primary)
                .lineLimit(1)

            // Persona
            Text(agent.persona.rawValue)
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(agent.persona.color)
                .lineLimit(1)

            // Description
            Text(agent.description)
                .font(.caption)
                .foregroundColor(.secondary)
                .lineLimit(3)
                .multilineTextAlignment(.leading)
        }
    }

    // MARK: - Features Section
    private var featuresSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            // Features Header
            HStack {
                Text("Available Features")
                    .font(.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)

                Spacer()

                Button(action: {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        showFeatures.toggle()
                    }
                }) {
                    Image(systemName: showFeatures ? "chevron.up" : "chevron.down")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }

            // Features List
            if showFeatures {
                VStack(alignment: .leading, spacing: 4) {
                    ForEach(agent.availableFeatures.prefix(3), id: \.self) { feature in
                        HStack(spacing: 6) {
                            Circle()
                                .fill(agent.tier.color)
                                .frame(width: 4, height: 4)

                            Text(feature)
                                .font(.caption2)
                                .foregroundColor(.secondary)
                                .lineLimit(1)

                            Spacer()
                        }
                    }

                    if agent.availableFeatures.count > 3 {
                        Text("+ \(agent.availableFeatures.count - 3) more")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                            .padding(.leading, 10)
                    }
                }
                .transition(.opacity.combined(with: .move(edge: .top)))
            } else {
                // Compact Features Preview
                HStack(spacing: 4) {
                    ForEach(agent.availableFeatures.prefix(2), id: \.self) { feature in
                        Text(feature.components(separatedBy: " ").first ?? "")
                            .font(.caption2)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(agent.tier.color.opacity(0.1))
                            .foregroundColor(agent.tier.color)
                            .cornerRadius(4)
                    }

                    if agent.availableFeatures.count > 2 {
                        Text("+\(agent.availableFeatures.count - 2)")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }

                    Spacer()
                }
            }
        }
    }

    // MARK: - Action Button
    private var actionButton: some View {
        HStack {
            Image(systemName: "message.fill")
                .font(.caption)

            Text("Start Learning")
                .font(.caption)
                .fontWeight(.semibold)
        }
        .foregroundColor(.white)
        .frame(maxWidth: .infinity)
        .padding(.vertical, 10)
        .background(
            LinearGradient(
                colors: [agent.persona.color, agent.tier.color],
                startPoint: .leading,
                endPoint: .trailing
            )
        )
        .cornerRadius(12)
    }
}

// MARK: - Agent Detail View
struct AgentDetailView: View {
    let agent: LearningAgent
    @Environment(\.presentationMode) var presentationMode
    @State private var showingChat = false

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 24) {
                    // Hero Section
                    heroSection

                    // Specialties Section
                    specialtiesSection

                    // Features Section
                    featuresSection

                    // Language Info Section
                    languageInfoSection

                    // Action Buttons
                    actionButtonsSection
                }
                .padding()
            }
            .navigationTitle(agent.name)
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
            }
        }
        .sheet(isPresented: $showingChat) {
            AgentChatView(agent: agent)
        }
    }

    // MARK: - Hero Section
    private var heroSection: some View {
        VStack(spacing: 16) {
            // Large Avatar
            ZStack {
                Circle()
                    .fill(agent.persona.color.gradient)
                    .frame(width: 100, height: 100)

                Text(agent.avatar)
                    .font(.system(size: 40))
            }

            // Agent Info
            VStack(spacing: 8) {
                Text(agent.persona.rawValue)
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(agent.persona.color)

                Text(agent.description)
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)

                HStack(spacing: 4) {
                    Image(systemName: "globe")
                        .font(.caption)
                    Text(agent.language.displayName)
                        .font(.caption)
                        .fontWeight(.medium)
                }
                .foregroundColor(.secondary)
            }
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(agent.persona.color.opacity(0.05))
        )
    }

    // MARK: - Specialties Section
    private var specialtiesSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Specialties")
                .font(.headline)
                .fontWeight(.bold)

            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                ForEach(agent.specialties, id: \.self) { specialty in
                    HStack {
                        Image(systemName: "star.fill")
                            .font(.caption)
                            .foregroundColor(agent.persona.color)

                        Text(specialty)
                            .font(.subheadline)
                            .foregroundColor(.primary)

                        Spacer()
                    }
                    .padding()
                    .background(Color(.systemGray6))
                    .cornerRadius(12)
                }
            }
        }
    }

    // MARK: - Features Section
    private var featuresSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Available Features")
                .font(.headline)
                .fontWeight(.bold)

            VStack(alignment: .leading, spacing: 8) {
                ForEach(agent.availableFeatures, id: \.self) { feature in
                    HStack(spacing: 12) {
                        Image(systemName: "checkmark.circle.fill")
                            .foregroundColor(agent.tier.color)

                        Text(feature)
                            .font(.subheadline)
                            .foregroundColor(.primary)

                        Spacer()
                    }
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(agent.tier.color.opacity(0.05))
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(agent.tier.color.opacity(0.2), lineWidth: 1)
                    )
            )
        }
    }

    // MARK: - Language Info Section
    private var languageInfoSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Language Information")
                .font(.headline)
                .fontWeight(.bold)

            VStack(spacing: 12) {
                InfoRow(title: "Language", value: agent.language.displayName)
                InfoRow(title: "Learners Worldwide", value: agent.language.learnerCount)
                InfoRow(title: "Writing System", value: agent.language.writingSystem)
            }
            .padding()
            .background(Color(.systemGray6))
            .cornerRadius(16)
        }
    }

    // MARK: - Action Buttons Section
    private var actionButtonsSection: some View {
        VStack(spacing: 12) {
            // Primary Action
            Button(action: {
                showingChat = true
            }) {
                HStack {
                    Image(systemName: "message.fill")
                        .font(.headline)

                    Text("Start Learning with \(agent.name)")
                        .font(.headline)
                        .fontWeight(.semibold)
                }
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .padding()
                .background(agent.persona.color.gradient)
                .cornerRadius(16)
            }

            // Secondary Action
            Button(action: {
                // TODO: Add to favorites or bookmark
            }) {
                HStack {
                    Image(systemName: "heart")
                        .font(.subheadline)

                    Text("Add to Favorites")
                        .font(.subheadline)
                        .fontWeight(.medium)
                }
                .foregroundColor(agent.persona.color)
                .frame(maxWidth: .infinity)
                .padding()
                .background(agent.persona.color.opacity(0.1))
                .cornerRadius(16)
            }
        }
    }
}

// MARK: - Supporting Views
struct InfoRow: View {
    let title: String
    let value: String

    var body: some View {
        HStack {
            Text(title)
                .font(.subheadline)
                .foregroundColor(.secondary)

            Spacer()

            Text(value)
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(.primary)
        }
    }
}

// MARK: - Agent Chat View (Placeholder)
struct AgentChatView: View {
    let agent: LearningAgent
    @Environment(\.presentationMode) var presentationMode

    var body: some View {
        NavigationView {
            VStack {
                Spacer()

                VStack(spacing: 16) {
                    Text(agent.avatar)
                        .font(.system(size: 60))

                    Text("Chat with \(agent.name)")
                        .font(.title2)
                        .fontWeight(.bold)

                    Text("Advanced AI chat coming soon!")
                        .font(.body)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }

                Spacer()
            }
            .navigationTitle("Chat")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
            }
        }
    }
}

#Preview {
    AgentCard(agent: LearningAgent.getAgents(for: .spanish).first!) {
        // Preview action
    }
}
