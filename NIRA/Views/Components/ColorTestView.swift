//
//  ColorTestView.swift
//  NIRA
//
//  Created by NIRA Team on 27/05/2025.
//

import SwiftUI

struct ColorTestView: View {
    var body: some View {
        VStack(spacing: 16) {
            Text("Color Test")
                .font(Font.title)
                .foregroundColor(Color("NiraPrimary"))
            
            HStack {
                Rectangle()
                    .fill(Color("NiraPrimary"))
                    .frame(width: 50, height: 50)
                
                Rectangle()
                    .fill(Color("NiraSecondary"))
                    .frame(width: 50, height: 50)
                
                Rectangle()
                    .fill(Color("NiraSuccess"))
                    .frame(width: 50, height: 50)
                
                Rectangle()
                    .fill(Color("NiraInfo"))
                    .frame(width: 50, height: 50)
            }
        }
        .padding()
    }
}

#Preview {
    ColorTestView()
} 