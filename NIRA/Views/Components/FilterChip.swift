//
//  FilterChip.swift
//  NIRA
//
//  Created by NIRA Team on 27/05/2025.
//

import SwiftUI

struct FilterChip: View {
    let title: String
    let subtitle: String?
    let icon: String?
    let isSelected: Bool
    let color: Color
    let action: () -> Void

    init(title: String, subtitle: String? = nil, icon: String? = nil, isSelected: Bool, color: Color, action: @escaping () -> Void) {
        self.title = title
        self.subtitle = subtitle
        self.icon = icon
        self.isSelected = isSelected
        self.color = color
        self.action = action
    }

    var body: some View {
        Button(action: action) {
            HStack(spacing: 6) {
                if let icon = icon {
                    Text(icon)
                        .font(.caption)
                }

                VStack(spacing: 2) {
                    Text(title)
                        .font(.subheadline)
                        .fontWeight(isSelected ? .semibold : .regular)

                    if let subtitle = subtitle {
                        Text(subtitle)
                            .font(.caption2)
                            .opacity(0.8)
                    }
                }
            }
            .foregroundColor(isSelected ? .white : color)
            .padding(.horizontal, 16)
            .padding(.vertical, 8)
            .background(
                Capsule()
                    .fill(isSelected ? color : color.opacity(0.1))
            )
            .overlay(
                Capsule()
                    .stroke(color.opacity(0.3), lineWidth: isSelected ? 0 : 1)
            )
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isSelected ? 1.05 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isSelected)
    }
}