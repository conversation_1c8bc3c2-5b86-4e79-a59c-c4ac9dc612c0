//
//  LanguageStatsView.swift
//  NIRA
//
//  Created by NIRA Team on 29/05/2025.
//

import SwiftUI

// MARK: - Language Statistics Overview

struct LanguageStatsView: View {
    @StateObject private var dashboardCoordinator = DashboardCoordinatorService.shared

    var body: some View {
        VStack(spacing: 16) {
            headerSection
            statsGrid
            tierBreakdown
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
    }

    private var headerSection: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text("Language Portfolio")
                    .font(.headline)
                    .fontWeight(.bold)

                Text("Your learning journey across 50 languages")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            Spacer()

            Image(systemName: "globe")
                .font(.title2)
                .foregroundColor(.niraPrimary)
        }
    }

    private var statsGrid: some View {
        LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 3), spacing: 12) {
            StatCard(
                title: "Total Languages",
                value: "\(Language.allCases.count)",
                icon: "globe.americas.fill",
                color: .blue
            )

            StatCard(
                title: "Started",
                value: "\(startedLanguagesCount)",
                icon: "play.circle.fill",
                color: .green
            )

            StatCard(
                title: "Completed",
                value: "\(completedLanguagesCount)",
                icon: "checkmark.circle.fill",
                color: .orange
            )
        }
    }

    private var tierBreakdown: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Feature Tiers")
                .font(.subheadline)
                .fontWeight(.semibold)

            VStack(spacing: 8) {
                ForEach(LanguageTier.allCases, id: \.self) { tier in
                    TierBreakdownRow(tier: tier)
                }
            }
        }
    }

    private var startedLanguagesCount: Int {
        Language.allCases.filter { language in
            let progress = dashboardCoordinator.getLanguageProgress(for: language)
            return progress.completedLessons > 0
        }.count
    }

    private var completedLanguagesCount: Int {
        Language.allCases.filter { language in
            let progress = dashboardCoordinator.getLanguageProgress(for: language)
            return progress.progressPercentage >= 0.8 // 80% completion
        }.count
    }
}

struct StatCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color

    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)

            Text(value)
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(.primary)

            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .frame(height: 80)
        .frame(maxWidth: .infinity)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(color.opacity(0.1))
        )
    }
}

struct TierBreakdownRow: View {
    let tier: LanguageTier

    private var languageCount: Int {
        Language.allCases.filter { LanguageTier.getTier(for: $0) == tier }.count
    }

    var body: some View {
        HStack(spacing: 12) {
            HStack(spacing: 6) {
                Image(systemName: tier.icon)
                    .font(.caption)
                    .foregroundColor(tier.color)

                Text(tier.rawValue)
                    .font(.subheadline)
                    .fontWeight(.medium)
            }

            Spacer()

            VStack(alignment: .trailing, spacing: 2) {
                Text("\(languageCount) languages")
                    .font(.caption)
                    .fontWeight(.semibold)

                Text(tier.description)
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(tier.color.opacity(0.1))
        )
    }
}

// MARK: - Popular Languages Quick Access

struct PopularLanguagesQuickAccess: View {
    @Binding var selectedLanguage: Language
    let onLanguageSelected: (Language) -> Void
    @StateObject private var dashboardCoordinator = DashboardCoordinatorService.shared

    private let popularLanguages: [Language] = [
        .english, .spanish, .french, .german, .italian, .portuguese,
        .chinese, .japanese, .korean, .hindi, .arabic, .russian
    ]

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("Popular Languages")
                    .font(.headline)
                    .fontWeight(.bold)

                Spacer()

                Text("Quick Access")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color(.systemGray6))
                    .cornerRadius(8)
            }

            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(popularLanguages, id: \.self) { language in
                        CompactLanguageCard(
                            language: language,
                            isSelected: selectedLanguage == language,
                            progress: dashboardCoordinator.getLanguageProgress(for: language),
                            onSelect: {
                                withAnimation(.spring()) {
                                    selectedLanguage = language
                                    onLanguageSelected(language)
                                }
                            }
                        )
                    }
                }
                .padding(.horizontal)
            }
        }
    }
}

struct CompactLanguageCard: View {
    let language: Language
    let isSelected: Bool
    let progress: LanguageProgress
    let onSelect: () -> Void

    private var tier: LanguageTier {
        LanguageTier.getTier(for: language)
    }

    var body: some View {
        Button(action: onSelect) {
            VStack(spacing: 8) {
                ZStack {
                    Circle()
                        .fill(isSelected ? Color.niraPrimary : Color(.systemGray6))
                        .frame(width: 60, height: 60)

                    Text(language.flag)
                        .font(.title2)
                }

                VStack(spacing: 2) {
                    Text(language.displayName)
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                        .lineLimit(1)

                    HStack(spacing: 2) {
                        Image(systemName: tier.icon)
                            .font(.caption2)
                        Text("\(Int(progress.progressPercentage * 100))%")
                            .font(.caption2)
                            .fontWeight(.medium)
                    }
                    .foregroundColor(tier.color)
                }
            }
            .frame(width: 80)
            .scaleEffect(isSelected ? 1.1 : 1.0)
            .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isSelected)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Language Search Suggestions

struct LanguageSearchSuggestions: View {
    let searchText: String
    @Binding var selectedLanguage: Language
    let onLanguageSelected: (Language) -> Void

    private var suggestions: [Language] {
        if searchText.isEmpty {
            return []
        }

        return Language.allCases.filter { language in
            language.displayName.localizedCaseInsensitiveContains(searchText)
        }.prefix(5).map { $0 }
    }

    var body: some View {
        if !suggestions.isEmpty {
            VStack(alignment: .leading, spacing: 8) {
                Text("Suggestions")
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.secondary)

                ForEach(suggestions, id: \.self) { language in
                    Button(action: {
                        selectedLanguage = language
                        onLanguageSelected(language)
                    }) {
                        HStack(spacing: 12) {
                            Text(language.flag)
                                .font(.title3)

                            Text(language.displayName)
                                .font(.subheadline)
                                .foregroundColor(.primary)

                            Spacer()

                            let tier = LanguageTier.getTier(for: language)
                            Image(systemName: tier.icon)
                                .font(.caption)
                                .foregroundColor(tier.color)
                        }
                        .padding(.horizontal, 12)
                        .padding(.vertical, 8)
                        .background(Color(.systemGray6))
                        .cornerRadius(8)
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemBackground))
                    .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
            )
        }
    }
}
