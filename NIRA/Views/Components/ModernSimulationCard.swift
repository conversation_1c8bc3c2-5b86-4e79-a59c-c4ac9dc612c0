//
//  ModernSimulationCard.swift
//  NIRA
//
//  Created by NIRA Team on 1/2025.
//

import SwiftUI

struct ModernSimulationCard: View {
    let simulation: SupabaseSimulation
    let onStart: () -> Void
    @State private var isPressed = false

    // Convenience initializer for CulturalSimulation
    init(culturalSimulation: CulturalSimulation, onStart: @escaping () -> Void) {
        self.simulation = SupabaseSimulation(
            id: culturalSimulation.id,
            personaId: UUID(),
            languageId: UUID(),
            title: culturalSimulation.title,
            description: culturalSimulation.description,
            difficultyLevel: culturalSimulation.difficulty.lowercased(),
            estimatedDuration: Int(culturalSimulation.duration.replacingOccurrences(of: " min", with: "")) ?? 15,
            scenarioType: culturalSimulation.category.displayName.lowercased(),
            learningObjectives: culturalSimulation.learningObjectives,
            vocabularyFocus: [],
            conversationStarters: nil,
            successCriteria: nil,
            culturalNotes: culturalSimulation.culturalNotes.joined(separator: ". "),
            isActive: true,
            createdAt: Date(),
            updatedAt: Date()
        )
        self.onStart = onStart
    }

    // Original initializer for SupabaseSimulation
    init(simulation: SupabaseSimulation, onStart: @escaping () -> Void) {
        self.simulation = simulation
        self.onStart = onStart
    }

    private var difficultyColor: Color {
        switch simulation.difficultyLevel.lowercased() {
        case "a1", "beginner": return .levelA1Color
        case "a2", "elementary": return .levelA2Color
        case "b1", "intermediate": return .levelB1Color
        case "b2", "upper-intermediate": return .levelB2Color
        case "c1", "advanced": return .levelC1Color
        case "c2", "proficiency": return .levelC2Color
        default: return .levelA1Color
        }
    }

    private var difficultyGradient: [Color] {
        switch simulation.difficultyLevel.lowercased() {
        case "a1", "beginner": return Color.levelA1Gradient
        case "a2", "elementary": return Color.levelA2Gradient
        case "b1", "intermediate": return Color.levelB1Gradient
        case "b2", "upper-intermediate": return Color.levelB2Gradient
        case "c1", "advanced": return Color.levelC1Gradient
        case "c2", "proficiency": return Color.levelC2Gradient
        default: return Color.levelA1Gradient
        }
    }

    private var difficultyText: String {
        switch simulation.difficultyLevel.lowercased() {
        case "a1", "beginner": return "A1"
        case "a2", "elementary": return "A2"
        case "b1", "intermediate": return "B1"
        case "b2", "upper-intermediate": return "B2"
        case "c1", "advanced": return "C1"
        case "c2", "proficiency": return "C2"
        default: return simulation.difficultyLevel.uppercased()
        }
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Header with cultural context
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(simulation.title)
                        .font(.headline)
                        .fontWeight(.bold)
                        .foregroundColor(.primaryText)
                        .lineLimit(2)

                    if let culturalNotes = simulation.culturalNotes, !culturalNotes.isEmpty {
                        HStack(spacing: 4) {
                            Image(systemName: "globe")
                                .font(.caption)
                                .foregroundColor(difficultyColor)

                            Text(culturalNotes)
                                .font(.caption)
                                .foregroundColor(.secondaryText)
                                .lineLimit(1)
                        }
                    }
                }

                Spacer()

                // Difficulty badge
                Text(difficultyText)
                    .font(.caption)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(
                        LinearGradient(
                            colors: difficultyGradient,
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .cornerRadius(8)
            }

            // Description as conversation preview
            VStack(alignment: .leading, spacing: 8) {
                Text("Scenario")
                    .font(.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(.secondaryText)
                    .textCase(.uppercase)

                Text(simulation.description)
                    .font(.subheadline)
                    .foregroundColor(.primaryText)
                    .lineLimit(3)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 8)
                    .background(Color.cardBackground.opacity(0.5))
                    .cornerRadius(8)
            }

            // Features row
            HStack(spacing: 16) {
                // Scenario type indicator
                HStack(spacing: 4) {
                    Image(systemName: "text.bubble.fill")
                        .font(.caption)
                        .foregroundColor(.niraThemeTeal)

                    Text(simulation.scenarioType.capitalized)
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(.secondaryText)
                }

                // AI agent indicator (always available)
                HStack(spacing: 4) {
                    Image(systemName: "brain.head.profile")
                        .font(.caption)
                        .foregroundColor(.niraThemeIndigo)

                    Text("AI Ready")
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(.secondaryText)
                }

                Spacer()

                // Duration
                HStack(spacing: 4) {
                    Image(systemName: "clock")
                        .font(.caption)
                        .foregroundColor(.secondaryText)

                    Text("\(simulation.estimatedDuration) min")
                        .font(.caption)
                        .foregroundColor(.secondaryText)
                }
            }

            // Start button
            Button(action: onStart) {
                HStack {
                    Image(systemName: "play.fill")
                        .font(.subheadline)

                    Text("Start Simulation")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                }
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .padding(.vertical, 12)
                .background(
                    LinearGradient(
                        colors: difficultyGradient,
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .cornerRadius(12)
            }
            .buttonStyle(PlainButtonStyle())
        }
        .padding(20)
        .background(Color.cardBackground)
        .cornerRadius(16)
        .shadow(
            color: difficultyColor.opacity(0.2),
            radius: 8,
            x: 0,
            y: 4
        )
        .scaleEffect(isPressed ? 0.98 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.6), value: isPressed)
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            isPressed = pressing
        }, perform: {})
    }
}

// MARK: - Preview

#Preview {
    ModernSimulationCard(
        simulation: SupabaseSimulation(
            id: UUID(),
            personaId: UUID(),
            languageId: UUID(),
            title: "Ordering Coffee in Paris",
            description: "Practice ordering coffee at a Parisian café with authentic French expressions and cultural etiquette",
            difficultyLevel: "A2",
            estimatedDuration: 15,
            scenarioType: "dining",
            learningObjectives: ["Master café vocabulary", "Practice polite ordering", "Learn French dining customs"],
            vocabularyFocus: ["café", "bonjour", "s'il vous plaît", "merci"],
            conversationStarters: nil,
            successCriteria: nil,
            culturalNotes: "French cafés are social spaces where greeting everyone is important",
            isActive: true,
            createdAt: Date(),
            updatedAt: Date()
        ),
        onStart: {}
    )
    .padding()
}
