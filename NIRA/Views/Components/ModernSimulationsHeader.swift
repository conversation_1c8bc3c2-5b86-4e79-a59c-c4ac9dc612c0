import SwiftUI

struct ModernSimulationsHeader: View {
    let selectedLanguage: String
    @Binding var searchText: String
    @State private var showingLanguageSelector = false
    @Environment(\.colorScheme) var colorScheme
    @StateObject private var userPreferences = UserPreferencesService.shared

    private var languageDisplayName: String {
        userPreferences.selectedLanguage.displayName
    }

    private var languageFlag: String {
        switch userPreferences.selectedLanguage.displayName {
        case "English": return "🇺🇸"
        case "Spanish": return "🇪🇸"
        case "French": return "🇫🇷"
        case "German": return "🇩🇪"
        case "Italian": return "🇮🇹"
        case "Portuguese": return "🇵🇹"
        case "Chinese": return "🇨🇳"
        case "Japanese": return "🇯🇵"
        case "Korean": return "🇰🇷"
        case "Russian": return "🇷🇺"
        case "Arabic": return "🇸🇦"
        case "Hindi": return "🇮🇳"
        case "Tamil": return "🇮🇳"
        case "Telugu": return "🇮🇳"
        case "Kannada": return "🇮🇳"
        case "Malayalam": return "🇮🇳"
        case "Bengali": return "🇧🇩"
        case "Marathi": return "🇮🇳"
        case "Punjabi": return "🇮🇳"
        case "Gujarati": return "🇮🇳"
        case "Dutch": return "🇳🇱"
        case "Swedish": return "🇸🇪"
        case "Norwegian": return "🇳🇴"
        case "Thai": return "🇹🇭"
        case "Vietnamese": return "🇻🇳"
        case "Indonesian": return "🇮🇩"
        case "Turkish": return "🇹🇷"
        case "Greek": return "🇬🇷"
        case "Ukrainian": return "🇺🇦"
        case "Hebrew": return "🇮🇱"
        case "Farsi": return "🇮🇷"
        case "Tagalog": return "🇵🇭"
        default: return "🌍"
        }
    }

    var body: some View {
        VStack(spacing: 0) {
            // Status bar background
            Rectangle()
                .fill(colorScheme == .dark ? Color.black : Color.white)
                .frame(height: 0)
                .ignoresSafeArea(edges: .top)

            // Main header content
            VStack(spacing: 12) {
                // Top row with title and language selector
                HStack(alignment: .center) {
                    // Title section
                    HStack(spacing: 8) {
                        Image(systemName: "theatermasks.fill")
                            .font(.title3)
                            .foregroundColor(.primary)

                        Text("Simulations")
                            .font(.title2)
                            .fontWeight(.semibold)
                            .foregroundColor(.primary)
                            .lineLimit(1)
                    }
                    .frame(maxWidth: .infinity, alignment: .leading)

                    // Language Selector
                    Button(action: {
                        showingLanguageSelector = true
                    }) {
                        HStack(spacing: 6) {
                            Text(languageFlag)
                                .font(.subheadline)

                            Text(languageDisplayName)
                                .font(.caption)
                                .fontWeight(.medium)
                                .foregroundColor(.primary)
                                .lineLimit(1)

                            Image(systemName: "chevron.down")
                                .font(.caption2)
                                .foregroundColor(.secondary)
                        }
                        .padding(.horizontal, 12)
                        .padding(.vertical, 8)
                        .background(Color.gray.opacity(0.1))
                        .clipShape(Capsule())
                    }
                    .frame(maxWidth: 120)
                }
                .padding(.horizontal, 20)

                // Search Bar
                HStack(spacing: 12) {
                    Image(systemName: "magnifyingglass")
                        .foregroundColor(.gray)
                        .font(.system(size: 16, weight: .medium))

                    TextField("Search simulations...", text: $searchText)
                        .textFieldStyle(PlainTextFieldStyle())
                        .font(.subheadline)
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
                .background(Color.gray.opacity(0.1))
                .clipShape(RoundedRectangle(cornerRadius: 12))
                .padding(.horizontal, 20)
            }
            .padding(.top, 12)
            .padding(.bottom, 16)
            .background(colorScheme == .dark ? Color.black : Color.white)
        }
        .sheet(isPresented: $showingLanguageSelector) {
            LanguageSelectorSheet()
        }
    }

    private func getLanguageDisplayName(_ languageCode: String) -> String {
        switch languageCode {
        case "English": return "English"
        case "Spanish": return "Spanish"
        case "French": return "French"
        case "German": return "German"
        case "Italian": return "Italian"
        case "Portuguese": return "Portuguese"
        case "Chinese": return "Chinese"
        case "Japanese": return "Japanese"
        case "Korean": return "Korean"
        case "Russian": return "Russian"
        case "Arabic": return "Arabic"
        case "Hindi": return "Hindi"
        case "Tamil": return "Tamil"
        case "Telugu": return "Telugu"
        case "Kannada": return "Kannada"
        case "Malayalam": return "Malayalam"
        case "Bengali": return "Bengali"
        case "Marathi": return "Marathi"
        case "Punjabi": return "Punjabi"
        case "Gujarati": return "Gujarati"
        case "Dutch": return "Dutch"
        case "Swedish": return "Swedish"
        case "Norwegian": return "Norwegian"
        case "Thai": return "Thai"
        case "Vietnamese": return "Vietnamese"
        case "Indonesian": return "Indonesian"
        case "Turkish": return "Turkish"
        case "Greek": return "Greek"
        case "Ukrainian": return "Ukrainian"
        case "Hebrew": return "Hebrew"
        case "Farsi": return "Farsi"
        case "Tagalog": return "Tagalog"
        case "Swahili": return "Swahili"
        case "Afrikaans": return "Afrikaans"
        case "Finnish": return "Finnish"
        case "Danish": return "Danish"
        case "Hungarian": return "Hungarian"
        case "Czech": return "Czech"
        case "Polish": return "Polish"
        case "Romanian": return "Romanian"
        case "Bulgarian": return "Bulgarian"
        case "Croatian": return "Croatian"
        case "Slovak": return "Slovak"
        case "Slovenian": return "Slovenian"
        case "Latvian": return "Latvian"
        case "Lithuanian": return "Lithuanian"
        case "Estonian": return "Estonian"
        case "Urdu": return "Urdu"
        default: return languageCode
        }
    }
}

#Preview {
    @Previewable @State var searchText = ""

    ModernSimulationsHeader(
        selectedLanguage: "Tamil",
        searchText: $searchText
    )
    .background(Color.gray.opacity(0.1))
}
