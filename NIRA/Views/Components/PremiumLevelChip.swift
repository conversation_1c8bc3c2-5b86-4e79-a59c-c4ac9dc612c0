import SwiftUI

struct PremiumLevelChip: View {
    let title: String
    let isSelected: Bool
    let action: () -> Void
    @State private var isPressed = false

    var body: some View {
        Button(action: action) {
            HStack(spacing: 8) {
                // Level icon based on difficulty
                Image(systemName: levelIcon)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(isSelected ? .white : levelColor)

                Text(title)
                    .font(.system(size: 14, weight: .semibold))
                    .foregroundColor(isSelected ? .white : .primary)

                if isSelected {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(.white)
                        .transition(.scale.combined(with: .opacity))
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(
                Group {
                    if isSelected {
                        LinearGradient(
                            colors: levelGradientColors,
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    } else {
                        Color(.systemBackground)
                    }
                }
            )
            .overlay(
                RoundedRectangle(cornerRadius: 25)
                    .stroke(
                        isSelected ? Color.clear : levelColor.opacity(0.3),
                        lineWidth: 2
                    )
            )
            .cornerRadius(25)
            .shadow(
                color: isSelected ? levelColor.opacity(0.4) : Color.black.opacity(0.05),
                radius: isSelected ? 12 : 4,
                x: 0,
                y: isSelected ? 6 : 2
            )
            .scaleEffect(isPressed ? 0.95 : 1.0)
            .animation(.spring(response: 0.3, dampingFraction: 0.6), value: isPressed)
            .animation(.spring(response: 0.6, dampingFraction: 0.8), value: isSelected)
        }
        .buttonStyle(PlainButtonStyle())
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            isPressed = pressing
        }, perform: {})
    }

    private var levelColor: Color {
        return Color.getLevelColorByName(title)
    }

    private var levelGradientColors: [Color] {
        return Color.getLevelGradientByName(title)
    }

    private var levelIcon: String {
        switch title {
        case "All":
            return "square.grid.2x2.fill"
        case "Beginner Foundations":
            return "leaf.fill"
        case "Elementary Essentials":
            return "star.fill"
        case "Intermediate Conversations":
            return "message.fill"
        case "Advanced Communication":
            return "brain.head.profile"
        case "Professional Mastery":
            return "briefcase.fill"
        case "Expert Fluency":
            return "crown.fill"
        default:
            return "circle.fill"
        }
    }
}

#Preview {
    VStack(spacing: 12) {
        PremiumLevelChip(
            title: "Beginner Foundations",
            isSelected: true,
            action: {}
        )
        
        PremiumLevelChip(
            title: "Intermediate Conversations",
            isSelected: false,
            action: {}
        )
        
        PremiumLevelChip(
            title: "Expert Fluency",
            isSelected: false,
            action: {}
        )
    }
    .padding()
}
