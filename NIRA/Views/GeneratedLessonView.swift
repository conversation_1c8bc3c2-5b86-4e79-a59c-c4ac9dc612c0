import SwiftUI

struct GeneratedLessonView: View {
    let lesson: SupabaseLesson
    @State private var currentSection = 0
    @State private var showingExercise = false
    @State private var currentExerciseIndex = 0
    @Environment(\.dismiss) var dismiss

    // Analytics integration
    @StateObject private var analyticsService = LearningAnalyticsService.shared
    @State private var lessonStartTime = Date()
    @State private var sectionStartTime = Date()
    @State private var vocabularyMastery: [String: VocabularyMasteryData] = [:]
    @State private var exerciseResults: [String: AdaptiveExerciseResult] = [:]
    @State private var totalAccuracy: Double = 0.0
    @State private var interactionCount = 0

    // Sample user data - in real app this would come from authentication
    private let userId = UUID()
    private let languageId = UUID()

    private let sections = ["Overview", "Vocabulary", "Grammar", "Culture", "Dialogue", "Exercises"]

    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // Dynamic background based on lesson level
                backgroundGradient
                    .ignoresSafeArea()

                VStack(spacing: 0) {
                    // Header
                    lessonHeader

                    // Section Navigation
                    sectionNavigation

                    // Content
                    ScrollView {
                        VStack(spacing: 24) {
                            currentSectionView
                        }
                        .padding()
                    }
                }
            }
        }
        .navigationBarHidden(true)
        .sheet(isPresented: $showingExercise) {
            if !lesson.exercises.isEmpty {
                ExerciseView(
                    exercises: lesson.exercises.map { supabaseExerciseToExerciseItem($0) },
                    currentIndex: $currentExerciseIndex,
                    onExerciseComplete: handleExerciseCompletion
                )
            }
        }
        .onAppear {
            startLessonTracking()
        }
        .onDisappear {
            endLessonTracking()
        }
    }

    // MARK: - Analytics Integration

    private func startLessonTracking() {
        lessonStartTime = Date()

        // Start analytics session
        analyticsService.startSession(
            userId: userId,
            languageId: languageId,
            sessionType: .lesson
        )

        // Track lesson start
        analyticsService.trackInteraction(
            userId: userId,
            lessonId: lesson.id,
            interactionType: .lessonStart,
            contentType: nil,
            contentId: lesson.id.uuidString,
            isCorrect: nil,
            responseTime: nil
        )
    }

    private func endLessonTracking() {
        let timeSpent = Int(Date().timeIntervalSince(lessonStartTime))

        // Calculate overall lesson completion status and accuracy
        let completionStatus: AdaptiveUserProgress.CompletionStatus = determineCompletionStatus()
        let accuracy = calculateOverallAccuracy()

        // Update lesson progress
        Task {
            await analyticsService.updateLessonProgress(
                userId: userId,
                lessonId: lesson.id,
                languageId: languageId,
                completionStatus: completionStatus,
                accuracyScore: accuracy,
                timeSpent: timeSpent,
                vocabularyResults: vocabularyMastery,
                exerciseResults: exerciseResults
            )

            // End analytics session
            await analyticsService.endSession()
        }
    }

    private func trackSectionChange(to section: Int) {
        let timeSpent = Int(Date().timeIntervalSince(sectionStartTime))
        sectionStartTime = Date()

        analyticsService.trackInteraction(
            userId: userId,
            lessonId: lesson.id,
            interactionType: .lessonStart, // Could add section-specific types
            contentType: .vocabulary, // Determine based on section
            contentId: sections[section],
            isCorrect: nil,
            responseTime: timeSpent,
            metadata: ["section": SupabaseAnyCodable(sections[section])]
        )

        interactionCount += 1
    }

    private func trackInteraction(
        userId: UUID,
        lessonId: UUID,
        interactionType: LearningAnalytics.InteractionType,
        contentType: LearningAnalytics.ContentType?,
        contentId: String?,
        isCorrect: Bool?,
        responseTime: Int?,
        hintUsed: Bool = false,
        attemptsBeforeCorrect: Int? = nil,
        metadata: [String: SupabaseAnyCodable]? = nil
    ) {
        analyticsService.trackInteraction(
            userId: userId,
            lessonId: lessonId,
            interactionType: interactionType,
            contentType: contentType,
            contentId: contentId,
            isCorrect: isCorrect,
            responseTime: responseTime,
            hintUsed: hintUsed,
            attemptsBeforeCorrect: attemptsBeforeCorrect,
            metadata: metadata
        )

        interactionCount += 1
    }

    private func trackVocabularyInteraction(_ vocabulary: SupabaseVocabularyItem, isCorrect: Bool? = nil) {
        analyticsService.trackInteraction(
            userId: userId,
            lessonId: lesson.id,
            interactionType: .vocabularyReview,
            contentType: .vocabulary,
            contentId: vocabulary.word,
            isCorrect: isCorrect,
            responseTime: nil,
            metadata: [
                "word": SupabaseAnyCodable(vocabulary.word),
                "translation": SupabaseAnyCodable(vocabulary.translation)
            ]
        )

        // Update vocabulary mastery
        let currentMastery = vocabularyMastery[vocabulary.word] ?? VocabularyMasteryData(
            attempts: 0,
            correct: 0,
            lastSeen: Date()
        )

        vocabularyMastery[vocabulary.word] = VocabularyMasteryData(
            attempts: currentMastery.attempts + 1,
            correct: currentMastery.correct + (isCorrect == true ? 1 : 0),
            lastSeen: Date()
        )

        interactionCount += 1
    }

    private func handleExerciseCompletion(exerciseId: String, isCorrect: Bool, timeSpent: Int, hintsUsed: Int) {
        analyticsService.trackInteraction(
            userId: userId,
            lessonId: lesson.id,
            interactionType: .exerciseAttempt,
            contentType: .exercise,
            contentId: exerciseId,
            isCorrect: isCorrect,
            responseTime: timeSpent,
            hintUsed: hintsUsed > 0,
            metadata: [
                "hints_used": SupabaseAnyCodable(hintsUsed),
                "exercise_type": SupabaseAnyCodable("multiple_choice") // Could be dynamic
            ]
        )

        // Update exercise results
        let currentResult = exerciseResults[exerciseId] ?? AdaptiveExerciseResult(
            exerciseId: exerciseId,
            attempts: 0,
            correct: false,
            timeSpent: 0,
            hintsUsed: 0
        )

        exerciseResults[exerciseId] = AdaptiveExerciseResult(
            exerciseId: exerciseId,
            attempts: currentResult.attempts + 1,
            correct: isCorrect,
            timeSpent: currentResult.timeSpent + timeSpent,
            hintsUsed: currentResult.hintsUsed + hintsUsed
        )

        interactionCount += 1
    }

    private func determineCompletionStatus() -> AdaptiveUserProgress.CompletionStatus {
        // Simple heuristic - in real app this would be more sophisticated
        let sectionsVisited = currentSection + 1
        let exercisesCompleted = exerciseResults.values.filter { $0.correct }.count
        let totalExercises = lesson.exercises.count

        if sectionsVisited >= sections.count && exercisesCompleted >= totalExercises {
            return totalAccuracy >= 90 ? .mastered : .completed
        } else if sectionsVisited > 1 || exercisesCompleted > 0 {
            return .inProgress
        } else {
            return .notStarted
        }
    }

    private func calculateOverallAccuracy() -> Double {
        let exerciseAccuracy = exerciseResults.values.map { $0.correct ? 100.0 : 0.0 }
        let vocabularyAccuracy = vocabularyMastery.values.map { $0.masteryLevel * 100.0 }

        let allAccuracies = exerciseAccuracy + vocabularyAccuracy

        if allAccuracies.isEmpty {
            return 0.0
        }

        return allAccuracies.reduce(0, +) / Double(allAccuracies.count)
    }

    // MARK: - Header

    private var lessonHeader: some View {
        VStack(spacing: 16) {
            HStack {
                Button(action: {
                    endLessonTracking()
                    dismiss()
                }) {
                    Image(systemName: "xmark.circle.fill")
                        .font(.title2)
                        .foregroundColor(.white)
                        .background(Color.black.opacity(0.3))
                        .clipShape(Circle())
                }

                Spacer()

                VStack(spacing: 4) {
                    Text(lesson.title)
                        .font(.headline)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                        .multilineTextAlignment(.center)

                    Text("Level \(levelName) • \(lesson.estimatedDuration ?? 15) min")
                        .font(.subheadline)
                        .foregroundColor(.white.opacity(0.8))
                }

                Spacer()

                VStack(spacing: 4) {
                    Text("\(lesson.vocabulary.count)")
                        .font(.title3)
                        .fontWeight(.bold)
                        .foregroundColor(.white)

                    Text("words")
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.8))
                }
            }

            if let description = lesson.description {
                Text(description)
                    .font(.subheadline)
                    .foregroundColor(.white.opacity(0.9))
                    .multilineTextAlignment(.center)
                    .padding(.horizontal)
            }

            // Progress indicator
            HStack(spacing: 8) {
                ForEach(0..<sections.count, id: \.self) { index in
                    Circle()
                        .fill(index <= currentSection ? Color.white : Color.white.opacity(0.3))
                        .frame(width: 8, height: 8)
                }
            }
        }
        .padding()
    }

    // MARK: - Section Navigation

    private var sectionNavigation: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 16) {
                ForEach(Array(sections.enumerated()), id: \.offset) { index, section in
                    Button(action: {
                        trackSectionChange(to: index)
                        currentSection = index
                    }) {
                        VStack(spacing: 4) {
                            Text(section)
                                .font(.subheadline)
                                .fontWeight(currentSection == index ? .bold : .medium)
                                .foregroundColor(currentSection == index ? .white : .white.opacity(0.7))

                            if currentSection == index {
                                RoundedRectangle(cornerRadius: 2)
                                    .fill(Color.white)
                                    .frame(height: 3)
                                    .transition(.scale)
                            }
                        }
                        .frame(minWidth: 80)
                    }
                }
            }
            .padding(.horizontal)
        }
        .padding(.vertical, 12)
        .background(Color.black.opacity(0.2))
        .animation(.spring(), value: currentSection)
    }

    // MARK: - Content Sections

    @ViewBuilder
    private var currentSectionView: some View {
        switch currentSection {
        case 0: overviewSection
        case 1: vocabularySection
        case 2: grammarSection
        case 3: cultureSection
        case 4: dialogueSection
        case 5: exercisesSection
        default: overviewSection
        }
    }

    private var overviewSection: some View {
        VStack(spacing: 20) {
            SectionCard(title: "Lesson Overview", icon: "book.fill") {
                VStack(alignment: .leading, spacing: 16) {
                    Text(lesson.description ?? "This lesson will help you learn new vocabulary and grammar concepts.")
                        .font(.body)
                        .lineSpacing(4)

                    // Lesson Stats
                    LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                        StatBadge(
                            title: "Vocabulary Words",
                            value: "\(lesson.vocabulary.count)",
                            color: .niraInfo
                        )

                        StatBadge(
                            title: "Exercises",
                            value: "\(lesson.exercises.count)",
                            color: .niraSuccess
                        )

                        StatBadge(
                            title: "Learning Objectives",
                            value: "\(lesson.learningObjectives?.count ?? 0)",
                            color: .niraWarning
                        )

                        StatBadge(
                            title: "Topics",
                            value: "\(lesson.topics.count)",
                            color: .niraPrimary
                        )
                    }

                    // Skills Focus
                    if !(lesson.learningObjectives?.isEmpty ?? true) {
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Learning Objectives")
                                .font(.headline)
                                .fontWeight(.semibold)

                            FlowLayout(spacing: 8) {
                                ForEach(lesson.learningObjectives ?? [], id: \.self) { objective in
                                    SkillTag(skill: objective)
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    private var vocabularySection: some View {
        VStack(spacing: 16) {
            if !lesson.vocabulary.isEmpty {
                ForEach(Array(lesson.vocabulary.enumerated()), id: \.offset) { index, vocab in
                    InteractiveVocabularyCard(
                        vocabulary: vocab,
                        index: index + 1,
                        onInteraction: { isCorrect in
                            trackVocabularyInteraction(vocab, isCorrect: isCorrect)
                        }
                    )
                }
            } else {
                Text("No vocabulary available")
                    .foregroundColor(.secondary)
            }
        }
    }

    private var grammarSection: some View {
        VStack(spacing: 16) {
            if !(lesson.learningObjectives?.isEmpty ?? true) {
                ForEach(Array((lesson.learningObjectives ?? []).enumerated()), id: \.offset) { index, objective in
                    ObjectiveCard(objective: objective, index: index + 1)
                }
            } else {
                Text("No learning objectives available")
                    .foregroundColor(.secondary)
            }
        }
    }

    private var cultureSection: some View {
        SectionCard(title: "Cultural Context", icon: "globe") {
            VStack(alignment: .leading, spacing: 16) {
                if !(lesson.culturalContext?.isEmpty ?? true) {
                    Text(lesson.culturalContext ?? "")
                        .font(.body)
                        .lineSpacing(4)
                }

                // Cultural Tips (if available in do's and don'ts)
                VStack(alignment: .leading, spacing: 12) {
                    Text("Cultural Tips")
                        .font(.headline)
                        .fontWeight(.semibold)

                    VStack(spacing: 8) {
                        CulturalTip(text: "Be aware of formal vs informal greetings", type: .tip)
                        CulturalTip(text: "Avoid overly familiar gestures in business settings", type: .warning)
                        CulturalTip(text: "Small gifts are appreciated when visiting", type: .tip)
                    }
                }
            }
        }
    }

    private var dialogueSection: some View {
        VStack(spacing: 16) {
            if !lesson.topics.isEmpty {
                ForEach(Array(lesson.topics.enumerated()), id: \.offset) { index, topic in
                    TopicCard(topic: topic, index: index + 1)
                }
            } else {
                Text("No topics available")
                    .foregroundColor(.secondary)
            }
        }
    }

    private var exercisesSection: some View {
        VStack(spacing: 16) {
            SectionCard(title: "Practice Exercises", icon: "pencil.circle.fill") {
                VStack(spacing: 12) {
                    Text("Ready to test your knowledge?")
                        .font(.body)
                        .multilineTextAlignment(.center)

                    Text("\(lesson.exercises.count) exercises • \(lesson.exercises.count * 100) points total")
                        .font(.subheadline)
                        .foregroundColor(.secondary)

                    Button(action: { showingExercise = true }) {
                        HStack {
                            Image(systemName: "play.circle.fill")
                            Text("Start Exercises")
                                .fontWeight(.semibold)
                        }
                        .font(.headline)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color.green.gradient)
                        )
                    }
                }
            }

            // Exercise Preview
            if !lesson.exercises.isEmpty {
                ForEach(Array(lesson.exercises.enumerated()), id: \.offset) { index, exercise in
                    ExercisePreviewCard(exercise: supabaseExerciseToExerciseItem(exercise), index: index + 1)
                }
            }
        }
    }

    // MARK: - Computed Properties

    private var backgroundGradient: some View {
        LinearGradient(
            colors: languageGradient,
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    }

    private var languageGradient: [Color] {
        // Try to detect language from lesson title or use a smart heuristic
        let title = lesson.title.lowercased()

        if title.contains("french") || title.contains("français") || title.contains("bonjour") || title.contains("appétit") {
            return Color.primaryGradient
        } else if title.contains("spanish") || title.contains("español") || title.contains("hola") || title.contains("viaje") {
            return Color.primaryGradient
        } else if title.contains("german") || title.contains("deutsch") || title.contains("wie") || title.contains("spät") {
            return Color.primaryGradient
        } else if title.contains("italian") || title.contains("italiano") || title.contains("ciao") {
            return Color.primaryGradient
        } else if title.contains("portuguese") || title.contains("português") || title.contains("olá") {
            return Color.primaryGradient
        } else if title.contains("japanese") || title.contains("日本語") || title.contains("こんにちは") {
            return Color.primaryGradient
        } else if title.contains("tamil") || title.contains("தமிழ்") {
            return Color.primaryGradient
        } else {
            // Default to primary gradient
            return Color.primaryGradient
        }
    }

    private var levelColor: Color {
        // Use consistent primary color instead of different colors per level
        return .niraPrimary
    }

    private var levelName: String {
        switch lesson.difficultyLevel {
        case 1: return "A1"
        case 2: return "A2"
        case 3: return "B1"
        case 4: return "B2"
        case 5: return "C1"
        case 6: return "C2"
        default: return "Unknown"
        }
    }

    // MARK: - Conversion Helpers

    private func vocabularyToSupabaseVocabulary(_ vocab: GeneratedVocabulary) -> SupabaseVocabularyItem {
        return SupabaseVocabularyItem(
            word: vocab.word,
            translation: vocab.translation,
            partOfSpeech: vocab.partOfSpeech,
            context: nil,
            difficulty: nil,
            pronunciation: vocab.pronunciation,
            example: vocab.example,
            exampleTranslation: vocab.exampleTranslation
        )
    }

    private func grammarToSupabaseGrammar(_ grammar: GeneratedGrammarPoint) -> SupabaseGrammarPoint {
        return SupabaseGrammarPoint(
            rule: grammar.rule,
            explanation: grammar.explanation,
            examples: grammar.examples,
            tips: grammar.tips
        )
    }

    private func dialogueToSupabaseDialogue(_ dialogue: GeneratedDialogue) -> SupabaseDialogueItem {
        return SupabaseDialogueItem(
            speaker: dialogue.speaker,
            text: dialogue.text,
            translation: dialogue.translation,
            culturalNote: nil
        )
    }

    private func supabaseExerciseToExerciseItem(_ exercise: SupabaseExercise) -> SupabaseExerciseItem {
        return SupabaseExerciseItem(
            type: exercise.type,
            question: exercise.question,
            options: exercise.options,
            correctAnswer: Int(exercise.correctAnswer) ?? 0,
            explanation: exercise.explanation ?? "Complete this exercise to continue",
            hints: nil,
            points: exercise.points,
            pairs: nil
        )
    }


}

// MARK: - Enhanced Interactive Components

struct InteractiveVocabularyCard: View {
    let vocabulary: SupabaseVocabularyItem
    let index: Int
    let onInteraction: (Bool?) -> Void

    @State private var showTranslation = false
    @State private var hasInteracted = false

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("\(index).")
                    .font(.caption)
                    .fontWeight(.bold)
                    .foregroundColor(.secondary)
                    .frame(width: 30, alignment: .leading)

                VStack(alignment: .leading, spacing: 4) {
                    HStack {
                        Text(vocabulary.word)
                            .font(.title3)
                            .fontWeight(.bold)

                        Spacer()

                        Text(vocabulary.pronunciation ?? "")
                            .font(.caption)
                            .italic()
                            .foregroundColor(.secondary)
                    }

                    if showTranslation {
                        Text(vocabulary.translation)
                            .font(.subheadline)
                            .foregroundColor(.niraPrimary)
                            .transition(.opacity)
                    }
                }
            }

            // Interactive buttons
            HStack(spacing: 12) {
                Button(action: {
                    withAnimation {
                        showTranslation.toggle()
                    }
                    if !hasInteracted {
                        onInteraction(nil)
                        hasInteracted = true
                    }
                }) {
                    Text(showTranslation ? "Hide" : "Show Translation")
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(.niraPrimary)
                        .padding(.horizontal, 12)
                        .padding(.vertical, 6)
                        .background(
                            Capsule()
                                .fill(Color.niraPrimary.opacity(0.1))
                        )
                }

                if showTranslation {
                    Button(action: {
                        onInteraction(true)
                    }) {
                        HStack(spacing: 4) {
                            Image(systemName: "checkmark")
                            Text("Got it!")
                        }
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(.green)
                        .padding(.horizontal, 12)
                        .padding(.vertical, 6)
                        .background(
                            Capsule()
                                .fill(Color.green.opacity(0.1))
                        )
                    }

                    Button(action: {
                        onInteraction(false)
                    }) {
                        HStack(spacing: 4) {
                            Image(systemName: "questionmark")
                            Text("Need practice")
                        }
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(.orange)
                        .padding(.horizontal, 12)
                        .padding(.vertical, 6)
                        .background(
                            Capsule()
                                .fill(Color.orange.opacity(0.1))
                        )
                    }
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemGray6))
        )
    }
}

// MARK: - Supporting Views (keeping existing implementations)

struct SectionCard<Content: View>: View {
    let title: String
    let icon: String
    @ViewBuilder let content: Content

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: icon)
                    .foregroundColor(.niraPrimary)
                Text(title)
                    .font(.headline)
                    .fontWeight(.bold)
            }

            content
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
        )
    }
}

struct StatBadge: View {
    let title: String
    let value: String
    let color: Color // Keep parameter for backward compatibility but override internally
    @State private var pulseAnimation = false

    var body: some View {
        VStack(spacing: 4) {
            Text(value)
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(.niraPrimary) // Use primary color consistently
                .scaleEffect(pulseAnimation ? 1.1 : 1.0)
                .animation(.easeInOut(duration: 1.5).repeatForever(autoreverses: true), value: pulseAnimation)

            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.niraPrimary.opacity(0.1)) // Use primary color consistently
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(
                            LinearGradient(
                                colors: [.niraPrimary.opacity(0.3), .niraSecondary.opacity(0.3)],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ),
                            lineWidth: 1
                        )
                )
        )
        .onAppear {
            // Start pulse animation with random delay
            DispatchQueue.main.asyncAfter(deadline: .now() + Double.random(in: 0...1)) {
                pulseAnimation = true
            }
        }
    }
}

struct SkillTag: View {
    let skill: String

    var body: some View {
        Text(skill.capitalized)
            .font(.caption)
            .fontWeight(.medium)
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(
                Capsule()
                    .fill(Color.niraPrimary.opacity(0.2))
            )
            .foregroundColor(.niraPrimary)
    }
}

struct VocabularyCard: View {
    let vocabulary: SupabaseVocabularyItem
    let index: Int

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("\(index).")
                    .font(.caption)
                    .fontWeight(.bold)
                    .foregroundColor(.secondary)
                    .frame(width: 30, alignment: .leading)

                VStack(alignment: .leading, spacing: 4) {
                    HStack {
                        Text(vocabulary.word)
                            .font(.title3)
                            .fontWeight(.bold)

                        Spacer()

                        Text(vocabulary.pronunciation ?? "")
                            .font(.caption)
                            .italic()
                            .foregroundColor(.secondary)
                    }

                    Text(vocabulary.translation)
                        .font(.subheadline)
                        .foregroundColor(.niraPrimary)
                }
            }

            // Example usage would go here if available in the model
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemGray6))
        )
    }
}

struct GrammarCard: View {
    let grammar: SupabaseGrammarPoint
    let index: Int

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("\(index).")
                    .font(.caption)
                    .fontWeight(.bold)
                    .foregroundColor(.secondary)
                    .frame(width: 30, alignment: .leading)

                Text(grammar.rule)
                    .font(.headline)
                    .fontWeight(.semibold)
            }

            Text(grammar.explanation)
                .font(.body)
                .padding(.leading, 30)

            if !grammar.examples.isEmpty {
                VStack(alignment: .leading, spacing: 6) {
                    Text("Examples:")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .padding(.leading, 30)

                    ForEach(grammar.examples, id: \.self) { example in
                        Text("• \(example)")
                            .font(.body)
                            .italic()
                            .padding(.leading, 45)
                    }
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.orange.opacity(0.1))
        )
    }
}

struct DialogueCard: View {
    let dialogue: SupabaseDialogueItem
    let index: Int

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text("\(index).")
                    .font(.caption)
                    .fontWeight(.bold)
                    .foregroundColor(.secondary)
                    .frame(width: 30, alignment: .leading)

                Text(dialogue.speaker)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.purple)
            }

            VStack(alignment: .leading, spacing: 4) {
                Text(dialogue.text)
                    .font(.body)
                    .fontWeight(.medium)
                    .padding(.leading, 30)

                Text(dialogue.translation)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .padding(.leading, 30)
            }

            // Cultural note removed as it's not available in current model
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.purple.opacity(0.1))
        )
    }
}

struct ExercisePreviewCard: View {
    let exercise: SupabaseExerciseItem
    let index: Int

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text("\(index).")
                    .font(.caption)
                    .fontWeight(.bold)
                    .foregroundColor(.secondary)
                    .frame(width: 30, alignment: .leading)

                Text(exercise.type.replacingOccurrences(of: "_", with: " ").capitalized)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.green)

                Spacer()

                Text("100 pts")
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.secondary)
            }

            Text(exercise.question)
                .font(.body)
                .padding(.leading, 30)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.green.opacity(0.1))
        )
    }
}

struct TopicCard: View {
    let topic: String
    let index: Int

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text("\(index).")
                    .font(.caption)
                    .fontWeight(.bold)
                    .foregroundColor(.secondary)
                    .frame(width: 30, alignment: .leading)

                Text(topic.capitalized)
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.purple)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.purple.opacity(0.1))
        )
    }
}

struct ObjectiveCard: View {
    let objective: String
    let index: Int

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text("\(index).")
                    .font(.caption)
                    .fontWeight(.bold)
                    .foregroundColor(.secondary)
                    .frame(width: 30, alignment: .leading)

                Text(objective)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.orange)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.orange.opacity(0.1))
        )
    }
}

struct CulturalTip: View {
    let text: String
    let type: TipType

    enum TipType {
        case tip, warning

        var icon: String {
            switch self {
            case .tip: return "lightbulb.fill"
            case .warning: return "exclamationmark.triangle.fill"
            }
        }

        var color: Color {
            switch self {
            case .tip: return .blue
            case .warning: return .orange
            }
        }
    }

    var body: some View {
        HStack(alignment: .top, spacing: 8) {
            Image(systemName: type.icon)
                .foregroundColor(type.color)
                .frame(width: 20)

            Text(text)
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }
}



// Exercise View (enhanced with analytics)
struct ExerciseView: View {
    let exercises: [SupabaseExerciseItem]
    @Binding var currentIndex: Int
    let onExerciseComplete: (String, Bool, Int, Int) -> Void
    @Environment(\.presentationMode) var presentationMode

    @State private var exerciseStartTime = Date()
    @State private var hintsUsed = 0

    var body: some View {
        NavigationView {
            VStack {
                Text("Exercise functionality would go here")
                    .font(.headline)
                    .padding()

                Text("Exercise \(currentIndex + 1) of \(exercises.count)")
                    .font(.subheadline)
                    .foregroundColor(.secondary)

                Spacer()

                // Mock exercise completion
                Button("Complete Exercise (Correct)") {
                    completeExercise(isCorrect: true)
                }
                .font(.headline)
                .foregroundColor(.white)
                .padding()
                .background(Color.green)
                .cornerRadius(12)

                Button("Complete Exercise (Incorrect)") {
                    completeExercise(isCorrect: false)
                }
                .font(.headline)
                .foregroundColor(.white)
                .padding()
                .background(Color.red)
                .cornerRadius(12)

                Button("Close") {
                    presentationMode.wrappedValue.dismiss()
                }
                .font(.headline)
                .foregroundColor(.white)
                .padding()
                .background(Color.blue)
                .cornerRadius(12)
            }
            .navigationTitle("Exercises")
            .navigationBarTitleDisplayMode(.inline)
        }
        .onAppear {
            exerciseStartTime = Date()
        }
    }

    private func completeExercise(isCorrect: Bool) {
        let timeSpent = Int(Date().timeIntervalSince(exerciseStartTime))
        let exerciseId = exercises[currentIndex].type + "_\(currentIndex)"

        onExerciseComplete(exerciseId, isCorrect, timeSpent, hintsUsed)

        if currentIndex < exercises.count - 1 {
            currentIndex += 1
            exerciseStartTime = Date()
            hintsUsed = 0
        } else {
            presentationMode.wrappedValue.dismiss()
        }
    }
}

#Preview {
    // Create sample content metadata in the correct format
    let _ = [
        "vocabulary": SupabaseAnyCodable([
            [
                "word": "café",
                "translation": "coffee",
                "pronunciation": "ka-FEH",
                "example": "Un café, s'il vous plaît",
                "exampleTranslation": "A coffee, please",
                "partOfSpeech": "noun"
            ],
            [
                "word": "s'il vous plaît",
                "translation": "please",
                "pronunciation": "see-voo-PLEH",
                "example": "Un café, s'il vous plaît",
                "exampleTranslation": "A coffee, please",
                "partOfSpeech": "phrase"
            ]
        ]),
        "grammarPoints": SupabaseAnyCodable([
            [
                "rule": "Politeness",
                "explanation": "Always use 's'il vous plaît' when ordering",
                "examples": ["Un café, s'il vous plaît"],
                "tips": "Remember to be polite in French culture"
            ]
        ]),
        "dialogues": SupabaseAnyCodable([
            [
                "speaker": "Marie",
                "text": "Bonjour! Un café, s'il vous plaît.",
                "translation": "Hello! A coffee, please."
            ]
        ]),
        "exercises": SupabaseAnyCodable([
            [
                "type": "multiple_choice",
                "question": "How do you say 'please' in French?",
                "options": ["s'il vous plaît", "merci", "bonjour"],
                "correctAnswer": "s'il vous plaît"
            ]
        ])
    ]

    GeneratedLessonView(lesson: SupabaseLesson(
        id: UUID(),
        pathId: UUID(),
        title: "French Café Basics",
        description: "Learn essential vocabulary and cultural etiquette for ordering in French cafés.",
        lessonType: "conversation",
        difficultyLevel: 1,
        estimatedDuration: 20,
        sequenceOrder: 1,
        learningObjectives: ["Learn café vocabulary", "Practice polite ordering"],
        vocabularyFocus: ["café", "ordering"],
        grammarConcepts: ["Politeness expressions"],
        culturalNotes: "Café culture is central to French daily life.",
        prerequisiteLessons: [],
        contentMetadata: SupabaseAnyCodable([
            "vocabulary": [
                [
                    "word": "café",
                    "translation": "coffee",
                    "partOfSpeech": "noun",
                    "context": "Un café, s'il vous plaît",
                    "difficulty": "beginner",
                    "pronunciation": "ka-FEH",
                    "example": "Un café, s'il vous plaît",
                    "exampleTranslation": "A coffee, please"
                ]
            ],
            "exercises": [
                [
                    "type": "multiple_choice",
                    "question": "How do you say 'please' in French?",
                    "options": ["s'il vous plaît", "merci", "bonjour"],
                    "correctAnswer": "s'il vous plaît",
                    "explanation": "s'il vous plaît means please in French",
                    "points": 100,
                    "difficulty": 1
                ]
            ]
        ]),
        isActive: true,
        createdAt: Date(),
        updatedAt: Date(),
        audioUrl: nil,
        hasAudio: false,
        audioMetadata: nil
    ))
}