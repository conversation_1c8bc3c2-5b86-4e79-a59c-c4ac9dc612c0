//
//  HomeView.swift
//  NIRA
//
//  Created by NIRA Team on 1/2025.
//

import SwiftUI

// MARK: - Premium Home View with Approved 21st Dev Design

struct HomeView: View {
    @Binding var selectedTab: Int
    @State private var currentStreak = 7
    @StateObject private var userPreferences = UserPreferencesService.shared
    @StateObject private var dashboardCoordinator = DashboardCoordinatorService.shared
    @Environment(\.colorScheme) var colorScheme
    @State private var showingCelebration = false
    @State private var particleAnimation = false

    var body: some View {
        VStack(spacing: 0) {
            // Modern Header
            ModernDashboardHeader(
                selectedLanguage: userPreferences.selectedLanguage.displayName,
                userName: "Alex",
                currentStreak: currentStreak
            )

            // Main Content
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    // Today's Goal Card with Rich Design
                    PremiumGoalCardView(
                        onGoalTap: {
                            selectedTab = 1 // Switch to Lessons tab
                        }
                    )

                    // Language Portfolio with Premium Design
                    PremiumLanguagePortfolioView()

                    // Feature Tiers Section
                    PremiumFeatureTiersView()

                    // Quick Actions with Rich Design
                    PremiumQuickActionsView(
                        selectedLanguage: userPreferences.selectedLanguage,
                        onActionSelected: { action in
                            handleQuickAction(action)
                        }
                    )

                    // Recommended for You Section
                    PremiumRecommendationsView()
                }
                .padding(.horizontal, 20)
                .padding(.top, 10)
            }
            .background(
                // Premium European-style gradient background
                premiumBackgroundGradient
                    .ignoresSafeArea()
            )
        }
        .navigationBarHidden(true)
        .onAppear {
            Task {
                await dashboardCoordinator.refreshDashboard()
            }
        }
    }

    // MARK: - Action Handlers

    private func handleQuickAction(_ action: QuickActionType) {
        switch action {
        case .startLesson:
            selectedTab = 1 // Navigate to Lessons tab
        case .practiceSpeak:
            // TODO: Navigate to AI conversation with selected language
            break
        case .culturalSim:
            selectedTab = 2 // Navigate to Simulations tab
        case .reviewWords:
            // TODO: Navigate to vocabulary review
            break
        }
    }

    private var premiumBackgroundGradient: some View {
        let hour = Calendar.current.component(.hour, from: Date())
        let gradientColors: [Color]

        if colorScheme == .dark {
            // Dark mode gradients
            switch hour {
            case 5..<12:  // Morning - Dark Alpine
                gradientColors = [
                    Color(red: 0.05, green: 0.05, blue: 0.08),
                    Color(red: 0.08, green: 0.08, blue: 0.12),
                    Color(red: 0.10, green: 0.10, blue: 0.15)
                ]
            case 12..<17: // Afternoon - Dark Mediterranean
                gradientColors = [
                    Color(red: 0.04, green: 0.06, blue: 0.08),
                    Color(red: 0.06, green: 0.08, blue: 0.12),
                    Color(red: 0.08, green: 0.10, blue: 0.15)
                ]
            case 17..<20: // Evening - Dark Tuscan
                gradientColors = [
                    Color(red: 0.08, green: 0.06, blue: 0.04),
                    Color(red: 0.12, green: 0.08, blue: 0.06),
                    Color(red: 0.15, green: 0.10, blue: 0.08)
                ]
            default:      // Night - Deep Dark
                gradientColors = [
                    Color(red: 0.02, green: 0.02, blue: 0.05),
                    Color(red: 0.04, green: 0.04, blue: 0.08),
                    Color(red: 0.06, green: 0.06, blue: 0.10)
                ]
            }
        } else {
            // Light mode gradients
            switch hour {
            case 5..<12:  // Morning - Soft Alpine
                gradientColors = [
                    Color(red: 0.95, green: 0.97, blue: 1.0),
                    Color(red: 0.90, green: 0.94, blue: 0.98),
                    Color(red: 0.85, green: 0.91, blue: 0.96)
                ]
            case 12..<17: // Afternoon - Mediterranean
                gradientColors = [
                    Color(red: 0.96, green: 0.98, blue: 1.0),
                    Color(red: 0.92, green: 0.96, blue: 0.99),
                    Color(red: 0.88, green: 0.93, blue: 0.97)
                ]
            case 17..<20: // Evening - Tuscan
                gradientColors = [
                    Color(red: 0.98, green: 0.96, blue: 0.94),
                    Color(red: 0.95, green: 0.93, blue: 0.90),
                    Color(red: 0.92, green: 0.89, blue: 0.85)
                ]
            default:      // Night - Deep Swiss
                gradientColors = [
                    Color(red: 0.94, green: 0.95, blue: 0.98),
                    Color(red: 0.90, green: 0.92, blue: 0.96),
                    Color(red: 0.86, green: 0.88, blue: 0.94)
                ]
            }
        }

        return LinearGradient(
            colors: gradientColors,
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    }

}

// MARK: - Premium Home View Components

struct PremiumWelcomeHeaderView: View {
    let streak: Int
    let userName: String

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Welcome back, \(userName)! 👋")
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(.primaryText)

            HStack {
                HStack(spacing: 8) {
                    Image(systemName: "flame.fill")
                        .foregroundColor(.orange)

                    Text("\(streak) day streak")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.orange)
                }
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(Color.orange.opacity(0.15))
                .cornerRadius(20)

                Spacer()

                Text(currentTimeGreeting)
                    .font(.subheadline)
                    .foregroundColor(.secondaryText)
            }
        }
        .padding(20)
        .background(
            LinearGradient(
                colors: [
                    Color(red: 0.4, green: 0.6, blue: 1.0).opacity(0.8),
                    Color(red: 0.6, green: 0.4, blue: 0.9).opacity(0.6)
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        )
        .cornerRadius(20)
        .shadow(color: Color.blue.opacity(0.2), radius: 10, x: 0, y: 5)
    }

    private var currentTimeGreeting: String {
        let hour = Calendar.current.component(.hour, from: Date())
        switch hour {
        case 5..<12: return "Good morning! ☀️"
        case 12..<17: return "Good afternoon! 🌤️"
        case 17..<22: return "Good evening! 🌅"
        default: return "Good night! 🌙"
        }
    }
}

struct PremiumGoalCardView: View {
    let onGoalTap: () -> Void

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("Today's Goal 🎯")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.primaryText)

                Spacer()

                Text("3/3 lessons")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.green)
            }

            // Progress bar
            VStack(alignment: .leading, spacing: 8) {
                ProgressView(value: 1.0)
                    .progressViewStyle(LinearProgressViewStyle(tint: .green))
                    .scaleEffect(y: 2)

                HStack {
                    Image(systemName: "flag.fill")
                        .foregroundColor(.gray)
                    Text("Tamil")
                        .font(.subheadline)
                        .foregroundColor(.gray)

                    Spacer()

                    Text("0/15 total")
                        .font(.caption)
                        .foregroundColor(.gray)
                }
            }

            // Achievement badge
            HStack {
                Image(systemName: "checkmark.circle.fill")
                    .foregroundColor(.green)
                Text("Goal achieved! You're on fire! 🔥")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.green)
            }
            .padding(12)
            .background(Color.green.opacity(0.1))
            .cornerRadius(12)
        }
        .padding(20)
        .background(Color.cardBackground)
        .cornerRadius(16)
        .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 4)
        .onTapGesture(perform: onGoalTap)
    }
}

struct PremiumLanguagePortfolioView: View {
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("Language Portfolio")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.primaryText)

                Spacer()

                Image(systemName: "globe")
                    .foregroundColor(.blue)
            }

            Text("Your learning journey across 50 languages")
                .font(.subheadline)
                .foregroundColor(.secondaryText)

            HStack(spacing: 16) {
                // Total Languages
                VStack(spacing: 8) {
                    ZStack {
                        Circle()
                            .fill(Color.blue.opacity(0.1))
                            .frame(width: 50, height: 50)

                        Image(systemName: "globe.americas.fill")
                            .foregroundColor(.blue)
                            .font(.title3)
                    }

                    Text("73")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.primaryText)

                    Text("Total Languages")
                        .font(.caption)
                        .foregroundColor(.secondaryText)
                }
                .frame(maxWidth: .infinity)

                // Started
                VStack(spacing: 8) {
                    ZStack {
                        Circle()
                            .fill(Color.green.opacity(0.1))
                            .frame(width: 50, height: 50)

                        Image(systemName: "play.circle.fill")
                            .foregroundColor(.green)
                            .font(.title3)
                    }

                    Text("4")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.primaryText)

                    Text("Started")
                        .font(.caption)
                        .foregroundColor(.secondaryText)
                }
                .frame(maxWidth: .infinity)

                // Completed
                VStack(spacing: 8) {
                    ZStack {
                        Circle()
                            .fill(Color.orange.opacity(0.1))
                            .frame(width: 50, height: 50)

                        Image(systemName: "checkmark.circle.fill")
                            .foregroundColor(.orange)
                            .font(.title3)
                    }

                    Text("0")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.primaryText)

                    Text("Completed")
                        .font(.caption)
                        .foregroundColor(.secondaryText)
                }
                .frame(maxWidth: .infinity)
            }
        }
        .padding(20)
        .background(Color.cardBackground)
        .cornerRadius(16)
        .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 4)
    }
}

struct PremiumFeatureTiersView: View {
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Feature Tiers")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(.primaryText)

            VStack(spacing: 12) {
                // Full Features
                HStack {
                    Image(systemName: "crown.fill")
                        .foregroundColor(.green)

                    VStack(alignment: .leading, spacing: 2) {
                        Text("Full Features")
                            .font(.subheadline)
                            .fontWeight(.semibold)
                            .foregroundColor(.primaryText)

                        Text("Voice, Live Chat, Full AI")
                            .font(.caption)
                            .foregroundColor(.secondaryText)
                    }

                    Spacer()

                    Text("21 languages")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.primaryText)
                }
                .padding(12)
                .background(Color.green.opacity(0.1))
                .cornerRadius(12)

                // Partial Features
                HStack {
                    Image(systemName: "star.fill")
                        .foregroundColor(.orange)

                    VStack(alignment: .leading, spacing: 2) {
                        Text("Partial Features")
                            .font(.subheadline)
                            .fontWeight(.semibold)
                            .foregroundColor(.primaryText)

                        Text("Text Chat, Limited AI")
                            .font(.caption)
                            .foregroundColor(.secondaryText)
                    }

                    Spacer()

                    Text("18 languages")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.primaryText)
                }
                .padding(12)
                .background(Color.orange.opacity(0.1))
                .cornerRadius(12)

                // Basic Features
                HStack {
                    Image(systemName: "circle.fill")
                        .foregroundColor(.blue)

                    VStack(alignment: .leading, spacing: 2) {
                        Text("Basic Features")
                            .font(.subheadline)
                            .fontWeight(.semibold)
                            .foregroundColor(.primaryText)

                        Text("Text Only, Basic AI")
                            .font(.caption)
                            .foregroundColor(.secondaryText)
                    }

                    Spacer()

                    Text("34 languages")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.primaryText)
                }
                .padding(12)
                .background(Color.blue.opacity(0.1))
                .cornerRadius(12)
            }
        }
        .padding(20)
        .background(Color.cardBackground)
        .cornerRadius(16)
        .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 4)
    }
}

struct PremiumQuickActionsView: View {
    let selectedLanguage: Language
    let onActionSelected: (QuickActionType) -> Void

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Quick Actions ⚡")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(.primaryText)

            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                // Start Lesson
                QuickActionCard(
                    icon: "book.fill",
                    title: "Start Lesson",
                    subtitle: "for \(selectedLanguage.displayName)",
                    color: .blue,
                    action: { onActionSelected(.startLesson) }
                )

                // Practice Speaking
                QuickActionCard(
                    icon: "mic.fill",
                    title: "Practice Speaking",
                    subtitle: "for \(selectedLanguage.displayName)",
                    color: .blue,
                    action: { onActionSelected(.practiceSpeak) }
                )

                // Cultural Sim
                QuickActionCard(
                    icon: "globe",
                    title: "Cultural Sim",
                    subtitle: "for \(selectedLanguage.displayName)",
                    color: .blue,
                    action: { onActionSelected(.culturalSim) }
                )

                // Review Words
                QuickActionCard(
                    icon: "arrow.clockwise",
                    title: "Review Words",
                    subtitle: "for \(selectedLanguage.displayName)",
                    color: .blue,
                    action: { onActionSelected(.reviewWords) }
                )
            }
        }
    }
}

struct QuickActionCard: View {
    let icon: String
    let title: String
    let subtitle: String
    let color: Color
    let action: () -> Void
    @State private var isPressed = false

    var body: some View {
        Button(action: action) {
            VStack(spacing: 12) {
                ZStack {
                    Circle()
                        .fill(color.gradient)
                        .frame(width: 50, height: 50)

                    Image(systemName: icon)
                        .font(.title3)
                        .foregroundColor(.white)
                }

                VStack(spacing: 4) {
                    Text(title)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primaryText)

                    Text(subtitle)
                        .font(.caption)
                        .foregroundColor(.secondaryText)
                        .multilineTextAlignment(.center)
                }
            }
            .frame(maxWidth: .infinity)
            .padding(16)
            .background(Color.cardBackground)
            .cornerRadius(16)
            .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 4)
            .scaleEffect(isPressed ? 0.95 : 1.0)
            .animation(.spring(response: 0.3, dampingFraction: 0.6), value: isPressed)
        }
        .buttonStyle(PlainButtonStyle())
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            isPressed = pressing
        }, perform: {})
    }
}

struct PremiumRecommendationsView: View {
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Recommended for You 💡")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(.primaryText)

            HStack(spacing: 12) {
                Image(systemName: "brain.head.profile")
                    .font(.title2)
                    .foregroundColor(.blue)

                VStack(alignment: .leading, spacing: 4) {
                    Text("AI is learning about you")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primaryText)

                    Text("Complete more lessons to get personalized")
                        .font(.caption)
                        .foregroundColor(.secondaryText)
                }

                Spacer()
            }
            .padding(16)
            .background(Color.cardBackground)
            .cornerRadius(12)
        }
    }
}

#Preview {
    HomeView(selectedTab: .constant(0))
}