import SwiftUI

struct VocabularyDetailView: View {
    let vocabularyItems: [LessonVocabularyItem]
    @Environment(\.dismiss) private var dismiss
    @State private var currentIndex = 0
    @State private var showTranslation = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Header
                headerView
                
                // Main content
                if !vocabularyItems.isEmpty {
                    TabView(selection: $currentIndex) {
                        ForEach(Array(vocabularyItems.enumerated()), id: \.offset) { index, vocab in
                            VocabularyDetailCard(
                                vocabulary: vocab,
                                showTranslation: $showTranslation
                            )
                            .tag(index)
                        }
                    }
                    .tabViewStyle(PageTabViewStyle(indexDisplayMode: .automatic))
                    .indexViewStyle(PageIndexViewStyle(backgroundDisplayMode: .always))
                } else {
                    emptyStateView
                }
                
                // Bottom controls
                bottomControls
            }
        }
        .navigationBarHidden(true)
    }
    
    private var headerView: some View {
        VStack(spacing: 16) {
            HStack {
                Button(action: { dismiss() }) {
                    HStack(spacing: 8) {
                        Image(systemName: "chevron.left")
                            .font(.system(size: 16, weight: .medium))
                        Text("Back")
                            .font(.system(size: 16, weight: .medium))
                    }
                    .foregroundColor(.primary)
                }
                
                Spacer()
                
                Text("Vocabulary")
                    .font(.system(size: 20, weight: .bold))
                    .foregroundColor(.primary)
                
                Spacer()
                
                // Placeholder for symmetry
                HStack(spacing: 8) {
                    Image(systemName: "chevron.left")
                        .font(.system(size: 16, weight: .medium))
                    Text("Back")
                        .font(.system(size: 16, weight: .medium))
                }
                .opacity(0)
            }
            .padding(.horizontal, 20)
            .padding(.top, 10)
            
            if !vocabularyItems.isEmpty {
                Text("\(currentIndex + 1) of \(vocabularyItems.count)")
                    .font(.system(size: 16))
                    .foregroundColor(.secondary)
            }
        }
        .padding(.bottom, 20)
        .background(Color(.systemBackground))
    }
    
    private var emptyStateView: some View {
        VStack(spacing: 16) {
            Image(systemName: "book.closed")
                .font(.system(size: 48))
                .foregroundColor(.gray)
            
            Text("No Vocabulary Items")
                .font(.headline)
                .foregroundColor(.primary)
            
            Text("This lesson doesn't have vocabulary items yet.")
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .padding()
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    private var bottomControls: some View {
        VStack(spacing: 16) {
            // Show/Hide translation toggle
            Button(action: {
                withAnimation(.easeInOut(duration: 0.3)) {
                    showTranslation.toggle()
                }
            }) {
                HStack(spacing: 8) {
                    Image(systemName: showTranslation ? "eye.slash" : "eye")
                        .font(.system(size: 16, weight: .medium))
                    Text(showTranslation ? "Hide Translation" : "Show Translation")
                        .font(.system(size: 16, weight: .medium))
                }
                .foregroundColor(.blue)
                .padding(.horizontal, 20)
                .padding(.vertical, 12)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.blue.opacity(0.1))
                )
            }
            
            // Navigation buttons
            if vocabularyItems.count > 1 {
                HStack(spacing: 20) {
                    Button(action: {
                        withAnimation {
                            currentIndex = max(0, currentIndex - 1)
                        }
                    }) {
                        HStack(spacing: 8) {
                            Image(systemName: "chevron.left")
                            Text("Previous")
                        }
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(currentIndex > 0 ? .blue : .gray)
                        .padding(.horizontal, 20)
                        .padding(.vertical, 12)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color(.systemGray6))
                        )
                    }
                    .disabled(currentIndex <= 0)
                    
                    Button(action: {
                        withAnimation {
                            currentIndex = min(vocabularyItems.count - 1, currentIndex + 1)
                        }
                    }) {
                        HStack(spacing: 8) {
                            Text("Next")
                            Image(systemName: "chevron.right")
                        }
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(currentIndex < vocabularyItems.count - 1 ? .blue : .gray)
                        .padding(.horizontal, 20)
                        .padding(.vertical, 12)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color(.systemGray6))
                        )
                    }
                    .disabled(currentIndex >= vocabularyItems.count - 1)
                }
            }
        }
        .padding(.horizontal, 20)
        .padding(.bottom, 20)
        .background(Color(.systemBackground))
    }
}

struct VocabularyDetailCard: View {
    let vocabulary: LessonVocabularyItem
    @Binding var showTranslation: Bool
    
    var body: some View {
        VStack(spacing: 24) {
            // Main word
            VStack(spacing: 12) {
                HStack {
                    Text(vocabulary.word)
                        .font(.system(size: 32, weight: .bold))
                        .foregroundColor(.primary)
                        .multilineTextAlignment(.center)
                    
                    Spacer()
                    
                    // Audio button for word
                    AudioButton(
                        audioURL: vocabulary.wordAudioURL,
                        size: 44,
                        color: .green
                    )
                }
                
                if let pronunciation = vocabulary.pronunciation {
                    Text(pronunciation)
                        .font(.system(size: 18))
                        .foregroundColor(.green)
                        .italic()
                }
                
                if let partOfSpeech = vocabulary.partOfSpeech {
                    Text(partOfSpeech.capitalized)
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(.white)
                        .padding(.horizontal, 12)
                        .padding(.vertical, 4)
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .fill(Color.blue)
                        )
                }
            }
            
            Divider()
            
            // Translation (conditional)
            if showTranslation, let translation = vocabulary.translation {
                VStack(spacing: 8) {
                    Text("Translation")
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(.secondary)
                        .textCase(.uppercase)
                    
                    Text(translation)
                        .font(.system(size: 24, weight: .semibold))
                        .foregroundColor(.blue)
                        .multilineTextAlignment(.center)
                }
                .transition(.opacity.combined(with: .scale))
            }
            
            // Example (if available)
            if let example = vocabulary.example {
                VStack(spacing: 12) {
                    HStack {
                        VStack(alignment: .leading, spacing: 4) {
                            Text("Example")
                                .font(.caption)
                                .fontWeight(.semibold)
                                .foregroundColor(.secondary)
                                .textCase(.uppercase)
                            
                            Text(example)
                                .font(.system(size: 18))
                                .foregroundColor(.primary)
                        }
                        
                        Spacer()
                        
                        // Audio button for example
                        AudioButton(
                            audioURL: vocabulary.exampleAudioURL,
                            size: 36,
                            color: .orange
                        )
                    }
                }
                .padding(16)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color(.systemGray6).opacity(0.5))
                )
            }
            
            Spacer()
        }
        .padding(20)
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    

}

#Preview {
    VocabularyDetailView(vocabularyItems: [
        LessonVocabularyItem(
            word: "வணக்கம்",
            translation: "Hello/Greetings",
            pronunciation: "/vaṇakkam/",
            partOfSpeech: "noun",
            example: "வணக்கம், எப்படி இருக்கிறீர்கள்?",
            wordAudioURL: nil,
            exampleAudioURL: nil
        ),
        LessonVocabularyItem(
            word: "நன்றி",
            translation: "Thank you",
            pronunciation: "/nanṟi/",
            partOfSpeech: "noun",
            example: "உதவிக்கு நன்றி.",
            wordAudioURL: nil,
            exampleAudioURL: nil
        )
    ])
}
