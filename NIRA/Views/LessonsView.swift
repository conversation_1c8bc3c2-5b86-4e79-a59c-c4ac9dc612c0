//
//  LessonsView.swift
//  NIRA
//
//  Created by NIRA Team on 1/2025.
//

import SwiftUI

// MARK: - Lessons View - Completely Rewritten for Supabase Integration

struct LessonsView: View {
    @State private var lessons: [SupabaseLesson] = []
    @State private var isLoading = true
    @State private var selectedLevel: String = "All"
    @State private var errorMessage: String?
    @State private var isRefreshing = false
    @State private var selectedLesson: SupabaseLesson?
    @State private var showingLessonDetail = false
    @State private var searchText = ""
    @StateObject private var userPreferences = UserPreferencesService.shared
    @StateObject private var dashboardCoordinator = DashboardCoordinatorService.shared

    // Meaningful level names instead of A1, A2, etc.
    private let levels = ["All", "Beginner Foundations", "Elementary Essentials", "Intermediate Conversations", "Advanced Communication", "Professional Mastery", "Expert Fluency"]

    // Mapping from display names to CEFR levels for database queries
    private let levelMapping: [String: String] = [
        "All": "All",
        "Beginner Foundations": "A1",
        "Elementary Essentials": "A2",
        "Intermediate Conversations": "B1",
        "Advanced Communication": "B2",
        "Professional Mastery": "C1",
        "Expert Fluency": "C2"
    ]

    // Dynamic languages based on available lessons and user preference
    private var availableLanguages: [String] {
        let lessonLanguages = Set(lessons.compactMap { $0.languageName }).sorted()
        return ["All"] + lessonLanguages
    }

    // Current selected language from user preferences
    private var selectedLanguage: String {
        return userPreferences.selectedLanguage.displayName
    }

    var body: some View {
        VStack(spacing: 0) {
            // Modern Header
            ModernLessonsHeader(
                selectedLanguage: selectedLanguage,
                searchText: $searchText
            )

            // Filter Controls
            filterControlsView

            // Main Content
            if isLoading {
                loadingView
            } else if let error = errorMessage {
                errorView(error: error)
            } else if lessons.isEmpty {
                emptyStateView
            } else {
                lessonsListView
            }
        }
        .background(Color(.systemGroupedBackground))
        .navigationBarHidden(true)
        .task {
            await loadLessonsFromSupabase()
        }
        .onAppear {
            // Refresh lessons when view appears to show current language
            Task {
                await loadLessonsFromSupabase()
            }
        }
        .onChange(of: userPreferences.selectedLanguage) { oldValue, newValue in
            // Refresh lessons when language changes
            Task {
                await loadLessonsFromSupabase()
            }
        }
        .sheet(isPresented: $showingLessonDetail) {
            if let lesson = selectedLesson {
                LessonDetailView(lesson: lesson)
            }
        }
    }

    // MARK: - Views

    private var filterControlsView: some View {
        VStack(spacing: 16) {
            // Modern Level Filter with Premium Design
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(levels, id: \.self) { level in
                        PremiumLevelChip(
                            title: level,
                            isSelected: selectedLevel == level,
                            action: {
                                withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                                    selectedLevel = level
                                }
                            }
                        )
                    }
                }
                .padding(.horizontal, 20)
            }
        }
        .padding(.vertical, 16)
        .background(
            LinearGradient(
                colors: [Color(.systemBackground), Color(.systemGray6).opacity(0.3)],
                startPoint: .top,
                endPoint: .bottom
            )
        )
    }

    private var loadingView: some View {
        VStack(spacing: 20) {
            ProgressView()
                .scaleEffect(1.5)

            Text("Loading lessons from Supabase...")
                .font(.headline)
                .foregroundColor(.secondary)

            Text("This may take a moment")
                .font(.subheadline)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(.systemBackground))
    }

    private func errorView(error: String) -> some View {
        VStack(spacing: 20) {
            Image(systemName: "exclamationmark.triangle")
                .font(.system(size: 60))
                .foregroundColor(.red)

            Text("Connection Error")
                .font(.title2)
                .fontWeight(.semibold)
                .foregroundColor(.primary)

            Text(error)
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal, 40)

            VStack(spacing: 12) {
                Button("Retry Connection") {
                    Task { await loadLessonsFromSupabase() }
                }
                .font(.headline)
                .foregroundColor(.white)
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.blue.gradient)
                )

                Button("Check Admin Panel") {
                    // This would switch to admin tab
                }
                .font(.subheadline)
                .foregroundColor(.blue)
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(.systemBackground))
    }

    private var emptyStateView: some View {
        VStack(spacing: 20) {
            Image(systemName: "book.closed")
                .font(.system(size: 60))
                .foregroundColor(.gray)

            Text("No Lessons Found")
                .font(.title2)
                .fontWeight(.semibold)
                .foregroundColor(.primary)

            Text("Generate lessons using the content generator in the Admin panel")
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal, 40)

            VStack(spacing: 12) {
                Button("Generate Lessons") {
                    // This would switch to admin tab
                }
                .font(.headline)
                .foregroundColor(.white)
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.green.gradient)
                )

                Button("Refresh") {
                    Task { await loadLessonsFromSupabase() }
                }
                .font(.subheadline)
                .foregroundColor(.blue)
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(.systemBackground))
    }

    private var lessonsListView: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                ForEach(filteredLessons, id: \.id) { lesson in
                    ModernLessonCard(
                        lesson: lesson,
                        language: getLanguageDisplayName(selectedLanguage),
                        onStart: {
                            selectedLesson = lesson
                            showingLessonDetail = true
                        }
                    )
                    .transition(.scale.combined(with: .opacity))
                }
            }
            .padding()
        }
        .animation(.easeInOut, value: filteredLessons.count)
        .refreshable {
            await loadLessonsFromSupabase()
        }
    }

    // MARK: - Computed Properties

    private var filteredLessons: [SupabaseLesson] {
        var filtered = lessons

        // Filter by search text
        if !searchText.isEmpty {
            filtered = filtered.filter { lesson in
                lesson.title.localizedCaseInsensitiveContains(searchText) ||
                (lesson.description?.localizedCaseInsensitiveContains(searchText) ?? false) ||
                (lesson.vocabularyFocus?.joined(separator: " ").localizedCaseInsensitiveContains(searchText) ?? false)
            }
        }

        // Filter by level using the mapping
        if selectedLevel != "All" {
            if let cefrLevel = levelMapping[selectedLevel] {
                filtered = filtered.filter { lesson in
                    lesson.difficultyLevel == getDifficultyNumber(for: cefrLevel)
                }
            }
        }

        // Sort by difficulty level first (A1 first), then by title to ensure consistent ordering
        return filtered.sorted { lesson1, lesson2 in
            let level1 = lesson1.difficultyLevel ?? 1
            let level2 = lesson2.difficultyLevel ?? 1

            if level1 != level2 {
                return level1 < level2 // A1 (1) comes before A2 (2), etc.
            }

            // If same level, sort by title alphabetically (excluding "Review" lessons which should come last)
            let title1 = lesson1.title
            let title2 = lesson2.title

            // Put "Review" lessons at the end
            let isReview1 = title1.lowercased().contains("review")
            let isReview2 = title2.lowercased().contains("review")

            if isReview1 && !isReview2 {
                return false // lesson1 (review) comes after lesson2
            } else if !isReview1 && isReview2 {
                return true // lesson1 comes before lesson2 (review)
            } else {
                // Both are review or both are not review, sort alphabetically
                return title1 < title2
            }
        }
    }

    // MARK: - Functions

    private func getDifficultyNumber(for level: String) -> Int {
        switch level {
        case "A1": return 1
        case "A2": return 2
        case "B1": return 3
        case "B2": return 4
        case "C1": return 5
        case "C2": return 6
        default: return 1
        }
    }

    private func refreshLessons() {
        isRefreshing = true
        Task {
            await loadLessonsFromSupabase()
            isRefreshing = false
        }
    }

    private func getLanguageDisplayName(_ languageCode: String) -> String {
        // Convert language code to display name
        switch languageCode {
        case "Tamil": return "Tamil"
        case "English": return "English"
        case "Spanish": return "Spanish"
        case "French": return "French"
        case "German": return "German"
        case "Italian": return "Italian"
        case "Portuguese": return "Portuguese"
        case "Russian": return "Russian"
        case "Chinese": return "Chinese"
        case "Japanese": return "Japanese"
        case "Korean": return "Korean"
        case "Arabic": return "Arabic"
        case "Hindi": return "Hindi"
        case "Bengali": return "Bengali"
        case "Telugu": return "Telugu"
        case "Marathi": return "Marathi"
        case "Gujarati": return "Gujarati"
        case "Kannada": return "Kannada"
        case "Malayalam": return "Malayalam"
        case "Punjabi": return "Punjabi"
        case "Urdu": return "Urdu"
        default: return languageCode
        }
    }

    private func loadLessonsFromSupabase() async {
        isLoading = true
        errorMessage = nil

        do {
            // Get the current language code for API call
            let languageCode = userPreferences.selectedLanguage.rawValue

            // Load lessons from Supabase for the selected language
            let supabaseClient = NIRASupabaseClient.shared
            let fetchedLessons = try await supabaseClient.getLessons(language: languageCode)

            await MainActor.run {
                self.lessons = fetchedLessons
                self.isLoading = false
                print("✅ Loaded \(fetchedLessons.count) lessons for \(languageCode)")
            }
        } catch {
            await MainActor.run {
                self.errorMessage = "Failed to load lessons: \(error.localizedDescription)"
                self.isLoading = false
                print("❌ Error loading lessons: \(error)")
            }
        }
    }
}

// MARK: - Supporting Views



