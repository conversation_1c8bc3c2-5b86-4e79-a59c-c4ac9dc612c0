import SwiftUI
import AVFoundation

// MARK: - Live Voice Interface View

struct LiveVoiceInterfaceView: View {
    let agent: LanguageTutor
    @ObservedObject var voiceService = GeminiLiveVoiceService.shared
    @Environment(\.presentationMode) var presentationMode
    
    @State private var showingTranscription = true
    @State private var audioLevels: [CGFloat] = Array(repeating: 0.1, count: 50)
    @State private var animationTimer: Timer?
    
    var body: some View {
        NavigationView {
            ZStack {
                // Background gradient
                LinearGradient(
                    colors: [
                        Color.black,
                        Color.niraPrimary.opacity(0.3),
                        Color.black
                    ],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()
                
                VStack(spacing: 30) {
                    // Header
                    headerSection
                    
                    Spacer()
                    
                    // Audio Visualization
                    audioVisualizationSection
                    
                    // Live Transcription
                    if showingTranscription {
                        transcriptionSection
                    }
                    
                    Spacer()
                    
                    // Controls
                    controlsSection
                    
                    // Status
                    statusSection
                }
                .padding()
            }
            .navigationBarHidden(true)
            .onAppear {
                startAudioVisualization()
            }
            .onDisappear {
                stopAudioVisualization()
            }
        }
    }
    
    // MARK: - Header Section
    
    private var headerSection: some View {
        VStack(spacing: 12) {
            // Agent Avatar
            ZStack {
                Circle()
                    .fill(
                        LinearGradient(
                            colors: Color.primaryGradient,
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 80, height: 80)
                    .shadow(color: .niraPrimary.opacity(0.3), radius: 10, x: 0, y: 5)
                
                Text(agent.avatar)
                    .font(.system(size: 40))
                    .scaleEffect(voiceService.isPlaying ? 1.1 : 1.0)
                    .animation(.easeInOut(duration: 0.3), value: voiceService.isPlaying)
            }
            
            VStack(spacing: 4) {
                Text("Live Conversation")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                
                Text("with \(agent.name)")
                    .font(.headline)
                    .foregroundColor(.white.opacity(0.8))
            }
        }
    }
    
    // MARK: - Audio Visualization Section
    
    private var audioVisualizationSection: some View {
        VStack(spacing: 20) {
            // Connection Status Indicator
            HStack(spacing: 8) {
                Circle()
                    .fill(connectionStatusColor)
                    .frame(width: 12, height: 12)
                    .scaleEffect(voiceService.isConnected ? 1.0 : 0.8)
                    .animation(.easeInOut(duration: 0.5).repeatForever(autoreverses: true), value: voiceService.isConnected)
                
                Text(connectionStatusText)
                    .font(.subheadline)
                    .foregroundColor(.white.opacity(0.9))
            }
            
            // Audio Waveform
            HStack(alignment: .center, spacing: 3) {
                ForEach(0..<audioLevels.count, id: \.self) { index in
                    RoundedRectangle(cornerRadius: 2)
                        .fill(
                            LinearGradient(
                                colors: [
                                    Color.niraPrimary.opacity(0.3),
                                    Color.niraPrimary,
                                    Color.white
                                ],
                                startPoint: .bottom,
                                endPoint: .top
                            )
                        )
                        .frame(width: 4, height: max(4, audioLevels[index] * 60))
                        .animation(.easeInOut(duration: 0.1), value: audioLevels[index])
                }
            }
            .frame(height: 80)
            .padding(.horizontal)
        }
    }
    
    // MARK: - Transcription Section
    
    private var transcriptionSection: some View {
        VStack(spacing: 12) {
            HStack {
                Text("Live Transcription")
                    .font(.headline)
                    .foregroundColor(.white)
                
                Spacer()
                
                Button(action: {
                    withAnimation(.spring()) {
                        showingTranscription.toggle()
                    }
                }) {
                    Image(systemName: showingTranscription ? "eye.slash" : "eye")
                        .foregroundColor(.white.opacity(0.7))
                }
            }
            
            ScrollView {
                VStack(alignment: .leading, spacing: 8) {
                    if !voiceService.liveTranscription.isEmpty {
                        Text(voiceService.liveTranscription)
                            .font(.body)
                            .foregroundColor(.white.opacity(0.9))
                            .padding()
                            .background(
                                RoundedRectangle(cornerRadius: 12)
                                    .fill(Color.white.opacity(0.1))
                            )
                    } else {
                        Text("Start speaking to see live transcription...")
                            .font(.body)
                            .foregroundColor(.white.opacity(0.6))
                            .italic()
                    }
                }
                .frame(maxWidth: .infinity, alignment: .leading)
            }
            .frame(maxHeight: 120)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.black.opacity(0.3))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.white.opacity(0.1), lineWidth: 1)
                )
        )
    }
    
    // MARK: - Controls Section
    
    private var controlsSection: some View {
        HStack(spacing: 40) {
            // Mute/Unmute Button
            Button(action: {
                // Toggle mute functionality
            }) {
                ZStack {
                    Circle()
                        .fill(Color.white.opacity(0.2))
                        .frame(width: 60, height: 60)
                    
                    Image(systemName: voiceService.isRecording ? "mic.fill" : "mic.slash.fill")
                        .font(.title2)
                        .foregroundColor(.white)
                }
            }
            .scaleEffect(voiceService.isRecording ? 1.1 : 1.0)
            .animation(.spring(response: 0.3), value: voiceService.isRecording)
            
            // End Call Button
            Button(action: {
                endLiveConversation()
            }) {
                ZStack {
                    Circle()
                        .fill(Color.red)
                        .frame(width: 80, height: 80)
                        .shadow(color: .red.opacity(0.3), radius: 10, x: 0, y: 5)
                    
                    Image(systemName: "phone.down.fill")
                        .font(.title)
                        .foregroundColor(.white)
                }
            }
            .scaleEffect(1.0)
            .animation(.spring(response: 0.3), value: voiceService.isConnected)
            
            // Speaker Button
            Button(action: {
                // Toggle speaker functionality
            }) {
                ZStack {
                    Circle()
                        .fill(Color.white.opacity(0.2))
                        .frame(width: 60, height: 60)
                    
                    Image(systemName: voiceService.isPlaying ? "speaker.wave.3.fill" : "speaker.fill")
                        .font(.title2)
                        .foregroundColor(.white)
                }
            }
            .scaleEffect(voiceService.isPlaying ? 1.1 : 1.0)
            .animation(.spring(response: 0.3), value: voiceService.isPlaying)
        }
    }
    
    // MARK: - Status Section
    
    private var statusSection: some View {
        VStack(spacing: 8) {
            if let error = voiceService.lastError {
                HStack {
                    Image(systemName: "exclamationmark.triangle.fill")
                        .foregroundColor(.orange)
                    
                    Text(error.localizedDescription)
                        .font(.caption)
                        .foregroundColor(.orange)
                }
                .padding(.horizontal)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Color.orange.opacity(0.1))
                )
            }
            
            Text("Powered by Gemini 2.0 Flash Live")
                .font(.caption2)
                .foregroundColor(.white.opacity(0.5))
        }
    }
    
    // MARK: - Computed Properties
    
    private var connectionStatusColor: Color {
        switch voiceService.connectionStatus {
        case .connected:
            return .green
        case .connecting:
            return .orange
        case .disconnected, .error:
            return .red
        case .interrupted:
            return .yellow
        }
    }
    
    private var connectionStatusText: String {
        switch voiceService.connectionStatus {
        case .connected:
            return "Connected"
        case .connecting:
            return "Connecting..."
        case .disconnected:
            return "Disconnected"
        case .error:
            return "Connection Error"
        case .interrupted:
            return "Interrupted"
        }
    }
    
    // MARK: - Methods
    
    private func startAudioVisualization() {
        animationTimer = Timer.scheduledTimer(withTimeInterval: 0.1, repeats: true) { _ in
            updateAudioLevels()
        }
    }
    
    private func stopAudioVisualization() {
        animationTimer?.invalidate()
        animationTimer = nil
    }
    
    private func updateAudioLevels() {
        withAnimation(.easeInOut(duration: 0.1)) {
            for i in 0..<audioLevels.count {
                if voiceService.isRecording || voiceService.isPlaying {
                    audioLevels[i] = CGFloat.random(in: 0.1...1.0)
                } else {
                    audioLevels[i] = 0.1
                }
            }
        }
    }
    
    private func endLiveConversation() {
        Task {
            await voiceService.stopLiveConversation()
            await MainActor.run {
                presentationMode.wrappedValue.dismiss()
            }
        }
    }
}

// MARK: - Preview

struct LiveVoiceInterfaceView_Previews: PreviewProvider {
    static var previews: some View {
        LiveVoiceInterfaceView(
            agent: LanguageTutor(
                name: "Sophie",
                language: .french,
                avatar: "🇫🇷",
                personality: "encouraging",
                specialty: "Conversational French",
                description: "A friendly French teacher",
                level: "All Levels",
                rating: 4.9,
                conversationCount: 100
            )
        )
    }
} 