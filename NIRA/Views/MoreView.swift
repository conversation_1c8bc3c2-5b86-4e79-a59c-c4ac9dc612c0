import SwiftUI

// MARK: - More View with Achievements and Leaderboard

struct MoreView: View {
    @StateObject private var analyticsService = LearningAnalyticsService.shared
    @State private var showingAchievements = false
    @State private var showingLeaderboard = false
    @State private var showingSettings = false
    @State private var showingProfile = false
    @State private var showingKnowledgeBase = false

    var body: some View {
        VStack(spacing: 0) {
            // Modern Header
            ModernMoreHeader(
                userName: "Alex",
                currentStreak: 7
            )

            // Main Content
            ScrollView {
                VStack(spacing: 24) {
                    // Quick Stats
                    quickStatsSection

                    // Main Menu Options
                    menuOptionsSection

                    // Social Features
                    socialFeaturesSection

                    // Settings and Support
                    settingsSection
                }
                .padding()
            }
            .background(
                LinearGradient(
                    colors: [Color.niraPrimary.opacity(0.05), Color.clear],
                    startPoint: .top,
                    endPoint: .bottom
                )
            )
        }
        .background(Color(.systemGroupedBackground))
        .navigationBarHidden(true)
        .sheet(isPresented: $showingAchievements) {
            AllAchievementsView()
        }
        .sheet(isPresented: $showingLeaderboard) {
            LeaderboardView()
        }
        .sheet(isPresented: $showingSettings) {
            SettingsView()
        }
        .sheet(isPresented: $showingProfile) {
            ProfileDetailView()
        }
        .sheet(isPresented: $showingKnowledgeBase) {
            KnowledgeBaseView()
        }
    }



    private var quickStatsSection: some View {
        LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 3), spacing: 16) {
            MoreStatCard(title: "Lessons", value: "47", icon: "book.fill", color: .niraPrimary)
            MoreStatCard(title: "Streak", value: "7", icon: "flame.fill", color: .orange)
            MoreStatCard(title: "Points", value: "1.2K", icon: "star.fill", color: .niraSecondary)
        }
    }

    private var menuOptionsSection: some View {
        VStack(spacing: 16) {
            Text("Learning")
                .font(.headline)
                .fontWeight(.semibold)
                .frame(maxWidth: .infinity, alignment: .leading)

            VStack(spacing: 12) {
                MenuOptionRow(
                    icon: "trophy.fill",
                    title: "Achievements",
                    subtitle: "View all your accomplishments",
                    color: .niraSecondary,
                    action: { showingAchievements = true }
                )

                MenuOptionRow(
                    icon: "chart.bar.fill",
                    title: "Leaderboard",
                    subtitle: "Compete with friends",
                    color: .niraPrimary,
                    action: { showingLeaderboard = true }
                )

                MenuOptionRow(
                    icon: "person.2.fill",
                    title: "Friends",
                    subtitle: "Connect with other learners",
                    color: .niraSuccess,
                    action: { /* TODO: Implement friends */ }
                )

                MenuOptionRow(
                    icon: "chart.line.uptrend.xyaxis",
                    title: "Analytics",
                    subtitle: "Detailed learning insights",
                    color: .niraInfo,
                    action: { /* TODO: Navigate to analytics */ }
                )

                MenuOptionRow(
                    icon: "doc.text.fill",
                    title: "Knowledge Base",
                    subtitle: "Manage your learning documents",
                    color: .niraPrimary,
                    action: { showingKnowledgeBase = true }
                )

                NavigationLink(destination: AudioLessonDemoView()) {
                    HStack(spacing: 16) {
                        Image(systemName: "speaker.wave.2.fill")
                            .font(.title2)
                            .foregroundColor(.orange)
                            .frame(width: 30)

                        VStack(alignment: .leading, spacing: 2) {
                            Text("Audio Lessons Demo")
                                .font(.subheadline)
                                .fontWeight(.semibold)
                                .foregroundColor(.primary)

                            Text("Tamil A1 lessons with ElevenLabs audio")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }

                        Spacer()

                        Image(systemName: "chevron.right")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color(.systemBackground))
                            .overlay(
                                RoundedRectangle(cornerRadius: 12)
                                    .stroke(Color(.systemGray5), lineWidth: 1)
                            )
                    )
                }
            }
        }
    }

    private var socialFeaturesSection: some View {
        VStack(spacing: 16) {
            Text("Social")
                .font(.headline)
                .fontWeight(.semibold)
                .frame(maxWidth: .infinity, alignment: .leading)

            VStack(spacing: 12) {
                MenuOptionRow(
                    icon: "square.and.arrow.up",
                    title: "Share Progress",
                    subtitle: "Show off your achievements",
                    color: .niraWarning,
                    action: { /* TODO: Implement sharing */ }
                )

                MenuOptionRow(
                    icon: "heart.fill",
                    title: "Rate NIRA",
                    subtitle: "Help us improve",
                    color: .pink,
                    action: { /* TODO: Implement rating */ }
                )
            }
        }
    }

    private var settingsSection: some View {
        VStack(spacing: 16) {
            Text("Settings & Support")
                .font(.headline)
                .fontWeight(.semibold)
                .frame(maxWidth: .infinity, alignment: .leading)

            VStack(spacing: 12) {
                MenuOptionRow(
                    icon: "gear",
                    title: "Settings",
                    subtitle: "Preferences and notifications",
                    color: .gray,
                    action: { showingSettings = true }
                )

                MenuOptionRow(
                    icon: "questionmark.circle",
                    title: "Help & Support",
                    subtitle: "Get help and contact us",
                    color: .niraInfo,
                    action: { /* TODO: Implement help */ }
                )

                MenuOptionRow(
                    icon: "doc.text",
                    title: "Privacy Policy",
                    subtitle: "How we protect your data",
                    color: .secondary,
                    action: { /* TODO: Show privacy policy */ }
                )
            }
        }
    }
}

struct MoreStatCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color

    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)

            Text(value)
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(.primary)

            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(color.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(color.opacity(0.3), lineWidth: 1)
                )
        )
    }
}

struct MenuOptionRow: View {
    let icon: String
    let title: String
    let subtitle: String
    let color: Color
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            HStack(spacing: 16) {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(color)
                    .frame(width: 30)

                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)

                    Text(subtitle)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                Spacer()

                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemBackground))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(Color(.systemGray5), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - All Achievements View

struct AllAchievementsView: View {
    @Environment(\.presentationMode) var presentationMode
    @StateObject private var analyticsService = LearningAnalyticsService.shared
    @State private var selectedCategory: AchievementCategory = .all

    enum AchievementCategory: String, CaseIterable {
        case all = "All"
        case progress = "progress"
        case performance = "performance"
        case consistency = "consistency"
        case social = "social"
        case special = "special"

        var icon: String {
            switch self {
            case .all: return "star.fill"
            case .progress: return "chart.line.uptrend.xyaxis"
            case .performance: return "target"
            case .consistency: return "calendar"
            case .social: return "person.2"
            case .special: return "star"
            }
        }
    }

    var filteredAchievements: [LearningAchievement] {
        if selectedCategory == .all {
            return analyticsService.achievements
        }
        return analyticsService.achievements.filter { achievement in
            achievement.category.rawValue == selectedCategory.rawValue
        }
    }

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Category Filter
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 12) {
                        ForEach(AchievementCategory.allCases, id: \.self) { category in
                            CategoryChip(
                                category: category,
                                isSelected: selectedCategory == category
                            ) {
                                selectedCategory = category
                            }
                        }
                    }
                    .padding(.horizontal)
                }
                .padding(.vertical)

                // Achievements Grid
                ScrollView {
                    LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                        ForEach(filteredAchievements, id: \.id) { achievement in
                            DetailedAchievementCard(
                                achievement: achievement,
                                isEarned: analyticsService.userAchievements.contains { $0.achievementId == achievement.id }
                            )
                        }
                    }
                    .padding()
                }
            }
            .navigationTitle("Achievements")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
            }
        }
        .task {
            await analyticsService.loadAchievements()
        }
    }
}

struct CategoryChip: View {
    let category: AllAchievementsView.AchievementCategory
    let isSelected: Bool
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            HStack(spacing: 6) {
                Image(systemName: category.icon)
                    .font(.caption)

                Text(category.rawValue)
                    .font(.caption)
                    .fontWeight(.medium)
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(
                Capsule()
                    .fill(isSelected ? Color.niraPrimary : Color(.systemGray6))
            )
            .foregroundColor(isSelected ? .white : .primary)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct DetailedAchievementCard: View {
    let achievement: LearningAchievement
    let isEarned: Bool

    var body: some View {
        VStack(spacing: 12) {
            ZStack {
                Circle()
                    .fill(isEarned ? achievement.difficulty.color.gradient : Color.gray.opacity(0.3).gradient)
                    .frame(width: 60, height: 60)

                Text(achievement.iconName ?? "🏆")
                    .font(.title2)
                    .opacity(isEarned ? 1.0 : 0.5)
            }

            VStack(spacing: 4) {
                Text(achievement.name)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(isEarned ? .primary : .secondary)
                    .multilineTextAlignment(.center)

                Text(achievement.description)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .lineLimit(3)

                HStack(spacing: 4) {
                    Text("\(achievement.pointsReward)")
                        .font(.caption2)
                        .fontWeight(.medium)

                    Image(systemName: "star.fill")
                        .font(.caption2)
                }
                .foregroundColor(achievement.difficulty.color)
                .padding(.horizontal, 8)
                .padding(.vertical, 2)
                .background(
                    Capsule()
                        .fill(achievement.difficulty.color.opacity(0.2))
                )
            }
        }
        .padding()
        .frame(height: 180)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(isEarned ? achievement.difficulty.color.opacity(0.5) : Color.gray.opacity(0.3), lineWidth: 2)
                )
                .shadow(color: isEarned ? achievement.difficulty.color.opacity(0.3) : .clear, radius: 8, x: 0, y: 4)
        )
        .scaleEffect(isEarned ? 1.0 : 0.95)
        .opacity(isEarned ? 1.0 : 0.7)
    }
}

// MARK: - Leaderboard View

struct LeaderboardView: View {
    @Environment(\.presentationMode) var presentationMode
    @State private var selectedTimeframe: MoreViewLeaderboardTimeframe = .weekly
    @State private var leaderboardData: [MoreViewLeaderboardEntry] = []
    @State private var userRank: Int = 15
    @State private var isLoading = true

    enum MoreViewLeaderboardTimeframe: String, CaseIterable {
        case daily = "Daily"
        case weekly = "Weekly"
        case monthly = "Monthly"
        case allTime = "All Time"
    }

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Timeframe Selector
                Picker("Timeframe", selection: $selectedTimeframe) {
                    ForEach(MoreViewLeaderboardTimeframe.allCases, id: \.self) { timeframe in
                        Text(timeframe.rawValue).tag(timeframe)
                    }
                }
                .pickerStyle(SegmentedPickerStyle())
                .padding()

                if isLoading {
                    Spacer()
                    ProgressView("Loading leaderboard...")
                    Spacer()
                } else {
                    ScrollView {
                        VStack(spacing: 16) {
                            // Top 3 Podium
                            if leaderboardData.count >= 3 {
                                podiumView
                            }

                            // Rest of the leaderboard
                            VStack(spacing: 8) {
                                ForEach(Array(leaderboardData.dropFirst(3).enumerated()), id: \.offset) { index, entry in
                                    LeaderboardRow(
                                        entry: entry,
                                        rank: index + 4,
                                        isCurrentUser: entry.isCurrentUser
                                    )
                                }
                            }
                            .padding(.horizontal)

                            // Current user position if not in top
                            if userRank > 10 {
                                currentUserPositionView
                            }
                        }
                        .padding(.bottom)
                    }
                }
            }
            .navigationTitle("Leaderboard")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
            }
        }
        .task {
            await loadLeaderboardData()
        }
        .onChange(of: selectedTimeframe) { _, _ in
            Task {
                await loadLeaderboardData()
            }
        }
    }

    private var podiumView: some View {
        HStack(alignment: .bottom, spacing: 16) {
            // 2nd Place
            PodiumPosition(
                entry: leaderboardData[1],
                rank: 2,
                height: 80,
                color: .gray
            )

            // 1st Place
            PodiumPosition(
                entry: leaderboardData[0],
                rank: 1,
                height: 100,
                color: .niraSecondary
            )

            // 3rd Place
            PodiumPosition(
                entry: leaderboardData[2],
                rank: 3,
                height: 60,
                color: .orange
            )
        }
        .padding()
    }

    private var currentUserPositionView: some View {
        VStack(spacing: 12) {
            Text("Your Position")
                .font(.headline)
                .foregroundColor(.secondary)

            LeaderboardRow(
                                        entry: MoreViewLeaderboardEntry(
                    userId: UUID(),
                    username: "Alex",
                    avatar: "A",
                    points: 850,
                    lessonsCompleted: 25,
                    streak: 7,
                    isCurrentUser: true
                ),
                rank: userRank,
                isCurrentUser: true
            )
            .padding(.horizontal)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.niraPrimary.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.niraPrimary.opacity(0.3), lineWidth: 1)
                )
        )
        .padding(.horizontal)
    }

    private func loadLeaderboardData() async {
        isLoading = true

        // Simulate API call
        try? await Task.sleep(nanoseconds: 1_000_000_000)

        // Mock data - in real app this would come from Supabase
        leaderboardData = [
            MoreViewLeaderboardEntry(userId: UUID(), username: "Sarah", avatar: "S", points: 2450, lessonsCompleted: 89, streak: 23, isCurrentUser: false),
            MoreViewLeaderboardEntry(userId: UUID(), username: "Mike", avatar: "M", points: 2380, lessonsCompleted: 85, streak: 19, isCurrentUser: false),
            MoreViewLeaderboardEntry(userId: UUID(), username: "Emma", avatar: "E", points: 2250, lessonsCompleted: 78, streak: 15, isCurrentUser: false),
            MoreViewLeaderboardEntry(userId: UUID(), username: "David", avatar: "D", points: 2100, lessonsCompleted: 72, streak: 12, isCurrentUser: false),
            MoreViewLeaderboardEntry(userId: UUID(), username: "Lisa", avatar: "L", points: 1950, lessonsCompleted: 68, streak: 9, isCurrentUser: false),
            MoreViewLeaderboardEntry(userId: UUID(), username: "Tom", avatar: "T", points: 1800, lessonsCompleted: 61, streak: 8, isCurrentUser: false),
            MoreViewLeaderboardEntry(userId: UUID(), username: "Anna", avatar: "A", points: 1650, lessonsCompleted: 55, streak: 6, isCurrentUser: false),
            MoreViewLeaderboardEntry(userId: UUID(), username: "Chris", avatar: "C", points: 1500, lessonsCompleted: 48, streak: 5, isCurrentUser: false),
            MoreViewLeaderboardEntry(userId: UUID(), username: "Maya", avatar: "M", points: 1350, lessonsCompleted: 42, streak: 4, isCurrentUser: false),
            MoreViewLeaderboardEntry(userId: UUID(), username: "Jake", avatar: "J", points: 1200, lessonsCompleted: 38, streak: 3, isCurrentUser: false)
        ]

        isLoading = false
    }
}

struct MoreViewLeaderboardEntry {
    let userId: UUID
    let username: String
    let avatar: String
    let points: Int
    let lessonsCompleted: Int
    let streak: Int
    let isCurrentUser: Bool
}

struct PodiumPosition: View {
    let entry: MoreViewLeaderboardEntry
    let rank: Int
    let height: CGFloat
    let color: Color

    var body: some View {
        VStack(spacing: 8) {
            // Avatar
            ZStack {
                Circle()
                    .fill(color.gradient)
                    .frame(width: 50, height: 50)

                Text(entry.avatar)
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
            }

            // Username
            Text(entry.username)
                .font(.caption)
                .fontWeight(.semibold)
                .foregroundColor(.primary)

            // Points
            Text("\(entry.points)")
                .font(.caption2)
                .fontWeight(.medium)
                .foregroundColor(.secondary)

            // Podium
            VStack {
                Text("\(rank)")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
            }
            .frame(width: 60, height: height)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(color.gradient)
            )
        }
    }
}

struct LeaderboardRow: View {
    let entry: MoreViewLeaderboardEntry
    let rank: Int
    let isCurrentUser: Bool

    var body: some View {
        HStack(spacing: 16) {
            // Rank
            Text("\(rank)")
                .font(.headline)
                .fontWeight(.bold)
                .foregroundColor(isCurrentUser ? .niraPrimary : .secondary)
                .frame(width: 30, alignment: .leading)

            // Avatar
            ZStack {
                Circle()
                    .fill(isCurrentUser ? Color.niraPrimary.gradient : Color.gray.gradient)
                    .frame(width: 40, height: 40)

                Text(entry.avatar)
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
            }

            // User info
            VStack(alignment: .leading, spacing: 2) {
                Text(entry.username)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(isCurrentUser ? .niraPrimary : .primary)

                HStack(spacing: 12) {
                    HStack(spacing: 4) {
                        Image(systemName: "star.fill")
                            .font(.caption2)
                            .foregroundColor(.niraSecondary)
                        Text("\(entry.points)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }

                    HStack(spacing: 4) {
                        Image(systemName: "flame.fill")
                            .font(.caption2)
                            .foregroundColor(.orange)
                        Text("\(entry.streak)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }

            Spacer()

            // Lessons completed
            VStack(alignment: .trailing, spacing: 2) {
                Text("\(entry.lessonsCompleted)")
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)

                Text("lessons")
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(isCurrentUser ? Color.niraPrimary.opacity(0.1) : Color(.systemBackground))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(isCurrentUser ? Color.niraPrimary.opacity(0.3) : Color(.systemGray6), lineWidth: 1)
                )
        )
    }
}

// MARK: - Settings and Profile Views (Placeholders)

struct SettingsView: View {
    @Environment(\.presentationMode) var presentationMode

    var body: some View {
        NavigationView {
            List {
                Section("Notifications") {
                    Text("Daily reminders")
                    Text("Achievement alerts")
                    Text("Friend activity")
                }

                Section("Learning") {
                    Text("Daily goal")
                    Text("Difficulty level")
                    Text("Preferred languages")
                }

                Section("Account") {
                    Text("Privacy settings")
                    Text("Data export")
                    Text("Delete account")
                }
            }
            .navigationTitle("Settings")
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
            }
        }
    }
}

struct ProfileDetailView: View {
    @Environment(\.presentationMode) var presentationMode

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // Profile picture and basic info
                    VStack(spacing: 16) {
                        ZStack {
                            Circle()
                                .fill(
                                    LinearGradient(
                                        colors: [.niraPrimary, .niraSecondary],
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    )
                                )
                                .frame(width: 120, height: 120)

                            Text("A")
                                .font(.system(size: 48, weight: .bold))
                                .foregroundColor(.white)
                        }

                        VStack(spacing: 4) {
                            Text("Alex")
                                .font(.title)
                                .fontWeight(.bold)

                            Text("Intermediate Learner")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                        }
                    }

                    // Stats grid
                    LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                        MoreStatCard(title: "Total Lessons", value: "47", icon: "book.fill", color: .niraPrimary)
                        MoreStatCard(title: "Current Streak", value: "7", icon: "flame.fill", color: .orange)
                        MoreStatCard(title: "Total Points", value: "1.2K", icon: "star.fill", color: .niraSecondary)
                        MoreStatCard(title: "Achievements", value: "12", icon: "trophy.fill", color: .niraSuccess)
                    }

                    // Languages learning
                    VStack(alignment: .leading, spacing: 12) {
                        Text("Languages Learning")
                            .font(.headline)
                            .fontWeight(.semibold)

                        VStack(spacing: 8) {
                            LanguageProgressRow(language: "French", level: "A2", progress: 0.65)
                            LanguageProgressRow(language: "Spanish", level: "A1", progress: 0.30)
                            LanguageProgressRow(language: "Japanese", level: "A1", progress: 0.15)
                        }
                    }
                }
                .padding()
            }
            .navigationTitle("Profile")
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
            }
        }
    }
}

struct LanguageProgressRow: View {
    let language: String
    let level: String
    let progress: Double

    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 2) {
                Text(language)
                    .font(.subheadline)
                    .fontWeight(.semibold)

                Text("Level \(level)")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            Spacer()

            VStack(alignment: .trailing, spacing: 2) {
                Text("\(Int(progress * 100))%")
                    .font(.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(.niraPrimary)

                ProgressView(value: progress)
                    .progressViewStyle(LinearProgressViewStyle(tint: .niraPrimary))
                    .frame(width: 80)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemGray6))
        )
    }
}

#Preview {
    MoreView()
}