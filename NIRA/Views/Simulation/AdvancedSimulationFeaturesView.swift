import SwiftUI
import AVFoundation

struct AdvancedSimulationFeaturesView: View {
    @StateObject private var simulationService = SimulationService()
    @State private var selectedTab = 0
    @State private var showingVoiceRecorder = false
    @State private var showingGroupCreation = false
    @State private var learningInsights: [LearningInsight] = []
    
    var body: some View {
        NavigationView {
            TabView(selection: $selectedTab) {
                // Voice Interaction Tab
                VoiceInteractionView(simulationService: simulationService)
                    .tabItem {
                        Image(systemName: "mic.circle.fill")
                        Text("Voice Practice")
                    }
                    .tag(0)
                
                // Social Features Tab
                SocialFeaturesView(simulationService: simulationService)
                    .tabItem {
                        Image(systemName: "person.3.fill")
                        Text("Community")
                    }
                    .tag(1)
                
                // Advanced Analytics Tab
                AdvancedAnalyticsView(
                    simulationService: simulationService,
                    insights: $learningInsights
                )
                .tabItem {
                    Image(systemName: "chart.line.uptrend.xyaxis")
                    Text("Insights")
                }
                .tag(2)
                
                // AR/VR Potential Tab
                ARVRPotentialView()
                    .tabItem {
                        Image(systemName: "arkit")
                        Text("AR/VR")
                    }
                    .tag(3)
            }
            .navigationTitle("Advanced Features")
            .onAppear {
                loadLearningInsights()
            }
        }
    }
    
    private func loadLearningInsights() {
        Task {
            if let userId = getCurrentUserId(),
               let languageId = getCurrentLanguageId() {
                learningInsights = await simulationService.generateLearningInsights(
                    for: userId,
                    languageId: languageId
                )
            }
        }
    }
    
    private func getCurrentUserId() -> UUID? {
        guard let userIdString = UserDefaults.standard.string(forKey: "user_id"),
              let userId = UUID(uuidString: userIdString) else { return nil }
        return userId
    }
    
    private func getCurrentLanguageId() -> UUID? {
        // This would get the current language from user preferences
        return UUID() // Placeholder
    }
}

// MARK: - Voice Interaction View

struct VoiceInteractionView: View {
    let simulationService: SimulationService
    @State private var isRecording = false
    @State private var audioRecorder: AVAudioRecorder?
    @State private var voiceInteractions: [VoiceInteraction] = []
    @State private var selectedSimulation: Simulation?
    @State private var recordingProgress: Double = 0.0
    
    var body: some View {
        VStack(spacing: 20) {
            // Header
            VStack(spacing: 12) {
                Image(systemName: "waveform.circle.fill")
                    .font(.system(size: 60))
                    .foregroundColor(.blue)
                
                Text("Voice Practice")
                    .font(.title)
                    .fontWeight(.bold)
                
                Text("Practice pronunciation and fluency with AI-powered feedback")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            .padding()
            
            // Recording Interface
            VStack(spacing: 20) {
                // Recording Button
                Button(action: { toggleRecording() }) {
                    ZStack {
                        Circle()
                            .fill(isRecording ? Color.red : Color.blue)
                            .frame(width: 120, height: 120)
                        
                        Image(systemName: isRecording ? "stop.fill" : "mic.fill")
                            .font(.system(size: 40))
                            .foregroundColor(.white)
                        
                        if isRecording {
                            Circle()
                                .stroke(Color.red.opacity(0.3), lineWidth: 4)
                                .frame(width: 140, height: 140)
                                .scaleEffect(1 + recordingProgress * 0.2)
                                .animation(.easeInOut(duration: 1).repeatForever(), value: recordingProgress)
                        }
                    }
                }
                .buttonStyle(PlainButtonStyle())
                
                Text(isRecording ? "Recording... Tap to stop" : "Tap to start recording")
                    .font(.headline)
                    .foregroundColor(isRecording ? .red : .primary)
                
                if isRecording {
                    ProgressView(value: recordingProgress)
                        .progressViewStyle(LinearProgressViewStyle(tint: .red))
                        .frame(maxWidth: 200)
                }
            }
            .padding()
            
            // Recent Voice Interactions
            if !voiceInteractions.isEmpty {
                VStack(alignment: .leading, spacing: 16) {
                    Text("Recent Practice Sessions")
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    ScrollView {
                        LazyVStack(spacing: 12) {
                            ForEach(voiceInteractions) { interaction in
                                VoiceInteractionCard(interaction: interaction)
                            }
                        }
                    }
                }
                .padding()
            }
            
            Spacer()
        }
        .onAppear {
            loadVoiceInteractions()
        }
    }
    
    private func toggleRecording() {
        if isRecording {
            stopRecording()
        } else {
            startRecording()
        }
    }
    
    private func startRecording() {
        isRecording = true
        recordingProgress = 0.0
        
        // Simulate recording progress
        Timer.scheduledTimer(withTimeInterval: 0.1, repeats: true) { timer in
            recordingProgress += 0.01
            if recordingProgress >= 1.0 || !isRecording {
                timer.invalidate()
            }
        }
    }
    
    private func stopRecording() {
        isRecording = false
        
        // Simulate processing voice interaction
        Task {
            let audioData = Data() // Placeholder
            if let simulation = selectedSimulation {
                _ = await simulationService.processVoiceInteraction(
                    audioData: audioData,
                    simulationId: simulation.id,
                    dialogueId: UUID() // Placeholder
                )
            }
            loadVoiceInteractions()
        }
    }
    
    private func loadVoiceInteractions() {
        // Load recent voice interactions from the service
        // This would be implemented to fetch from the database
    }
}

struct VoiceInteractionCard: View {
    let interaction: VoiceInteraction
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "waveform")
                    .foregroundColor(.blue)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text("Practice Session")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                    
                    Text(interaction.createdAt, style: .relative)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                if interaction.processingStatus == "completed" {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                } else {
                    ProgressView()
                        .scaleEffect(0.8)
                }
            }
            
            if let transcription = interaction.transcribedText {
                Text("Transcription: \(transcription)")
                    .font(.caption)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 8)
                    .background(Color(.systemGray6))
                    .cornerRadius(8)
            }
            
            if interaction.processingStatus == "completed" {
                HStack(spacing: 16) {
                    ScoreIndicator(
                        title: "Pronunciation",
                        score: interaction.pronunciationScore ?? 0,
                        color: .blue
                    )
                    
                    ScoreIndicator(
                        title: "Fluency",
                        score: interaction.fluencyScore ?? 0,
                        color: .green
                    )
                    
                    ScoreIndicator(
                        title: "Accuracy",
                        score: interaction.accuracyScore ?? 0,
                        color: .orange
                    )
                }
            }
            
            if let feedback = interaction.aiFeedback {
                Text(feedback)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .padding(.top, 4)
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(radius: 2)
    }
}

struct ScoreIndicator: View {
    let title: String
    let score: Double
    let color: Color
    
    var body: some View {
        VStack(spacing: 4) {
            Text("\(Int(score))%")
                .font(.caption)
                .fontWeight(.bold)
                .foregroundColor(color)
            
            Text(title)
                .font(.caption2)
                .foregroundColor(.secondary)
        }
    }
}

// MARK: - Social Features View

struct SocialFeaturesView: View {
    let simulationService: SimulationService
    @State private var publicGroups: [SimulationGroup] = []
    @State private var showingGroupCreation = false
    @State private var newGroupName = ""
    @State private var newGroupDescription = ""
    @State private var isPublicGroup = false
    
    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                // Header
                VStack(spacing: 12) {
                    Image(systemName: "person.3.fill")
                        .font(.system(size: 50))
                        .foregroundColor(.green)
                    
                    Text("Community Features")
                        .font(.title)
                        .fontWeight(.bold)
                    
                    Text("Connect with other learners and share your progress")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
                .padding()
                
                // Create Group Button
                Button(action: { showingGroupCreation = true }) {
                    HStack {
                        Image(systemName: "plus.circle.fill")
                        Text("Create Learning Group")
                    }
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.green)
                    .cornerRadius(12)
                }
                .padding(.horizontal)
                
                // Public Groups
                VStack(alignment: .leading, spacing: 16) {
                    Text("Public Learning Groups")
                        .font(.headline)
                        .fontWeight(.semibold)
                        .padding(.horizontal)
                    
                    if publicGroups.isEmpty {
                        Text("No public groups available")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .padding()
                    } else {
                        LazyVStack(spacing: 12) {
                            ForEach(publicGroups) { group in
                                SimulationGroupCard(group: group, simulationService: simulationService)
                            }
                        }
                        .padding(.horizontal)
                    }
                }
                
                // Sharing Features
                VStack(alignment: .leading, spacing: 16) {
                    Text("Share Your Progress")
                        .font(.headline)
                        .fontWeight(.semibold)
                        .padding(.horizontal)
                    
                    ShareProgressCard()
                        .padding(.horizontal)
                }
            }
        }
        .onAppear {
            loadPublicGroups()
        }
        .sheet(isPresented: $showingGroupCreation) {
            GroupCreationView(
                groupName: $newGroupName,
                groupDescription: $newGroupDescription,
                isPublic: $isPublicGroup,
                simulationService: simulationService,
                onDismiss: { showingGroupCreation = false }
            )
        }
    }
    
    private func loadPublicGroups() {
        Task {
            publicGroups = await simulationService.getPublicSimulationGroups()
        }
    }
}

struct SimulationGroupCard: View {
    let group: SimulationGroup
    let simulationService: SimulationService
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(group.name)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                    
                    if let description = group.description {
                        Text(description)
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .lineLimit(2)
                    }
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 4) {
                    Text("\(group.memberCount) members")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text("\(group.simulationCount) simulations")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            Button("Join Group") {
                // Implement join group functionality
            }
            .font(.caption)
            .fontWeight(.semibold)
            .foregroundColor(.white)
            .frame(maxWidth: .infinity)
            .padding(.vertical, 8)
            .background(Color.blue)
            .cornerRadius(8)
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

struct ShareProgressCard: View {
    var body: some View {
        VStack(spacing: 12) {
            HStack {
                Image(systemName: "square.and.arrow.up")
                    .foregroundColor(.blue)
                
                Text("Share Your Achievements")
                    .font(.subheadline)
                    .fontWeight(.semibold)
                
                Spacer()
            }
            
            HStack(spacing: 12) {
                Button("Share Simulation") {
                    // Implement share simulation
                }
                .font(.caption)
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(Color.blue.opacity(0.2))
                .cornerRadius(8)
                
                Button("Share Progress") {
                    // Implement share progress
                }
                .font(.caption)
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(Color.green.opacity(0.2))
                .cornerRadius(8)
                
                Spacer()
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(radius: 2)
    }
}

struct GroupCreationView: View {
    @Binding var groupName: String
    @Binding var groupDescription: String
    @Binding var isPublic: Bool
    let simulationService: SimulationService
    let onDismiss: () -> Void
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                TextField("Group Name", text: $groupName)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                
                TextField("Description (optional)", text: $groupDescription, axis: .vertical)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .lineLimit(3...6)
                
                Toggle("Public Group", isOn: $isPublic)
                
                Button("Create Group") {
                    Task {
                        _ = await simulationService.createSimulationGroup(
                            name: groupName,
                            description: groupDescription.isEmpty ? nil : groupDescription,
                            isPublic: isPublic
                        )
                        onDismiss()
                    }
                }
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .padding()
                .background(Color.blue)
                .cornerRadius(12)
                .disabled(groupName.isEmpty)
                
                Spacer()
            }
            .padding()
            .navigationTitle("Create Group")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") { onDismiss() }
                }
            }
        }
    }
}

// MARK: - Advanced Analytics View

struct AdvancedAnalyticsView: View {
    let simulationService: SimulationService
    @Binding var insights: [LearningInsight]
    
    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                // Header
                VStack(spacing: 12) {
                    Image(systemName: "chart.line.uptrend.xyaxis")
                        .font(.system(size: 50))
                        .foregroundColor(.purple)
                    
                    Text("Learning Insights")
                        .font(.title)
                        .fontWeight(.bold)
                    
                    Text("AI-powered analysis of your learning patterns")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
                .padding()
                
                // Insights Cards
                LazyVStack(spacing: 16) {
                    ForEach(insights) { insight in
                        SimulationInsightCard(insight: insight)
                    }
                }
                .padding(.horizontal)
                
                if insights.isEmpty {
                    Text("Complete more simulations to generate insights")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .padding()
                }
            }
        }
    }
}

struct SimulationInsightCard: View {
    let insight: LearningInsight
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: iconForInsightType(insight.insightType))
                    .foregroundColor(colorForInsightType(insight.insightType))
                
                Text(insight.insightType.capitalized)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Text("\(Int(insight.confidenceScore))% confidence")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Text(descriptionForInsight(insight))
                .font(.body)
                .foregroundColor(.primary)
            
            if insight.isActionable {
                Text("💡 Actionable insight")
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.blue)
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    
    private func iconForInsightType(_ type: String) -> String {
        switch type {
        case "performance": return "chart.bar.fill"
        case "pattern": return "brain.head.profile"
        case "recommendation": return "lightbulb.fill"
        default: return "info.circle.fill"
        }
    }
    
    private func colorForInsightType(_ type: String) -> Color {
        switch type {
        case "performance": return .blue
        case "pattern": return .purple
        case "recommendation": return .orange
        default: return .gray
        }
    }
    
    private func descriptionForInsight(_ insight: LearningInsight) -> String {
        // Generate description based on insight data
        switch insight.insightType {
        case "performance":
            let score = insight.insightData["average_score"]?.value as? Double ?? 0
            return "Your average performance is \(Int(score))%. \(score > 75 ? "Great progress!" : "Focus on practice to improve.")"
        case "pattern":
            return "You prefer interactive learning with visual elements. This learning style suits simulation-based practice."
        case "recommendation":
            return "Focus on cultural competency scenarios to strengthen your weakest areas."
        default:
            return "AI-generated insight about your learning progress."
        }
    }
}

// MARK: - AR/VR Potential View

struct ARVRPotentialView: View {
    var body: some View {
        ScrollView {
            VStack(spacing: 30) {
                // Header
                VStack(spacing: 12) {
                    Image(systemName: "arkit")
                        .font(.system(size: 60))
                        .foregroundColor(.purple)
                    
                    Text("AR/VR Integration")
                        .font(.title)
                        .fontWeight(.bold)
                    
                    Text("Future immersive learning experiences")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
                .padding()
                
                // AR Features
                FeatureCard(
                    icon: "camera.viewfinder",
                    title: "Augmented Reality",
                    description: "Point your camera at objects to practice vocabulary in real-world contexts",
                    color: .blue,
                    isComingSoon: true
                )
                
                // VR Features
                FeatureCard(
                    icon: "visionpro",
                    title: "Virtual Reality",
                    description: "Immerse yourself in realistic scenarios like ordering at a restaurant or attending meetings",
                    color: .purple,
                    isComingSoon: true
                )
                
                // Mixed Reality
                FeatureCard(
                    icon: "globe.americas.fill",
                    title: "Mixed Reality",
                    description: "Blend digital characters with your real environment for interactive conversations",
                    color: .green,
                    isComingSoon: true
                )
                
                // 3D Environments
                FeatureCard(
                    icon: "cube.fill",
                    title: "3D Environments",
                    description: "Explore virtual cities, offices, and cultural sites while practicing language skills",
                    color: .orange,
                    isComingSoon: true
                )
            }
            .padding()
        }
    }
}

struct FeatureCard: View {
    let icon: String
    let title: String
    let description: String
    let color: Color
    let isComingSoon: Bool
    
    var body: some View {
        VStack(spacing: 16) {
            HStack {
                Image(systemName: icon)
                    .font(.system(size: 30))
                    .foregroundColor(color)
                
                VStack(alignment: .leading, spacing: 4) {
                    HStack {
                        Text(title)
                            .font(.headline)
                            .fontWeight(.semibold)
                        
                        if isComingSoon {
                            Text("Coming Soon")
                                .font(.caption)
                                .fontWeight(.medium)
                                .foregroundColor(.white)
                                .padding(.horizontal, 8)
                                .padding(.vertical, 2)
                                .background(color)
                                .cornerRadius(8)
                        }
                    }
                    
                    Text(description)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

#Preview {
    AdvancedSimulationFeaturesView()
} 