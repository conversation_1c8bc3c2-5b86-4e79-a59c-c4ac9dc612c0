import SwiftUI

struct TestAIView: View {
    @StateObject private var geminiService = GeminiService.shared
    @State private var userInput: String = ""
    @State private var aiResponse: String = ""
    @State private var isGenerating: Bool = false
    @State private var selectedLanguage: Language = .french
    @State private var selectedDifficulty: Difficulty = .beginner
    @State private var selectedCategory: LessonCategory = .conversation

    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                Text("🤖 AI Testing Interface")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .padding(.top)

                // Configuration Section
                VStack(alignment: .leading, spacing: 12) {
                    Text("Configuration")
                        .font(.headline)

                    HStack {
                        Text("Language:")
                        Picker("Language", selection: $selectedLanguage) {
                            ForEach(Language.allCases, id: \.self) { language in
                                Text(language.displayName).tag(language)
                            }
                        }
                        .pickerStyle(SegmentedPickerStyle())
                    }

                    HStack {
                        Text("Difficulty:")
                        Picker("Difficulty", selection: $selectedDifficulty) {
                            ForEach([Difficulty.beginner, Difficulty.intermediate, Difficulty.advanced], id: \.self) { difficulty in
                                Text(difficulty.displayName).tag(difficulty)
                            }
                        }
                        .pickerStyle(SegmentedPickerStyle())
                    }
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(12)

                // Test Buttons
                VStack(spacing: 12) {
                    Button("🎓 Generate Lesson") {
                        Task {
                            await testLessonGeneration()
                        }
                    }
                    .buttonStyle(TestAIButtonStyle(color: .blue))

                    Button("📝 Generate Exercises") {
                        Task {
                            await testExerciseGeneration()
                        }
                    }
                    .buttonStyle(TestAIButtonStyle(color: .green))

                    Button("🌍 Generate Cultural Context") {
                        Task {
                            await testCulturalGeneration()
                        }
                    }
                    .buttonStyle(TestAIButtonStyle(color: .purple))
                }

                // Direct Chat Test
                VStack(alignment: .leading, spacing: 12) {
                    Text("Direct Chat Test")
                        .font(.headline)

                    TextField("Ask the AI something...", text: $userInput, axis: .vertical)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                        .lineLimit(3)

                    Button("Send to AI") {
                        Task {
                            await testDirectChat()
                        }
                    }
                    .buttonStyle(TestAIButtonStyle(color: .orange))
                    .disabled(userInput.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(12)

                // Response Display
                ScrollView {
                    VStack(alignment: .leading, spacing: 8) {
                        Text("AI Response:")
                            .font(.headline)

                        if isGenerating {
                            HStack {
                                ProgressView()
                                    .scaleEffect(0.8)
                                Text("AI is thinking...")
                                    .foregroundColor(.secondary)
                            }
                        } else if !aiResponse.isEmpty {
                            Text(aiResponse)
                                .padding()
                                .background(Color(.systemBackground))
                                .cornerRadius(8)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 8)
                                        .stroke(Color.blue.opacity(0.3), lineWidth: 1)
                                )
                        } else {
                            Text("No response yet. Try one of the test buttons above!")
                                .foregroundColor(.secondary)
                                .italic()
                        }
                    }
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)

                Spacer()
            }
            .padding()
            .navigationTitle("AI Test")
            .navigationBarTitleDisplayMode(.inline)
        }
    }

    // Test Functions
    private func testLessonGeneration() async {
        isGenerating = true
        aiResponse = ""

        do {
            let lesson = try await geminiService.generateLesson(
                language: selectedLanguage,
                difficulty: selectedDifficulty,
                category: selectedCategory
            )

            await MainActor.run {
                aiResponse = """
                ✅ LESSON GENERATED SUCCESSFULLY!

                Title: \(lesson.title)
                Description: \(lesson.description)
                Duration: \(lesson.estimatedDuration) minutes

                Objectives:
                Learning objectives will be displayed here

                Vocabulary (\(lesson.vocabulary.count) words):
                \(lesson.vocabulary.prefix(3).map { "• \($0.word) = \($0.translation)" }.joined(separator: "\n"))

                Grammar Points:
                \(lesson.grammarPoints.map { "• \($0.rule): \($0.explanation)" }.joined(separator: "\n"))
                """
            }
        } catch {
            await MainActor.run {
                aiResponse = "❌ Error generating lesson: \(error.localizedDescription)"
            }
        }

        isGenerating = false
    }

    private func testExerciseGeneration() async {
        isGenerating = true
        aiResponse = ""

        // Create a mock lesson for exercise generation
        let mockLesson = Lesson(
            title: "Test Lesson",
            lessonDescription: "A test lesson for exercise generation",
            language: selectedLanguage,
            difficulty: selectedDifficulty,
            category: selectedCategory,
            estimatedDuration: 15
        )

        do {
            let exercises = try await geminiService.generateExercises(for: mockLesson, count: 3)

            await MainActor.run {
                aiResponse = """
                ✅ EXERCISES GENERATED SUCCESSFULLY!

                Generated \(exercises.count) exercises:

                \(exercises.enumerated().map { index, exercise in
                    """
                    Exercise \(index + 1):
                    Type: \(exercise.type)
                    Question: \(exercise.question)
                    Options: \(exercise.options?.joined(separator: ", ") ?? "N/A")
                    Answer: \(exercise.correctAnswer ?? "N/A")
                    Points: \(exercise.points)
                    """
                }.joined(separator: "\n\n"))
                """
            }
        } catch {
            await MainActor.run {
                aiResponse = "❌ Error generating exercises: \(error.localizedDescription)"
            }
        }

        isGenerating = false
    }

    private func testCulturalGeneration() async {
        isGenerating = true
        aiResponse = ""

        do {
            let context = try await geminiService.generateCulturalContext(
                language: selectedLanguage,
                scenario: "Restaurant dining",
                difficulty: selectedDifficulty
            )

            await MainActor.run {
                aiResponse = """
                ✅ CULTURAL CONTEXT GENERATED SUCCESSFULLY!

                Scenario: \(context.scenario)
                Setting: \(context.setting)

                Participants: \(context.participants.joined(separator: ", "))

                Social Norms:
                \(context.socialNorms.map { "• \($0)" }.joined(separator: "\n"))

                Tips:
                \(context.tips.map { "• \($0)" }.joined(separator: "\n"))

                Do's and Don'ts:
                \(context.doAndDonts.map { "• \($0)" }.joined(separator: "\n"))
                """
            }
        } catch {
            await MainActor.run {
                aiResponse = "❌ Error generating cultural context: \(error.localizedDescription)"
            }
        }

        isGenerating = false
    }

    private func testDirectChat() async {
        let userMessage = userInput
        userInput = ""
        isGenerating = true

        await MainActor.run {
            aiResponse = "You asked: \"\(userMessage)\"\n\nAI is responding..."
        }

        // Create a simple prompt for direct testing
        let prompt = """
        You are a helpful language learning AI assistant. The user said: "\(userMessage)"

        Respond helpfully and conversationally. If they're asking about language learning, provide useful tips. If they're practicing a language, help them with corrections and encouragement. Keep your response concise but helpful.
        """

        do {
            // Make direct API call to test Gemini 2.0 Flash
            guard let url = URL(string: "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=\(APIKeys.geminiAPIKey)") else {
                throw NSError(domain: "TestAI", code: 1, userInfo: [NSLocalizedDescriptionKey: "Invalid API URL"])
            }

            var request = URLRequest(url: url)
            request.httpMethod = "POST"
            request.setValue("application/json", forHTTPHeaderField: "Content-Type")

            let requestBody = [
                "contents": [
                    [
                        "parts": [
                            ["text": prompt]
                        ]
                    ]
                ]
            ]

            request.httpBody = try JSONSerialization.data(withJSONObject: requestBody)

            let (data, response) = try await URLSession.shared.data(for: request)

            guard let httpResponse = response as? HTTPURLResponse,
                  httpResponse.statusCode == 200 else {
                throw NSError(domain: "TestAI", code: 2, userInfo: [NSLocalizedDescriptionKey: "API request failed"])
            }

            if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any],
               let candidates = json["candidates"] as? [[String: Any]],
               let firstCandidate = candidates.first,
               let content = firstCandidate["content"] as? [String: Any],
               let parts = content["parts"] as? [[String: Any]],
               let firstPart = parts.first,
               let text = firstPart["text"] as? String {

                await MainActor.run {
                    aiResponse = """
                    ✅ DIRECT AI CHAT SUCCESS!

                    You: \(userMessage)

                    AI: \(text)

                    🎉 Your Gemini 2.0 Flash integration is working!
                    """
                }
            } else {
                await MainActor.run {
                    aiResponse = "❌ Could not parse AI response"
                }
            }

        } catch {
            await MainActor.run {
                aiResponse = """
                ❌ DIRECT CHAT FAILED

                Error: \(error.localizedDescription)

                Check your API key and internet connection.
                """
            }
        }

        isGenerating = false
    }
}

struct TestAIButtonStyle: ButtonStyle {
    let color: Color

    func makeBody(configuration: Self.Configuration) -> some View {
        configuration.label
            .font(Font.headline)
            .foregroundColor(Color.white)
            .frame(maxWidth: CGFloat.infinity)
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(color)
                    .opacity(configuration.isPressed ? 0.8 : 1.0)
            )
            .scaleEffect(configuration.isPressed ? 0.98 : 1.0)
            .animation(Animation.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}

#Preview {
    TestAIView()
}