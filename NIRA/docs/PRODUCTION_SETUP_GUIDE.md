# 🚀 NIRA Production Setup Guide

## 📋 Overview

This guide will help you configure real API keys to activate all NIRA features and move from development mode to production mode.

## 🔑 Step 1: Get Your API Keys

### 1.1 Gemini API Key
1. Go to [Google AI Studio](https://ai.google.dev/tutorials/setup)
2. Click "Get API Key"
3. Create a new project or select existing
4. Generate API key (starts with `AIzaSy...`)
5. Copy the key securely

### 1.2 Supabase Configuration
1. Go to [Supabase Dashboard](https://app.supabase.com)
2. Select your project (or create new one)
3. Go to **Settings** → **API**
4. Copy:
   - **Project URL** (e.g., `https://abcdefgh.supabase.co`)
   - **Anon/Public Key** (starts with `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`)

### 1.3 OpenAI API Key (Optional)
1. Go to [OpenAI Platform](https://platform.openai.com/api-keys)
2. Create new secret key
3. Copy the key (starts with `sk-...`)

## ⚙️ Step 2: Configure API Keys

### Method 1: Using ConfigureAPIKeys.swift (Recommended)

1. **Open** `NIRA/Config/ConfigureAPIKeys.swift`

2. **Replace the placeholder values** with your real keys:
   ```swift
   // Replace these with your actual keys:
   let geminiAPIKey = "AIzaSyYourActualGeminiKeyHere"
   let supabaseURL = "https://yourprojectid.supabase.co"
   let supabaseAnonKey = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.yourkey..."
   let openAIAPIKey = "sk-yourOpenAIKeyHere" // Optional
   ```

3. **Run the configuration** by adding this to your app startup or calling from Xcode console:
   ```swift
   APIKeyConfigurator.configureProductionKeys()
   ```

### Method 2: Direct Configuration (Advanced)

```swift
#if DEBUG
try SecureAPIKeys.storeAPIKeys(
    gemini: "AIzaSyYourActualGeminiKeyHere",
    supabaseURL: "https://yourprojectid.supabase.co",
    supabaseKey: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.yourkey...",
    openAI: "sk-yourOpenAIKeyHere" // Optional
)
#endif
```

## ✅ Step 3: Verify Configuration

### 3.1 Check Status
Run this to verify your configuration:
```swift
APIKeyConfigurator.checkCurrentConfiguration()
```

### 3.2 Expected Output
```
🔍 Current API Key Configuration Status:
=====================================
🔑 Gemini API Key: ✅ Configured
🗄️ Supabase URL: ✅ Configured
🔐 Supabase Anon Key: ✅ Configured
🤖 OpenAI API Key: ✅ Configured

🎯 Overall Status: ✅ READY FOR PRODUCTION
```

### 3.3 App Console Output
When properly configured, you should see:
```
✅ Created in-memory ModelContainer to avoid migration issues
✅ Supabase client initialized with production URL
✅ User authenticated successfully
✅ Content cache service initialized
✅ Essential content preloaded
```

## 🎯 Step 4: Test Features

### 4.1 Supabase Features
- ✅ User authentication
- ✅ Lesson loading from database
- ✅ Progress tracking
- ✅ Real-time collaboration

### 4.2 AI Features
- ✅ Gemini chat responses
- ✅ Live voice conversations
- ✅ Content generation
- ✅ Grammar corrections

### 4.3 Advanced Features
- ✅ Multi-agent conversations
- ✅ Cultural context insights
- ✅ Pronunciation assessment
- ✅ Adaptive learning paths

## 🔒 Security Notes

### ✅ Secure Storage
- Keys are stored in iOS Keychain
- Encrypted and app-specific
- Persist between app launches
- Not accessible to other apps

### ✅ Development Safety
- Configuration only works in DEBUG builds
- No keys stored in source code
- Safe to commit ConfigureAPIKeys.swift
- Automatic validation and error handling

### ✅ Production Deployment
- Keys automatically used in production
- No additional configuration needed
- Secure network communication
- Rate limiting and error handling

## 🚨 Troubleshooting

### Issue: "Development Mode" Still Active
**Solution**: Verify all keys are properly configured
```swift
APIKeyConfigurator.checkCurrentConfiguration()
```

### Issue: Supabase Connection Errors
**Solution**: Check URL format and anon key
- URL should be: `https://yourproject.supabase.co`
- Key should start with: `eyJhbGciOiJIUzI1NiIs...`

### Issue: Gemini API Errors
**Solution**: Verify API key and quota
- Key should start with: `AIzaSy...`
- Check [Google Cloud Console](https://console.cloud.google.com) for quota

### Issue: Keys Not Persisting
**Solution**: Ensure DEBUG build and proper error handling
```swift
do {
    try SecureAPIKeys.validateConfiguration()
    print("✅ Configuration valid")
} catch {
    print("❌ Configuration error: \(error)")
}
```

## 🎉 Success!

Once configured, your NIRA app will have:
- ⚡ **Full AI-powered conversations**
- 🗄️ **Real-time database sync**
- 🎙️ **Live voice interactions**
- 📊 **Advanced analytics**
- 🌍 **Multi-language support**
- 🔒 **Enterprise-grade security**

**Your NIRA app is now ready for production! 🚀**
