# NIRA Content Generation Guide

A comprehensive guide for generating high-quality language learning content using AI and best practices derived from NIRA's development.

## 🎯 **Overview**

This guide consolidates all content generation knowledge from NIRA's development into reusable templates and best practices. Use this as the single source of truth for content generation.

## 📋 **Content Generation Workflow**

### **Phase 1: Planning**
1. **Language Analysis**: Identify target language and CEFR levels
2. **Content Audit**: Check existing content gaps
3. **Vocabulary Planning**: Plan 25 words per CEFR level
4. **Cultural Integration**: Research cultural context and scenarios

### **Phase 2: Generation**
1. **AI-Powered Creation**: Use Gemini 2.0 Flash for content generation
2. **Quality Assurance**: Validate content structure and accuracy
3. **Database Integration**: Upload to <PERSON>pa<PERSON> with proper metadata
4. **iOS Integration**: Verify content appears correctly in app

### **Phase 3: Enhancement**
1. **AI Agent Integration**: Ensure tutors can reference content
2. **User Testing**: Validate learning effectiveness
3. **Iterative Improvement**: Refine based on feedback

## 🛠 **Content Generation Template**

### **Master Content Generator Script**

```python
#!/usr/bin/env python3
"""
NIRA Content Generator - Master Template
Generates comprehensive language learning content using Gemini AI
"""

import json
import time
import logging
from datetime import datetime
from typing import Dict, List, Optional
from supabase import create_client, Client

# Configuration
GEMINI_API_KEY = "your-gemini-api-key"
SUPABASE_URL = "your-supabase-url"
SUPABASE_KEY = "your-supabase-anon-key"

# Initialize clients
supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)

# Logging setup
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'content_generation_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)

class ContentGenerator:
    """Master content generator for NIRA language learning content"""
    
    def __init__(self):
        self.generated_count = 0
        self.uploaded_count = 0
        self.failed_count = 0
    
    def generate_lesson_content(self, language: str, level: str, topic: str) -> Dict:
        """Generate comprehensive lesson content using AI"""
        
        prompt = self._create_lesson_prompt(language, level, topic)
        
        try:
            # Call Gemini API
            response = self._call_gemini_api(prompt)
            content = self._parse_ai_response(response)
            
            # Validate content structure
            if self._validate_content(content):
                self.generated_count += 1
                logging.info(f"✅ Generated lesson: {content.get('title', 'Untitled')}")
                return content
            else:
                raise ValueError("Content validation failed")
                
        except Exception as e:
            self.failed_count += 1
            logging.error(f"❌ Failed to generate lesson: {str(e)}")
            return None
    
    def _create_lesson_prompt(self, language: str, level: str, topic: str) -> str:
        """Create AI prompt based on NIRA's proven templates"""
        
        # CEFR-specific vocabulary targets
        vocab_targets = {
            "A1": 8, "A2": 10, "B1": 12, "B2": 15, "C1": 18, "C2": 20
        }
        
        vocab_count = vocab_targets.get(level, 10)
        
        return f"""
        Create a comprehensive {language} language lesson for {level} level on the topic: {topic}

        REQUIREMENTS:
        1. VOCABULARY: Exactly {vocab_count} words with:
           - {language} word with proper spelling/characters
           - English translation
           - Pronunciation guide (IPA when possible)
           - Part of speech
           - Example sentence in {language}
           - Example sentence translation
           - Cultural context (when relevant)

        2. GRAMMAR POINTS: 2-3 key grammar concepts with:
           - Clear explanations
           - Multiple examples
           - Common mistakes to avoid
           - Practice tips

        3. CULTURAL CONTEXT: Real-world scenarios including:
           - Cultural etiquette and customs
           - Do's and don'ts
           - Regional variations
           - Practical usage tips

        4. DIALOGUES: 6-8 realistic conversations with:
           - Natural, authentic exchanges
           - Cultural context notes
           - English translations
           - Pronunciation guides

        5. EXERCISES: Interactive practice including:
           - Multiple choice questions (5)
           - Fill-in-the-blank exercises (5)
           - Matching activities (3)
           - Pronunciation practice (3)

        LEVEL-SPECIFIC GUIDELINES:
        {self._get_level_guidelines(level)}

        CULTURAL AUTHENTICITY:
        - Use authentic cultural scenarios
        - Include proper etiquette and social norms
        - Reference real places and situations
        - Avoid stereotypes, focus on practical cultural knowledge

        OUTPUT FORMAT: Valid JSON with this exact structure:
        {{
            "title": "Engaging lesson title",
            "description": "Brief lesson description",
            "vocabulary": [array of vocabulary objects],
            "grammar_points": [array of grammar objects],
            "cultural_context": [array of cultural notes],
            "dialogues": [array of dialogue objects],
            "exercises": [array of exercise objects]
        }}
        """
    
    def _get_level_guidelines(self, level: str) -> str:
        """Get CEFR level-specific content guidelines"""
        
        guidelines = {
            "A1": """
            - Basic survival vocabulary (greetings, numbers, family, food)
            - Present tense focus
            - Simple sentence structures
            - Everyday situations (introductions, shopping, directions)
            """,
            "A2": """
            - Daily life vocabulary (activities, places, time, weather)
            - Past and future tenses introduction
            - Simple descriptions and comparisons
            - Routine activities and personal information
            """,
            "B1": """
            - Intermediate vocabulary (work, education, hobbies, travel)
            - Complex sentence structures
            - Expressing opinions and preferences
            - Problem-solving and decision-making scenarios
            """,
            "B2": """
            - Professional and academic vocabulary
            - Advanced grammar structures
            - Abstract concepts and ideas
            - Formal and informal registers
            """,
            "C1": """
            - Sophisticated vocabulary (academic, professional, cultural)
            - Complex grammatical structures
            - Nuanced expressions and idioms
            - Critical thinking and analysis topics
            """,
            "C2": """
            - Advanced literary and academic vocabulary
            - Mastery of all grammatical structures
            - Cultural and historical references
            - Professional and academic discourse
            """
        }
        
        return guidelines.get(level, guidelines["A1"])
    
    def upload_to_supabase(self, content: Dict, language: str, level: str) -> bool:
        """Upload generated content to Supabase database"""
        
        try:
            # Prepare lesson data
            lesson_data = {
                "title": content["title"],
                "description": content["description"],
                "content": content,
                "language": language.lower(),
                "level": level,
                "topic": content.get("topic", "General"),
                "difficulty_level": self._get_difficulty_number(level),
                "estimated_duration_minutes": self._calculate_duration(content),
                "lesson_type": "ai_generated",
                "is_published": True,
                "metadata": {
                    "generated_at": datetime.now().isoformat(),
                    "generator_version": "2.0",
                    "vocabulary_count": len(content.get("vocabulary", [])),
                    "exercise_count": len(content.get("exercises", []))
                }
            }
            
            # Upload to database
            result = supabase.table("lessons").insert(lesson_data).execute()
            
            if result.data:
                self.uploaded_count += 1
                logging.info(f"✅ Uploaded lesson: {content['title']}")
                return True
            else:
                raise Exception("No data returned from insert")
                
        except Exception as e:
            self.failed_count += 1
            logging.error(f"❌ Failed to upload lesson: {str(e)}")
            return False
    
    def _get_difficulty_number(self, level: str) -> int:
        """Convert CEFR level to difficulty number"""
        mapping = {"A1": 1, "A2": 2, "B1": 3, "B2": 4, "C1": 5, "C2": 6}
        return mapping.get(level, 1)
    
    def _calculate_duration(self, content: Dict) -> int:
        """Calculate estimated lesson duration based on content"""
        base_duration = 15
        vocab_time = len(content.get("vocabulary", [])) * 1
        exercise_time = len(content.get("exercises", [])) * 2
        dialogue_time = len(content.get("dialogues", [])) * 3
        
        return base_duration + vocab_time + exercise_time + dialogue_time
    
    def generate_batch_content(self, config: Dict) -> None:
        """Generate content in batches based on configuration"""
        
        languages = config.get("languages", ["French"])
        levels = config.get("levels", ["A1"])
        topics = config.get("topics", ["Basic Greetings"])
        
        total_lessons = len(languages) * len(levels) * len(topics)
        current_lesson = 0
        
        logging.info(f"🚀 Starting batch generation: {total_lessons} lessons")
        
        for language in languages:
            for level in levels:
                for topic in topics:
                    current_lesson += 1
                    logging.info(f"📚 Generating lesson {current_lesson}/{total_lessons}: {language} {level} - {topic}")
                    
                    # Generate content
                    content = self.generate_lesson_content(language, level, topic)
                    
                    if content:
                        # Upload to database
                        self.upload_to_supabase(content, language, level)
                    
                    # Rate limiting
                    time.sleep(2)
        
        # Final report
        self._print_generation_report()
    
    def _print_generation_report(self) -> None:
        """Print final generation statistics"""
        
        logging.info("📊 Generation Complete!")
        logging.info(f"   Generated: {self.generated_count}")
        logging.info(f"   Uploaded: {self.uploaded_count}")
        logging.info(f"   Failed: {self.failed_count}")
        
        if self.failed_count > 0:
            logging.warning(f"⚠️  {self.failed_count} lessons failed. Check logs for details.")

# Usage Examples
if __name__ == "__main__":
    generator = ContentGenerator()
    
    # Example 1: Single lesson
    content = generator.generate_lesson_content("French", "A1", "Café Ordering")
    if content:
        generator.upload_to_supabase(content, "French", "A1")
    
    # Example 2: Batch generation
    batch_config = {
        "languages": ["French", "Spanish", "Japanese"],
        "levels": ["A1", "A2"],
        "topics": ["Basic Greetings", "Food and Dining", "Travel Essentials"]
    }
    generator.generate_batch_content(batch_config)
```

## 📊 **Content Quality Standards**

### **Vocabulary Requirements**
- **A1**: 8 basic survival words (greetings, numbers, family)
- **A2**: 10 daily life words (activities, places, time)
- **B1**: 12 intermediate words (work, education, hobbies)
- **B2**: 15 professional words (business, media, society)
- **C1**: 18 advanced academic words (philosophy, research)
- **C2**: 20 sophisticated terms (literary, cultural concepts)

### **Cultural Authenticity Checklist**
- ✅ Real-world scenarios and situations
- ✅ Proper cultural etiquette and customs
- ✅ Regional variations when relevant
- ✅ Authentic dialogue and expressions
- ✅ Cultural do's and don'ts
- ❌ Stereotypes or oversimplifications
- ❌ Outdated cultural references
- ❌ Generic, non-specific content

### **Technical Quality Standards**
- ✅ Valid JSON structure
- ✅ Proper character encoding (UTF-8)
- ✅ Consistent data types
- ✅ Required fields populated
- ✅ Reasonable content length
- ✅ Error handling and validation

## 🔧 **Database Integration**

### **Supabase Schema Requirements**

```sql
-- Lessons table structure
CREATE TABLE lessons (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title TEXT NOT NULL,
    description TEXT,
    content JSONB NOT NULL,
    language TEXT NOT NULL,
    level TEXT NOT NULL,
    topic TEXT NOT NULL,
    difficulty_level INTEGER NOT NULL,
    estimated_duration_minutes INTEGER DEFAULT 25,
    lesson_type TEXT DEFAULT 'ai_generated',
    is_published BOOLEAN DEFAULT true,
    is_active BOOLEAN DEFAULT true,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_lessons_language ON lessons(language);
CREATE INDEX idx_lessons_level ON lessons(level);
CREATE INDEX idx_lessons_difficulty ON lessons(difficulty_level);
CREATE INDEX idx_lessons_active ON lessons(is_active);
```

### **Content Structure Template**

```json
{
  "title": "Lesson Title",
  "description": "Brief description",
  "vocabulary": [
    {
      "word": "bonjour",
      "translation": "hello",
      "pronunciation": "/bon.ˈʒuʁ/",
      "part_of_speech": "interjection",
      "example": "Bonjour, comment allez-vous?",
      "example_translation": "Hello, how are you?",
      "cultural_context": "Used in formal and informal settings"
    }
  ],
  "grammar_points": [
    {
      "concept": "Present tense conjugation",
      "explanation": "French verbs change endings based on subject",
      "examples": ["je parle", "tu parles", "il/elle parle"],
      "common_mistakes": ["Don't forget the 's' in 'tu parles'"],
      "practice_tips": ["Practice with regular -er verbs first"]
    }
  ],
  "cultural_context": [
    {
      "topic": "Greeting etiquette",
      "description": "French greeting customs vary by region and relationship",
      "dos": ["Use 'vous' for formal situations", "Shake hands in business"],
      "donts": ["Don't use first names immediately", "Avoid overly casual greetings"],
      "regional_notes": "In southern France, cheek kisses are more common"
    }
  ],
  "dialogues": [
    {
      "speakers": ["Customer", "Waiter"],
      "exchanges": [
        {
          "speaker": "Customer",
          "text": "Bonjour, je voudrais un café, s'il vous plaît.",
          "translation": "Hello, I would like a coffee, please.",
          "pronunciation": "/bon.ˈʒuʁ ʒə vu.ˈdʁɛ œ̃ ka.ˈfe sil vu ˈplɛ/"
        }
      ],
      "cultural_notes": "Always greet before ordering in French cafés"
    }
  ],
  "exercises": [
    {
      "type": "multiple_choice",
      "question": "How do you say 'hello' in French?",
      "options": ["Bonjour", "Bonsoir", "Salut", "Au revoir"],
      "correct_answer": 0,
      "explanation": "Bonjour is the standard greeting for hello",
      "points": 10
    }
  ]
}
```

## 🎯 **Best Practices**

### **Content Generation**
1. **Consistency**: Use standardized templates and structures
2. **Quality over Quantity**: Focus on high-quality, culturally authentic content
3. **Progressive Difficulty**: Ensure proper CEFR level progression
4. **Cultural Sensitivity**: Research and validate cultural content
5. **Regular Updates**: Keep content current and relevant

### **Technical Implementation**
1. **Error Handling**: Implement comprehensive error handling and logging
2. **Rate Limiting**: Respect API rate limits with appropriate delays
3. **Validation**: Validate all generated content before database insertion
4. **Monitoring**: Track generation metrics and success rates
5. **Backup**: Maintain backups of generated content

### **AI Integration**
1. **Prompt Engineering**: Use proven prompt templates
2. **Response Parsing**: Implement robust JSON parsing with fallbacks
3. **Quality Assurance**: Validate AI-generated content for accuracy
4. **Iterative Improvement**: Refine prompts based on output quality
5. **Fallback Strategies**: Have backup plans for API failures

## 📈 **Performance Metrics**

### **Content Quality Metrics**
- **Vocabulary Accuracy**: 95%+ correct translations and pronunciations
- **Cultural Authenticity**: Validated by native speakers
- **Grammar Accuracy**: Linguistically correct examples
- **Exercise Effectiveness**: Proven learning outcomes

### **Technical Performance**
- **Generation Success Rate**: 95%+ successful content generation
- **Upload Success Rate**: 99%+ successful database uploads
- **Response Time**: <10 seconds per lesson generation
- **Error Rate**: <5% failures with proper error handling

## 🔄 **Maintenance and Updates**

### **Regular Maintenance Tasks**
1. **Content Audits**: Monthly review of generated content quality
2. **Cultural Updates**: Quarterly updates for cultural relevance
3. **Technical Updates**: Regular updates to AI prompts and templates
4. **Performance Monitoring**: Continuous monitoring of generation metrics
5. **User Feedback Integration**: Incorporate user feedback into content improvements

### **Version Control**
- Use semantic versioning for content generator scripts
- Maintain changelog for all content generation updates
- Tag releases with comprehensive documentation
- Archive old versions for rollback capabilities

---

**🚀 This guide represents the consolidated knowledge from NIRA's content generation development. Use it as the single source of truth for all future content generation work.** 