# NIRA Content Generator

A Python script that generates comprehensive language learning content using Google's Gemini AI and uploads it directly to your Supabase database.

## 🚀 **Current Status: Integrated with AI Agents**

The content generator is now fully integrated with NIRA's AI-powered tutors. Generated lessons appear in the iOS app and can be enhanced by AI agents during conversations.

## Features

- **AI-Powered Content**: Uses Gemini 2.0 Flash for high-quality lesson generation
- **Multiple Languages**: Supports French, Spanish, German, Italian, Japanese, Tamil, and more
- **CEFR Levels**: Generates content for A1 through C2 proficiency levels
- **iOS App Integration**: Lessons appear directly in the NIRA iOS app
- **AI Agent Enhancement**: Tutors can reference and expand on generated content
- **Comprehensive Lessons**: Each lesson includes:
  - Vocabulary with pronunciations and examples
  - Grammar points with explanations
  - Cultural context and scenarios
  - Interactive dialogues
  - Multiple exercise types (multiple choice, fill-in-blank, matching, pronunciation)
- **Direct Database Upload**: Uploads content directly to Supabase
- **Batch Generation**: Can generate hundreds of lessons at once
- **Progress Tracking**: Real-time logging and statistics

## Prerequisites

1. **Python 3.8+**
2. **Supabase Account** with a project set up (already configured)
3. **Gemini API Key** (already included in the script)

## Setup

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

### 2. Supabase Configuration (Already Done)

The Supabase credentials are already configured:

```python
# Already configured in content_generator.py
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
```

### 3. Database Schema (Already Set Up)

The Supabase database already has the proper `lessons` table structure and contains generated content for multiple languages.

## Usage

### Generate a Single Lesson

```bash
python content_generator.py --language "French" --level "A1" --topic "Basic Greetings"
```

### Generate Multiple Lessons for the Same Topic

```bash
python content_generator.py --language "Spanish" --level "B1" --topic "Travel and Transportation" --count 5
```

### Batch Generate (Recommended for New Languages)

This will generate lessons for 4 languages × 4 levels × 3 topics = 48 lessons:

```bash
python content_generator.py --batch
```

### Custom Batch Generation

Edit the `get_default_lesson_configs()` function to customize:
- Languages to generate
- CEFR levels to include
- Topics to cover
- Number of lessons per configuration

## Integration with iOS App & AI Agents

### 🎯 **How Generated Content Works with AI Tutors**

1. **Content Generation**: Python script creates comprehensive lessons
2. **Database Storage**: Lessons stored in Supabase with rich metadata
3. **iOS App Display**: Lessons appear in the "Lessons" tab with filtering
4. **AI Agent Enhancement**: Tutors can reference lesson content during conversations

### 📱 **Viewing Generated Lessons in iOS App**

1. Open NIRA iOS app
2. Navigate to "Lessons" tab
3. Filter by language (French, Spanish, Japanese, Tamil)
4. Filter by level (A1, A2, B1, B2, C1, C2)
5. Tap any lesson to view full content
6. Start lesson to practice with interactive exercises

### 🤖 **AI Agent Integration**

AI tutors can now:
- Reference vocabulary from generated lessons
- Explain grammar points in conversational context
- Provide cultural insights from lesson content
- Create personalized practice based on lesson topics

Example conversation:
```
User: "I just completed the French A1 café lesson"
French Teacher: "Excellent! Let's practice ordering. Can you tell me how you'd ask for a coffee with milk in French?"
```

## Content Structure

Each generated lesson includes:

### Vocabulary Section
- 8-12 words appropriate for the level
- English translations
- Phonetic pronunciations (IPA when possible)
- Part of speech
- Example sentences with translations

### Grammar Points
- 2-3 grammar concepts
- Clear explanations
- Multiple examples
- Learning tips

### Cultural Context
- Real-world scenarios
- Cultural notes and etiquette
- Do's and don'ts
- Regional variations when relevant

### Dialogues
- 6-10 realistic exchanges
- Natural conversations
- Cultural context notes
- English translations

### Exercises
- Multiple choice questions
- Fill-in-the-blank exercises
- Matching activities
- Pronunciation practice
- Points and hints system

## Current Database Status

### ✅ **Languages with Generated Content**
- **French**: 101 lessons across A1-C2 levels
- **Spanish**: Partial content (A1-A2 levels)
- **Tamil**: Enhanced content across all levels
- **Japanese**: Basic structure, needs vocabulary enhancement

### 📊 **Content Statistics**
- **Total Lessons**: 200+ generated lessons
- **Vocabulary Words**: 2000+ unique entries
- **Cultural Scenarios**: 500+ real-world situations
- **Interactive Exercises**: 1000+ practice activities

## Output and Logging

The script provides detailed logging:

```
2024-01-20 10:30:00 - INFO - 🚀 Starting batch generation...
2024-01-20 10:30:05 - INFO - 🎯 Generating 1 lessons for French A1 - Basic Greetings
2024-01-20 10:30:12 - INFO - ✅ Generated lesson: Bonjour! Your First French Greetings
2024-01-20 10:30:13 - INFO - ✅ Uploaded lesson: Bonjour! Your First French Greetings
2024-01-20 10:35:42 - INFO - 📊 Generation Complete!
2024-01-20 10:35:42 - INFO -    Generated: 48
2024-01-20 10:35:42 - INFO -    Uploaded: 47
2024-01-20 10:35:42 - INFO -    Failed: 1
```

## Rate Limiting and Costs

- The script includes 2-second delays between API calls to respect rate limits
- Gemini 2.0 Flash is cost-effective for content generation
- Monitor your API usage in the Google Cloud Console

## Next Development Priorities

### 🎯 **Immediate Enhancements**
1. **Japanese Vocabulary Enhancement**: Expand Japanese lessons to match other languages
2. **German & Italian Content**: Add comprehensive content for these languages
3. **Advanced Exercise Types**: Voice recognition and pronunciation exercises

### 🚧 **AI-Powered Lesson Generation**
```python
# TODO: Implement dynamic lesson generation based on user progress
def generate_personalized_lesson(user_id, weak_areas, interests):
    # Use AI to create custom lessons targeting user's specific needs
    # Integrate with conversation history from AI tutors
    pass
```

### 🎮 **Interactive Content**
- **Voice Exercises**: Integration with speech recognition
- **AR Scenarios**: Cultural immersion experiences
- **Adaptive Difficulty**: AI-adjusted content based on performance

## Troubleshooting

### Common Issues

1. **"Please configure your Supabase URL and key"**
   - Already configured, but verify the credentials are correct

2. **JSON parsing errors**
   - The script handles most formatting issues automatically
   - Check your Gemini API key if issues persist

3. **Supabase upload errors**
   - Database schema is already set up correctly
   - Check network connection and Supabase status

4. **Rate limit errors**
   - Increase the delay between requests (currently 2 seconds)
   - Consider running smaller batches

### Getting Help

If you encounter issues:
1. Check the logs for specific error messages
2. Verify your API credentials in the script
3. Test with a single lesson first
4. Check the iOS app to see if lessons appear correctly

## Customization

### Adding New Languages

Add languages to the `languages` list in `get_default_lesson_configs()`:

```python
languages = [
    "French", "Spanish", "Japanese", "Tamil", "English",
    "German", "Italian", "Portuguese", "Korean", "Mandarin"  # Add new languages here
]
```

### Custom Topics

Modify the `topics` list to focus on specific learning areas:

```python
topics = [
    "Business Communication", "Medical Vocabulary", "Academic Writing",
    "Travel Essentials", "Cultural Etiquette", "Technical Terms"
]
```

### Integration with AI Agents

The generated content automatically integrates with AI tutors. To enhance this integration:

1. **Reference Lesson Content**: AI agents can access lesson vocabulary and topics
2. **Personalized Practice**: Create exercises based on completed lessons
3. **Progress Tracking**: Monitor which lessons users have completed
4. **Adaptive Conversations**: Adjust tutor responses based on lesson progress

---

**🚀 The content generator is now fully integrated with NIRA's AI-powered learning experience!** 