# NIRA Quick Reference Guide - Developer Operations

## 🚀 Quick Start Commands

### Start Lesson Generation
```bash
cd content_generation
./lesson_gen_env/bin/python3 generate_new_languages_lessons.py
```

### Monitor Progress
```bash
# Real-time log monitoring
tail -f new_languages_generation_*.log

# Check success rate
grep -c "✅ Successfully uploaded" *.log
grep -c "❌ Failed" *.log

# Current progress
grep "📝 \[" *.log | tail -5
```

### Emergency Stop
```bash
# Stop generation gracefully
pkill -f "generate_new_languages_lessons.py"

# Force stop if needed
pkill -9 -f "python3"
```

---

## 📊 Status Monitoring

### Current Generation Status (Live)
```bash
# Check if process is running
ps aux | grep "generate_new_languages_lessons"

# Monitor system resources
top -p $(pgrep -f "generate_new_languages_lessons")

# Check API rate limits
curl -H "Authorization: Bearer $GEMINI_API_KEY" \
     "https://generativelanguage.googleapis.com/v1beta/models"
```

### Database Verification
```bash
# Quick lesson count check
python3 -c "
from supabase import create_client
client = create_client('$SUPABASE_URL', '$SUPABASE_KEY')
result = client.table('lessons').select('id', count='exact').execute()
print(f'Total lessons: {result.count}')
"

# Check by language
python3 scripts/check_lesson_counts.py
```

---

## 🛠 Common Operations

### Restart Generation
```bash
# The script automatically resumes from where it left off
cd content_generation
./lesson_gen_env/bin/python3 generate_new_languages_lessons.py
```

### Generate Single Language
```bash
# Modify the MISSING_LANGUAGES list in the script
# Or use the single lesson generator
python3 content_generator_master.py --language="Kannada" --level="A1"
```

### Fix Failed Lessons
```bash
# Use the retry script
python3 retry_failed_lessons.py

# Or manually retry specific lessons
python3 content_generator_master.py --retry-failed
```

---

## 🔍 Troubleshooting

### Common Issues & Solutions

#### 1. **Script Won't Start**
```bash
# Check Python environment
which python3
python3 --version

# Activate virtual environment
source lesson_gen_env/bin/activate  # Linux/Mac
# OR
lesson_gen_env\Scripts\activate     # Windows

# Install dependencies
pip install -r requirements.txt
```

#### 2. **API Rate Limiting**
```bash
# Check current rate limits
grep "429" *.log

# Increase delays in the script
# Edit comprehensive_lesson_generator.py line 226:
# time.sleep(5)  # Increase from 2 to 5 seconds
```

#### 3. **Database Connection Issues**
```bash
# Test Supabase connection
python3 -c "
from supabase import create_client
try:
    client = create_client('$SUPABASE_URL', '$SUPABASE_KEY')
    result = client.table('languages').select('count').execute()
    print('✅ Database connection successful')
except Exception as e:
    print(f'❌ Database error: {e}')
"
```

#### 4. **JSON Parsing Errors**
```bash
# Check for malformed JSON in logs
grep "JSON" *.log | grep -i error

# The script automatically retries with simplified prompts
# No manual intervention needed
```

#### 5. **Memory Issues**
```bash
# Check memory usage
free -h
ps aux --sort=-%mem | head -10

# Restart if memory usage > 80%
sudo systemctl restart nira-content-generator
```

---

## 📈 Performance Optimization

### Speed Up Generation
```bash
# Reduce delays (be careful with API limits)
# Edit comprehensive_lesson_generator.py:
# time.sleep(1)  # Reduce from 2 to 1 second

# Parallel processing (advanced)
python3 parallel_lesson_generator.py --workers=3
```

### Reduce API Costs
```bash
# Use shorter prompts for advanced levels
# Already implemented in the script for B2-C2 levels

# Monitor API usage
python3 scripts/api_usage_report.py
```

---

## 🗄️ Database Operations

### Quick Queries
```sql
-- Check lesson distribution by language
SELECT l.name, COUNT(lessons.id) as lesson_count
FROM languages l
LEFT JOIN lessons ON l.id = lessons.language_id
GROUP BY l.name
ORDER BY lesson_count DESC;

-- Check lessons by level
SELECT level, COUNT(*) as count
FROM lessons
GROUP BY level
ORDER BY level;

-- Find failed uploads (empty content)
SELECT id, title, language_id
FROM lessons
WHERE content = '{}' OR content IS NULL;
```

### Backup Operations
```bash
# Create backup
pg_dump $DATABASE_URL > backup_$(date +%Y%m%d_%H%M%S).sql

# Restore from backup
psql $DATABASE_URL < backup_20250129_080000.sql
```

---

## 📱 Mobile App Updates

### Refresh Language Data
```swift
// In iOS app, refresh language list
Task {
    await dashboardCoordinator.refreshDashboard()
    await userPreferences.refreshLanguages()
}
```

### Update Language Stats
```bash
# Trigger stats update in database
python3 scripts/update_language_stats.py

# Verify stats in mobile app
# Check LanguageStatsView component
```

---

## 🚨 Emergency Procedures

### System Down
```bash
# 1. Check system status
systemctl status nira-content-generator

# 2. Check logs
journalctl -u nira-content-generator -f

# 3. Restart service
sudo systemctl restart nira-content-generator

# 4. Verify recovery
tail -f /var/log/nira/content-generation.log
```

### Data Corruption
```bash
# 1. Stop all processes
sudo systemctl stop nira-content-generator

# 2. Backup current state
pg_dump $DATABASE_URL > emergency_backup_$(date +%Y%m%d_%H%M%S).sql

# 3. Run integrity check
python3 scripts/data_integrity_check.py

# 4. Restore from last good backup if needed
python3 scripts/restore_from_backup.py --latest
```

### API Key Issues
```bash
# 1. Test API key
curl -H "Authorization: Bearer $GEMINI_API_KEY" \
     "https://generativelanguage.googleapis.com/v1beta/models"

# 2. Rotate API key if needed
# Update in comprehensive_lesson_generator.py
# Update environment variables

# 3. Restart generation
python3 generate_new_languages_lessons.py
```

---

## 📞 Contact Information

### Escalation Path
1. **Level 1**: Check logs and restart
2. **Level 2**: Database integrity check
3. **Level 3**: Contact development team
4. **Level 4**: Contact AI/API provider support

### Key Files to Check
- `content_generation/new_languages_generation_*.log` - Current generation log
- `content_generation/comprehensive_lesson_generator.py` - Main generator
- `NIRA/Views/Components/LanguageStatsView.swift` - Mobile UI component
- `supabase/migrations/` - Database schema changes

### Useful Scripts
- `scripts/check_lesson_counts.py` - Verify lesson distribution
- `scripts/api_usage_report.py` - Monitor API costs
- `scripts/quality_assurance_report.py` - Content quality check
- `scripts/performance_report.py` - System performance analysis

---

## 📋 Daily Checklist

### Morning Check (5 minutes)
- [ ] Verify generation process is running
- [ ] Check overnight progress in logs
- [ ] Monitor system resources (CPU, memory, disk)
- [ ] Verify database connectivity

### Midday Check (3 minutes)
- [ ] Check generation progress percentage
- [ ] Monitor API rate limits
- [ ] Verify no critical errors in logs

### Evening Check (5 minutes)
- [ ] Review daily generation statistics
- [ ] Check lesson quality samples
- [ ] Plan next day's tasks
- [ ] Backup important logs

---

*Quick Reference Version: 1.0*  
*Last Updated: January 29, 2025*  
*Print this page for easy reference during operations*
