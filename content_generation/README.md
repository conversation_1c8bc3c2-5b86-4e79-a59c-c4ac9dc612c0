# NIRA Content Generation System

A comprehensive AI-powered content generation system for creating high-quality language learning lessons using Google Gemini 2.0 Flash and Supabase integration.

## 📁 **Folder Contents**

```
content_generation/
├── README.md                           # This file - Complete guide
├── content_generator_master.py         # Master content generator script
├── CONTENT_GENERATION_GUIDE.md         # Detailed best practices guide
├── CONTENT_GENERATOR_README.md         # Legacy documentation
├── requirements.txt                    # Python dependencies
└── adaptive_learning_schema.sql        # Database schema
```

## 🚀 **Quick Start**

### **1. Setup Environment**
```bash
# Navigate to content generation folder
cd content_generation

# Install dependencies
pip install -r requirements.txt

# Configure API keys in content_generator_master.py
# Update GEMINI_API_KEY, SUPABASE_URL, and SUPABASE_KEY
```

### **2. Generate Single Lesson**
```python
from content_generator_master import NIRAContentGenerator

generator = NIRAContentGenerator()
content = generator.generate_lesson_content("French", "A1", "Café Ordering")
if content:
    generator.upload_to_supabase(content, "French", "A1")
```

### **3. Batch Generation**
```python
batch_config = {
    "languages": ["French", "Spanish"],
    "levels": ["A1", "A2"],
    "topics": ["Basic Greetings", "Food and Dining"]
}
generator.generate_batch_content(batch_config)
```

## 🎯 **Content Generation Features**

### **AI-Powered Creation**
- **Gemini 2.0 Flash Integration**: Latest Google AI model
- **CEFR-Aligned Content**: European language proficiency standards
- **Cultural Authenticity**: Real-world scenarios and cultural context
- **Quality Assurance**: Automated validation and error checking

### **Comprehensive Lesson Structure**
- **Vocabulary**: 8-20 words per lesson (CEFR-specific)
- **Grammar Points**: 2-3 key concepts with examples
- **Cultural Context**: Etiquette, customs, and regional variations
- **Dialogues**: 6-8 realistic conversations
- **Exercises**: Interactive practice activities

### **Database Integration**
- **Supabase**: Direct database uploads
- **Real-time**: Instant availability in iOS app
- **Metadata**: Comprehensive lesson tracking
- **Versioning**: Content generation history

## 📊 **CEFR Content Standards**

### **Vocabulary Requirements by Level**
- **A1**: 8 basic survival words (greetings, numbers, family, food)
- **A2**: 10 daily life words (activities, places, time, weather)
- **B1**: 12 intermediate words (work, education, hobbies, travel)
- **B2**: 15 professional words (business, media, society)
- **C1**: 18 advanced academic words (philosophy, research)
- **C2**: 20 sophisticated terms (literary, cultural concepts)

### **Content Progression**
```
A1 → Basic survival language
A2 → Daily life communication
B1 → Independent user level
B2 → Professional competency
C1 → Advanced proficiency
C2 → Near-native mastery
```

## 🛠 **Technical Architecture**

### **Core Components**
1. **NIRAContentGenerator**: Main class for content generation
2. **Gemini API Integration**: AI-powered content creation
3. **Supabase Client**: Database operations
4. **Content Validation**: Quality assurance system
5. **Batch Processing**: Efficient bulk generation

### **Content Structure**
```json
{
  "title": "Lesson Title",
  "description": "Brief description",
  "vocabulary": [
    {
      "word": "bonjour",
      "translation": "hello",
      "pronunciation": "/bon.ˈʒuʁ/",
      "part_of_speech": "interjection",
      "example": "Bonjour, comment allez-vous?",
      "example_translation": "Hello, how are you?",
      "cultural_context": "Used in formal and informal settings"
    }
  ],
  "grammar_points": [...],
  "cultural_context": [...],
  "dialogues": [...],
  "exercises": [...]
}
```

## 🔧 **Configuration**

### **API Keys Setup**
```python
# Update these in content_generator_master.py
GEMINI_API_KEY = "your-gemini-api-key"
SUPABASE_URL = "your-supabase-url"
SUPABASE_KEY = "your-supabase-anon-key"
```

### **Generation Parameters**
```python
# CEFR-specific vocabulary targets
vocab_targets = {
    "A1": 8, "A2": 10, "B1": 12, 
    "B2": 15, "C1": 18, "C2": 20
}

# Gemini API configuration
generation_config = {
    "temperature": 0.8,
    "topK": 40,
    "topP": 0.95,
    "maxOutputTokens": 4096
}
```

## 📚 **Usage Examples**

### **Example 1: French A1 Lesson**
```python
generator = NIRAContentGenerator()
content = generator.generate_lesson_content(
    language="French",
    level="A1", 
    topic="Restaurant Ordering"
)
```

### **Example 2: Multi-Language Batch**
```python
config = {
    "languages": ["French", "Spanish", "Japanese", "Tamil"],
    "levels": ["A1", "A2", "B1", "B2", "C1", "C2"],
    "topics": [
        "Basic Greetings",
        "Food and Dining", 
        "Travel Essentials",
        "Family and Relationships",
        "Work and Career"
    ]
}
generator.generate_batch_content(config)
```

### **Example 3: Custom Topic Generation**
```python
custom_topics = [
    "Technology and Social Media",
    "Environmental Awareness",
    "Cultural Festivals",
    "Business Etiquette",
    "Academic Writing"
]

for topic in custom_topics:
    content = generator.generate_lesson_content("Spanish", "B2", topic)
    if content:
        generator.upload_to_supabase(content, "Spanish", "B2")
```

## 🎨 **Content Quality Guidelines**

### **Cultural Authenticity Checklist**
- ✅ Real-world scenarios and situations
- ✅ Proper cultural etiquette and customs
- ✅ Regional variations when relevant
- ✅ Authentic dialogue and expressions
- ✅ Cultural do's and don'ts
- ❌ Stereotypes or oversimplifications
- ❌ Outdated cultural references
- ❌ Generic, non-specific content

### **Technical Quality Standards**
- ✅ Valid JSON structure
- ✅ Proper character encoding (UTF-8)
- ✅ Consistent data types
- ✅ Required fields populated
- ✅ Reasonable content length
- ✅ Error handling and validation

## 📈 **Performance Metrics**

### **Content Quality Metrics**
- **Vocabulary Accuracy**: 95%+ correct translations and pronunciations
- **Cultural Authenticity**: Validated by native speakers
- **Grammar Accuracy**: Linguistically correct examples
- **Exercise Effectiveness**: Proven learning outcomes

### **Technical Performance**
- **Generation Success Rate**: 95%+ successful content generation
- **Upload Success Rate**: 99%+ successful database uploads
- **Response Time**: <10 seconds per lesson generation
- **Error Rate**: <5% failures with proper error handling

## 🔄 **Maintenance and Updates**

### **Regular Tasks**
1. **Content Audits**: Monthly review of generated content quality
2. **Cultural Updates**: Quarterly updates for cultural relevance
3. **Technical Updates**: Regular updates to AI prompts and templates
4. **Performance Monitoring**: Continuous monitoring of generation metrics
5. **User Feedback Integration**: Incorporate user feedback into improvements

### **Version Control**
- Use semantic versioning for content generator scripts
- Maintain changelog for all content generation updates
- Tag releases with comprehensive documentation
- Archive old versions for rollback capabilities

## 🚨 **Troubleshooting**

### **Common Issues**

**1. API Key Errors**
```bash
Error: Invalid API key
Solution: Update GEMINI_API_KEY in content_generator_master.py
```

**2. Database Connection Issues**
```bash
Error: Could not connect to Supabase
Solution: Verify SUPABASE_URL and SUPABASE_KEY are correct
```

**3. Content Validation Failures**
```bash
Error: Content validation failed
Solution: Check AI response format and required fields
```

**4. Rate Limiting**
```bash
Error: API rate limit exceeded
Solution: Increase delay between requests (time.sleep)
```

### **Debug Mode**
```python
# Enable debug logging
import logging
logging.basicConfig(level=logging.DEBUG)

# Test single generation
generator = NIRAContentGenerator()
content = generator.generate_lesson_content("French", "A1", "Test Topic")
```

## 📊 **Database Schema**

### **Lessons Table Structure**
```sql
CREATE TABLE lessons (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title TEXT NOT NULL,
    description TEXT,
    content JSONB NOT NULL,
    language TEXT NOT NULL,
    level TEXT NOT NULL,
    topic TEXT NOT NULL,
    difficulty_level INTEGER NOT NULL,
    estimated_duration_minutes INTEGER DEFAULT 25,
    lesson_type TEXT DEFAULT 'ai_generated',
    is_published BOOLEAN DEFAULT true,
    is_active BOOLEAN DEFAULT true,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### **Indexes for Performance**
```sql
CREATE INDEX idx_lessons_language ON lessons(language);
CREATE INDEX idx_lessons_level ON lessons(level);
CREATE INDEX idx_lessons_difficulty ON lessons(difficulty_level);
CREATE INDEX idx_lessons_active ON lessons(is_active);
```

## 🌍 **Language Support**

### **Currently Supported**
- **French**: Complete A1-C2 curriculum
- **Spanish**: Enhanced vocabulary and cultural content
- **Japanese**: Comprehensive with proper writing systems
- **Tamil**: Full cultural integration
- **English**: Advanced conversation practice

### **Adding New Languages**
1. Update language list in batch configuration
2. Research cultural context and etiquette
3. Validate pronunciation guides and character encoding
4. Test with native speakers for authenticity
5. Generate sample lessons for quality assurance

## 🎯 **Best Practices**

### **Content Generation**
1. **Consistency**: Use standardized templates and structures
2. **Quality over Quantity**: Focus on high-quality, culturally authentic content
3. **Progressive Difficulty**: Ensure proper CEFR level progression
4. **Cultural Sensitivity**: Research and validate cultural content
5. **Regular Updates**: Keep content current and relevant

### **Technical Implementation**
1. **Error Handling**: Implement comprehensive error handling and logging
2. **Rate Limiting**: Respect API rate limits with appropriate delays
3. **Validation**: Validate all generated content before database insertion
4. **Monitoring**: Track generation metrics and success rates
5. **Backup**: Maintain backups of generated content

## 📞 **Support and Documentation**

### **Additional Resources**
- **CONTENT_GENERATION_GUIDE.md**: Detailed best practices and templates
- **CONTENT_GENERATOR_README.md**: Legacy documentation and examples
- **adaptive_learning_schema.sql**: Complete database schema
- **Main NIRA README**: Repository overview and setup instructions

### **Getting Help**
1. Check this README for common solutions
2. Review the detailed guide in CONTENT_GENERATION_GUIDE.md
3. Examine the master script for implementation details
4. Test with small batches before large-scale generation

---

**🚀 This content generation system represents the consolidated knowledge from NIRA's development. Use it as the single source of truth for all content generation work.** 