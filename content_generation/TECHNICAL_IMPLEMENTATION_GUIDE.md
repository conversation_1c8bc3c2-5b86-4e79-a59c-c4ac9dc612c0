# NIRA Technical Implementation Guide - 50 Language Expansion

## 🏗️ System Architecture Overview

### Core Components

```mermaid
graph TD
    A[Gemini Flash 2.0 API] --> B[Content Generator]
    B --> C[JSON Parser & Validator]
    C --> D[Supabase Database]
    E[Language Configuration] --> B
    F[CEFR Standards] --> B
    G[Cultural Context] --> B
    D --> H[Learning Paths]
    D --> I[Mobile App]
    D --> J[Web Dashboard]
```

### Technology Stack

| Component | Technology | Version | Purpose |
|-----------|------------|---------|---------|
| AI Model | Gemini Flash 2.0 | Latest | Content generation |
| Database | Supabase | 2.7.4 | Data storage & real-time sync |
| Backend | Python | 3.13+ | Content processing |
| Frontend | Swift/SwiftUI | iOS 18.2+ | Mobile application |
| API | REST/GraphQL | - | Data communication |

---

## 🗄️ Database Schema

### Core Tables

#### `languages`
```sql
CREATE TABLE languages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    code VARCHAR(10) UNIQUE NOT NULL,
    native_name VARCHAR(100),
    difficulty_level INTEGER DEFAULT 1,
    writing_system VARCHAR(50),
    is_active BOOLEAN DEFAULT true,
    tier INTEGER DEFAULT 1, -- 1: Full, 2: Partial, 3: Basic
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

#### `lessons`
```sql
CREATE TABLE lessons (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title VARCHAR(200) NOT NULL,
    description TEXT,
    content JSONB NOT NULL,
    language_id UUID REFERENCES languages(id),
    level VARCHAR(10) NOT NULL, -- A1, A2, B1, B2, C1, C2
    topic VARCHAR(200),
    difficulty_level INTEGER DEFAULT 1,
    estimated_duration_minutes INTEGER DEFAULT 15,
    sequence_order INTEGER,
    is_published BOOLEAN DEFAULT true,
    metadata JSONB,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

#### `learning_paths`
```sql
CREATE TABLE learning_paths (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    language_id UUID REFERENCES languages(id),
    agent_id UUID REFERENCES agents(id),
    name VARCHAR(200) NOT NULL,
    description TEXT,
    level VARCHAR(10) NOT NULL,
    estimated_hours INTEGER DEFAULT 30,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

### Content Structure (JSONB)

#### Lesson Content Schema
```json
{
  "title": "string",
  "description": "string", 
  "estimated_duration": "number",
  "vocabulary": [
    {
      "word": "string",
      "translation": "string",
      "pronunciation": "string",
      "part_of_speech": "string",
      "example": "string",
      "example_translation": "string",
      "cultural_note": "string"
    }
  ],
  "grammar_points": [
    {
      "rule": "string",
      "explanation": "string", 
      "examples": ["string"],
      "translations": ["string"],
      "common_mistakes": "string"
    }
  ],
  "dialogues": [
    {
      "speakers": ["string"],
      "conversation": [
        {
          "speaker": "string",
          "text": "string",
          "translation": "string",
          "pronunciation": "string"
        }
      ],
      "cultural_notes": "string"
    }
  ],
  "exercises": [
    {
      "type": "multiple_choice|fill_blank|translation",
      "question": "string",
      "options": ["string"],
      "correct_answer": "number|string",
      "explanation": "string"
    }
  ],
  "cultural_notes": "string",
  "learning_objectives": ["string"]
}
```

---

## 🤖 AI Integration

### Gemini Flash 2.0 Configuration

#### API Setup
```python
GEMINI_CONFIG = {
    "model": "gemini-2.0-flash",
    "temperature": 0.8,
    "top_k": 40,
    "top_p": 0.95,
    "max_output_tokens": 4096,
    "safety_settings": [
        {
            "category": "HARM_CATEGORY_HARASSMENT",
            "threshold": "BLOCK_MEDIUM_AND_ABOVE"
        }
    ]
}
```

#### Prompt Engineering

##### Standard Prompt Template (A1-B1)
```python
def create_standard_prompt(language_name, level, topic, specs):
    return f"""
    Create a comprehensive {language_name} lesson for {level} level on: {topic}
    
    REQUIREMENTS:
    1. VOCABULARY: {specs['vocab']} words with translations, pronunciation, examples
    2. GRAMMAR: {specs['grammar']} points with explanations and examples
    3. DIALOGUES: 2-3 realistic conversations with cultural context
    4. EXERCISES: {specs['exercises']} varied exercises (multiple choice, fill-in, translation)
    5. CULTURAL NOTES: Relevant cultural context and etiquette
    
    OUTPUT: Valid JSON only, following exact schema
    """
```

##### Simplified Prompt Template (B2-C2)
```python
def create_simplified_prompt(language_name, level, topic, specs):
    return f"""
    Create a {language_name} lesson for {level} level on: {topic}
    
    REQUIREMENTS:
    - {specs['vocab']} vocabulary words with translations
    - {specs['grammar']} grammar points with examples  
    - {specs['exercises']} exercises (multiple choice only)
    - Keep content concise and focused
    
    CRITICAL: Output valid JSON only, no trailing commas, proper escaping
    """
```

### Error Handling & Retry Logic

```python
def generate_with_retry(prompt, max_retries=3):
    for attempt in range(max_retries):
        try:
            response = call_gemini_api(prompt, retry_count=attempt)
            content = parse_json_response(response)
            
            if validate_content_structure(content):
                return content
            else:
                raise ValueError("Content validation failed")
                
        except Exception as e:
            if attempt < max_retries - 1:
                wait_time = 2 * (attempt + 1)  # Exponential backoff
                time.sleep(wait_time)
                logging.warning(f"Retry {attempt + 1} after {wait_time}s: {e}")
            else:
                logging.error(f"Failed after {max_retries} attempts: {e}")
                return None
```

---

## 📱 Mobile App Integration

### SwiftUI Components

#### Language Stats View
```swift
struct LanguageStatsView: View {
    @StateObject private var dashboardCoordinator = DashboardCoordinatorService.shared
    
    var body: some View {
        VStack(spacing: 20) {
            // Portfolio Overview
            PortfolioOverviewCard()
            
            // Tier Breakdown
            TierBreakdownView()
            
            // Language Grid
            LazyVGrid(columns: gridColumns, spacing: 16) {
                ForEach(languages, id: \.code) { language in
                    LanguageProgressCard(language: language)
                }
            }
        }
    }
}
```

#### Language Selection Integration
```swift
struct ConnectedLanguageSelectionView: View {
    @Binding var selectedLanguage: Language
    let onLanguageSelected: (Language) -> Void
    
    var body: some View {
        ModernLanguageSelectionView(
            languages: availableLanguages,
            selectedLanguage: $selectedLanguage,
            onLanguageSelected: onLanguageSelected
        )
    }
}
```

### Data Models

#### Language Model
```swift
struct Language: Codable, Identifiable {
    let id: UUID
    let name: String
    let code: String
    let nativeName: String
    let difficultyLevel: Int
    let writingSystem: String
    let tier: Int
    let isActive: Bool
    
    var tierInfo: TierInfo {
        switch tier {
        case 1: return .full
        case 2: return .partial  
        case 3: return .basic
        default: return .basic
        }
    }
}

enum TierInfo {
    case full, partial, basic
    
    var features: [String] {
        switch self {
        case .full: return ["Voice Chat", "Live AI", "Full Lessons", "Cultural Context"]
        case .partial: return ["AI Chat", "Full Lessons", "Cultural Context"]
        case .basic: return ["Basic Lessons", "Limited AI"]
        }
    }
}
```

---

## 🔄 Content Generation Pipeline

### Batch Processing Workflow

```python
class ComprehensiveLessonGenerator:
    def generate_all_missing_lessons(self):
        """Main generation workflow"""
        
        for language_name, language_code in self.config.MISSING_LANGUAGES:
            # 1. Ensure language exists in database
            language_id = self._ensure_language_exists(language_name, language_code)
            
            # 2. Generate lessons for each CEFR level
            for level in self.config.LEVELS:  # A1, A2, B1, B2, C1, C2
                topics = self.config.TOPICS_BY_LEVEL[level]
                
                # 3. Generate lessons for each topic
                for topic in topics:
                    content = self._generate_lesson_content(
                        language_name, language_code, level, topic
                    )
                    
                    if content:
                        # 4. Upload to database
                        success = self._upload_to_supabase(content, language_id, level)
                        if success:
                            self.generated_count += 1
                        else:
                            self.failed_count += 1
                    
                    # 5. Rate limiting
                    time.sleep(2)
```

### Quality Assurance Pipeline

```python
def validate_content(content):
    """Comprehensive content validation"""
    
    required_fields = ['title', 'description', 'vocabulary', 'grammar_points', 'exercises']
    
    # 1. Structure validation
    for field in required_fields:
        if field not in content:
            return False
    
    # 2. Vocabulary validation
    if len(content['vocabulary']) < 5:
        return False
        
    for vocab in content['vocabulary']:
        required_vocab_fields = ['word', 'translation', 'pronunciation']
        if not all(field in vocab for field in required_vocab_fields):
            return False
    
    # 3. Exercise validation
    if len(content['exercises']) < 3:
        return False
        
    # 4. JSON structure validation
    try:
        json.dumps(content)
    except (TypeError, ValueError):
        return False
        
    return True
```

---

## 🚀 Deployment & Scaling

### Production Environment

#### Server Configuration
```yaml
# docker-compose.yml
version: '3.8'
services:
  content-generator:
    build: ./content_generation
    environment:
      - GEMINI_API_KEY=${GEMINI_API_KEY}
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_KEY=${SUPABASE_KEY}
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
```

#### Environment Variables
```bash
# .env
GEMINI_API_KEY=your_gemini_api_key
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_KEY=your_supabase_anon_key
ENVIRONMENT=production
LOG_LEVEL=INFO
```

### Monitoring & Alerting

#### Health Checks
```python
def health_check():
    """System health monitoring"""
    
    checks = {
        'gemini_api': test_gemini_connection(),
        'supabase_db': test_supabase_connection(),
        'disk_space': check_disk_space(),
        'memory_usage': check_memory_usage()
    }
    
    return {
        'status': 'healthy' if all(checks.values()) else 'unhealthy',
        'checks': checks,
        'timestamp': datetime.now().isoformat()
    }
```

#### Performance Metrics
```python
METRICS = {
    'lessons_generated_total': Counter('lessons_generated_total'),
    'generation_duration_seconds': Histogram('generation_duration_seconds'),
    'api_requests_total': Counter('api_requests_total'),
    'database_operations_total': Counter('database_operations_total'),
    'errors_total': Counter('errors_total')
}
```

---

## 🔧 Maintenance Procedures

### Daily Tasks
```bash
# Check generation progress
tail -f content_generation/logs/latest.log

# Verify database integrity
python3 scripts/verify_database_integrity.py

# Monitor API usage
python3 scripts/check_api_quotas.py
```

### Weekly Tasks
```bash
# Content quality review
python3 scripts/quality_assurance_report.py

# Performance analysis
python3 scripts/performance_report.py

# Backup verification
python3 scripts/verify_backups.py
```

### Emergency Procedures

#### System Recovery
```bash
# Stop all generation processes
pkill -f "generate_new_languages_lessons.py"

# Check system resources
df -h
free -m
ps aux | grep python

# Restart with recovery mode
python3 generate_new_languages_lessons.py --recovery-mode
```

#### Data Recovery
```bash
# Restore from backup
python3 scripts/restore_from_backup.py --date=YYYY-MM-DD

# Verify data integrity
python3 scripts/verify_restored_data.py

# Resume generation from last checkpoint
python3 scripts/resume_generation.py
```

---

*Technical Guide Version: 1.0*  
*Last Updated: January 29, 2025*  
*Next Review: February 12, 2025*
