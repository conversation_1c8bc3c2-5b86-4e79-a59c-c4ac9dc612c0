-- Adaptive Learning System Database Schema
-- This schema supports comprehensive learning analytics and personalization

-- User Progress Tracking
CREATE TABLE user_progress (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    lesson_id UUID NOT NULL REFERENCES lessons(id),
    language_id UUID NOT NULL REFERENCES languages(id),
    
    -- Progress Metrics
    completion_status VARCHAR(20) NOT NULL DEFAULT 'not_started', -- not_started, in_progress, completed, mastered
    accuracy_score DECIMAL(5,2), -- 0.00 to 100.00
    time_spent_seconds INTEGER NOT NULL DEFAULT 0,
    attempts_count INTEGER NOT NULL DEFAULT 0,
    
    -- Performance Details
    vocabulary_mastery JSONB, -- {"word_id": {"attempts": 3, "correct": 2, "last_seen": "timestamp"}}
    exercise_results JSONB, -- Detailed results for each exercise
    mistakes_pattern JSONB, -- Common mistake types and frequencies
    
    -- Learning Behavior
    session_duration INTEGER, -- How long this session lasted
    engagement_score DECIMAL(3,2), -- 0.00 to 1.00 based on interaction patterns
    difficulty_rating INTEGER, -- User's perceived difficulty (1-5)
    
    -- Timestamps
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    last_reviewed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(user_id, lesson_id)
);

-- Learning Analytics (Granular Interaction Data)
CREATE TABLE learning_analytics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    lesson_id UUID REFERENCES lessons(id),
    session_id UUID NOT NULL, -- Groups interactions within a session
    
    -- Interaction Details
    interaction_type VARCHAR(50) NOT NULL, -- lesson_start, exercise_attempt, vocabulary_review, etc.
    content_id VARCHAR(100), -- ID of specific content (vocabulary word, exercise, etc.)
    content_type VARCHAR(50), -- vocabulary, exercise, grammar, dialogue
    
    -- Performance Data
    is_correct BOOLEAN,
    response_time_ms INTEGER,
    hint_used BOOLEAN DEFAULT FALSE,
    attempts_before_correct INTEGER,
    
    -- Context
    difficulty_level INTEGER, -- CEFR level (1-6)
    time_of_day INTEGER, -- Hour of day (0-23)
    device_type VARCHAR(20), -- mobile, tablet, desktop
    
    -- Metadata
    metadata JSONB, -- Flexible storage for additional context
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Adaptive Learning Profiles
CREATE TABLE adaptive_learning_profiles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL UNIQUE,
    language_id UUID NOT NULL REFERENCES languages(id),
    
    -- Learning Preferences (AI-determined)
    optimal_session_length INTEGER, -- Minutes
    preferred_difficulty_progression DECIMAL(3,2), -- Rate of difficulty increase
    learning_style_weights JSONB, -- {"visual": 0.7, "auditory": 0.3, "kinesthetic": 0.5}
    
    -- Performance Patterns
    strength_areas JSONB, -- ["vocabulary", "grammar", "pronunciation"]
    weakness_areas JSONB, -- Areas needing more focus
    learning_velocity DECIMAL(5,2), -- Lessons per week
    retention_rate DECIMAL(3,2), -- How well they remember content
    
    -- Behavioral Patterns
    optimal_study_times JSONB, -- [{"hour": 9, "performance": 0.85}, ...]
    engagement_patterns JSONB, -- What keeps them motivated
    challenge_preference VARCHAR(20), -- easy, moderate, challenging
    
    -- Spaced Repetition Data
    forgetting_curve_params JSONB, -- Parameters for spaced repetition algorithm
    next_review_schedule JSONB, -- When to review specific content
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Achievements System
CREATE TABLE achievements (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Achievement Details
    name VARCHAR(100) NOT NULL,
    description TEXT NOT NULL,
    category VARCHAR(50) NOT NULL, -- progress, performance, consistency, social, special
    difficulty VARCHAR(20) NOT NULL, -- bronze, silver, gold, platinum
    
    -- Visual Elements
    icon_name VARCHAR(50), -- System icon or emoji
    color_hex VARCHAR(7), -- #FF5733
    badge_image_url TEXT,
    
    -- Criteria (JSON for flexibility)
    criteria JSONB NOT NULL, -- {"type": "lesson_completion", "count": 10, "language": "french"}
    points_reward INTEGER NOT NULL DEFAULT 0,
    
    -- Metadata
    is_active BOOLEAN DEFAULT TRUE,
    is_secret BOOLEAN DEFAULT FALSE, -- Hidden until unlocked
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User Achievements
CREATE TABLE user_achievements (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    achievement_id UUID NOT NULL REFERENCES achievements(id),
    
    -- Achievement Data
    earned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    progress_data JSONB, -- Data that led to earning this achievement
    
    -- Social Features
    is_showcased BOOLEAN DEFAULT FALSE, -- Display on profile
    shared_at TIMESTAMP WITH TIME ZONE,
    
    UNIQUE(user_id, achievement_id)
);

-- Study Sessions
CREATE TABLE study_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    language_id UUID NOT NULL REFERENCES languages(id),
    
    -- Session Details
    session_type VARCHAR(50), -- lesson, review, practice, assessment
    lessons_completed INTEGER DEFAULT 0,
    exercises_attempted INTEGER DEFAULT 0,
    exercises_correct INTEGER DEFAULT 0,
    
    -- Performance Metrics
    total_time_seconds INTEGER NOT NULL,
    average_accuracy DECIMAL(5,2),
    words_learned INTEGER DEFAULT 0,
    words_reviewed INTEGER DEFAULT 0,
    
    -- Engagement Metrics
    interactions_count INTEGER DEFAULT 0,
    hints_used INTEGER DEFAULT 0,
    breaks_taken INTEGER DEFAULT 0,
    
    -- Context
    device_type VARCHAR(20),
    location_context VARCHAR(50), -- home, commute, work, etc.
    
    -- Timestamps
    started_at TIMESTAMP WITH TIME ZONE NOT NULL,
    ended_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Learning Goals
CREATE TABLE learning_goals (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    language_id UUID NOT NULL REFERENCES languages(id),
    
    -- Goal Details
    goal_type VARCHAR(50) NOT NULL, -- daily_lessons, weekly_time, cefr_level, vocabulary_count
    target_value INTEGER NOT NULL,
    current_value INTEGER DEFAULT 0,
    
    -- Timeline
    target_date DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    
    -- Status
    is_active BOOLEAN DEFAULT TRUE,
    is_achieved BOOLEAN DEFAULT FALSE
);

-- Recommendation Engine Data
CREATE TABLE content_recommendations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    
    -- Recommendation Details
    content_type VARCHAR(50) NOT NULL, -- lesson, vocabulary_review, grammar_practice
    content_id UUID, -- ID of recommended content
    recommendation_reason TEXT, -- Why this was recommended
    confidence_score DECIMAL(3,2), -- 0.00 to 1.00
    
    -- Interaction Tracking
    shown_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    clicked_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    dismissed_at TIMESTAMP WITH TIME ZONE,
    
    -- Metadata
    algorithm_version VARCHAR(20),
    context_data JSONB -- Additional context used for recommendation
);

-- Indexes for Performance
CREATE INDEX idx_user_progress_user_id ON user_progress(user_id);
CREATE INDEX idx_user_progress_lesson_id ON user_progress(lesson_id);
CREATE INDEX idx_user_progress_completion ON user_progress(completion_status);
CREATE INDEX idx_learning_analytics_user_session ON learning_analytics(user_id, session_id);
CREATE INDEX idx_learning_analytics_content ON learning_analytics(content_type, content_id);
CREATE INDEX idx_user_achievements_user_id ON user_achievements(user_id);
CREATE INDEX idx_study_sessions_user_date ON study_sessions(user_id, started_at);

-- Functions for automatic updates
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers
CREATE TRIGGER update_user_progress_updated_at BEFORE UPDATE ON user_progress FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_adaptive_profiles_updated_at BEFORE UPDATE ON adaptive_learning_profiles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column(); 