#!/usr/bin/env python3
"""
Add 10 New Languages to NIRA
Adds Kannada, Malayalam, Bengali, Marathi, Punjabi, Dutch, Swedish, Thai, Russian, Norwegian
"""

import os
import time
import logging
from supabase import create_client
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'add_new_languages_{int(time.time())}.log'),
        logging.StreamHandler()
    ]
)

class NewLanguageAdder:
    def __init__(self):
        # Use same credentials as the lesson generator
        self.SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
        self.SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"

        self.supabase = create_client(self.SUPABASE_URL, self.SUPABASE_KEY)

        # New languages to add (name, code, native_name)
        self.NEW_LANGUAGES = [
            ("Kannada", "kn", "ಕನ್ನಡ"),
            ("Malayalam", "ml", "മലയാളം"),
            ("Bengali", "bn", "বাংলা"),
            ("Marathi", "mr", "मराठी"),
            ("Punjabi", "pa", "ਪੰਜਾਬੀ"),
            ("Dutch", "nl", "Nederlands"),
            ("Swedish", "sv", "Svenska"),
            ("Thai", "th", "ไทย"),
            ("Russian", "ru", "Русский"),
            ("Norwegian", "no", "Norsk")
        ]

        self.LEVELS = ["A1", "A2", "B1", "B2", "C1", "C2"]

    def add_languages(self):
        """Add new languages to the database"""
        logging.info("🌍 Adding 10 new languages to NIRA...")

        added_languages = []

        for name, code, native_name in self.NEW_LANGUAGES:
            try:
                # Check if language already exists
                existing = self.supabase.table("languages").select("id").eq("code", code).execute()

                if existing.data:
                    logging.info(f"⚠️ {name} ({code}) already exists, skipping...")
                    continue

                # Add language with appropriate difficulty level and writing system
                difficulty_levels = {
                    "Dutch": 1, "Swedish": 1, "Norwegian": 1,  # Germanic languages
                    "Russian": 4,  # Cyrillic script
                    "Bengali": 3, "Hindi": 3, "Marathi": 3, "Punjabi": 3, "Kannada": 3, "Malayalam": 3,  # Indic languages
                    "Thai": 4  # Unique script
                }

                writing_systems = {
                    "Dutch": "Latin", "Swedish": "Latin", "Norwegian": "Latin",
                    "Russian": "Cyrillic",
                    "Bengali": "Bengali", "Marathi": "Devanagari", "Punjabi": "Gurmukhi",
                    "Kannada": "Kannada", "Malayalam": "Malayalam",
                    "Thai": "Thai"
                }

                language_data = {
                    "name": name,
                    "code": code,
                    "native_name": native_name,
                    "difficulty_level": difficulty_levels.get(name, 2),
                    "writing_system": writing_systems.get(name, "Latin"),
                    "is_active": True
                }

                result = self.supabase.table("languages").insert(language_data).execute()

                if result.data:
                    language_id = result.data[0]["id"]
                    added_languages.append((name, code, language_id))
                    logging.info(f"✅ Added {name} ({code}) - ID: {language_id}")
                else:
                    logging.error(f"❌ Failed to add {name} ({code})")

            except Exception as e:
                logging.error(f"❌ Error adding {name}: {str(e)}")

        return added_languages

    def create_learning_paths(self, added_languages):
        """Create learning paths for each language and level"""
        logging.info("📚 Creating learning paths for new languages...")

        created_paths = []

        for name, code, language_id in added_languages:
            logging.info(f"📖 Creating learning paths for {name}...")

            for level in self.LEVELS:
                try:
                    # Get a default agent (use Marie)
                    agent_result = self.supabase.table("agents").select("id").eq("name", "Marie").execute()
                    if not agent_result.data:
                        logging.error("No default agent found")
                        continue

                    agent_id = agent_result.data[0]["id"]

                    path_data = {
                        "language_id": language_id,
                        "agent_id": agent_id,
                        "level": level,
                        "name": f"{name} {level}",
                        "description": f"Learn {name} at {level} level according to CEFR standards",
                        "is_active": True,
                        "estimated_hours": self._get_level_duration(level)
                    }

                    result = self.supabase.table("learning_paths").insert(path_data).execute()

                    if result.data:
                        path_id = result.data[0]["id"]
                        created_paths.append((name, level, path_id))
                        logging.info(f"  ✅ Created {name} {level} path - ID: {path_id}")
                    else:
                        logging.error(f"  ❌ Failed to create {name} {level} path")

                except Exception as e:
                    logging.error(f"  ❌ Error creating {name} {level} path: {str(e)}")

        return created_paths

    def _get_level_duration(self, level):
        """Get estimated duration for each level"""
        durations = {
            "A1": 40,  # hours
            "A2": 60,
            "B1": 80,
            "B2": 100,
            "C1": 120,
            "C2": 150
        }
        return durations.get(level, 60)

    def update_language_stats(self):
        """Update language statistics"""
        try:
            # Get total language count
            languages = self.supabase.table("languages").select("id").eq("is_active", True).execute()
            total_languages = len(languages.data)

            logging.info(f"📊 Total active languages: {total_languages}")

            # Get total learning paths
            paths = self.supabase.table("learning_paths").select("id").eq("is_active", True).execute()
            total_paths = len(paths.data)

            logging.info(f"📊 Total learning paths: {total_paths}")

        except Exception as e:
            logging.error(f"❌ Error updating stats: {str(e)}")

def main():
    """Main execution function"""
    print("🌍 NIRA New Language Adder")
    print("=" * 40)
    print("Adding 10 new languages:")
    print("16. Kannada (kn)")
    print("17. Malayalam (ml)")
    print("18. Bengali (bn)")
    print("19. Marathi (mr)")
    print("20. Punjabi (pa)")
    print("21. Dutch (nl)")
    print("22. Swedish (sv)")
    print("23. Thai (th)")
    print("24. Russian (ru)")
    print("25. Norwegian (no)")
    print("=" * 40)

    try:
        adder = NewLanguageAdder()

        # Add languages
        added_languages = adder.add_languages()

        if not added_languages:
            print("⚠️ No new languages were added (they may already exist)")
            return

        print(f"\n✅ Successfully added {len(added_languages)} languages")

        # Create learning paths
        created_paths = adder.create_learning_paths(added_languages)

        print(f"✅ Successfully created {len(created_paths)} learning paths")

        # Update stats
        adder.update_language_stats()

        print("\n🎉 New languages successfully added to NIRA!")
        print("Next step: Generate lessons for these languages")

    except Exception as e:
        print(f"❌ Error: {str(e)}")
        logging.error(f"Fatal error: {str(e)}")

if __name__ == "__main__":
    main()
