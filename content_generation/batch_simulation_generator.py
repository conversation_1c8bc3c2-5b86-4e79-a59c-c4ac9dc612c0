#!/usr/bin/env python3
"""
NIRA Batch Simulation Generator
Generates simulations in batches using Gemini -> OpenAI -> Supabase pipeline
Creates 250 simulations per language (50 per persona × 5 personas)
"""

import json
import os
import time
import logging
from datetime import datetime
from typing import Dict, List, Optional
from simulation_generator import SimulationGenerator, SCENARIO_CATEGORIES, AGENT_PERSONAS, LANGUAGES

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'batch_simulation_generation_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)

class BatchSimulationGenerator:
    """Generate simulations in organized batches"""
    
    def __init__(self):
        self.generator = SimulationGenerator()
        self.batch_stats = {
            "total_generated": 0,
            "total_failed": 0,
            "languages_completed": 0,
            "current_language": "",
            "start_time": None,
            "estimated_completion": None
        }
    
    def generate_sample_batch(self, language: str = "French", num_simulations: int = 20) -> None:
        """Generate a small sample batch for testing"""
        
        logging.info(f"🧪 SAMPLE BATCH GENERATION")
        logging.info(f"📊 Language: {language}")
        logging.info(f"📊 Target: {num_simulations} simulations")
        logging.info(f"📊 Estimated time: {num_simulations * 0.5:.1f} minutes")
        
        self.batch_stats["start_time"] = time.time()
        self.batch_stats["current_language"] = language
        
        generated = 0
        failed = 0
        
        # Generate simulations across different personas and categories
        for persona_key in AGENT_PERSONAS.keys():
            if generated >= num_simulations:
                break
                
            for category_key, category in SCENARIO_CATEGORIES.items():
                if generated >= num_simulations:
                    break
                
                # Take first 2 scenarios from each category for variety
                for scenario in category["scenarios"][:2]:
                    if generated >= num_simulations:
                        break
                    
                    logging.info(f"📝 Generating {generated+1}/{num_simulations}: {persona_key} - {scenario}")
                    
                    result = self.generator.generate_simulation_batch(language, persona_key, category_key, scenario)
                    
                    if result:
                        generated += 1
                        self.batch_stats["total_generated"] += 1
                        logging.info(f"✅ Success! Generated: {result['title']}")
                    else:
                        failed += 1
                        self.batch_stats["total_failed"] += 1
                        logging.error(f"❌ Failed to generate simulation")
                    
                    # Rate limiting
                    time.sleep(2)
        
        self._print_batch_report(language, generated, failed)
    
    def generate_language_batch(self, language: str, max_per_persona: int = 10) -> None:
        """Generate a batch for a specific language (limited simulations per persona)"""
        
        total_target = len(AGENT_PERSONAS) * max_per_persona
        
        logging.info(f"🌍 LANGUAGE BATCH GENERATION")
        logging.info(f"📊 Language: {language}")
        logging.info(f"📊 Target: {total_target} simulations ({max_per_persona} per persona)")
        logging.info(f"📊 Estimated time: {total_target * 0.5:.1f} minutes")
        
        self.batch_stats["start_time"] = time.time()
        self.batch_stats["current_language"] = language
        
        generated = 0
        failed = 0
        
        for persona_key in AGENT_PERSONAS.keys():
            logging.info(f"👤 Starting {persona_key} persona - Target: {max_per_persona} simulations")
            
            persona_generated = 0
            
            for category_key, category in SCENARIO_CATEGORIES.items():
                if persona_generated >= max_per_persona:
                    break
                
                # Generate scenarios for this category
                scenarios_needed = min(len(category["scenarios"]), max_per_persona - persona_generated)
                
                for scenario in category["scenarios"][:scenarios_needed]:
                    if persona_generated >= max_per_persona:
                        break
                    
                    logging.info(f"📝 {language} - {persona_key} ({persona_generated+1}/{max_per_persona}): {scenario}")
                    
                    result = self.generator.generate_simulation_batch(language, persona_key, category_key, scenario)
                    
                    if result:
                        generated += 1
                        persona_generated += 1
                        self.batch_stats["total_generated"] += 1
                        logging.info(f"✅ Success! Generated: {result['title']}")
                    else:
                        failed += 1
                        self.batch_stats["total_failed"] += 1
                        logging.error(f"❌ Failed to generate simulation")
                    
                    # Rate limiting
                    time.sleep(2)
            
            logging.info(f"✅ {persona_key} complete: {persona_generated}/{max_per_persona} simulations")
        
        self.batch_stats["languages_completed"] += 1
        self._print_batch_report(language, generated, failed)
    
    def generate_full_language_simulations(self, language: str) -> None:
        """Generate all 250 simulations for a language (50 per persona × 5 personas)"""
        
        logging.info(f"🚀 FULL LANGUAGE GENERATION")
        logging.info(f"📊 Language: {language}")
        logging.info(f"📊 Target: 250 simulations (50 per persona)")
        logging.info(f"📊 Estimated time: 2-3 hours")
        
        self.batch_stats["start_time"] = time.time()
        self.batch_stats["current_language"] = language
        
        generated = 0
        failed = 0
        
        for persona_key in AGENT_PERSONAS.keys():
            logging.info(f"👤 Starting {persona_key} persona - Target: 50 simulations")
            
            persona_generated = 0
            
            for category_key, category in SCENARIO_CATEGORIES.items():
                logging.info(f"📂 Category: {category['name']} - Target: 5 simulations")
                
                # Generate all 5 scenarios for this category
                for scenario in category["scenarios"]:
                    logging.info(f"📝 {language} - {persona_key} ({persona_generated+1}/50): {scenario}")
                    
                    result = self.generator.generate_simulation_batch(language, persona_key, category_key, scenario)
                    
                    if result:
                        generated += 1
                        persona_generated += 1
                        self.batch_stats["total_generated"] += 1
                        logging.info(f"✅ Success! Generated: {result['title']}")
                    else:
                        failed += 1
                        self.batch_stats["total_failed"] += 1
                        logging.error(f"❌ Failed to generate simulation")
                    
                    # Rate limiting - important for API limits
                    time.sleep(3)
                
                logging.info(f"✅ {category['name']} complete for {persona_key}")
            
            logging.info(f"✅ {persona_key} complete: {persona_generated}/50 simulations")
        
        self.batch_stats["languages_completed"] += 1
        self._print_batch_report(language, generated, failed)
    
    def generate_multi_language_batch(self, languages: List[str], simulations_per_language: int = 50) -> None:
        """Generate simulations for multiple languages"""
        
        total_target = len(languages) * simulations_per_language
        
        logging.info(f"🌍 MULTI-LANGUAGE BATCH GENERATION")
        logging.info(f"📊 Languages: {', '.join(languages)}")
        logging.info(f"📊 Target: {total_target} simulations ({simulations_per_language} per language)")
        logging.info(f"📊 Estimated time: {total_target * 0.5 / 60:.1f} hours")
        
        self.batch_stats["start_time"] = time.time()
        
        for language in languages:
            logging.info(f"\n🔥 Starting {language} - Target: {simulations_per_language} simulations")
            
            if simulations_per_language <= 50:
                # Use language batch method for smaller batches
                max_per_persona = simulations_per_language // len(AGENT_PERSONAS)
                self.generate_language_batch(language, max_per_persona)
            else:
                # Use full generation for larger batches
                self.generate_full_language_simulations(language)
            
            logging.info(f"🎉 {language} complete!")
        
        self._print_final_multi_language_report(languages)
    
    def _print_batch_report(self, language: str, generated: int, failed: int) -> None:
        """Print batch completion report"""
        
        elapsed_time = time.time() - self.batch_stats["start_time"]
        success_rate = (generated / max(1, generated + failed)) * 100
        
        logging.info(f"\n📊 {language} Batch Report:")
        logging.info(f"   Generated: {generated}")
        logging.info(f"   Failed: {failed}")
        logging.info(f"   Success Rate: {success_rate:.1f}%")
        logging.info(f"   Time Elapsed: {elapsed_time/60:.1f} minutes")
        
        if generated > 0:
            avg_time = elapsed_time / generated
            logging.info(f"   Average per simulation: {avg_time:.1f} seconds")
    
    def _print_final_multi_language_report(self, languages: List[str]) -> None:
        """Print final multi-language report"""
        
        elapsed_time = time.time() - self.batch_stats["start_time"]
        
        logging.info(f"\n🎉 MULTI-LANGUAGE GENERATION COMPLETE!")
        logging.info(f"📊 Final Statistics:")
        logging.info(f"   Languages: {len(languages)}")
        logging.info(f"   Total Generated: {self.batch_stats['total_generated']}")
        logging.info(f"   Total Failed: {self.batch_stats['total_failed']}")
        logging.info(f"   Total Time: {elapsed_time/3600:.1f} hours")
        
        if self.batch_stats['total_generated'] > 0:
            avg_time = elapsed_time / self.batch_stats['total_generated']
            logging.info(f"   Average per simulation: {avg_time:.1f} seconds")
            
            success_rate = (self.batch_stats['total_generated'] / 
                          max(1, self.batch_stats['total_generated'] + self.batch_stats['total_failed'])) * 100
            logging.info(f"   Overall Success Rate: {success_rate:.1f}%")

def main():
    """Main function with batch generation options"""
    
    print("🎭 NIRA Batch Simulation Generator")
    print("Gemini → OpenAI → Supabase Pipeline")
    print("=" * 50)
    print("1. Sample batch (20 simulations)")
    print("2. Language batch (50 simulations)")
    print("3. Full language (250 simulations)")
    print("4. Multi-language batch (5 languages × 50 each)")
    print("5. Production scale (5 languages × 250 each)")
    print("6. Exit")
    
    choice = input("\nEnter your choice (1-6): ")
    
    batch_gen = BatchSimulationGenerator()
    
    if choice == "1":
        language = input("Enter language (default: French): ") or "French"
        batch_gen.generate_sample_batch(language, 20)
        
    elif choice == "2":
        language = input("Enter language (default: Spanish): ") or "Spanish"
        batch_gen.generate_language_batch(language, 10)  # 10 per persona = 50 total
        
    elif choice == "3":
        language = input("Enter language (default: German): ") or "German"
        print(f"⚠️  This will generate 250 simulations for {language}")
        print("⚠️  Estimated time: 2-3 hours")
        confirm = input("Continue? (yes/no): ")
        if confirm.lower() == 'yes':
            batch_gen.generate_full_language_simulations(language)
        
    elif choice == "4":
        languages = ["French", "Spanish", "German", "Italian", "Portuguese"]
        batch_gen.generate_multi_language_batch(languages, 50)
        
    elif choice == "5":
        languages = ["French", "Spanish", "German", "Italian", "Portuguese"]
        print("⚠️  This will generate 1,250 simulations (5 languages × 250 each)")
        print("⚠️  Estimated time: 10-15 hours")
        print("⚠️  API costs: ~$200-400")
        confirm = input("Continue? (yes/no): ")
        if confirm.lower() == 'yes':
            for language in languages:
                batch_gen.generate_full_language_simulations(language)
        
    elif choice == "6":
        print("Goodbye!")
        
    else:
        print("Invalid choice. Please try again.")
        main()

if __name__ == "__main__":
    main()
