#!/usr/bin/env python3
"""
Complete Lesson Generator for NIRA
Generates lessons with all 4 components: vocabulary, conversations, grammar, exercises
"""

import json
import logging
import requests
import os
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class CompleteLessonGenerator:
    def __init__(self):
        self.gemini_api_key = os.getenv('GEMINI_API_KEY')
        self.gemini_url = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key={self.gemini_api_key}"
        
    def generate_complete_lesson(self, language: str, level: str, topic: str, lesson_number: int):
        """Generate a complete lesson with all 4 components"""
        
        logging.info(f"📚 Generating {language} {level} lesson {lesson_number}: {topic}")
        
        # Generate in parts to avoid JSON complexity issues
        vocabulary = self._generate_vocabulary(language, level, topic)
        conversations = self._generate_conversations(language, level, topic)
        grammar = self._generate_grammar(language, level, topic)
        exercises = self._generate_exercises(language, level, topic)
        
        if all([vocabulary, conversations, grammar, exercises]):
            # Combine into complete lesson
            complete_lesson = {
                "title": f"{language} {level}: {topic}",
                "description": f"Complete {language} lesson covering {topic} for {level} level learners",
                "language": language,
                "level": level,
                "topic": topic,
                "lesson_number": lesson_number,
                "estimated_duration_minutes": 25,
                "vocabulary": vocabulary,
                "conversations": conversations,
                "grammar": grammar,
                "exercises": exercises,
                "generation_metadata": {
                    "generated_at": datetime.now().isoformat(),
                    "generator_version": "complete_v1.0"
                }
            }
            
            logging.info(f"✅ Complete lesson generated: {complete_lesson['title']}")
            return complete_lesson
        else:
            logging.error("❌ Failed to generate one or more lesson components")
            return None
    
    def _generate_vocabulary(self, language: str, level: str, topic: str):
        """Generate 15 vocabulary items"""
        
        prompt = f"""
Generate exactly 15 vocabulary items for a {language} {level} lesson about {topic}.

Return ONLY valid JSON array:
[
    {{
        "word": "word_in_{language}",
        "translation": "English translation",
        "pronunciation": "/phonetic/",
        "part_of_speech": "noun",
        "example": "Example sentence in {language}",
        "example_translation": "English translation of example"
    }}
]

Make sure to include exactly 15 items relevant to {topic} for {level} level learners.
"""
        
        return self._call_gemini_api(prompt, "vocabulary")
    
    def _generate_conversations(self, language: str, level: str, topic: str):
        """Generate 10 guided conversations"""
        
        prompt = f"""
Generate exactly 10 short conversations for a {language} {level} lesson about {topic}.

Return ONLY valid JSON array:
[
    {{
        "id": 1,
        "title": "Conversation title",
        "scenario": "Brief scenario description",
        "exchanges": [
            {{
                "speaker": "Person A",
                "text": "Text in {language}",
                "translation": "English translation"
            }},
            {{
                "speaker": "Person B",
                "text": "Response in {language}",
                "translation": "English translation"
            }}
        ]
    }}
]

Each conversation should have 2-4 exchanges and be relevant to {topic}.
"""
        
        return self._call_gemini_api(prompt, "conversations")
    
    def _generate_grammar(self, language: str, level: str, topic: str):
        """Generate 5 grammar essentials"""
        
        prompt = f"""
Generate exactly 5 grammar points for a {language} {level} lesson about {topic}.

Return ONLY valid JSON array:
[
    {{
        "rule": "Grammar rule name",
        "explanation": "Clear explanation of the rule",
        "examples": ["Example 1 in {language}", "Example 2 in {language}"],
        "example_translations": ["Translation 1", "Translation 2"],
        "tips": "Helpful learning tip"
    }}
]

Focus on grammar relevant to {topic} and appropriate for {level} level.
"""
        
        return self._call_gemini_api(prompt, "grammar")
    
    def _generate_exercises(self, language: str, level: str, topic: str):
        """Generate 10 practice exercises"""
        
        prompt = f"""
Generate exactly 10 practice exercises for a {language} {level} lesson about {topic}.

Return ONLY valid JSON array:
[
    {{
        "id": 1,
        "type": "multiple_choice",
        "question": "Question text in English",
        "question_in_{language}": "Question in {language} if applicable",
        "options": ["Option 1", "Option 2", "Option 3", "Option 4"],
        "correct_answer": 0,
        "explanation": "Why this answer is correct",
        "points": 10
    }}
]

Include varied exercise types: multiple_choice, fill_blank, translation, matching.
"""
        
        return self._call_gemini_api(prompt, "exercises")
    
    def _call_gemini_api(self, prompt: str, component_type: str):
        """Call Gemini API and parse response"""
        
        try:
            headers = {"Content-Type": "application/json"}
            data = {
                "contents": [{"parts": [{"text": prompt}]}],
                "generationConfig": {
                    "temperature": 0.4,
                    "topK": 30,
                    "topP": 0.8,
                    "maxOutputTokens": 2048
                }
            }
            
            logging.info(f"🔄 Generating {component_type}...")
            response = requests.post(self.gemini_url, headers=headers, json=data)
            response.raise_for_status()
            
            result = response.json()
            generated_text = result['candidates'][0]['content']['parts'][0]['text']
            
            # Parse JSON
            if "```json" in generated_text:
                json_text = generated_text.split("```json")[1].split("```")[0].strip()
            elif "```" in generated_text:
                json_text = generated_text.split("```")[1].strip()
            else:
                json_text = generated_text.strip()
            
            parsed_data = json.loads(json_text)
            
            logging.info(f"✅ {component_type} generated: {len(parsed_data)} items")
            return parsed_data
            
        except Exception as e:
            logging.error(f"❌ Failed to generate {component_type}: {str(e)}")
            return None

# Test function
if __name__ == "__main__":
    generator = CompleteLessonGenerator()
    
    # Test with Tamil A1 lesson
    lesson = generator.generate_complete_lesson(
        language="Tamil",
        level="A1",
        topic="Basic Greetings and Introductions", 
        lesson_number=1
    )
    
    if lesson:
        print("✅ Complete lesson generated successfully!")
        print(f"Title: {lesson['title']}")
        print(f"Vocabulary: {len(lesson['vocabulary'])} items")
        print(f"Conversations: {len(lesson['conversations'])} items")
        print(f"Grammar: {len(lesson['grammar'])} items") 
        print(f"Exercises: {len(lesson['exercises'])} items")
        
        # Save to file for inspection
        with open('test_lesson_output.json', 'w', encoding='utf-8') as f:
            json.dump(lesson, f, ensure_ascii=False, indent=2)
        print("📁 Lesson saved to test_lesson_output.json")
    else:
        print("❌ Lesson generation failed")
