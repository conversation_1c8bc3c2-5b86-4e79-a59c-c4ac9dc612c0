#!/usr/bin/env python3
"""
Complete Tamil A1 Curriculum Generator
Generate the remaining 14 A1 Tamil lessons to complete the curriculum as planned in the strategy document.
"""

import os
import sys
import json
import time
from datetime import datetime
from typing import Dict, List, Optional

# Add the current directory to Python path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from comprehensive_lesson_generator import ComprehensiveLessonGenerator
    from supabase_uploader import SupabaseUploader
    from elevenlabs_audio_generator import ElevenLabsAudioGenerator
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Please ensure all required modules are available")
    sys.exit(1)

# Tamil A1 Lesson Topics (15 total, 1 already exists)
TAMIL_A1_LESSONS = [
    # Lesson 1 already exists: "Basic Greetings and Introductions"
    {
        "title": "Family Members and Relationships",
        "description": "Learn to talk about family members, relationships, and basic personal information",
        "topic": "Family and Relationships",
        "sequence": 2
    },
    {
        "title": "Numbers and Counting",
        "description": "Master Tamil numbers from 1-100, basic counting, and simple arithmetic expressions",
        "topic": "Numbers and Mathematics",
        "sequence": 3
    },
    {
        "title": "Days, Months, and Time",
        "description": "Learn days of the week, months, telling time, and basic temporal expressions",
        "topic": "Time and Calendar",
        "sequence": 4
    },
    {
        "title": "Colors and Basic Descriptions",
        "description": "Identify colors, describe objects, and use basic adjectives in Tamil",
        "topic": "Colors and Descriptions",
        "sequence": 5
    },
    {
        "title": "Food and Drinks",
        "description": "Essential vocabulary for food, drinks, ordering meals, and expressing preferences",
        "topic": "Food and Beverages",
        "sequence": 6
    },
    {
        "title": "Shopping and Money",
        "description": "Basic shopping vocabulary, asking for prices, and handling money transactions",
        "topic": "Shopping and Commerce",
        "sequence": 7
    },
    {
        "title": "Directions and Locations",
        "description": "Ask for and give directions, describe locations, and navigate in Tamil",
        "topic": "Navigation and Places",
        "sequence": 8
    },
    {
        "title": "Weather and Seasons",
        "description": "Describe weather conditions, seasons, and climate-related vocabulary",
        "topic": "Weather and Climate",
        "sequence": 9
    },
    {
        "title": "Body Parts and Health",
        "description": "Learn body parts, basic health vocabulary, and expressing physical conditions",
        "topic": "Health and Body",
        "sequence": 10
    },
    {
        "title": "Transportation and Travel",
        "description": "Transportation methods, travel vocabulary, and getting around the city",
        "topic": "Transportation",
        "sequence": 11
    },
    {
        "title": "Hobbies and Interests",
        "description": "Express personal interests, hobbies, and leisure activities in Tamil",
        "topic": "Hobbies and Recreation",
        "sequence": 12
    },
    {
        "title": "Work and Occupations",
        "description": "Job titles, workplace vocabulary, and talking about professions",
        "topic": "Work and Career",
        "sequence": 13
    },
    {
        "title": "Emotions and Feelings",
        "description": "Express emotions, feelings, and psychological states in Tamil",
        "topic": "Emotions and Psychology",
        "sequence": 14
    },
    {
        "title": "Daily Routines and Activities",
        "description": "Describe daily activities, routines, and time-based actions",
        "topic": "Daily Life and Routines",
        "sequence": 15
    }
]

class TamilA1CurriculumGenerator:
    def __init__(self):
        self.lesson_generator = ComprehensiveLessonGenerator()
        self.supabase_uploader = SupabaseUploader()
        self.audio_generator = ElevenLabsAudioGenerator()
        self.generated_lessons = []
        self.failed_lessons = []
        
    def generate_single_lesson(self, lesson_config: Dict) -> Optional[Dict]:
        """Generate a single A1 Tamil lesson."""
        try:
            print(f"\n🎯 Generating Lesson {lesson_config['sequence']}: {lesson_config['title']}")
            
            # Generate lesson content
            lesson_data = self.lesson_generator.generate_complete_lesson(
                language="Tamil",
                level="A1",
                topic=lesson_config["topic"],
                title=lesson_config["title"],
                description=lesson_config["description"],
                sequence_order=lesson_config["sequence"]
            )
            
            if not lesson_data:
                print(f"❌ Failed to generate lesson content for: {lesson_config['title']}")
                return None
            
            print(f"✅ Lesson content generated successfully")
            
            # Generate audio files
            print(f"🎵 Generating audio files...")
            audio_metadata = self.audio_generator.generate_lesson_audio(
                lesson_data=lesson_data,
                lesson_id=f"tamil_a1_lesson_{lesson_config['sequence']}",
                voice_id="9BWtsMINqrJLrRacOk9x"  # Tamil voice
            )
            
            if audio_metadata:
                lesson_data['audio_metadata'] = audio_metadata
                print(f"✅ Audio files generated successfully")
            else:
                print(f"⚠️ Audio generation failed, continuing without audio")
            
            # Upload to Supabase
            print(f"📤 Uploading to database...")
            upload_success = self.supabase_uploader.upload_lesson(
                lesson_data=lesson_data,
                language="Tamil",
                level="A1"
            )
            
            if upload_success:
                print(f"✅ Lesson uploaded to database successfully")
                return lesson_data
            else:
                print(f"❌ Failed to upload lesson to database")
                return None
                
        except Exception as e:
            print(f"❌ Error generating lesson {lesson_config['title']}: {e}")
            return None
    
    def generate_all_lessons(self) -> Dict:
        """Generate all missing A1 Tamil lessons."""
        print("🚀 Starting Tamil A1 Curriculum Generation")
        print("=" * 60)
        print(f"📚 Generating {len(TAMIL_A1_LESSONS)} lessons")
        print(f"🎯 Target: Complete A1 curriculum (15 lessons total)")
        
        start_time = datetime.now()
        
        for lesson_config in TAMIL_A1_LESSONS:
            lesson_data = self.generate_single_lesson(lesson_config)
            
            if lesson_data:
                self.generated_lessons.append({
                    'config': lesson_config,
                    'data': lesson_data
                })
            else:
                self.failed_lessons.append(lesson_config)
            
            # Rate limiting - wait between lessons
            print("⏳ Waiting 30 seconds before next lesson...")
            time.sleep(30)
        
        end_time = datetime.now()
        duration = end_time - start_time
        
        # Generate summary report
        return self.generate_summary_report(start_time, end_time, duration)
    
    def generate_summary_report(self, start_time, end_time, duration) -> Dict:
        """Generate a summary report of the generation process."""
        report = {
            'generation_summary': {
                'start_time': start_time.isoformat(),
                'end_time': end_time.isoformat(),
                'duration_minutes': duration.total_seconds() / 60,
                'total_lessons_attempted': len(TAMIL_A1_LESSONS),
                'lessons_generated': len(self.generated_lessons),
                'lessons_failed': len(self.failed_lessons),
                'success_rate': len(self.generated_lessons) / len(TAMIL_A1_LESSONS) * 100
            },
            'generated_lessons': [
                {
                    'sequence': lesson['config']['sequence'],
                    'title': lesson['config']['title'],
                    'topic': lesson['config']['topic']
                }
                for lesson in self.generated_lessons
            ],
            'failed_lessons': [
                {
                    'sequence': lesson['sequence'],
                    'title': lesson['title'],
                    'topic': lesson['topic']
                }
                for lesson in self.failed_lessons
            ]
        }
        
        # Save report to file
        report_filename = f"tamil_a1_generation_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        # Print summary
        print("\n" + "=" * 60)
        print("📊 TAMIL A1 CURRICULUM GENERATION SUMMARY")
        print("=" * 60)
        print(f"⏱️  Duration: {duration.total_seconds() / 60:.1f} minutes")
        print(f"✅ Lessons Generated: {len(self.generated_lessons)}/{len(TAMIL_A1_LESSONS)}")
        print(f"❌ Lessons Failed: {len(self.failed_lessons)}")
        print(f"📈 Success Rate: {report['generation_summary']['success_rate']:.1f}%")
        
        if self.generated_lessons:
            print(f"\n✅ Successfully Generated Lessons:")
            for lesson in self.generated_lessons:
                print(f"   {lesson['config']['sequence']}. {lesson['config']['title']}")
        
        if self.failed_lessons:
            print(f"\n❌ Failed Lessons:")
            for lesson in self.failed_lessons:
                print(f"   {lesson['sequence']}. {lesson['title']}")
        
        print(f"\n📄 Detailed report saved: {report_filename}")
        
        return report

def main():
    """Main function to run the Tamil A1 curriculum generation."""
    print("🎯 Tamil A1 Curriculum Completion")
    print("Generating the remaining 14 A1 lessons to complete the curriculum")
    print("=" * 70)
    
    # Initialize generator
    generator = TamilA1CurriculumGenerator()
    
    # Generate all lessons
    report = generator.generate_all_lessons()
    
    # Final status
    if report['generation_summary']['success_rate'] >= 80:
        print("\n🎉 Tamil A1 curriculum generation completed successfully!")
        print("📱 Your app should now have a complete A1 curriculum with interactive content.")
    else:
        print("\n⚠️ Tamil A1 curriculum generation completed with some failures.")
        print("📝 Check the failed lessons and retry if needed.")
    
    print(f"\n📊 Final Status: {report['generation_summary']['lessons_generated']}/15 A1 lessons available")

if __name__ == "__main__":
    main()
