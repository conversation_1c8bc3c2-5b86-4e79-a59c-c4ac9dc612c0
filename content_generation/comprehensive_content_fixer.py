#!/usr/bin/env python3
"""
NIRA Comprehensive Content Fixer
Fixes all issues identified in the database audit:
- Replaces placeholder content in 482 lessons
- Creates missing vocabulary entries
- Fills A2, C1, C2 level gaps
- Adds missing cultural notes, descriptions, objectives
- Supports all 15 languages
"""

import json
import os
import time
import logging
import requests
from datetime import datetime
from typing import Dict, List, Optional, Tuple

# Configuration
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY", "YOUR_GEMINI_API_KEY_HERE")
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"

# Supabase headers
SUPABASE_HEADERS = {
    "apikey": SUPABASE_KEY,
    "Authorization": f"Bearer {SUPABASE_KEY}",
    "Content-Type": "application/json",
    "Prefer": "return=representation"
}

# Logging setup
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'content_fix_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)

class NIRAContentFixer:
    """Comprehensive content fixer for NIRA database issues"""

    # Supabase helper methods
    def supabase_select(self, table: str, columns: str = "*", filters: Dict = None) -> List[Dict]:
        """Select data from Supabase table"""
        url = f"{SUPABASE_URL}/rest/v1/{table}"
        params = {"select": columns}

        if filters:
            for key, value in filters.items():
                if key == "or":
                    params["or"] = value
                else:
                    params[key] = f"eq.{value}"

        response = requests.get(url, headers=SUPABASE_HEADERS, params=params)
        response.raise_for_status()
        return response.json()

    def supabase_insert(self, table: str, data: Dict) -> Dict:
        """Insert data into Supabase table"""
        url = f"{SUPABASE_URL}/rest/v1/{table}"
        response = requests.post(url, headers=SUPABASE_HEADERS, json=data)
        response.raise_for_status()
        return response.json()

    def supabase_update(self, table: str, data: Dict, filters: Dict) -> Dict:
        """Update data in Supabase table"""
        url = f"{SUPABASE_URL}/rest/v1/{table}"
        params = {}
        for key, value in filters.items():
            params[key] = f"eq.{value}"

        response = requests.patch(url, headers=SUPABASE_HEADERS, json=data, params=params)
        response.raise_for_status()
        return response.json()

    def __init__(self):
        self.fixed_count = 0
        self.created_count = 0
        self.failed_count = 0
        self.gemini_url = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key={GEMINI_API_KEY}"

        # All 15 languages to support
        self.all_languages = [
            "English", "French", "Spanish", "German", "Italian", "Portuguese",
            "Japanese", "Tamil", "Chinese", "Arabic", "Russian", "Korean",
            "Hindi", "Dutch", "Swedish"
        ]

        # CEFR levels
        self.cefr_levels = ["A1", "A2", "B1", "B2", "C1", "C2"]

        # Comprehensive topic list
        self.comprehensive_topics = [
            "Basic Greetings", "Personal Information", "Numbers 1-100", "Time & Dates",
            "Family Members", "Colors & Descriptions", "Food & Drinks (Basic)",
            "Food & Drinks (Ordering)", "Shopping (Clothes)", "Shopping (Money)",
            "Transportation", "Directions & Places", "Weather & Seasons",
            "Hobbies & Free Time", "Work & Professions", "Daily Routines",
            "Health & Body", "Technology & Communication", "Celebrations & Holidays",
            "Travel Essentials", "Education & Learning", "Sports & Exercise",
            "Home & Living", "Environment & Nature", "Culture & Traditions",
            "Business & Finance", "Art & Literature", "Science & Innovation",
            "Politics & Society", "Philosophy & Ethics"
        ]

    def run_comprehensive_fix(self):
        """Run all fixes systematically"""
        logging.info("🚀 Starting comprehensive content fix...")

        # Step 1: Fix placeholder content
        logging.info("📝 Step 1: Fixing placeholder content...")
        self.fix_placeholder_content()

        # Step 2: Create missing vocabulary
        logging.info("📚 Step 2: Creating missing vocabulary...")
        self.create_missing_vocabulary()

        # Step 3: Fill level gaps
        logging.info("📈 Step 3: Filling level gaps...")
        self.fill_level_gaps()

        # Step 4: Add missing content fields
        logging.info("🔧 Step 4: Adding missing content fields...")
        self.fix_missing_fields()

        # Step 5: Ensure all 15 languages have content
        logging.info("🌍 Step 5: Ensuring all 15 languages have content...")
        self.ensure_all_languages()

        # Final report
        self.print_final_report()

    def fix_placeholder_content(self):
        """Fix all lessons with placeholder content"""
        try:
            # Get lessons with placeholder content
            placeholder_lessons = self.supabase_select("lessons", "*", {
                "or": "(content_metadata.cs.Option A,content_metadata.cs.placeholder)"
            })

            logging.info(f"Found {len(placeholder_lessons)} lessons with placeholder content")

            for lesson in placeholder_lessons:
                self.fix_single_lesson_placeholder(lesson)
                time.sleep(1)  # Rate limiting

        except Exception as e:
            logging.error(f"Error fixing placeholder content: {str(e)}")

    def fix_single_lesson_placeholder(self, lesson: Dict):
        """Fix placeholder content in a single lesson"""
        try:
            # Get lesson details
            language_result = supabase.table("learning_paths").select("*").eq("id", lesson["path_id"]).execute()
            if not language_result.data:
                return

            path = language_result.data[0]
            lang_result = supabase.table("languages").select("*").eq("id", path["language_id"]).execute()
            if not lang_result.data:
                return

            language = lang_result.data[0]["name"]
            level = path["level"]

            # Generate new content
            new_content = self.generate_enhanced_lesson_content(
                language, level, lesson["title"], lesson.get("description", "")
            )

            if new_content:
                # Update lesson with new content
                update_data = {
                    "content_metadata": new_content,
                    "description": new_content.get("description", lesson.get("description")),
                    "learning_objectives": new_content.get("learning_objectives", []),
                    "vocabulary_focus": [vocab["word"] for vocab in new_content.get("vocabulary", [])],
                    "grammar_concepts": [gp["concept"] for gp in new_content.get("grammar_points", [])],
                    "cultural_notes": "; ".join([cc["description"] for cc in new_content.get("cultural_context", [])]),
                    "updated_at": datetime.now().isoformat()
                }

                supabase.table("lessons").update(update_data).eq("id", lesson["id"]).execute()
                self.fixed_count += 1
                logging.info(f"✅ Fixed placeholder content for: {lesson['title']}")

        except Exception as e:
            self.failed_count += 1
            logging.error(f"❌ Failed to fix lesson {lesson.get('title', 'Unknown')}: {str(e)}")

    def create_missing_vocabulary(self):
        """Create vocabulary entries for languages that have none"""
        languages_without_vocab = ["Italian", "Portuguese", "German", "Tamil"]

        for language in languages_without_vocab:
            logging.info(f"Creating vocabulary for {language}...")
            self.create_language_vocabulary(language)

    def create_language_vocabulary(self, language: str):
        """Create comprehensive vocabulary for a language"""
        try:
            # Get language ID
            lang_result = supabase.table("languages").select("*").eq("name", language).execute()
            if not lang_result.data:
                logging.error(f"Language {language} not found in database")
                return

            language_id = lang_result.data[0]["id"]

            # Generate vocabulary for different levels
            for level in ["A1", "A2", "B1", "B2", "C1", "C2"]:
                vocab_list = self.generate_vocabulary_for_level(language, level)

                for vocab in vocab_list:
                    vocab_data = {
                        "language_id": language_id,
                        "word": vocab["word"],
                        "pronunciation": vocab["pronunciation"],
                        "translation": {"en": vocab["translation"]},
                        "part_of_speech": vocab["part_of_speech"],
                        "difficulty_level": self.get_difficulty_number(level),
                        "example_sentences": [{"sentence": vocab["example"], "translation": vocab["example_translation"]}],
                        "cultural_context": vocab.get("cultural_context", ""),
                        "tags": [level, vocab["part_of_speech"]],
                        "created_at": datetime.now().isoformat()
                    }

                    try:
                        supabase.table("vocabulary").insert(vocab_data).execute()
                        self.created_count += 1
                    except Exception as e:
                        logging.error(f"Failed to insert vocabulary {vocab['word']}: {str(e)}")

                time.sleep(2)  # Rate limiting

        except Exception as e:
            logging.error(f"Error creating vocabulary for {language}: {str(e)}")

    def generate_vocabulary_for_level(self, language: str, level: str) -> List[Dict]:
        """Generate vocabulary list for a specific language and level"""
        vocab_counts = {"A1": 50, "A2": 75, "B1": 100, "B2": 125, "C1": 150, "C2": 200}
        count = vocab_counts.get(level, 50)

        prompt = f"""
        Generate {count} essential {language} vocabulary words for {level} level learners.

        For each word, provide:
        - The word in {language}
        - English translation
        - Pronunciation guide (IPA when possible)
        - Part of speech
        - Example sentence in {language}
        - English translation of example
        - Cultural context (when relevant)

        Focus on practical, everyday vocabulary appropriate for {level} level.

        Return as JSON array with this structure:
        [
            {{
                "word": "example_word",
                "translation": "english_translation",
                "pronunciation": "/pronunciation/",
                "part_of_speech": "noun",
                "example": "Example sentence in {language}",
                "example_translation": "English translation",
                "cultural_context": "Cultural notes when relevant"
            }}
        ]
        """

        try:
            response = self.call_gemini_api(prompt)
            content = self.parse_ai_response(response)

            if isinstance(content, list):
                return content
            else:
                logging.error(f"Invalid vocabulary response format for {language} {level}")
                return []

        except Exception as e:
            logging.error(f"Failed to generate vocabulary for {language} {level}: {str(e)}")
            return []

    def fill_level_gaps(self):
        """Fill missing A2, C1, C2 level content"""
        for language in self.all_languages:
            for level in ["A2", "C1", "C2"]:
                self.ensure_level_content(language, level)

    def ensure_level_content(self, language: str, level: str):
        """Ensure a language has sufficient content for a level"""
        try:
            # Check current lesson count for this language/level
            lang_result = supabase.table("languages").select("*").eq("name", language).execute()
            if not lang_result.data:
                return

            language_id = lang_result.data[0]["id"]

            # Get learning path for this level
            path_result = supabase.table("learning_paths").select("*").eq("language_id", language_id).eq("level", level).execute()

            if not path_result.data:
                # Create learning path if it doesn't exist
                path_data = {
                    "language_id": language_id,
                    "level": level,
                    "title": f"{language} {level} Learning Path",
                    "description": f"Comprehensive {level} level content for {language}",
                    "is_active": True,
                    "created_at": datetime.now().isoformat()
                }
                path_result = supabase.table("learning_paths").insert(path_data).execute()

            path_id = path_result.data[0]["id"]

            # Check existing lessons
            lessons_result = supabase.table("lessons").select("*").eq("path_id", path_id).execute()
            existing_count = len(lessons_result.data)

            # Target lesson counts by level
            target_counts = {"A2": 25, "C1": 15, "C2": 10}
            target = target_counts.get(level, 20)

            if existing_count < target:
                needed = target - existing_count
                logging.info(f"Creating {needed} lessons for {language} {level}")

                # Generate missing lessons
                for i in range(needed):
                    topic = self.comprehensive_topics[i % len(self.comprehensive_topics)]
                    self.create_lesson_for_path(path_id, language, level, topic, i + existing_count + 1)
                    time.sleep(2)  # Rate limiting

        except Exception as e:
            logging.error(f"Error ensuring content for {language} {level}: {str(e)}")

    def create_lesson_for_path(self, path_id: str, language: str, level: str, topic: str, sequence: int):
        """Create a single lesson for a learning path"""
        try:
            content = self.generate_enhanced_lesson_content(language, level, topic)

            if content:
                lesson_data = {
                    "path_id": path_id,
                    "title": content["title"],
                    "description": content["description"],
                    "lesson_type": self.get_lesson_type(level),
                    "difficulty_level": self.get_difficulty_number(level),
                    "estimated_duration": self.calculate_duration(content),
                    "sequence_order": sequence,
                    "learning_objectives": content.get("learning_objectives", []),
                    "vocabulary_focus": [vocab["word"] for vocab in content.get("vocabulary", [])],
                    "grammar_concepts": [gp["concept"] for gp in content.get("grammar_points", [])],
                    "cultural_notes": "; ".join([cc["description"] for cc in content.get("cultural_context", [])]),
                    "content_metadata": content,
                    "is_active": True,
                    "created_at": datetime.now().isoformat()
                }

                supabase.table("lessons").insert(lesson_data).execute()
                self.created_count += 1
                logging.info(f"✅ Created lesson: {content['title']}")

        except Exception as e:
            self.failed_count += 1
            logging.error(f"❌ Failed to create lesson for {language} {level} - {topic}: {str(e)}")

    def fix_missing_fields(self):
        """Fix lessons with missing descriptions, objectives, cultural notes, etc."""
        try:
            # Get lessons with missing fields
            result = supabase.table("lessons").select("*").or_(
                "description.is.null,learning_objectives.is.null,cultural_notes.is.null"
            ).execute()

            missing_field_lessons = result.data
            logging.info(f"Found {len(missing_field_lessons)} lessons with missing fields")

            for lesson in missing_field_lessons:
                self.fix_lesson_missing_fields(lesson)
                time.sleep(1)

        except Exception as e:
            logging.error(f"Error fixing missing fields: {str(e)}")

    def fix_lesson_missing_fields(self, lesson: Dict):
        """Fix missing fields in a single lesson"""
        try:
            updates = {}

            # Fix missing description
            if not lesson.get("description") or len(lesson.get("description", "")) < 20:
                updates["description"] = f"Comprehensive lesson covering {lesson.get('title', 'language concepts')} with practical exercises and cultural context."

            # Fix missing learning objectives
            if not lesson.get("learning_objectives"):
                updates["learning_objectives"] = [
                    f"Master key vocabulary related to {lesson.get('title', 'the topic')}",
                    "Apply grammar concepts in practical contexts",
                    "Understand cultural nuances and appropriate usage",
                    "Complete interactive exercises with confidence"
                ]

            # Fix missing cultural notes
            if not lesson.get("cultural_notes") or len(lesson.get("cultural_notes", "")) < 20:
                updates["cultural_notes"] = "Cultural context and etiquette considerations for real-world application of learned concepts."

            if updates:
                updates["updated_at"] = datetime.now().isoformat()
                supabase.table("lessons").update(updates).eq("id", lesson["id"]).execute()
                self.fixed_count += 1
                logging.info(f"✅ Fixed missing fields for: {lesson.get('title', 'Unknown')}")

        except Exception as e:
            self.failed_count += 1
            logging.error(f"❌ Failed to fix missing fields for lesson: {str(e)}")

    def ensure_all_languages(self):
        """Ensure all 15 languages have basic content"""
        for language in self.all_languages:
            self.ensure_language_exists(language)

    def ensure_language_exists(self, language: str):
        """Ensure a language exists with basic content"""
        try:
            # Check if language exists
            lang_result = supabase.table("languages").select("*").eq("name", language).execute()

            if not lang_result.data:
                # Create language entry
                lang_data = {
                    "code": language.lower()[:2],
                    "name": language,
                    "native_name": self.get_native_name(language),
                    "difficulty_level": self.get_language_difficulty(language),
                    "is_active": True,
                    "created_at": datetime.now().isoformat()
                }
                lang_result = supabase.table("languages").insert(lang_data).execute()
                logging.info(f"✅ Created language entry for {language}")

            language_id = lang_result.data[0]["id"]

            # Ensure basic learning paths exist
            for level in self.cefr_levels:
                self.ensure_learning_path_exists(language_id, language, level)

        except Exception as e:
            logging.error(f"Error ensuring language {language} exists: {str(e)}")

    def ensure_learning_path_exists(self, language_id: str, language: str, level: str):
        """Ensure a learning path exists for a language/level combination"""
        try:
            path_result = supabase.table("learning_paths").select("*").eq("language_id", language_id).eq("level", level).execute()

            if not path_result.data:
                path_data = {
                    "language_id": language_id,
                    "level": level,
                    "title": f"{language} {level} Learning Path",
                    "description": f"Comprehensive {level} level learning path for {language}",
                    "is_active": True,
                    "created_at": datetime.now().isoformat()
                }
                supabase.table("learning_paths").insert(path_data).execute()
                logging.info(f"✅ Created learning path for {language} {level}")

        except Exception as e:
            logging.error(f"Error creating learning path for {language} {level}: {str(e)}")

    # Helper methods
    def generate_enhanced_lesson_content(self, language: str, level: str, topic: str, existing_description: str = "") -> Optional[Dict]:
        """Generate enhanced lesson content using Gemini AI"""

        vocab_targets = {"A1": 8, "A2": 10, "B1": 12, "B2": 15, "C1": 18, "C2": 20}
        vocab_count = vocab_targets.get(level, 10)

        prompt = f"""
        Create a comprehensive {language} language lesson for {level} level on the topic: {topic}

        REQUIREMENTS:
        1. VOCABULARY: Exactly {vocab_count} words with proper {language} spelling, English translation, pronunciation, part of speech, example sentences
        2. GRAMMAR POINTS: 2-3 key grammar concepts with clear explanations and examples
        3. CULTURAL CONTEXT: Real-world scenarios with cultural etiquette and customs
        4. DIALOGUES: 4-6 realistic conversations with cultural context notes
        5. EXERCISES: Interactive practice including multiple choice, fill-in-blank, matching
        6. LEARNING OBJECTIVES: 4 clear, measurable learning goals

        OUTPUT FORMAT: Valid JSON with this structure:
        {{
            "title": "Engaging lesson title in English",
            "description": "Brief lesson description (50-100 words)",
            "learning_objectives": ["objective1", "objective2", "objective3", "objective4"],
            "vocabulary": [
                {{
                    "word": "word_in_{language}",
                    "translation": "english_translation",
                    "pronunciation": "/pronunciation/",
                    "part_of_speech": "noun/verb/etc",
                    "example": "Example sentence in {language}",
                    "example_translation": "English translation of example",
                    "cultural_context": "Cultural notes when relevant"
                }}
            ],
            "grammar_points": [
                {{
                    "concept": "Grammar concept name",
                    "explanation": "Clear explanation",
                    "examples": ["example1", "example2"],
                    "tips": "Practice tips"
                }}
            ],
            "cultural_context": [
                {{
                    "topic": "Cultural topic",
                    "description": "Description of cultural aspect",
                    "dos": ["do1", "do2"],
                    "donts": ["dont1", "dont2"]
                }}
            ],
            "dialogues": [
                {{
                    "speakers": ["Speaker1", "Speaker2"],
                    "exchanges": [
                        {{
                            "speaker": "Speaker1",
                            "text": "Text in {language}",
                            "translation": "English translation"
                        }}
                    ],
                    "cultural_notes": "Cultural context for dialogue"
                }}
            ],
            "exercises": [
                {{
                    "type": "multiple_choice",
                    "question": "Question text",
                    "options": ["option1", "option2", "option3", "option4"],
                    "correct_answer": 0,
                    "explanation": "Explanation of correct answer",
                    "points": 10
                }}
            ]
        }}
        """

        try:
            response = self.call_gemini_api(prompt)
            content = self.parse_ai_response(response)

            if self.validate_content(content):
                return content
            else:
                logging.error("Content validation failed")
                return None

        except Exception as e:
            logging.error(f"Failed to generate enhanced content: {str(e)}")
            return None

    def call_gemini_api(self, prompt: str) -> Dict:
        """Call Gemini API with the given prompt"""
        headers = {"Content-Type": "application/json"}

        data = {
            "contents": [{"parts": [{"text": prompt}]}],
            "generationConfig": {
                "temperature": 0.8,
                "topK": 40,
                "topP": 0.95,
                "maxOutputTokens": 4096
            }
        }

        response = requests.post(self.gemini_url, headers=headers, json=data)
        response.raise_for_status()
        return response.json()

    def parse_ai_response(self, response: Dict) -> Dict:
        """Parse AI response and extract JSON content"""
        try:
            text = response["candidates"][0]["content"]["parts"][0]["text"]

            if "```json" in text:
                json_start = text.find("```json") + 7
                json_end = text.find("```", json_start)
                json_text = text[json_start:json_end].strip()
            elif "{" in text and "}" in text:
                json_start = text.find("{")
                json_end = text.rfind("}") + 1
                json_text = text[json_start:json_end]
            else:
                raise ValueError("No JSON content found in response")

            return json.loads(json_text)

        except Exception as e:
            logging.error(f"Failed to parse AI response: {str(e)}")
            raise

    def validate_content(self, content: Dict) -> bool:
        """Validate generated content structure"""
        required_fields = ["title", "description", "vocabulary", "grammar_points", "cultural_context", "dialogues", "exercises"]

        for field in required_fields:
            if field not in content:
                logging.error(f"Missing required field: {field}")
                return False

        if not isinstance(content["vocabulary"], list) or len(content["vocabulary"]) == 0:
            logging.error("Invalid vocabulary structure")
            return False

        return True

    def get_difficulty_number(self, level: str) -> int:
        """Convert CEFR level to difficulty number"""
        mapping = {"A1": 1, "A2": 2, "B1": 3, "B2": 4, "C1": 5, "C2": 6}
        return mapping.get(level, 1)

    def get_lesson_type(self, level: str) -> str:
        """Get appropriate lesson type for level"""
        type_mapping = {
            "A1": "vocabulary_culture",
            "A2": "elementary_integrated",
            "B1": "intermediate_complex",
            "B2": "upper_intermediate_advanced",
            "C1": "advanced_proficiency",
            "C2": "mastery_proficiency"
        }
        return type_mapping.get(level, "ai_generated")

    def calculate_duration(self, content: Dict) -> int:
        """Calculate estimated lesson duration"""
        base_duration = 15
        vocab_time = len(content.get("vocabulary", [])) * 1
        exercise_time = len(content.get("exercises", [])) * 2
        dialogue_time = len(content.get("dialogues", [])) * 3
        return base_duration + vocab_time + exercise_time + dialogue_time

    def get_native_name(self, language: str) -> str:
        """Get native name for language"""
        native_names = {
            "English": "English", "French": "Français", "Spanish": "Español",
            "German": "Deutsch", "Italian": "Italiano", "Portuguese": "Português",
            "Japanese": "日本語", "Tamil": "தமிழ்", "Chinese": "中文",
            "Arabic": "العربية", "Russian": "Русский", "Korean": "한국어",
            "Hindi": "हिन्दी", "Dutch": "Nederlands", "Swedish": "Svenska"
        }
        return native_names.get(language, language)

    def get_language_difficulty(self, language: str) -> int:
        """Get difficulty level for language (for English speakers)"""
        difficulty_map = {
            "English": 1, "French": 3, "Spanish": 2, "German": 3, "Italian": 2,
            "Portuguese": 2, "Japanese": 5, "Tamil": 4, "Chinese": 5,
            "Arabic": 5, "Russian": 4, "Korean": 5, "Hindi": 4,
            "Dutch": 2, "Swedish": 2
        }
        return difficulty_map.get(language, 3)

    def print_final_report(self):
        """Print comprehensive final report"""
        logging.info("🎉 COMPREHENSIVE CONTENT FIX COMPLETE!")
        logging.info(f"   Fixed lessons: {self.fixed_count}")
        logging.info(f"   Created content: {self.created_count}")
        logging.info(f"   Failed operations: {self.failed_count}")

        if self.failed_count > 0:
            logging.warning(f"⚠️  {self.failed_count} operations failed. Check logs for details.")
        else:
            logging.info("✅ All operations completed successfully!")

def main():
    """Main execution function"""
    print("🚀 NIRA Comprehensive Content Fixer")
    print("This will fix all database issues identified in the audit:")
    print("- Replace placeholder content in 482 lessons")
    print("- Create missing vocabulary entries")
    print("- Fill A2, C1, C2 level gaps")
    print("- Add missing cultural notes, descriptions, objectives")
    print("- Ensure all 15 languages have content")
    print()

    if GEMINI_API_KEY == "YOUR_GEMINI_API_KEY_HERE":
        print("❌ ERROR: Please set your GEMINI_API_KEY environment variable")
        print("   export GEMINI_API_KEY='your-actual-api-key'")
        return

    confirm = input("Do you want to proceed? (yes/no): ")
    if confirm.lower() != 'yes':
        print("Operation cancelled.")
        return

    fixer = NIRAContentFixer()
    fixer.run_comprehensive_fix()

if __name__ == "__main__":
    main()
