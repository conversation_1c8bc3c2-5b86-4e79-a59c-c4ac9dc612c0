#!/usr/bin/env python3
"""
NIRA Comprehensive Lesson Generator
Generates lessons for all missing languages with 100% coverage and consistency
Uses Gemini Flash 2.0 and uploads to Supabase
"""

import json
import time
import logging
import requests
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from supabase import create_client, Client
import os
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'lesson_generation_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)

@dataclass
class LessonConfig:
    """Configuration for lesson generation"""
    # API Configuration
    GEMINI_API_KEY: str = "AIzaSyAYasSocME9nPr7HP625EEx4cofwm5MH3Q"  # Replace with your key
    SUPABASE_URL: str = "https://lyaojebttnqilmdosmjk.supabase.co"
    SUPABASE_KEY: str = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"  # Replace with your key
    OPEANI_API_KEY: str = "***********************************************************************************************************************************************************************" 

    # All languages that need lessons generated (original 7 + new 10)
    ALL_LANGUAGES = [
        # Original 7 languages (currently being processed)
        ("Korean", "ko"),
        ("Hindi", "hi"),
        ("Chinese", "zh"),
        ("Telugu", "te"),
        ("Vietnamese", "vi"),
        ("Indonesian", "id"),
        ("Arabic", "ar"),
        # New 10 languages
        ("Kannada", "kn"),
        ("Malayalam", "ml"),
        ("Bengali", "bn"),
        ("Marathi", "mr"),
        ("Punjabi", "pa"),
        ("Dutch", "nl"),
        ("Swedish", "sv"),
        ("Thai", "th"),
        ("Russian", "ru"),
        ("Norwegian", "no")
    ]

    # For backward compatibility with existing retry script
    MISSING_LANGUAGES = ALL_LANGUAGES[:7]  # Original 7 languages

    # CEFR Levels
    LEVELS = ["A1", "A2", "B1", "B2", "C1", "C2"]

    # Comprehensive topic list (15 topics per level = 90 lessons per language)
    TOPICS_BY_LEVEL = {
        "A1": [
            "Basic Greetings and Introductions",
            "Numbers and Time",
            "Family and Personal Information",
            "Food and Drinks",
            "Shopping and Money",
            "Directions and Transportation",
            "Weather and Seasons",
            "Daily Routines",
            "Colors and Descriptions",
            "Body Parts and Health",
            "Clothing and Appearance",
            "Home and Furniture",
            "School and Work",
            "Hobbies and Free Time",
            "Emergency Situations"
        ],
        "A2": [
            "Travel and Vacation",
            "Restaurant and Dining",
            "Technology and Communication",
            "Sports and Exercise",
            "Entertainment and Media",
            "Banking and Services",
            "Medical Appointments",
            "Job Interviews",
            "Cultural Events",
            "Environmental Issues",
            "Past Experiences",
            "Future Plans",
            "Opinions and Preferences",
            "Comparing Things",
            "Social Situations"
        ],
        "B1": [
            "Business and Professional Life",
            "Education and Learning",
            "Relationships and Emotions",
            "News and Current Events",
            "Art and Culture",
            "Science and Technology",
            "Politics and Society",
            "Philosophy and Ideas",
            "History and Traditions",
            "Geography and Nature",
            "Literature and Stories",
            "Music and Performance",
            "Cooking and Cuisine",
            "Fashion and Style",
            "Urban vs Rural Life"
        ],
        "B2": [
            "Global Issues and Solutions",
            "Innovation and Entrepreneurship",
            "Psychology and Human Behavior",
            "Economics and Finance",
            "Law and Justice",
            "Medicine and Healthcare",
            "Research and Academia",
            "International Relations",
            "Climate Change",
            "Digital Society",
            "Ethics and Morality",
            "Leadership and Management",
            "Creativity and Design",
            "Social Media Impact",
            "Future Technologies"
        ],
        "C1": [
            "Advanced Academic Discourse",
            "Professional Negotiations",
            "Complex Social Issues",
            "Philosophical Debates",
            "Scientific Research Methods",
            "Literary Analysis",
            "Political Commentary",
            "Economic Theory",
            "Cultural Criticism",
            "Historical Analysis",
            "Psychological Theories",
            "Technological Ethics",
            "Environmental Policy",
            "International Law",
            "Advanced Communication"
        ],
        "C2": [
            "Mastery of Nuanced Expression",
            "Expert Professional Communication",
            "Advanced Cultural Analysis",
            "Sophisticated Argumentation",
            "Complex Academic Writing",
            "Advanced Literary Interpretation",
            "Nuanced Political Discussion",
            "Expert Technical Communication",
            "Advanced Philosophical Discourse",
            "Mastery of Idiomatic Expression",
            "Complex Emotional Expression",
            "Advanced Humor and Irony",
            "Sophisticated Cultural Commentary",
            "Expert Presentation Skills",
            "Mastery of Register and Style"
        ]
    }

class ComprehensiveLessonGenerator:
    def __init__(self, config: LessonConfig):
        self.config = config
        self.supabase: Client = create_client(config.SUPABASE_URL, config.SUPABASE_KEY)
        self.generated_count = 0
        self.failed_count = 0
        self.total_lessons = len(config.MISSING_LANGUAGES) * len(config.LEVELS) * 15  # 15 topics per level

        logging.info(f"🚀 Initialized generator for {self.total_lessons} lessons")
        logging.info(f"📊 Languages: {len(config.MISSING_LANGUAGES)}, Levels: {len(config.LEVELS)}, Topics per level: 15")

    def generate_all_missing_lessons(self):
        """Generate lessons for all missing languages with full coverage"""
        logging.info("🎯 Starting comprehensive lesson generation...")

        start_time = time.time()
        current_lesson = 0

        for language_name, language_code in self.config.MISSING_LANGUAGES:
            logging.info(f"\n🌍 Starting {language_name} ({language_code}) - {len(self.config.LEVELS) * 15} lessons")

            # Ensure language exists in database
            language_id = self._ensure_language_exists(language_name, language_code)
            if not language_id:
                logging.error(f"❌ Failed to create language entry for {language_name}")
                continue

            # Generate lessons for each level
            for level in self.config.LEVELS:
                logging.info(f"📚 Generating {level} level lessons for {language_name}")

                topics = self.config.TOPICS_BY_LEVEL[level]
                for i, topic in enumerate(topics, 1):
                    current_lesson += 1
                    progress = (current_lesson / self.total_lessons) * 100

                    logging.info(f"📝 [{current_lesson}/{self.total_lessons}] ({progress:.1f}%) {language_name} {level} - {topic}")

                    # Generate lesson content
                    content = self._generate_lesson_content(language_name, language_code, level, topic)

                    if content:
                        # Upload to Supabase
                        success = self._upload_to_supabase(content, language_id, level, i)
                        if success:
                            self.generated_count += 1
                            logging.info(f"✅ Successfully uploaded lesson: {content['title']}")
                        else:
                            self.failed_count += 1
                            logging.error(f"❌ Failed to upload lesson: {content['title']}")
                    else:
                        self.failed_count += 1
                        logging.error(f"❌ Failed to generate content for {topic}")

                    # Rate limiting to avoid API limits
                    time.sleep(2)

        # Final report
        elapsed_time = time.time() - start_time
        self._print_final_report(elapsed_time)

    def _ensure_language_exists(self, language_name: str, language_code: str) -> Optional[str]:
        """Ensure language exists in database, create if not"""
        try:
            # Check if language exists
            result = self.supabase.table("languages").select("id").eq("code", language_code).execute()

            if result.data:
                language_id = result.data[0]["id"]
                logging.info(f"✅ Language {language_name} already exists with ID: {language_id}")

                # Check if learning paths exist, create if not
                path_check = self.supabase.table("learning_paths").select("id").eq("language_id", language_id).execute()
                if not path_check.data:
                    logging.info(f"🔧 Creating missing learning paths for {language_name}")
                    self._create_learning_path(language_id, language_name)

                return language_id

            # Create new language with native names
            native_names = {
                # Original 7 languages
                "Korean": "한국어",
                "Hindi": "हिन्दी",
                "Chinese": "中文",
                "Telugu": "తెలుగు",
                "Vietnamese": "Tiếng Việt",
                "Indonesian": "Bahasa Indonesia",
                "Arabic": "العربية",
                # New 10 languages
                "Kannada": "ಕನ್ನಡ",
                "Malayalam": "മലയാളം",
                "Bengali": "বাংলা",
                "Marathi": "मराठी",
                "Punjabi": "ਪੰਜਾਬੀ",
                "Dutch": "Nederlands",
                "Swedish": "Svenska",
                "Thai": "ไทย",
                "Russian": "Русский",
                "Norwegian": "Norsk"
            }

            new_language = {
                "name": language_name,
                "code": language_code,
                "native_name": native_names.get(language_name, language_name),
                "is_active": True
            }

            result = self.supabase.table("languages").insert(new_language).execute()
            if result.data:
                language_id = result.data[0]["id"]
                logging.info(f"✅ Created new language {language_name} with ID: {language_id}")

                # Create learning paths for this language
                self._create_learning_path(language_id, language_name)
                return language_id

        except Exception as e:
            logging.error(f"❌ Error ensuring language exists: {str(e)}")
            return None

    def _create_learning_path(self, language_id: str, language_name: str):
        """Create learning paths for each CEFR level"""
        try:
            # Get a default agent (use Marie)
            agent_result = self.supabase.table("agents").select("id").eq("name", "Marie").execute()
            if not agent_result.data:
                logging.error("No default agent found")
                return

            agent_id = agent_result.data[0]["id"]

            # Create learning paths for each CEFR level
            for level in self.config.LEVELS:
                learning_path = {
                    "language_id": language_id,
                    "agent_id": agent_id,
                    "name": f"{language_name} {level} Course",
                    "description": f"{language_name} course for {level} level",
                    "level": level,
                    "estimated_hours": 30,  # 15 lessons × 2 hours average
                    "is_active": True
                }

                result = self.supabase.table("learning_paths").insert(learning_path).execute()
                if result.data:
                    logging.info(f"✅ Created {level} learning path for {language_name}")

        except Exception as e:
            logging.error(f"❌ Error creating learning path: {str(e)}")

    def _generate_lesson_content(self, language_name: str, language_code: str, level: str, topic: str) -> Optional[Dict]:
        """Generate comprehensive lesson content using Gemini Flash 2.0 with retry logic"""

        max_retries = 3

        for attempt in range(max_retries):
            try:
                prompt = self._create_lesson_prompt(language_name, language_code, level, topic)

                # Call Gemini API with retry count for temperature adjustment
                response = self._call_gemini_api(prompt, retry_count=attempt)
                content = self._parse_ai_response(response)

                # Validate content structure
                if self._validate_content(content):
                    if attempt > 0:
                        logging.info(f"✅ Succeeded on retry {attempt + 1}")
                    return content
                else:
                    raise ValueError("Content validation failed")

            except Exception as e:
                if attempt < max_retries - 1:
                    logging.warning(f"⚠️ Attempt {attempt + 1} failed: {str(e)}, retrying...")
                    time.sleep(2 * (attempt + 1))  # Exponential backoff
                else:
                    logging.error(f"❌ Failed to generate lesson content after {max_retries} attempts: {str(e)}")

        return None

    def _create_lesson_prompt(self, language_name: str, language_code: str, level: str, topic: str) -> str:
        """Create comprehensive AI prompt for lesson generation"""

        # CEFR-specific targets - simplified for B2+ to avoid JSON errors
        level_specs = {
            "A1": {"vocab": 8, "grammar": 2, "exercises": 4, "duration": 15},
            "A2": {"vocab": 10, "grammar": 3, "exercises": 5, "duration": 20},
            "B1": {"vocab": 12, "grammar": 3, "exercises": 6, "duration": 25},
            "B2": {"vocab": 10, "grammar": 2, "exercises": 4, "duration": 30},  # Simplified
            "C1": {"vocab": 10, "grammar": 2, "exercises": 4, "duration": 35},  # Simplified
            "C2": {"vocab": 10, "grammar": 2, "exercises": 4, "duration": 40}   # Simplified
        }

        specs = level_specs.get(level, level_specs["A1"])

        # Use simplified prompt for advanced levels to avoid JSON parsing issues
        if level in ["B2", "C1", "C2"]:
            return self._create_simplified_prompt(language_name, language_code, level, topic, specs)
        else:
            return self._create_standard_prompt(language_name, language_code, level, topic, specs)

    def _create_simplified_prompt(self, language_name: str, language_code: str, level: str, topic: str, specs: dict) -> str:
        """Create simplified prompt for B2+ levels to avoid JSON parsing errors"""

        return f"""
Create a {language_name} language lesson for {level} level on: {topic}

REQUIREMENTS:
- {specs['vocab']} vocabulary words with translations
- {specs['grammar']} grammar points with examples
- {specs['exercises']} exercises (multiple choice only)
- Keep content concise and focused

CRITICAL JSON RULES:
1. Use ONLY double quotes for strings
2. Escape all quotes inside strings with \"
3. No trailing commas
4. Maximum 3 examples per section
5. Keep all text short and simple

OUTPUT (valid JSON only):
{{
  "title": "Lesson title in English",
  "description": "Brief description",
  "estimated_duration": {specs['duration']},
  "vocabulary": [
    {{
      "word": "{language_name} word",
      "translation": "English translation",
      "pronunciation": "pronunciation",
      "part_of_speech": "noun",
      "example": "Short example",
      "example_translation": "Example translation"
    }}
  ],
  "grammar_points": [
    {{
      "rule": "Grammar rule",
      "explanation": "Brief explanation",
      "examples": ["Example 1", "Example 2"],
      "translations": ["Translation 1", "Translation 2"]
    }}
  ],
  "exercises": [
    {{
      "type": "multiple_choice",
      "question": "Question in English",
      "options": ["Option 1", "Option 2", "Option 3", "Option 4"],
      "correct_answer": 0,
      "explanation": "Brief explanation"
    }}
  ],
  "learning_objectives": [
    "Objective 1",
    "Objective 2"
  ]
}}

Generate ONLY valid JSON. Start with {{ and end with }}.
"""

    def _create_standard_prompt(self, language_name: str, language_code: str, level: str, topic: str, specs: dict) -> str:
        """Create standard comprehensive prompt for A1-B1 levels"""

        return f"""
Create a comprehensive {language_name} language lesson for {level} level on: {topic}

STRICT REQUIREMENTS:
1. VOCABULARY: Exactly {specs['vocab']} words with:
   - {language_name} word (proper script/characters)
   - English translation
   - Pronunciation guide (IPA or romanization)
   - Part of speech
   - Example sentence in {language_name}
   - Example sentence translation
   - Cultural context notes

2. GRAMMAR: {specs['grammar']} grammar points with:
   - Clear rule explanation
   - {language_name} examples with translations
   - Common mistakes to avoid
   - Practice patterns

3. DIALOGUES: 2-3 realistic conversations with:
   - Natural {language_name} dialogue
   - English translations
   - Cultural appropriateness notes
   - Pronunciation guides

4. EXERCISES: {specs['exercises']} varied exercises:
   - Multiple choice (4 options each)
   - Fill in the blanks
   - Translation practice
   - Listening comprehension
   - Speaking practice prompts

5. CULTURAL NOTES:
   - Relevant cultural context for {topic}
   - Do's and don'ts
   - Regional variations
   - Social etiquette

LEVEL GUIDELINES for {level}:
{self._get_level_guidelines(level)}

CRITICAL JSON FORMATTING RULES:
1. Use ONLY double quotes for strings
2. Escape all quotes inside strings with \"
3. No trailing commas
4. All strings must be properly closed
5. No line breaks inside string values

OUTPUT FORMAT (MUST be valid JSON):
{{
  "title": "Engaging lesson title in English",
  "description": "Brief lesson description (2-3 sentences)",
  "estimated_duration": {specs['duration']},
  "vocabulary": [
    {{
      "word": "{language_name} word",
      "translation": "English translation",
      "pronunciation": "pronunciation guide",
      "part_of_speech": "noun/verb/adjective/etc",
      "example": "Example sentence in {language_name}",
      "example_translation": "Example sentence in English",
      "cultural_note": "Cultural context if relevant"
    }}
  ],
  "grammar_points": [
    {{
      "rule": "Grammar rule title",
      "explanation": "Clear explanation",
      "examples": ["Example 1 in {language_name}", "Example 2 in {language_name}"],
      "translations": ["Translation 1", "Translation 2"],
      "common_mistakes": "Common errors to avoid"
    }}
  ],
  "dialogues": [
    {{
      "title": "Dialogue title",
      "speakers": ["Speaker A", "Speaker B"],
      "lines": [
        {{
          "speaker": "Speaker A",
          "text": "{language_name} text",
          "translation": "English translation",
          "pronunciation": "pronunciation guide"
        }}
      ],
      "cultural_notes": "Cultural context for this dialogue"
    }}
  ],
  "exercises": [
    {{
      "type": "multiple_choice",
      "question": "Question in English",
      "question_native": "Question in {language_name}",
      "options": ["Option 1", "Option 2", "Option 3", "Option 4"],
      "correct_answer": 0,
      "explanation": "Why this answer is correct"
    }}
  ],
  "cultural_context": {{
    "main_points": ["Key cultural point 1", "Key cultural point 2"],
    "dos_and_donts": {{
      "dos": ["Do this", "Do that"],
      "donts": ["Don't do this", "Don't do that"]
    }},
    "regional_notes": "Regional variations or notes"
  }},
  "learning_objectives": [
    "Students will be able to...",
    "Students will understand...",
    "Students will practice..."
  ]
}}

IMPORTANT: Generate ONLY valid JSON. No markdown, no explanations, no additional text. Start with {{ and end with }}.
"""

    def _get_level_guidelines(self, level: str) -> str:
        """Get CEFR level-specific guidelines"""
        guidelines = {
            "A1": "Basic everyday expressions, simple phrases, concrete needs. Present tense focus.",
            "A2": "Routine tasks, familiar topics, simple past/future. Basic social interaction.",
            "B1": "Main points of clear standard input, travel situations, personal interests.",
            "B2": "Complex text main ideas, technical discussions, spontaneous interaction.",
            "C1": "Wide range of demanding texts, implicit meaning, fluent expression.",
            "C2": "Virtually everything, subtle shades of meaning, precise expression."
        }
        return guidelines.get(level, guidelines["A1"])

    def _call_gemini_api(self, prompt: str, retry_count: int = 0) -> Dict:
        """Call Gemini Flash 2.0 API with retry logic"""
        url = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key={self.config.GEMINI_API_KEY}"

        # Adjust temperature based on retry count (lower = more deterministic)
        temperature = max(0.3, 0.7 - (retry_count * 0.2))

        payload = {
            "contents": [{"parts": [{"text": prompt}]}],
            "generationConfig": {
                "temperature": temperature,
                "topK": 20,  # Reduced for more focused responses
                "topP": 0.8,  # Reduced for more deterministic output
                "maxOutputTokens": 4096,
                "responseMimeType": "application/json"
            }
        }

        response = requests.post(url, json=payload, headers={"Content-Type": "application/json"}, timeout=60)

        if not response.ok:
            raise Exception(f"Gemini API error: {response.status_code} - {response.text}")

        return response.json()

    def _parse_ai_response(self, response: Dict) -> Dict:
        """Parse Gemini API response with robust error handling"""
        try:
            content_text = response["candidates"][0]["content"]["parts"][0]["text"]

            # Clean up common JSON issues
            content_text = self._clean_json_text(content_text)

            return json.loads(content_text)
        except (KeyError, IndexError) as e:
            raise Exception(f"Invalid response structure: {str(e)}")
        except json.JSONDecodeError as e:
            # Try to fix common JSON issues
            try:
                fixed_content = self._fix_json_errors(content_text)
                return json.loads(fixed_content)
            except:
                raise Exception(f"Failed to parse AI response: {str(e)}")

    def _clean_json_text(self, text: str) -> str:
        """Clean up common JSON formatting issues"""
        # Remove any text before the first {
        start_idx = text.find('{')
        if start_idx > 0:
            text = text[start_idx:]

        # Remove any text after the last }
        end_idx = text.rfind('}')
        if end_idx > 0:
            text = text[:end_idx + 1]

        # Remove markdown code blocks if present
        text = text.replace('```json', '').replace('```', '')

        return text.strip()

    def _fix_json_errors(self, text: str) -> str:
        """Attempt to fix common JSON parsing errors"""
        import re

        # Fix unterminated strings by adding closing quotes
        # This is a simple heuristic - look for lines that seem to have unterminated strings
        lines = text.split('\n')
        fixed_lines = []

        for line in lines:
            # If line has an odd number of quotes and ends without a quote, add one
            quote_count = line.count('"')
            if quote_count % 2 == 1 and not line.rstrip().endswith('"'):
                # Find the last quote and see if it looks like an unterminated string
                if ':' in line and not line.rstrip().endswith(','):
                    line = line.rstrip() + '"'

            fixed_lines.append(line)

        fixed_text = '\n'.join(fixed_lines)

        # Fix missing commas between objects
        fixed_text = re.sub(r'}\s*{', '},{', fixed_text)

        # Fix trailing commas before closing brackets
        fixed_text = re.sub(r',(\s*[}\]])', r'\1', fixed_text)

        return fixed_text

    def _validate_content(self, content: Dict) -> bool:
        """Validate generated content structure"""
        required_fields = ["title", "description", "vocabulary", "grammar_points", "exercises"]

        for field in required_fields:
            if field not in content:
                logging.error(f"Missing required field: {field}")
                return False

        # Validate vocabulary count
        if len(content.get("vocabulary", [])) < 5:
            logging.error("Insufficient vocabulary items")
            return False

        # Validate exercises
        if len(content.get("exercises", [])) < 3:
            logging.error("Insufficient exercises")
            return False

        return True

    def _upload_to_supabase(self, content: Dict, language_id: str, level: str, sequence_order: int) -> bool:
        """Upload lesson content to Supabase"""
        try:
            # Get learning path for specific level
            path_result = self.supabase.table("learning_paths").select("id").eq("language_id", language_id).eq("level", level).execute()

            if not path_result.data:
                logging.error(f"No learning path found for language_id: {language_id}, level: {level}")
                return False

            path_id = path_result.data[0]["id"]

            # Prepare lesson data (only include fields that exist and are properly formatted)
            lesson_data = {
                "path_id": path_id,
                "title": content["title"],
                "description": content["description"],
                "lesson_type": "comprehensive",
                "difficulty_level": self._get_difficulty_number(level),
                "estimated_duration": content.get("estimated_duration", 20),
                "sequence_order": sequence_order,
                "learning_objectives": content.get("learning_objectives", []),
                "grammar_concepts": [point["rule"] for point in content.get("grammar_points", [])],
                "cultural_notes": self._extract_cultural_notes(content),
                "content_metadata": content,
                "is_active": True
            }

            # Insert lesson
            result = self.supabase.table("lessons").insert(lesson_data).execute()

            if result.data:
                return True
            else:
                logging.error(f"Failed to insert lesson: {result}")
                return False

        except Exception as e:
            logging.error(f"❌ Error uploading to Supabase: {str(e)}")
            return False

    def _get_difficulty_number(self, level: str) -> int:
        """Convert CEFR level to difficulty number"""
        level_map = {"A1": 1, "A2": 2, "B1": 3, "B2": 4, "C1": 5, "C2": 6}
        return level_map.get(level, 1)

    def _extract_cultural_notes(self, content: Dict) -> str:
        """Extract cultural notes from content"""
        cultural_context = content.get("cultural_context", {})
        if isinstance(cultural_context, dict):
            main_points = cultural_context.get("main_points", [])
            return "; ".join(main_points) if main_points else ""
        return ""

    def _print_final_report(self, elapsed_time: float):
        """Print comprehensive generation report"""
        hours = int(elapsed_time // 3600)
        minutes = int((elapsed_time % 3600) // 60)
        seconds = int(elapsed_time % 60)

        logging.info("\n" + "="*60)
        logging.info("🎯 COMPREHENSIVE LESSON GENERATION COMPLETE")
        logging.info("="*60)
        logging.info(f"⏱️  Total Time: {hours:02d}:{minutes:02d}:{seconds:02d}")
        logging.info(f"✅ Successfully Generated: {self.generated_count} lessons")
        logging.info(f"❌ Failed: {self.failed_count} lessons")
        logging.info(f"📊 Success Rate: {(self.generated_count/self.total_lessons)*100:.1f}%")
        logging.info(f"🌍 Languages Completed: {len(self.config.MISSING_LANGUAGES)}")
        logging.info(f"📚 Total Lessons per Language: 90 (15 per level × 6 levels)")
        logging.info("="*60)

        # Language breakdown
        lessons_per_language = self.generated_count // len(self.config.MISSING_LANGUAGES)
        logging.info(f"📈 Average lessons per language: {lessons_per_language}")

        if self.failed_count > 0:
            logging.warning(f"⚠️  {self.failed_count} lessons failed - check logs for details")

        logging.info("🎉 All missing languages now have comprehensive lesson coverage!")


def main():
    """Main execution function"""
    print("🚀 NIRA Comprehensive Lesson Generator")
    print("=" * 50)

    # Initialize configuration
    config = LessonConfig()

    # Validate API keys
    if "JOJOJO" in config.GEMINI_API_KEY:
        print("❌ Please update GEMINI_API_KEY in LessonConfig")
        return

    if "..." in config.SUPABASE_KEY:
        print("❌ Please update SUPABASE_KEY in LessonConfig")
        return

    # Initialize generator
    generator = ComprehensiveLessonGenerator(config)

    # Confirm generation
    print(f"\n📊 Generation Plan:")
    print(f"   Languages: {len(config.MISSING_LANGUAGES)} ({', '.join([lang[0] for lang in config.MISSING_LANGUAGES])})")
    print(f"   Levels: {len(config.LEVELS)} ({', '.join(config.LEVELS)})")
    print(f"   Topics per level: 15")
    print(f"   Total lessons: {generator.total_lessons}")
    print(f"   Estimated time: ~{generator.total_lessons * 2 / 60:.1f} hours")

    confirm = input("\n🤔 Proceed with generation? (y/N): ").strip().lower()
    if confirm != 'y':
        print("❌ Generation cancelled")
        return

    # Start generation
    try:
        generator.generate_all_missing_lessons()
    except KeyboardInterrupt:
        print("\n⚠️ Generation interrupted by user")
        generator._print_final_report(0)
    except Exception as e:
        print(f"\n❌ Generation failed: {str(e)}")
        logging.error(f"Fatal error: {str(e)}")


if __name__ == "__main__":
    main()
