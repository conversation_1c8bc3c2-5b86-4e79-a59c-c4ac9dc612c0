#!/usr/bin/env python3
"""
NIRA Master Content Generator
Consolidates all content generation best practices into a single, production-ready script.
"""

import json
import os
import time
import logging
import requests
from datetime import datetime
from typing import Dict, List, Optional
from supabase import create_client, Client

# Configuration - SECURITY: Use environment variables in production
# TODO: Move to environment variables - SECURITY RISK if committed
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY", "YOUR_GEMINI_API_KEY_HERE")
SUPABASE_URL = os.getenv("SUPABASE_URL", "https://your-project-id.supabase.co")
SUPABASE_KEY = os.getenv("SUPABASE_ANON_KEY", "YOUR_SUPABASE_ANON_KEY_HERE")

# Initialize Supabase client
supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)

# Logging setup
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'content_generation_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)

class NIRAContentGenerator:
    """Master content generator for NIRA language learning content"""

    def __init__(self):
        self.generated_count = 0
        self.uploaded_count = 0
        self.failed_count = 0
        self.gemini_url = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key={GEMINI_API_KEY}"

    def generate_lesson_content(self, language: str, level: str, topic: str) -> Optional[Dict]:
        """Generate comprehensive lesson content using Gemini AI"""

        prompt = self._create_lesson_prompt(language, level, topic)

        try:
            # Call Gemini API
            response = self._call_gemini_api(prompt)
            content = self._parse_ai_response(response)

            # Validate content structure
            if self._validate_content(content):
                self.generated_count += 1
                logging.info(f"✅ Generated lesson: {content.get('title', 'Untitled')}")
                return content
            else:
                raise ValueError("Content validation failed")

        except Exception as e:
            self.failed_count += 1
            logging.error(f"❌ Failed to generate lesson: {str(e)}")
            return None

    def _create_lesson_prompt(self, language: str, level: str, topic: str) -> str:
        """Create AI prompt based on NIRA's proven templates"""

        # CEFR-specific vocabulary targets
        vocab_targets = {
            "A1": 8, "A2": 10, "B1": 12, "B2": 15, "C1": 18, "C2": 20
        }

        vocab_count = vocab_targets.get(level, 10)
        level_guidelines = self._get_level_guidelines(level)

        return f"""
Create a comprehensive {language} language lesson for {level} level on the topic: {topic}

REQUIREMENTS:
1. VOCABULARY: Exactly {vocab_count} words with:
   - {language} word with proper spelling/characters
   - English translation
   - Pronunciation guide (IPA when possible)
   - Part of speech
   - Example sentence in {language}
   - Example sentence translation
   - Cultural context (when relevant)

2. GRAMMAR POINTS: 2-3 key grammar concepts with:
   - Clear explanations
   - Multiple examples
   - Common mistakes to avoid
   - Practice tips

3. CULTURAL CONTEXT: Real-world scenarios including:
   - Cultural etiquette and customs
   - Do's and don'ts
   - Regional variations
   - Practical usage tips

4. DIALOGUES: 6-8 realistic conversations with:
   - Natural, authentic exchanges
   - Cultural context notes
   - English translations
   - Pronunciation guides

5. EXERCISES: Interactive practice including:
   - Multiple choice questions (5)
   - Fill-in-the-blank exercises (5)
   - Matching activities (3)
   - Pronunciation practice (3)

LEVEL-SPECIFIC GUIDELINES:
{level_guidelines}

CULTURAL AUTHENTICITY:
- Use authentic cultural scenarios
- Include proper etiquette and social norms
- Reference real places and situations
- Avoid stereotypes, focus on practical cultural knowledge

OUTPUT FORMAT: Valid JSON with this exact structure:
{{
    "title": "Engaging lesson title",
    "description": "Brief lesson description",
    "vocabulary": [
        {{
            "word": "example_word",
            "translation": "english_translation",
            "pronunciation": "/pronunciation/",
            "part_of_speech": "noun/verb/etc",
            "example": "Example sentence in {language}",
            "example_translation": "English translation of example",
            "cultural_context": "Cultural notes when relevant"
        }}
    ],
    "grammar_points": [
        {{
            "concept": "Grammar concept name",
            "explanation": "Clear explanation",
            "examples": ["example1", "example2"],
            "common_mistakes": ["mistake1", "mistake2"],
            "practice_tips": ["tip1", "tip2"]
        }}
    ],
    "cultural_context": [
        {{
            "topic": "Cultural topic",
            "description": "Description of cultural aspect",
            "dos": ["do1", "do2"],
            "donts": ["dont1", "dont2"],
            "regional_notes": "Regional variations"
        }}
    ],
    "dialogues": [
        {{
            "speakers": ["Speaker1", "Speaker2"],
            "exchanges": [
                {{
                    "speaker": "Speaker1",
                    "text": "Text in {language}",
                    "translation": "English translation",
                    "pronunciation": "/pronunciation/"
                }}
            ],
            "cultural_notes": "Cultural context for dialogue"
        }}
    ],
    "exercises": [
        {{
            "type": "multiple_choice",
            "question": "Question text",
            "options": ["option1", "option2", "option3", "option4"],
            "correct_answer": 0,
            "explanation": "Explanation of correct answer",
            "points": 10
        }}
    ]
}}
"""

    def _get_level_guidelines(self, level: str) -> str:
        """Get CEFR level-specific content guidelines"""

        guidelines = {
            "A1": """
            - Basic survival vocabulary (greetings, numbers, family, food)
            - Present tense focus
            - Simple sentence structures
            - Everyday situations (introductions, shopping, directions)
            """,
            "A2": """
            - Daily life vocabulary (activities, places, time, weather)
            - Past and future tenses introduction
            - Simple descriptions and comparisons
            - Routine activities and personal information
            """,
            "B1": """
            - Intermediate vocabulary (work, education, hobbies, travel)
            - Complex sentence structures
            - Expressing opinions and preferences
            - Problem-solving and decision-making scenarios
            """,
            "B2": """
            - Professional and academic vocabulary
            - Advanced grammar structures
            - Abstract concepts and ideas
            - Formal and informal registers
            """,
            "C1": """
            - Sophisticated vocabulary (academic, professional, cultural)
            - Complex grammatical structures
            - Nuanced expressions and idioms
            - Critical thinking and analysis topics
            """,
            "C2": """
            - Advanced literary and academic vocabulary
            - Mastery of all grammatical structures
            - Cultural and historical references
            - Professional and academic discourse
            """
        }

        return guidelines.get(level, guidelines["A1"])

    def _call_gemini_api(self, prompt: str) -> Dict:
        """Call Gemini API with the given prompt"""

        headers = {
            "Content-Type": "application/json"
        }

        data = {
            "contents": [
                {
                    "parts": [
                        {"text": prompt}
                    ]
                }
            ],
            "generationConfig": {
                "temperature": 0.8,
                "topK": 40,
                "topP": 0.95,
                "maxOutputTokens": 4096
            }
        }

        response = requests.post(self.gemini_url, headers=headers, json=data)
        response.raise_for_status()

        return response.json()

    def _parse_ai_response(self, response: Dict) -> Dict:
        """Parse AI response and extract JSON content"""

        try:
            # Extract text from Gemini response
            text = response["candidates"][0]["content"]["parts"][0]["text"]

            # Find JSON content (remove markdown formatting if present)
            if "```json" in text:
                json_start = text.find("```json") + 7
                json_end = text.find("```", json_start)
                json_text = text[json_start:json_end].strip()
            elif "{" in text and "}" in text:
                json_start = text.find("{")
                json_end = text.rfind("}") + 1
                json_text = text[json_start:json_end]
            else:
                raise ValueError("No JSON content found in response")

            # Parse JSON
            content = json.loads(json_text)
            return content

        except Exception as e:
            logging.error(f"Failed to parse AI response: {str(e)}")
            raise

    def _validate_content(self, content: Dict) -> bool:
        """Validate generated content structure"""

        required_fields = ["title", "description", "vocabulary", "grammar_points", "cultural_context", "dialogues", "exercises"]

        for field in required_fields:
            if field not in content:
                logging.error(f"Missing required field: {field}")
                return False

        # Validate vocabulary structure
        if not isinstance(content["vocabulary"], list) or len(content["vocabulary"]) == 0:
            logging.error("Invalid vocabulary structure")
            return False

        # Validate exercises structure
        if not isinstance(content["exercises"], list) or len(content["exercises"]) == 0:
            logging.error("Invalid exercises structure")
            return False

        return True

    def upload_to_supabase(self, content: Dict, language: str, level: str) -> bool:
        """Upload generated content to Supabase database"""

        try:
            # Prepare lesson data
            lesson_data = {
                "title": content["title"],
                "description": content["description"],
                "content": content,
                "language": language.lower(),
                "level": level,
                "topic": content.get("topic", "General"),
                "difficulty_level": self._get_difficulty_number(level),
                "estimated_duration_minutes": self._calculate_duration(content),
                "lesson_type": "ai_generated",
                "is_published": True,
                "is_active": True,
                "metadata": {
                    "generated_at": datetime.now().isoformat(),
                    "generator_version": "2.0",
                    "vocabulary_count": len(content.get("vocabulary", [])),
                    "exercise_count": len(content.get("exercises", []))
                }
            }

            # Upload to database
            result = supabase.table("lessons").insert(lesson_data).execute()

            if result.data:
                self.uploaded_count += 1
                logging.info(f"✅ Uploaded lesson: {content['title']}")
                return True
            else:
                raise Exception("No data returned from insert")

        except Exception as e:
            self.failed_count += 1
            logging.error(f"❌ Failed to upload lesson: {str(e)}")
            return False

    def _get_difficulty_number(self, level: str) -> int:
        """Convert CEFR level to difficulty number"""
        mapping = {"A1": 1, "A2": 2, "B1": 3, "B2": 4, "C1": 5, "C2": 6}
        return mapping.get(level, 1)

    def _calculate_duration(self, content: Dict) -> int:
        """Calculate estimated lesson duration based on content"""
        base_duration = 15
        vocab_time = len(content.get("vocabulary", [])) * 1
        exercise_time = len(content.get("exercises", [])) * 2
        dialogue_time = len(content.get("dialogues", [])) * 3

        return base_duration + vocab_time + exercise_time + dialogue_time

    def generate_batch_content(self, config: Dict) -> None:
        """Generate content in batches based on configuration"""

        languages = config.get("languages", ["French"])
        levels = config.get("levels", ["A1"])
        topics = config.get("topics", ["Basic Greetings"])

        total_lessons = len(languages) * len(levels) * len(topics)
        current_lesson = 0

        logging.info(f"🚀 Starting batch generation: {total_lessons} lessons")

        for language in languages:
            for level in levels:
                for topic in topics:
                    current_lesson += 1
                    logging.info(f"📚 Generating lesson {current_lesson}/{total_lessons}: {language} {level} - {topic}")

                    # Generate content
                    content = self.generate_lesson_content(language, level, topic)

                    if content:
                        # Upload to database
                        self.upload_to_supabase(content, language, level)

                    # Rate limiting
                    time.sleep(2)

        # Final report
        self._print_generation_report()

    def _print_generation_report(self) -> None:
        """Print final generation statistics"""

        logging.info("📊 Generation Complete!")
        logging.info(f"   Generated: {self.generated_count}")
        logging.info(f"   Uploaded: {self.uploaded_count}")
        logging.info(f"   Failed: {self.failed_count}")

        if self.failed_count > 0:
            logging.warning(f"⚠️  {self.failed_count} lessons failed. Check logs for details.")

def get_default_batch_config() -> Dict:
    """Get default batch configuration for content generation"""

    return {
        "languages": ["French", "Spanish", "Japanese", "Tamil"],
        "levels": ["A1", "A2", "B1", "B2", "C1", "C2"],
        "topics": [
            "Basic Greetings",
            "Food and Dining",
            "Travel Essentials",
            "Family and Relationships",
            "Work and Career",
            "Health and Wellness",
            "Shopping and Money",
            "Entertainment and Hobbies",
            "Technology and Communication",
            "Culture and Traditions"
        ]
    }

def fix_placeholder_content():
    """Fix all lessons with placeholder content"""
    generator = NIRAContentGenerator()

    try:
        # Get lessons with placeholder content
        result = supabase.table("lessons").select("*").execute()
        all_lessons = result.data

        placeholder_lessons = []
        for lesson in all_lessons:
            content_str = json.dumps(lesson.get("content_metadata", {}))
            if "Option A" in content_str or "placeholder" in content_str.lower():
                placeholder_lessons.append(lesson)

        logging.info(f"Found {len(placeholder_lessons)} lessons with placeholder content")

        fixed_count = 0
        failed_count = 0

        for lesson in placeholder_lessons[:50]:  # Fix first 50 for now
            try:
                # Generate new content
                new_content = generator.generate_lesson_content("French", "A1", lesson.get("title", "Language Lesson"))

                if new_content:
                    # Update lesson
                    update_data = {
                        "content_metadata": new_content,
                        "description": new_content.get("description", lesson.get("description")),
                        "learning_objectives": new_content.get("learning_objectives", []),
                        "vocabulary_focus": [vocab["word"] for vocab in new_content.get("vocabulary", [])],
                        "grammar_concepts": [gp["concept"] for gp in new_content.get("grammar_points", [])],
                        "cultural_notes": "; ".join([cc["description"] for cc in new_content.get("cultural_context", [])]),
                        "updated_at": datetime.now().isoformat()
                    }

                    supabase.table("lessons").update(update_data).eq("id", lesson["id"]).execute()
                    fixed_count += 1
                    logging.info(f"✅ Fixed lesson: {lesson.get('title', 'Unknown')} ({fixed_count}/{len(placeholder_lessons[:50])})")

            except Exception as e:
                failed_count += 1
                logging.error(f"❌ Failed to fix lesson {lesson.get('title', 'Unknown')}: {str(e)}")

            time.sleep(2)  # Rate limiting

        logging.info(f"🎉 PLACEHOLDER FIX COMPLETE! Fixed: {fixed_count}, Failed: {failed_count}")

    except Exception as e:
        logging.error(f"Error fixing placeholder content: {str(e)}")

def main():
    """Main function with usage examples"""

    print("🚀 NIRA Content Generator & Fixer")
    print("Choose an option:")
    print("1. Fix placeholder content (recommended)")
    print("2. Generate new content")
    print("3. Exit")

    choice = input("\nEnter your choice (1-3): ")

    if choice == "1":
        print("🔧 Starting placeholder content fix...")
        fix_placeholder_content()
    elif choice == "2":
        generator = NIRAContentGenerator()

        # Example: Small batch generation
        print("🎯 Generating new content...")
        small_batch_config = {
            "languages": ["French", "Spanish"],
            "levels": ["A1", "A2"],
            "topics": ["Basic Greetings", "Numbers and Time", "Food and Dining"]
        }
        generator.generate_batch_content(small_batch_config)
    elif choice == "3":
        print("Goodbye!")
    else:
        print("Invalid choice. Please try again.")
        main()

if __name__ == "__main__":
    main()