#!/usr/bin/env python3
"""
Create Learning Paths for New Languages
Creates learning paths for the 10 newly added languages
"""

import logging
from supabase import create_client

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class LearningPathCreator:
    def __init__(self):
        # Use same credentials as the lesson generator
        self.SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
        self.SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"
        
        self.supabase = create_client(self.SUPABASE_URL, self.SUPABASE_KEY)
        self.LEVELS = ["A1", "A2", "B1", "B2", "C1", "C2"]
        
        # New language codes
        self.NEW_LANGUAGE_CODES = ["kn", "ml", "bn", "mr", "pa", "nl", "sv", "th", "ru", "no"]
    
    def create_learning_paths(self):
        """Create learning paths for new languages"""
        logging.info("📚 Creating learning paths for new languages...")
        
        # Get default agent
        agent_result = self.supabase.table("agents").select("id").eq("name", "Marie").execute()
        if not agent_result.data:
            logging.error("❌ No default agent found")
            return
        
        agent_id = agent_result.data[0]["id"]
        logging.info(f"✅ Using agent ID: {agent_id}")
        
        created_count = 0
        
        for code in self.NEW_LANGUAGE_CODES:
            # Get language info
            lang_result = self.supabase.table("languages").select("id, name").eq("code", code).execute()
            if not lang_result.data:
                logging.warning(f"⚠️ Language {code} not found")
                continue
            
            language_id = lang_result.data[0]["id"]
            language_name = lang_result.data[0]["name"]
            
            logging.info(f"📖 Creating learning paths for {language_name} ({code})")
            
            for level in self.LEVELS:
                try:
                    # Check if path already exists
                    existing = self.supabase.table("learning_paths").select("id").eq("language_id", language_id).eq("level", level).execute()
                    if existing.data:
                        logging.info(f"  ⚠️ {level} path already exists, skipping...")
                        continue
                    
                    path_data = {
                        "language_id": language_id,
                        "agent_id": agent_id,
                        "level": level,
                        "name": f"{language_name} {level}",
                        "description": f"Learn {language_name} at {level} level according to CEFR standards",
                        "is_active": True,
                        "estimated_hours": self._get_level_hours(level)
                    }
                    
                    result = self.supabase.table("learning_paths").insert(path_data).execute()
                    
                    if result.data:
                        created_count += 1
                        logging.info(f"  ✅ Created {level} path - ID: {result.data[0]['id']}")
                    else:
                        logging.error(f"  ❌ Failed to create {level} path")
                        
                except Exception as e:
                    logging.error(f"  ❌ Error creating {level} path: {str(e)}")
        
        logging.info(f"🎉 Successfully created {created_count} learning paths")
        return created_count
    
    def _get_level_hours(self, level):
        """Get estimated hours for each level"""
        hours = {
            "A1": 40,
            "A2": 60,
            "B1": 80,
            "B2": 100,
            "C1": 120,
            "C2": 150
        }
        return hours.get(level, 60)

def main():
    """Main execution function"""
    print("📚 NIRA Learning Path Creator")
    print("=" * 40)
    
    try:
        creator = LearningPathCreator()
        created_count = creator.create_learning_paths()
        
        if created_count > 0:
            print(f"🎉 Successfully created {created_count} learning paths!")
        else:
            print("⚠️ No new learning paths were created")
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        logging.error(f"Fatal error: {str(e)}")

if __name__ == "__main__":
    main()
