#!/usr/bin/env python3
"""
ElevenLabs Audio Generator for NIRA
Generates Tamil audio for lesson content using ElevenLabs TTS API
"""

import json
import logging
import requests
import os
import time
from datetime import datetime
from dotenv import load_dotenv
from typing import Dict, List, Optional, Any

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class ElevenLabsAudioGenerator:
    """Generate Tamil audio using ElevenLabs TTS API"""
    
    def __init__(self):
        self.elevenlabs_api_key = os.getenv('ELEVENLABS_API_KEY')
        self.base_url = "https://api.elevenlabs.io/v1"
        self.audio_output_dir = "/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Apps/NIRA/Assets/Audio"
        
        if not self.elevenlabs_api_key:
            raise ValueError("ElevenLabs API key not found. Check your .env file.")
        
        # Create audio directory if it doesn't exist
        os.makedirs(self.audio_output_dir, exist_ok=True)
        
        # Audio generation statistics
        self.audio_stats = {
            "generated": 0,
            "failed": 0,
            "total_characters": 0,
            "total_files": 0
        }
        
        # Get available voices
        self.tamil_voice_id = self._get_tamil_voice()
        
        logging.info("🎵 ElevenLabs Audio Generator initialized")
    
    def generate_lesson_audio(self, lesson_data: Dict, lesson_id: str) -> Dict:
        """
        Generate audio for all components of a lesson:
        1. Vocabulary pronunciations
        2. Conversation dialogues
        3. Exercise questions
        """
        
        logging.info(f"🎵 Generating audio for lesson: {lesson_data.get('title', 'Untitled')}")
        
        audio_files = {
            "vocabulary": [],
            "conversations": [],
            "exercises": [],
            "generation_metadata": {
                "generated_at": datetime.now().isoformat(),
                "lesson_id": lesson_id,
                "voice_id": self.tamil_voice_id,
                "generator_version": "elevenlabs_v1.0"
            }
        }
        
        try:
            # Generate vocabulary audio
            vocab_audio = self._generate_vocabulary_audio(
                lesson_data.get('vocabulary', []), 
                lesson_id
            )
            audio_files["vocabulary"] = vocab_audio
            
            # Generate conversation audio
            conv_audio = self._generate_conversation_audio(
                lesson_data.get('conversations', []), 
                lesson_id
            )
            audio_files["conversations"] = conv_audio
            
            # Generate exercise audio
            exercise_audio = self._generate_exercise_audio(
                lesson_data.get('exercises', []), 
                lesson_id
            )
            audio_files["exercises"] = exercise_audio
            
            logging.info(f"✅ Audio generation complete for lesson {lesson_id}")
            return audio_files
            
        except Exception as e:
            logging.error(f"❌ Audio generation failed: {str(e)}")
            return audio_files
    
    def _generate_vocabulary_audio(self, vocabulary: List[Dict], lesson_id: str) -> List[Dict]:
        """Generate audio for vocabulary items"""
        
        vocab_audio = []
        
        for i, vocab_item in enumerate(vocabulary):
            word = vocab_item.get('word', '')
            example = vocab_item.get('example', '')
            
            if word:
                # Generate audio for the word
                word_audio_file = self._generate_single_audio(
                    text=word,
                    filename=f"lesson_{lesson_id}_vocab_{i+1}_word.mp3",
                    voice_settings={
                        "stability": 0.8,
                        "similarity_boost": 0.9,
                        "style": 0.2
                    }
                )
                
                # Generate audio for the example sentence
                example_audio_file = None
                if example:
                    example_audio_file = self._generate_single_audio(
                        text=example,
                        filename=f"lesson_{lesson_id}_vocab_{i+1}_example.mp3",
                        voice_settings={
                            "stability": 0.7,
                            "similarity_boost": 0.8,
                            "style": 0.1
                        }
                    )
                
                vocab_audio.append({
                    "vocab_index": i + 1,
                    "word": word,
                    "word_audio_url": word_audio_file,
                    "example": example,
                    "example_audio_url": example_audio_file
                })
                
                # Small delay to avoid rate limiting
                time.sleep(0.5)
        
        logging.info(f"✅ Generated audio for {len(vocab_audio)} vocabulary items")
        return vocab_audio
    
    def _generate_conversation_audio(self, conversations: List[Dict], lesson_id: str) -> List[Dict]:
        """Generate audio for conversation dialogues"""
        
        conv_audio = []
        
        for conv_idx, conversation in enumerate(conversations):
            conv_id = conversation.get('id', conv_idx + 1)
            exchanges = conversation.get('exchanges', [])
            
            exchange_audio = []
            for ex_idx, exchange in enumerate(exchanges):
                text = exchange.get('text', '')
                
                if text:
                    # Clean text for TTS (remove pronunciation guides in parentheses)
                    clean_text = self._clean_text_for_tts(text)
                    
                    audio_file = self._generate_single_audio(
                        text=clean_text,
                        filename=f"lesson_{lesson_id}_conv_{conv_id}_ex_{ex_idx+1}.mp3",
                        voice_settings={
                            "stability": 0.7,
                            "similarity_boost": 0.8,
                            "style": 0.3
                        }
                    )
                    
                    exchange_audio.append({
                        "exchange_index": ex_idx + 1,
                        "speaker": exchange.get('speaker', 'Speaker'),
                        "text": clean_text,
                        "audio_url": audio_file
                    })
                    
                    time.sleep(0.3)
            
            conv_audio.append({
                "conversation_id": conv_id,
                "title": conversation.get('title', ''),
                "exchanges": exchange_audio
            })
        
        logging.info(f"✅ Generated audio for {len(conv_audio)} conversations")
        return conv_audio
    
    def _generate_exercise_audio(self, exercises: List[Dict], lesson_id: str) -> List[Dict]:
        """Generate audio for exercise questions"""
        
        exercise_audio = []
        
        for exercise in exercises:
            exercise_id = exercise.get('id', 0)
            question_tamil = exercise.get('question_in_Tamil', '')
            
            if question_tamil:
                audio_file = self._generate_single_audio(
                    text=question_tamil,
                    filename=f"lesson_{lesson_id}_exercise_{exercise_id}_question.mp3",
                    voice_settings={
                        "stability": 0.9,
                        "similarity_boost": 0.9,
                        "style": 0.1
                    }
                )
                
                exercise_audio.append({
                    "exercise_id": exercise_id,
                    "question": question_tamil,
                    "audio_url": audio_file
                })
                
                time.sleep(0.3)
        
        logging.info(f"✅ Generated audio for {len(exercise_audio)} exercises")
        return exercise_audio
    
    def _generate_single_audio(self, text: str, filename: str, voice_settings: Dict) -> Optional[str]:
        """Generate a single audio file using ElevenLabs TTS"""
        
        try:
            url = f"{self.base_url}/text-to-speech/{self.tamil_voice_id}"
            
            headers = {
                "Accept": "audio/mpeg",
                "Content-Type": "application/json",
                "xi-api-key": self.elevenlabs_api_key
            }
            
            data = {
                "text": text,
                "model_id": "eleven_multilingual_v2",
                "voice_settings": voice_settings
            }
            
            response = requests.post(url, json=data, headers=headers)
            response.raise_for_status()
            
            # Save audio file
            file_path = os.path.join(self.audio_output_dir, filename)
            with open(file_path, 'wb') as f:
                f.write(response.content)
            
            # Update statistics
            self.audio_stats["generated"] += 1
            self.audio_stats["total_characters"] += len(text)
            self.audio_stats["total_files"] += 1
            
            logging.info(f"🎵 Generated: {filename}")
            return file_path
            
        except Exception as e:
            logging.error(f"❌ Failed to generate audio for '{text}': {str(e)}")
            self.audio_stats["failed"] += 1
            return None
    
    def _get_tamil_voice(self) -> str:
        """Get the best Tamil voice from ElevenLabs"""
        
        try:
            url = f"{self.base_url}/voices"
            headers = {"xi-api-key": self.elevenlabs_api_key}
            
            response = requests.get(url, headers=headers)
            response.raise_for_status()
            
            voices = response.json().get('voices', [])
            
            # Look for Tamil or multilingual voices
            for voice in voices:
                name = voice.get('name', '').lower()
                if 'tamil' in name or 'multilingual' in name:
                    logging.info(f"🎵 Using voice: {voice.get('name')} ({voice.get('voice_id')})")
                    return voice.get('voice_id')
            
            # Fallback to first available voice
            if voices:
                fallback_voice = voices[0]
                logging.warning(f"⚠️ No Tamil voice found, using: {fallback_voice.get('name')}")
                return fallback_voice.get('voice_id')
            
            raise ValueError("No voices available")
            
        except Exception as e:
            logging.error(f"❌ Failed to get Tamil voice: {str(e)}")
            # Fallback to a known voice ID
            return "21m00Tcm4TlvDq8ikWAM"  # Default voice
    
    def _clean_text_for_tts(self, text: str) -> str:
        """Clean text for better TTS pronunciation"""
        
        # Remove pronunciation guides in parentheses
        import re
        cleaned = re.sub(r'\([^)]*\)', '', text)
        
        # Remove extra whitespace
        cleaned = ' '.join(cleaned.split())
        
        return cleaned.strip()
    
    def get_audio_statistics(self) -> Dict:
        """Get audio generation statistics"""
        return {
            **self.audio_stats,
            "success_rate": f"{(self.audio_stats['generated'] / max(1, self.audio_stats['generated'] + self.audio_stats['failed'])) * 100:.1f}%"
        }

# Test function
if __name__ == "__main__":
    # Load the test lesson we generated
    try:
        with open('test_lesson_output.json', 'r', encoding='utf-8') as f:
            test_lesson = json.load(f)
        
        audio_generator = ElevenLabsAudioGenerator()
        
        # Generate audio for the lesson
        audio_files = audio_generator.generate_lesson_audio(test_lesson, "tamil_a1_lesson_1")
        
        print("🎵 Audio Generation Results:")
        print("=" * 50)
        print(f"Vocabulary audio files: {len(audio_files['vocabulary'])}")
        print(f"Conversation audio files: {len(audio_files['conversations'])}")
        print(f"Exercise audio files: {len(audio_files['exercises'])}")
        
        # Save audio file references
        with open('lesson_audio_files.json', 'w', encoding='utf-8') as f:
            json.dump(audio_files, f, ensure_ascii=False, indent=2)
        print("\n📁 Audio file references saved to lesson_audio_files.json")
        
        print(f"\n📊 Statistics: {audio_generator.get_audio_statistics()}")
        
    except FileNotFoundError:
        print("❌ test_lesson_output.json not found. Run complete_lesson_generator.py first.")
    except Exception as e:
        print(f"❌ Audio generation test failed: {str(e)}")
