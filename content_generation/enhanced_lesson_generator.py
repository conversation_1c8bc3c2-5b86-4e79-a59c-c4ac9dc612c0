#!/usr/bin/env python3
"""
Enhanced Lesson Generator for NIRA
Generates complete lessons with vocabulary, conversations, grammar, and exercises
"""

import json
import logging
import requests
import time
from datetime import datetime
from typing import Dict, List, Optional, Any
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

class EnhancedLessonGenerator:
    """Generate complete NIRA lessons with all 4 components"""
    
    def __init__(self):
        self.gemini_api_key = os.getenv('GEMINI_API_KEY')
        self.openai_api_key = os.getenv('OPENAI_API_KEY')
        self.supabase_url = os.getenv('SUPABASE_URL')
        self.supabase_key = os.getenv('SUPABASE_ANON_KEY')
        
        if not all([self.gemini_api_key, self.openai_api_key, self.supabase_url, self.supabase_key]):
            raise ValueError("Missing required API keys. Check your .env file.")
        
        self.gemini_url = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key={self.gemini_api_key}"
        
        # Statistics
        self.generated_count = 0
        self.validated_count = 0
        self.failed_count = 0
        
        logging.info("🚀 Enhanced Lesson Generator initialized")
    
    def generate_complete_lesson(self, language: str, level: str, topic: str, lesson_number: int) -> Optional[Dict]:
        """
        Generate a complete lesson with all 4 components:
        1. Vocabulary Practice (15 items)
        2. Guided Conversations (10 conversations)
        3. Grammar Essentials (5 points)
        4. Practice Exercises (10 exercises)
        """
        
        logging.info(f"📚 Generating {language} {level} lesson {lesson_number}: {topic}")
        
        try:
            # Step 1: Generate with Gemini Flash 2.0
            lesson_content = self._generate_with_gemini(language, level, topic, lesson_number)
            if not lesson_content:
                raise ValueError("Gemini generation failed")
            
            # Step 2: Validate structure
            if not self._validate_lesson_structure(lesson_content):
                raise ValueError("Lesson structure validation failed")
            
            self.generated_count += 1
            logging.info(f"✅ Generated lesson: {lesson_content.get('title', 'Untitled')}")
            
            return lesson_content
            
        except Exception as e:
            self.failed_count += 1
            logging.error(f"❌ Failed to generate lesson: {str(e)}")
            return None
    
    def _generate_with_gemini(self, language: str, level: str, topic: str, lesson_number: int) -> Optional[Dict]:
        """Generate lesson content using Gemini Flash 2.0"""
        
        prompt = self._create_lesson_prompt(language, level, topic, lesson_number)
        
        try:
            headers = {"Content-Type": "application/json"}
            data = {
                "contents": [{"parts": [{"text": prompt}]}],
                "generationConfig": {
                    "temperature": 0.7,
                    "topK": 40,
                    "topP": 0.95,
                    "maxOutputTokens": 4096
                }
            }
            
            response = requests.post(self.gemini_url, headers=headers, json=data)
            response.raise_for_status()
            
            result = response.json()
            generated_text = result['candidates'][0]['content']['parts'][0]['text']
            
            # Parse JSON from response
            if "```json" in generated_text:
                json_text = generated_text.split("```json")[1].split("```")[0].strip()
            elif "```" in generated_text:
                json_text = generated_text.split("```")[1].strip()
            else:
                json_text = generated_text.strip()
            
            lesson_content = json.loads(json_text)
            
            # Add metadata
            lesson_content["generation_metadata"] = {
                "generated_at": datetime.now().isoformat(),
                "generator_version": "enhanced_v1.0",
                "language": language,
                "level": level,
                "topic": topic,
                "lesson_number": lesson_number
            }
            
            return lesson_content
            
        except Exception as e:
            logging.error(f"Gemini API error: {str(e)}")
            return None
    
    def _create_lesson_prompt(self, language: str, level: str, topic: str, lesson_number: int) -> str:
        """Create comprehensive prompt for lesson generation"""

        return f"""
Generate a complete {language} language lesson for {level} level learners.

LESSON DETAILS:
- Language: {language}
- Level: {level} (CEFR)
- Topic: {topic}
- Lesson Number: {lesson_number}

CRITICAL: Return ONLY valid JSON. No explanations, no markdown, just pure JSON.

REQUIRED STRUCTURE:
{{
    "title": "Engaging lesson title in English",
    "description": "Brief lesson description",
    "level": "{level}",
    "topic": "{topic}",
    "estimated_duration_minutes": 25,
    
    "vocabulary": [
        {{
            "word": "word_in_{language}",
            "translation": "English translation",
            "pronunciation": "/phonetic/",
            "part_of_speech": "noun",
            "example": "Example in {language}",
            "example_translation": "English translation"
        }}
    ],
    
    "conversations": [
        // EXACTLY 10 guided conversations
        {{
            "id": 1,
            "title": "Conversation title",
            "scenario": "Real-world scenario description",
            "participants": ["User", "Native Speaker"],
            "exchanges": [
                {{
                    "speaker": "Native Speaker",
                    "text": "Text in {language}",
                    "translation": "English translation",
                    "cultural_notes": "Cultural context if needed"
                }},
                {{
                    "speaker": "User",
                    "text": "Expected user response in {language}",
                    "translation": "English translation",
                    "alternative_responses": ["Alternative 1", "Alternative 2"]
                }}
            ]
        }}
        // ... 9 more conversations
    ],
    
    "grammar": [
        // EXACTLY 5 grammar essentials
        {{
            "rule": "Grammar rule name",
            "explanation": "Clear explanation of the rule",
            "examples": ["Example 1 in {language}", "Example 2 in {language}"],
            "example_translations": ["Translation 1", "Translation 2"],
            "tips": "Helpful learning tips",
            "common_mistakes": "Common errors to avoid"
        }}
        // ... 4 more grammar points
    ],
    
    "exercises": [
        // EXACTLY 10 practice exercises
        {{
            "id": 1,
            "type": "multiple_choice",
            "question": "Question text",
            "question_in_{language}": "Question in target language if applicable",
            "options": ["Option 1", "Option 2", "Option 3", "Option 4"],
            "correct_answer": 0,
            "explanation": "Why this answer is correct",
            "points": 10,
            "difficulty": "easy/medium/hard",
            "audio_needed": true
        }}
        // ... 9 more exercises with varied types: multiple_choice, fill_blank, translation, listening, speaking
    ]
}}

IMPORTANT REQUIREMENTS:
1. All content must be culturally appropriate and authentic
2. Use real-world scenarios relevant to {language} speakers
3. Include proper pronunciation guides
4. Ensure progressive difficulty within the lesson
5. Make content engaging and practical
6. Include cultural context where relevant
7. Ensure all arrays have EXACTLY the specified number of items

Generate authentic, high-quality content that helps learners practically use {language} in real situations.
"""
    
    def _validate_lesson_structure(self, lesson: Dict) -> bool:
        """Validate that lesson has all required components with correct counts"""
        
        required_fields = ['title', 'description', 'vocabulary', 'conversations', 'grammar', 'exercises']
        
        # Check required fields exist
        for field in required_fields:
            if field not in lesson:
                logging.error(f"Missing required field: {field}")
                return False
        
        # Check component counts
        if len(lesson['vocabulary']) != 15:
            logging.error(f"Vocabulary count: {len(lesson['vocabulary'])}, expected: 15")
            return False
            
        if len(lesson['conversations']) != 10:
            logging.error(f"Conversations count: {len(lesson['conversations'])}, expected: 10")
            return False
            
        if len(lesson['grammar']) != 5:
            logging.error(f"Grammar count: {len(lesson['grammar'])}, expected: 5")
            return False
            
        if len(lesson['exercises']) != 10:
            logging.error(f"Exercises count: {len(lesson['exercises'])}, expected: 10")
            return False
        
        logging.info("✅ Lesson structure validation passed")
        return True
    
    def get_statistics(self) -> Dict:
        """Get generation statistics"""
        return {
            "generated": self.generated_count,
            "validated": self.validated_count,
            "failed": self.failed_count,
            "success_rate": f"{(self.generated_count / max(1, self.generated_count + self.failed_count)) * 100:.1f}%"
        }

# Test function
if __name__ == "__main__":
    generator = EnhancedLessonGenerator()
    
    # Test with Tamil A1 lesson
    test_lesson = generator.generate_complete_lesson(
        language="Tamil",
        level="A1", 
        topic="Basic Greetings and Introductions",
        lesson_number=1
    )
    
    if test_lesson:
        print("✅ Test lesson generated successfully!")
        print(f"Title: {test_lesson['title']}")
        print(f"Vocabulary items: {len(test_lesson['vocabulary'])}")
        print(f"Conversations: {len(test_lesson['conversations'])}")
        print(f"Grammar points: {len(test_lesson['grammar'])}")
        print(f"Exercises: {len(test_lesson['exercises'])}")
    else:
        print("❌ Test lesson generation failed")
