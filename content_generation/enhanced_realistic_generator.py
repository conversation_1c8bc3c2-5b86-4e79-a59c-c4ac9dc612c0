#!/usr/bin/env python3
"""
Enhanced Realistic Simulation Generator
Creates human-like conversations with full database integration
"""

import logging
import json
import uuid
from datetime import datetime
from simulation_generator import supabase
# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

class EnhancedRealisticGenerator:
    """Generate realistic simulations with full database integration"""

    def __init__(self):
        self.generated_count = 0
        self.uploaded_count = 0
        self.failed_count = 0

    def generate_enhanced_simulation(self, language: str, persona_key: str, scenario: str, cefr_level: str):
        """Generate enhanced realistic simulation"""

        logging.info(f"🎭 Generating enhanced simulation: {language} - {persona_key} - {scenario} - {cefr_level}")

        prompt = f"""Create a realistic, human-like conversation simulation for {language} language learning.

CONTEXT:
- Language: {language}
- Persona: {persona_key}
- Scenario: {scenario}
- CEFR Level: {cefr_level}
- Purpose: Real-life practical application with ecosystem integration

REQUIREMENTS:
1. HUMAN-LIKE CONVERSATIONS - Natural flow with interruptions, hesitations
2. REAL-LIFE PRACTICAL - Users can apply immediately in actual situations
3. CULTURAL AUTHENTICITY - Genuine cultural context and etiquette
4. ECOSYSTEM INTEGRATION - Connect to lessons, AI agents, audio
5. PROGRESSIVE LEARNING - Appropriate for {cefr_level} level

OUTPUT JSON FORMAT:
{{
    "title": "Engaging {language} title with English subtitle",
    "description": "Detailed real-life scenario description",
    "cefr_level": "{cefr_level}",
    "estimated_duration": 25,
    "scenario_type": "daily_life|shopping|work|health|education|social|transportation|cultural|technology|problem_solving",
    "real_life_context": {{
        "situation": "Specific real-life situation description",
        "location": "Exact location (e.g., 'Mercadona supermarket in Valencia')",
        "participants": ["User", "Native speaker 1", "Native speaker 2"],
        "cultural_context": "Important cultural background and etiquette",
        "time_context": "When this typically happens (morning, evening, etc.)",
        "difficulty_factors": ["What makes this challenging for learners"]
    }},
    "learning_objectives": [
        "Specific practical skill 1",
        "Specific practical skill 2",
        "Specific practical skill 3"
    ],
    "prerequisite_lessons": [
        "{cefr_level}.1: Relevant lesson topic",
        "{cefr_level}.2: Another relevant topic"
    ],
    "vocabulary_focus": [
        {{
            "word": "{language} word",
            "translation": "English translation",
            "pronunciation": "IPA or phonetic guide",
            "context": "When and how to use",
            "formality": "formal|informal|neutral",
            "example_sentence": "Natural example in context",
            "cultural_notes": "Cultural significance if any"
        }}
    ],
    "realistic_dialogue": [
        {{
            "speaker": "Native Speaker Name",
            "text": "Natural {language} dialogue",
            "translation": "English translation",
            "audio_notes": "Tone, pace, accent details",
            "cultural_notes": "Cultural context for this response",
            "difficulty_level": "{cefr_level}",
            "response_triggers": ["What user responses this leads to"]
        }}
    ],
    "conversation_branches": [
        {{
            "trigger": "User action or response",
            "dialogue_path": "Where conversation goes",
            "difficulty": "{cefr_level}",
            "cultural_importance": "High|Medium|Low",
            "ai_agent_role": "How AI helps in this branch",
            "real_life_frequency": "How often this happens in real life"
        }}
    ],
    "ai_agent_integration": {{
        "agent_role": "Specific role (e.g., 'Experienced {language} tutor from Madrid')",
        "intervention_points": ["When AI should help"],
        "encouragement_style": "How AI encourages user",
        "correction_approach": "How AI corrects mistakes",
        "cultural_coaching": "How AI provides cultural guidance"
    }},
    "audio_integration": {{
        "voice_characteristics": "Native speaker details",
        "background_sounds": "Realistic environment sounds",
        "speech_patterns": "Natural {language} speech characteristics",
        "pronunciation_focus": ["Key pronunciation challenges"],
        "accent_type": "Regional accent (e.g., 'Madrid Spanish', 'Parisian French')"
    }},
    "cultural_deep_dive": {{
        "etiquette_rules": ["Specific cultural rules"],
        "common_mistakes": ["What learners often get wrong"],
        "regional_variations": ["How this differs by region"],
        "body_language": ["Important non-verbal communication"],
        "social_context": ["Social dynamics in this situation"]
    }},
    "success_criteria": {{
        "conversation_completion": "What constitutes successful completion",
        "cultural_appropriateness": "Cultural behavior expectations",
        "vocabulary_usage": "Vocabulary mastery requirements",
        "pronunciation_accuracy": "Pronunciation standards",
        "real_life_readiness": "Readiness for actual situation"
    }},
    "follow_up_lessons": [
        "Next level lesson suggestions",
        "Related advanced topics"
    ],
    "practice_suggestions": [
        "How to practice with AI agent",
        "Voice recognition exercises",
        "Real-life application ideas"
    ],
    "gamification_elements": {{
        "achievements": ["Specific achievements for this simulation"],
        "points_system": "How points are awarded",
        "challenges": ["Optional challenges for advanced users"],
        "social_features": ["How users can interact with others"]
    }},
    "real_life_readiness_score": 0.85
}}

Make this feel like a REAL conversation between REAL people in a REAL {language}-speaking environment. Include natural human elements like hesitations, cultural references, and authentic interactions."""

        try:
            # Use existing Gemini service
            from gemini_service import GeminiService
            gemini_service = GeminiService()

            response = gemini_service.make_gemini_request(prompt)

            # Parse JSON response
            if "```json" in response:
                json_str = response.split("```json")[1].split("```")[0].strip()
            elif "```" in response:
                json_str = response.split("```")[1].strip()
            else:
                json_str = response.strip()

            simulation = json.loads(json_str)

            # Validate and enhance
            simulation = self._validate_enhanced_simulation(simulation, language, persona_key, cefr_level)

            logging.info(f"✅ Enhanced simulation generated: {simulation['title']}")
            return simulation

        except Exception as e:
            logging.error(f"❌ Enhanced simulation generation failed: {str(e)}")
            return None

    def _validate_enhanced_simulation(self, simulation: dict, language: str, persona_key: str, cefr_level: str) -> dict:
        """Validate and enhance simulation data"""

        # Ensure required fields
        if not simulation.get("title"):
            simulation["title"] = f"Real-life {language} Conversation Practice"

        if not simulation.get("cefr_level"):
            simulation["cefr_level"] = cefr_level

        if not simulation.get("real_life_readiness_score"):
            simulation["real_life_readiness_score"] = 0.75

        # Ensure all enhanced fields exist
        required_fields = [
            "real_life_context", "realistic_dialogue", "conversation_branches",
            "ai_agent_integration", "audio_integration", "cultural_deep_dive",
            "prerequisite_lessons", "follow_up_lessons", "practice_suggestions",
            "gamification_elements"
        ]

        for field in required_fields:
            if not simulation.get(field):
                simulation[field] = self._get_default_field_value(field, language, cefr_level)

        return simulation

    def _get_default_field_value(self, field: str, language: str, cefr_level: str):
        """Get default values for missing fields"""

        defaults = {
            "real_life_context": {
                "situation": f"Practical {language} conversation",
                "location": f"{language}-speaking environment",
                "participants": ["User", "Native speaker"],
                "cultural_context": f"Authentic {language} cultural setting"
            },
            "realistic_dialogue": [],
            "conversation_branches": [],
            "ai_agent_integration": {
                "agent_role": f"Helpful {language} conversation partner",
                "intervention_points": ["When user needs help"],
                "encouragement_style": "Supportive and culturally aware",
                "correction_approach": "Gentle with explanation"
            },
            "audio_integration": {
                "voice_characteristics": f"Native {language} speaker",
                "background_sounds": "Realistic environment",
                "speech_patterns": f"Natural {language} rhythm",
                "pronunciation_focus": ["Key vocabulary"]
            },
            "cultural_deep_dive": {
                "etiquette_rules": [f"Basic {language} etiquette"],
                "common_mistakes": ["Common learner errors"],
                "regional_variations": [f"{language} regional differences"],
                "body_language": ["Important gestures"]
            },
            "prerequisite_lessons": [f"{cefr_level}: Basic vocabulary"],
            "follow_up_lessons": ["Advanced scenarios"],
            "practice_suggestions": ["Practice with AI agent", "Voice recognition exercises"],
            "gamification_elements": {
                "achievements": ["Complete conversation"],
                "points_system": "Based on natural flow",
                "challenges": ["Advanced scenarios"]
            }
        }

        return defaults.get(field, {})

    def upload_enhanced_simulation(self, simulation: dict, language: str, persona_key: str) -> bool:
        """Upload enhanced simulation to Supabase with all new fields"""

        try:
            # Get persona ID
            persona_result = supabase.table("simulation_personas").select("id").eq("name", persona_key).execute()
            if not persona_result.data:
                logging.error(f"❌ Persona not found: {persona_key}")
                return False
            persona_id = persona_result.data[0]["id"]

            # Get language ID
            language_result = supabase.table("languages").select("id").eq("name", language).execute()
            if not language_result.data:
                logging.error(f"❌ Language not found: {language}")
                return False
            language_id = language_result.data[0]["id"]

            # Prepare enhanced simulation data
            simulation_data = {
                "id": str(uuid.uuid4()),
                "persona_id": persona_id,
                "language_id": language_id,
                "title": simulation["title"],
                "description": simulation["description"],
                "difficulty_level": simulation["cefr_level"],
                "cefr_level": simulation["cefr_level"],
                "estimated_duration": simulation.get("estimated_duration", 25),
                "scenario_type": simulation.get("scenario_type", "daily_life"),
                "learning_objectives": simulation.get("learning_objectives", []),
                "vocabulary_focus": simulation.get("vocabulary_focus", []),
                "conversation_starters": simulation.get("realistic_dialogue", [])[:3],  # First 3 for compatibility
                "success_criteria": simulation.get("success_criteria", {}),
                "cultural_notes": simulation.get("cultural_deep_dive", {}).get("etiquette_rules", ["Cultural awareness"]),

                # Enhanced fields
                "real_life_context": simulation.get("real_life_context", {}),
                "realistic_dialogue": simulation.get("realistic_dialogue", []),
                "conversation_branches": simulation.get("conversation_branches", []),
                "ai_agent_integration": simulation.get("ai_agent_integration", {}),
                "audio_integration": simulation.get("audio_integration", {}),
                "cultural_deep_dive": simulation.get("cultural_deep_dive", {}),
                "prerequisite_lessons": simulation.get("prerequisite_lessons", []),
                "follow_up_lessons": simulation.get("follow_up_lessons", []),
                "practice_suggestions": simulation.get("practice_suggestions", []),
                "gamification_elements": simulation.get("gamification_elements", {}),
                "real_life_readiness_score": simulation.get("real_life_readiness_score", 0.75),

                "is_active": True,
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat()
            }

            # Upload to Supabase
            result = supabase.table("simulations").insert(simulation_data).execute()

            if result.data:
                simulation_id = result.data[0]["id"]

                # Create lesson links
                self._create_lesson_links(simulation_id, simulation.get("prerequisite_lessons", []), "prerequisite")
                self._create_lesson_links(simulation_id, simulation.get("follow_up_lessons", []), "follow_up")

                logging.info(f"✅ Enhanced simulation uploaded: {simulation_id}")
                return True
            else:
                logging.error("❌ Supabase upload failed")
                return False

        except Exception as e:
            logging.error(f"❌ Upload error: {str(e)}")
            return False

    def _create_lesson_links(self, simulation_id: str, lessons: list, relationship_type: str):
        """Create lesson-simulation links"""

        for lesson in lessons:
            try:
                link_data = {
                    "lesson_id": lesson,
                    "simulation_id": simulation_id,
                    "relationship_type": relationship_type,
                    "importance_level": 1 if relationship_type == "prerequisite" else 2
                }

                supabase.table("lesson_simulation_links").insert(link_data).execute()
                logging.info(f"✅ Created {relationship_type} link: {lesson} -> {simulation_id}")

            except Exception as e:
                logging.error(f"❌ Failed to create lesson link: {str(e)}")

def test_enhanced_generation():
    """Test enhanced realistic simulation generation"""

    print("🎭 TESTING ENHANCED REALISTIC SIMULATION")
    print("=" * 60)
    print("🎯 Human-like conversations with full integration")
    print("🎯 Database schema with lesson links and progress tracking")
    print("🎯 Real-life application ready")
    print()

    generator = EnhancedRealisticGenerator()

    # Test enhanced simulation
    simulation = generator.generate_enhanced_simulation(
        language="Spanish",
        persona_key="traveler",
        scenario="Ordering dinner at a traditional Spanish restaurant",
        cefr_level="A2"
    )

    if simulation:
        print("✅ ENHANCED SIMULATION GENERATED!")
        print(f"📚 Title: {simulation['title']}")
        print(f"🎯 CEFR Level: {simulation['cefr_level']}")
        print(f"📍 Location: {simulation.get('real_life_context', {}).get('location', 'N/A')}")
        print(f"🎭 Dialogue Exchanges: {len(simulation.get('realistic_dialogue', []))}")
        print(f"🔀 Conversation Branches: {len(simulation.get('conversation_branches', []))}")
        print(f"🤖 AI Integration: ✅")
        print(f"🔊 Audio Ready: ✅")
        print(f"📖 Prerequisites: {len(simulation.get('prerequisite_lessons', []))}")
        print(f"📈 Follow-ups: {len(simulation.get('follow_up_lessons', []))}")
        print(f"🏆 Achievements: {len(simulation.get('gamification_elements', {}).get('achievements', []))}")
        print(f"🎯 Readiness Score: {simulation.get('real_life_readiness_score', 0)}")

        # Upload to database
        success = generator.upload_enhanced_simulation(simulation, "Spanish", "traveler")
        if success:
            print(f"\n✅ Successfully uploaded to database with full integration!")
        else:
            print(f"\n❌ Failed to upload to database")

        return True
    else:
        print("❌ Failed to generate enhanced simulation")
        return False

if __name__ == "__main__":
    test_enhanced_generation()
