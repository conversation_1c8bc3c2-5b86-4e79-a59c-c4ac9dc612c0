#!/usr/bin/env python3
"""
Gemini-Only Simulation Generator
Uses only Gemini (free/very cheap) - NO OpenAI costs
"""

import logging
import time
import json
import uuid
from datetime import datetime
from simulation_generator import supabase

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

class GeminiOnlyGenerator:
    """Generator using only Gemini - zero OpenAI costs"""
    
    def __init__(self):
        self.generated_count = 0
        self.uploaded_count = 0
        self.failed_count = 0
    
    def generate_gemini_only(self, language: str, persona_key: str, scenario: str):
        """Generate simulation using only Gemini"""
        
        logging.info(f"🤖 Gemini-only generation: {language} - {persona_key} - {scenario}")
        
        # Enhanced Gemini prompt for better quality
        prompt = f"""Create a language learning conversation simulation for {language}.

REQUIREMENTS:
- Persona: {persona_key}
- Scenario: {scenario}
- Language: {language}
- Include cultural context and etiquette
- Make it educational and engaging

OUTPUT FORMAT (JSON):
{{
    "title": "Engaging title in {language} with English subtitle",
    "description": "Brief description of the conversation scenario",
    "difficulty_level": "A1|A2|B1|B2|C1|C2",
    "estimated_duration": 15,
    "scenario_type": "daily_life|shopping|work|health|education|social|transportation|cultural|technology|problem_solving",
    "learning_objectives": ["objective1", "objective2", "objective3"],
    "vocabulary_focus": [
        {{"word": "{language} word", "translation": "English translation", "pronunciation": "phonetic", "context": "usage context"}},
        {{"word": "{language} word 2", "translation": "English translation 2", "pronunciation": "phonetic", "context": "usage context"}}
    ],
    "conversation_starters": [
        "Opening phrase in {language}",
        "Follow-up question in {language}",
        "Response option in {language}"
    ],
    "cultural_notes": "Important cultural context and etiquette for this scenario in {language}-speaking regions",
    "success_criteria": {{
        "vocabulary_mastery": 0.3,
        "cultural_appropriateness": 0.3,
        "conversation_flow": 0.4
    }}
}}

Make it authentic, educational, and culturally appropriate for {language} speakers."""

        try:
            # Import here to avoid issues if not available
            from gemini_service import GeminiService
            gemini_service = GeminiService()
            
            response = gemini_service.make_gemini_request(prompt)
            
            # Parse JSON response
            if "```json" in response:
                json_str = response.split("```json")[1].split("```")[0].strip()
            elif "```" in response:
                json_str = response.split("```")[1].strip()
            else:
                json_str = response.strip()
            
            simulation = json.loads(json_str)
            
            # Validate and enhance
            simulation = self._validate_simulation(simulation, language, persona_key)
            
            logging.info(f"✅ Gemini generation successful: {simulation['title']}")
            return simulation
            
        except Exception as e:
            logging.error(f"❌ Gemini generation failed: {str(e)}")
            return None
    
    def _validate_simulation(self, simulation: dict, language: str, persona_key: str) -> dict:
        """Validate and enhance simulation data"""
        
        # Ensure required fields
        if not simulation.get("title"):
            simulation["title"] = f"{language} Conversation Practice"
        
        if not simulation.get("description"):
            simulation["description"] = f"Practice {language} conversation skills"
        
        if not simulation.get("difficulty_level"):
            simulation["difficulty_level"] = "A2"
        
        if not simulation.get("estimated_duration"):
            simulation["estimated_duration"] = 15
        
        if not simulation.get("scenario_type"):
            simulation["scenario_type"] = "daily_life"
        
        if not simulation.get("learning_objectives"):
            simulation["learning_objectives"] = [
                f"Practice {language} vocabulary",
                f"Improve {language} pronunciation", 
                f"Learn {language} cultural context"
            ]
        
        if not simulation.get("vocabulary_focus"):
            simulation["vocabulary_focus"] = []
        
        if not simulation.get("conversation_starters"):
            simulation["conversation_starters"] = [
                f"Hello in {language}",
                f"How are you in {language}",
                f"Thank you in {language}"
            ]
        
        if not simulation.get("cultural_notes"):
            simulation["cultural_notes"] = f"Practice respectful {language} communication"
        
        if not simulation.get("success_criteria"):
            simulation["success_criteria"] = {
                "vocabulary_mastery": 0.3,
                "cultural_appropriateness": 0.3,
                "conversation_flow": 0.4
            }
        
        return simulation
    
    def upload_to_supabase(self, simulation: dict, language: str, persona_key: str) -> bool:
        """Upload simulation to Supabase"""
        
        try:
            # Get persona ID
            persona_result = supabase.table("simulation_personas").select("id").eq("name", persona_key).execute()
            if not persona_result.data:
                logging.error(f"❌ Persona not found: {persona_key}")
                return False
            persona_id = persona_result.data[0]["id"]
            
            # Get language ID
            language_result = supabase.table("languages").select("id").eq("name", language).execute()
            if not language_result.data:
                logging.error(f"❌ Language not found: {language}")
                return False
            language_id = language_result.data[0]["id"]
            
            # Prepare simulation data
            simulation_data = {
                "id": str(uuid.uuid4()),
                "persona_id": persona_id,
                "language_id": language_id,
                "title": simulation["title"],
                "description": simulation["description"],
                "difficulty_level": simulation["difficulty_level"],
                "estimated_duration": simulation["estimated_duration"],
                "scenario_type": simulation["scenario_type"],
                "learning_objectives": simulation["learning_objectives"],
                "vocabulary_focus": simulation["vocabulary_focus"],
                "conversation_starters": simulation["conversation_starters"],
                "success_criteria": simulation["success_criteria"],
                "cultural_notes": simulation["cultural_notes"],
                "is_active": True,
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat()
            }
            
            # Upload to Supabase
            result = supabase.table("simulations").insert(simulation_data).execute()
            
            if result.data:
                logging.info(f"✅ Uploaded to Supabase: {simulation_data['id']}")
                return True
            else:
                logging.error("❌ Supabase upload failed")
                return False
                
        except Exception as e:
            logging.error(f"❌ Upload error: {str(e)}")
            return False

def generate_cost_free_batch(language: str, count: int = 10):
    """Generate batch with zero OpenAI costs"""
    
    print(f"🆓 COST-FREE SIMULATION GENERATION")
    print(f"📊 Language: {language}")
    print(f"📊 Target: {count} simulations")
    print(f"📊 Cost: $0.00 (Gemini only)")
    print()
    
    generator = GeminiOnlyGenerator()
    
    personas = ["beginner_enthusiast", "busy_professional", "traveler"]
    scenarios = [
        "Morning routine conversation",
        "Grocery store interaction", 
        "Restaurant ordering",
        "Bank transaction",
        "Doctor appointment",
        "Hotel check-in",
        "Asking for directions",
        "Shopping for clothes",
        "Ordering coffee",
        "Meeting new people"
    ]
    
    for i in range(count):
        persona = personas[i % len(personas)]
        scenario = scenarios[i % len(scenarios)]
        
        logging.info(f"📝 {language} ({i+1}/{count}): {scenario}")
        
        # Generate with Gemini only
        simulation = generator.generate_gemini_only(language, persona, scenario)
        
        if simulation:
            # Upload to Supabase
            success = generator.upload_to_supabase(simulation, language, persona)
            if success:
                generator.uploaded_count += 1
                logging.info(f"✅ Complete: {simulation['title']}")
            else:
                generator.failed_count += 1
        else:
            generator.failed_count += 1
        
        generator.generated_count += 1
        
        # Small delay
        time.sleep(2)
    
    # Final report
    success_rate = (generator.uploaded_count / max(1, generator.generated_count)) * 100
    
    print(f"\n🆓 COST-FREE GENERATION COMPLETE!")
    print(f"📊 Generated: {generator.generated_count}")
    print(f"📊 Uploaded: {generator.uploaded_count}")
    print(f"📊 Success rate: {success_rate:.1f}%")
    print(f"📊 Total cost: $0.00")
    print(f"📊 Savings: 100% vs OpenAI pipeline")

if __name__ == "__main__":
    print("🆓 GEMINI-ONLY GENERATOR")
    print("Zero OpenAI costs!")
    print("=" * 30)
    
    language = input("Enter language (default: Spanish): ") or "Spanish"
    count = int(input("Enter simulation count (default: 10): ") or "10")
    
    generate_cost_free_batch(language, count)
