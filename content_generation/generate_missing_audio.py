#!/usr/bin/env python3
"""
Generate Missing Audio Files for Tamil A1 Lesson
This script creates the audio files that are referenced in the database but missing from the file system.
"""

import os
import json
import requests
import time
from pathlib import Path
from typing import Dict, List, Optional

# ElevenLabs Configuration
ELEVENLABS_API_KEY = os.getenv('ELEVENLABS_API_KEY')
ELEVENLABS_VOICE_ID = "9BWtsMINqrJLrRacOk9x"  # Tamil voice from the database
ELEVENLABS_BASE_URL = "https://api.elevenlabs.io/v1"

# Audio output directory
AUDIO_OUTPUT_DIR = "/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Apps/NIRA/Assets/Audio"

def ensure_audio_directory():
    """Create the audio directory if it doesn't exist."""
    Path(AUDIO_OUTPUT_DIR).mkdir(parents=True, exist_ok=True)
    print(f"📁 Audio directory ready: {AUDIO_OUTPUT_DIR}")

def generate_audio_file(text: str, filename: str, voice_id: str = ELEVENLABS_VOICE_ID) -> bool:
    """Generate audio file using ElevenLabs API."""
    if not ELEVENLABS_API_KEY:
        print("❌ ELEVENLABS_API_KEY not found in environment variables")
        return False
    
    url = f"{ELEVENLABS_BASE_URL}/text-to-speech/{voice_id}"
    
    headers = {
        "Accept": "audio/mpeg",
        "Content-Type": "application/json",
        "xi-api-key": ELEVENLABS_API_KEY
    }
    
    data = {
        "text": text,
        "model_id": "eleven_multilingual_v2",
        "voice_settings": {
            "stability": 0.5,
            "similarity_boost": 0.75,
            "style": 0.0,
            "use_speaker_boost": True
        }
    }
    
    try:
        print(f"🎵 Generating audio for: '{text}' -> {filename}")
        response = requests.post(url, json=data, headers=headers, timeout=30)
        
        if response.status_code == 200:
            output_path = os.path.join(AUDIO_OUTPUT_DIR, filename)
            with open(output_path, 'wb') as f:
                f.write(response.content)
            print(f"✅ Audio saved: {output_path}")
            return True
        else:
            print(f"❌ ElevenLabs API error: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error generating audio: {e}")
        return False

def load_lesson_audio_data() -> Optional[Dict]:
    """Load the lesson audio file data."""
    audio_file_path = "lesson_audio_files.json"
    
    if not os.path.exists(audio_file_path):
        print(f"❌ Audio file data not found: {audio_file_path}")
        return None
    
    try:
        with open(audio_file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ Error loading audio file data: {e}")
        return None

def extract_filename_from_url(url: str) -> str:
    """Extract filename from the full URL path."""
    return os.path.basename(url)

def generate_vocabulary_audio(vocab_data: List[Dict]) -> int:
    """Generate audio files for vocabulary items."""
    generated_count = 0
    
    print("\n🎯 Generating Vocabulary Audio Files...")
    
    for vocab in vocab_data:
        # Generate word audio
        if 'word_audio_url' in vocab and 'word' in vocab:
            filename = extract_filename_from_url(vocab['word_audio_url'])
            if generate_audio_file(vocab['word'], filename):
                generated_count += 1
            time.sleep(1)  # Rate limiting
        
        # Generate example audio
        if 'example_audio_url' in vocab and 'example' in vocab:
            filename = extract_filename_from_url(vocab['example_audio_url'])
            if generate_audio_file(vocab['example'], filename):
                generated_count += 1
            time.sleep(1)  # Rate limiting
    
    return generated_count

def generate_conversation_audio(conv_data: List[Dict]) -> int:
    """Generate audio files for conversation exchanges."""
    generated_count = 0
    
    print("\n🎯 Generating Conversation Audio Files...")
    
    for conversation in conv_data:
        if 'exchanges' in conversation:
            for exchange in conversation['exchanges']:
                if 'audio_url' in exchange and 'text' in exchange:
                    filename = extract_filename_from_url(exchange['audio_url'])
                    if generate_audio_file(exchange['text'], filename):
                        generated_count += 1
                    time.sleep(1)  # Rate limiting
    
    return generated_count

def generate_exercise_audio(exercise_data: List[Dict]) -> int:
    """Generate audio files for exercise questions."""
    generated_count = 0
    
    print("\n🎯 Generating Exercise Audio Files...")
    
    for exercise in exercise_data:
        if 'audio_url' in exercise and 'question' in exercise:
            filename = extract_filename_from_url(exercise['audio_url'])
            if generate_audio_file(exercise['question'], filename):
                generated_count += 1
            time.sleep(1)  # Rate limiting
    
    return generated_count

def create_test_audio_files():
    """Create simple test audio files for immediate testing."""
    print("\n🧪 Creating Test Audio Files...")
    
    test_phrases = [
        ("வணக்கம்", "test_vanakkam.mp3"),
        ("நன்றி", "test_nandri.mp3"),
        ("என் பெயர்", "test_en_peyar.mp3")
    ]
    
    for text, filename in test_phrases:
        if generate_audio_file(text, filename):
            print(f"✅ Test audio created: {filename}")
        time.sleep(1)

def main():
    """Main function to generate all missing audio files."""
    print("🎵 Tamil A1 Lesson Audio Generator")
    print("=" * 50)
    
    # Ensure output directory exists
    ensure_audio_directory()
    
    # Check if ElevenLabs API key is available
    if not ELEVENLABS_API_KEY:
        print("❌ ElevenLabs API key not found!")
        print("Please set the ELEVENLABS_API_KEY environment variable.")
        print("\nFor testing purposes, creating placeholder files...")
        create_test_audio_files()
        return
    
    # Load lesson audio data
    audio_data = load_lesson_audio_data()
    if not audio_data:
        print("❌ Could not load lesson audio data")
        return
    
    total_generated = 0
    
    # Generate vocabulary audio
    if 'vocabulary' in audio_data:
        total_generated += generate_vocabulary_audio(audio_data['vocabulary'])
    
    # Generate conversation audio
    if 'conversations' in audio_data:
        total_generated += generate_conversation_audio(audio_data['conversations'])
    
    # Generate exercise audio
    if 'exercises' in audio_data:
        total_generated += generate_exercise_audio(audio_data['exercises'])
    
    print(f"\n🎉 Audio generation complete!")
    print(f"📊 Total files generated: {total_generated}")
    print(f"📁 Audio files saved to: {AUDIO_OUTPUT_DIR}")

if __name__ == "__main__":
    main()
