#!/usr/bin/env python3
"""
Generate Lessons for 10 New Languages
Creates 600 lessons (60 per language) for the newly added languages
"""

import time
import logging
from comprehensive_lesson_generator import ComprehensiveLessonGenerator, LessonConfig

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'new_languages_generation_{int(time.time())}.log'),
        logging.StreamHandler()
    ]
)

class NewLanguagesConfig(LessonConfig):
    """Configuration for new languages lesson generation"""
    
    # Override to use only the new 10 languages
    MISSING_LANGUAGES = [
        ("Kannada", "kn"),
        ("Malayalam", "ml"),
        ("Bengali", "bn"),
        ("Marathi", "mr"),
        ("Punjabi", "pa"),
        ("Dutch", "nl"),
        ("Swedish", "sv"),
        ("Thai", "th"),
        ("Russian", "ru"),
        ("Norwegian", "no")
    ]

class NewLanguagesGenerator(ComprehensiveLessonGenerator):
    """Generator specifically for the 10 new languages"""
    
    def __init__(self, config: NewLanguagesConfig):
        super().__init__(config)
        # Update total lessons calculation for new languages
        self.total_lessons = len(config.MISSING_LANGUAGES) * len(config.LEVELS) * 15  # 600 lessons
        
        logging.info(f"🌍 Initialized NEW LANGUAGES generator for {self.total_lessons} lessons")
        logging.info(f"📊 New Languages: {len(config.MISSING_LANGUAGES)}, Levels: {len(config.LEVELS)}, Topics per level: 15")
        
        # Print language list
        print("\n🌍 NEW LANGUAGES TO GENERATE:")
        for i, (name, code) in enumerate(config.MISSING_LANGUAGES, 16):
            print(f"{i}. {name} ({code})")
        print("=" * 50)

def main():
    """Main execution function"""
    print("🌍 NIRA New Languages Lesson Generator")
    print("=" * 50)
    print("Generating lessons for 10 new languages:")
    print("16. Kannada (kn)")
    print("17. Malayalam (ml)")
    print("18. Bengali (bn)")
    print("19. Marathi (mr)")
    print("20. Punjabi (pa)")
    print("21. Dutch (nl)")
    print("22. Swedish (sv)")
    print("23. Thai (th)")
    print("24. Russian (ru)")
    print("25. Norwegian (no)")
    print("=" * 50)
    print("📊 Total: 600 lessons (60 per language)")
    print("⏱️ Estimated time: ~5-6 hours")
    print("=" * 50)
    
    confirm = input("🤔 Start generating lessons for new languages? (y/N): ").strip().lower()
    if confirm != 'y':
        print("❌ Generation cancelled")
        return
    
    try:
        # Initialize configuration and generator
        config = NewLanguagesConfig()
        generator = NewLanguagesGenerator(config)
        
        # Start generation
        print("\n🚀 Starting lesson generation for new languages...")
        start_time = time.time()
        
        generator.generate_all_missing_lessons()
        
        # Final summary
        elapsed_time = time.time() - start_time
        hours = elapsed_time / 3600
        
        print("\n" + "="*60)
        print("🎉 NEW LANGUAGES LESSON GENERATION COMPLETE!")
        print("="*60)
        print(f"✅ Successfully Generated: {generator.generated_count} lessons")
        print(f"❌ Failed: {generator.failed_count} lessons")
        print(f"📊 Success Rate: {(generator.generated_count/(generator.generated_count + generator.failed_count))*100:.1f}%")
        print(f"⏱️ Total Time: {hours:.1f} hours")
        print(f"⚡ Average: {elapsed_time/generator.generated_count:.1f} seconds per lesson")
        print("="*60)
        
        if generator.failed_count > 0:
            print(f"⚠️ {generator.failed_count} lessons failed - you can retry them later")
        else:
            print("🎉 ALL LESSONS GENERATED SUCCESSFULLY!")
            print("🌍 NIRA now has complete coverage for 25 languages!")
        
    except KeyboardInterrupt:
        print("\n⚠️ Generation interrupted by user")
    except Exception as e:
        print(f"\n❌ Generation failed: {str(e)}")
        logging.error(f"Fatal error: {str(e)}")

if __name__ == "__main__":
    main()
