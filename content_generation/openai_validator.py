#!/usr/bin/env python3
"""
OpenAI Content Validator for NIRA
Validates generated lesson content for accuracy, cultural appropriateness, and learning effectiveness
"""

import json
import logging
import requests
import os
from datetime import datetime
from dotenv import load_dotenv
from typing import Dict, List, Optional, Any

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class OpenAIValidator:
    """Validate lesson content using OpenAI GPT-4o Turbo"""
    
    def __init__(self):
        self.openai_api_key = os.getenv('OPENAI_API_KEY')
        self.openai_url = "https://api.openai.com/v1/chat/completions"
        
        if not self.openai_api_key:
            raise ValueError("OpenAI API key not found. Check your .env file.")
        
        self.validation_stats = {
            "validated": 0,
            "passed": 0,
            "failed": 0,
            "issues_found": []
        }
        
        logging.info("🔍 OpenAI Validator initialized")
    
    def validate_complete_lesson(self, lesson_data: Dict, language: str) -> Dict:
        """
        Validate a complete lesson for:
        1. Language accuracy
        2. Cultural appropriateness
        3. Learning progression
        4. Content quality
        """
        
        logging.info(f"🔍 Validating {language} lesson: {lesson_data.get('title', 'Untitled')}")
        
        validation_results = {
            "overall_valid": True,
            "validation_score": 0.0,
            "issues": [],
            "recommendations": [],
            "validated_at": datetime.now().isoformat(),
            "validator_version": "openai_v1.0"
        }
        
        try:
            # Validate each component
            vocab_validation = self._validate_vocabulary(lesson_data.get('vocabulary', []), language)
            conv_validation = self._validate_conversations(lesson_data.get('conversations', []), language)
            grammar_validation = self._validate_grammar(lesson_data.get('grammar', []), language)
            exercise_validation = self._validate_exercises(lesson_data.get('exercises', []), language)
            
            # Combine results
            component_scores = [
                vocab_validation.get('score', 0),
                conv_validation.get('score', 0),
                grammar_validation.get('score', 0),
                exercise_validation.get('score', 0)
            ]
            
            validation_results["validation_score"] = sum(component_scores) / len(component_scores)
            validation_results["component_scores"] = {
                "vocabulary": vocab_validation.get('score', 0),
                "conversations": conv_validation.get('score', 0),
                "grammar": grammar_validation.get('score', 0),
                "exercises": exercise_validation.get('score', 0)
            }
            
            # Collect all issues
            all_issues = []
            all_issues.extend(vocab_validation.get('issues', []))
            all_issues.extend(conv_validation.get('issues', []))
            all_issues.extend(grammar_validation.get('issues', []))
            all_issues.extend(exercise_validation.get('issues', []))
            
            validation_results["issues"] = all_issues
            validation_results["overall_valid"] = validation_results["validation_score"] >= 0.7
            
            # Update stats
            self.validation_stats["validated"] += 1
            if validation_results["overall_valid"]:
                self.validation_stats["passed"] += 1
            else:
                self.validation_stats["failed"] += 1
                self.validation_stats["issues_found"].extend(all_issues)
            
            logging.info(f"✅ Validation complete. Score: {validation_results['validation_score']:.2f}")
            return validation_results
            
        except Exception as e:
            logging.error(f"❌ Validation failed: {str(e)}")
            validation_results["overall_valid"] = False
            validation_results["issues"].append(f"Validation error: {str(e)}")
            return validation_results
    
    def _validate_vocabulary(self, vocabulary: List[Dict], language: str) -> Dict:
        """Validate vocabulary items for accuracy and appropriateness"""
        
        # Sample a few vocabulary items for validation (cost optimization)
        sample_vocab = vocabulary[:5] if len(vocabulary) > 5 else vocabulary
        
        prompt = f"""
You are an expert {language} language teacher and linguist. Please validate the following vocabulary items for a beginner (A1) level lesson.

Vocabulary to validate:
{json.dumps(sample_vocab, ensure_ascii=False, indent=2)}

Please evaluate each item for:
1. Translation accuracy
2. Pronunciation correctness (if provided)
3. Appropriate difficulty level for A1 learners
4. Cultural appropriateness
5. Example sentence quality

Return your assessment as JSON:
{{
    "score": 0.0-1.0,
    "issues": ["list of specific issues found"],
    "recommendations": ["suggestions for improvement"],
    "validated_items": [
        {{
            "word": "word",
            "accurate": true/false,
            "issues": ["specific issues"],
            "suggestions": ["improvements"]
        }}
    ]
}}
"""
        
        return self._call_openai_api(prompt, "vocabulary validation")
    
    def _validate_conversations(self, conversations: List[Dict], language: str) -> Dict:
        """Validate conversations for naturalness and cultural accuracy"""
        
        # Sample a few conversations for validation
        sample_convs = conversations[:3] if len(conversations) > 3 else conversations
        
        prompt = f"""
You are an expert {language} language teacher and cultural consultant. Please validate the following conversations for a beginner (A1) level lesson.

Conversations to validate:
{json.dumps(sample_convs, ensure_ascii=False, indent=2)}

Please evaluate each conversation for:
1. Natural language flow
2. Cultural appropriateness and authenticity
3. Appropriate difficulty level for A1 learners
4. Translation accuracy
5. Realistic scenarios

Return your assessment as JSON:
{{
    "score": 0.0-1.0,
    "issues": ["list of specific issues found"],
    "recommendations": ["suggestions for improvement"],
    "validated_conversations": [
        {{
            "id": 1,
            "natural": true/false,
            "culturally_appropriate": true/false,
            "issues": ["specific issues"],
            "suggestions": ["improvements"]
        }}
    ]
}}
"""
        
        return self._call_openai_api(prompt, "conversation validation")
    
    def _validate_grammar(self, grammar: List[Dict], language: str) -> Dict:
        """Validate grammar explanations for accuracy and clarity"""
        
        prompt = f"""
You are an expert {language} grammar teacher. Please validate the following grammar explanations for a beginner (A1) level lesson.

Grammar points to validate:
{json.dumps(grammar, ensure_ascii=False, indent=2)}

Please evaluate each grammar point for:
1. Accuracy of the rule explanation
2. Clarity for beginner learners
3. Quality and accuracy of examples
4. Appropriate complexity for A1 level
5. Practical usefulness

Return your assessment as JSON:
{{
    "score": 0.0-1.0,
    "issues": ["list of specific issues found"],
    "recommendations": ["suggestions for improvement"],
    "validated_grammar": [
        {{
            "rule": "rule name",
            "accurate": true/false,
            "clear": true/false,
            "issues": ["specific issues"],
            "suggestions": ["improvements"]
        }}
    ]
}}
"""
        
        return self._call_openai_api(prompt, "grammar validation")
    
    def _validate_exercises(self, exercises: List[Dict], language: str) -> Dict:
        """Validate exercises for correctness and learning effectiveness"""
        
        # Sample exercises for validation
        sample_exercises = exercises[:5] if len(exercises) > 5 else exercises
        
        prompt = f"""
You are an expert {language} language teacher and assessment specialist. Please validate the following exercises for a beginner (A1) level lesson.

Exercises to validate:
{json.dumps(sample_exercises, ensure_ascii=False, indent=2)}

Please evaluate each exercise for:
1. Correct answers and options
2. Appropriate difficulty level for A1 learners
3. Clear question formulation
4. Learning effectiveness
5. Variety and engagement

Return your assessment as JSON:
{{
    "score": 0.0-1.0,
    "issues": ["list of specific issues found"],
    "recommendations": ["suggestions for improvement"],
    "validated_exercises": [
        {{
            "id": 1,
            "correct_answer": true/false,
            "appropriate_difficulty": true/false,
            "issues": ["specific issues"],
            "suggestions": ["improvements"]
        }}
    ]
}}
"""
        
        return self._call_openai_api(prompt, "exercise validation")
    
    def _call_openai_api(self, prompt: str, validation_type: str) -> Dict:
        """Call OpenAI API for validation"""
        
        try:
            headers = {
                "Authorization": f"Bearer {self.openai_api_key}",
                "Content-Type": "application/json"
            }
            
            data = {
                "model": "gpt-4o",
                "messages": [
                    {
                        "role": "system",
                        "content": "You are an expert language teacher and cultural consultant. Provide accurate, helpful validation feedback in valid JSON format."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                "temperature": 0.2,
                "max_tokens": 1500
            }
            
            logging.info(f"🔄 Calling OpenAI for {validation_type}...")
            response = requests.post(self.openai_url, headers=headers, json=data)
            response.raise_for_status()
            
            result = response.json()
            content = result['choices'][0]['message']['content']
            
            # Parse JSON response
            if "```json" in content:
                json_text = content.split("```json")[1].split("```")[0].strip()
            elif "```" in content:
                json_text = content.split("```")[1].strip()
            else:
                json_text = content.strip()
            
            validation_result = json.loads(json_text)
            
            logging.info(f"✅ {validation_type} completed. Score: {validation_result.get('score', 0):.2f}")
            return validation_result
            
        except Exception as e:
            logging.error(f"❌ OpenAI API error for {validation_type}: {str(e)}")
            return {
                "score": 0.0,
                "issues": [f"Validation API error: {str(e)}"],
                "recommendations": ["Manual review required"]
            }
    
    def get_validation_statistics(self) -> Dict:
        """Get validation statistics"""
        return {
            **self.validation_stats,
            "success_rate": f"{(self.validation_stats['passed'] / max(1, self.validation_stats['validated'])) * 100:.1f}%"
        }

# Test function
if __name__ == "__main__":
    # Load the test lesson we generated
    try:
        with open('test_lesson_output.json', 'r', encoding='utf-8') as f:
            test_lesson = json.load(f)
        
        validator = OpenAIValidator()
        validation_results = validator.validate_complete_lesson(test_lesson, "Tamil")
        
        print("🔍 Validation Results:")
        print("=" * 50)
        print(f"Overall Valid: {validation_results['overall_valid']}")
        print(f"Validation Score: {validation_results['validation_score']:.2f}")
        print(f"Component Scores: {validation_results.get('component_scores', {})}")
        
        if validation_results['issues']:
            print(f"\nIssues Found ({len(validation_results['issues'])}):")
            for i, issue in enumerate(validation_results['issues'], 1):
                print(f"{i}. {issue}")
        
        # Save validation results
        with open('validation_results.json', 'w', encoding='utf-8') as f:
            json.dump(validation_results, f, ensure_ascii=False, indent=2)
        print("\n📁 Validation results saved to validation_results.json")
        
        print(f"\n📊 Statistics: {validator.get_validation_statistics()}")
        
    except FileNotFoundError:
        print("❌ test_lesson_output.json not found. Run complete_lesson_generator.py first.")
    except Exception as e:
        print(f"❌ Validation test failed: {str(e)}")
