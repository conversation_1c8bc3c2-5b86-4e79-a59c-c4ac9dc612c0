#!/usr/bin/env python3
"""
Production Realistic Simulation Generator
Generate human-like conversations for real-life application
Cost-effective with full database integration
"""

import logging
import json
import uuid
import time
from datetime import datetime
from cost_optimized_generator import CostOptimizedGenerator

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

class ProductionRealisticGenerator(CostOptimizedGenerator):
    """Production-ready realistic simulation generator"""

    def __init__(self):
        super().__init__()
        self.realistic_count = 0

    def generate_realistic_simulation_cost_effective(self, language: str, persona_key: str, scenario: str, cefr_level: str = "A2"):
        """Generate realistic simulation with cost optimization"""

        logging.info(f"🎭 Generating realistic simulation: {language} - {persona_key} - {scenario} - {cefr_level}")

        # Enhanced prompt for realistic, human-like conversations
        prompt = f"""Create a realistic, human-like conversation simulation for {language} language learning.

CONTEXT:
- Language: {language}
- Persona: {persona_key}
- Scenario: {scenario}
- CEFR Level: {cefr_level}
- Purpose: Real-life practical application

REQUIREMENTS FOR REALISTIC SIMULATION:
1. HUMAN-LIKE CONVERSATIONS - Natural flow with interruptions, hesitations, authentic reactions
2. REAL-LIFE PRACTICAL - Users can immediately apply in actual situations
3. CULTURAL AUTHENTICITY - Genuine cultural context and proper etiquette
4. DETAILED DIALOGUE - Full conversation with natural pauses and reactions
5. INTEGRATION READY - Connect to lessons, AI agents, audio capabilities

OUTPUT JSON FORMAT:
{{
    "title": "Engaging {language} title with English subtitle (e.g., '¡Vamos a Cenar! - Dinner at Casa Pepe')",
    "description": "Detailed real-life scenario description that users will actually encounter",
    "cefr_level": "{cefr_level}",
    "estimated_duration": 25,
    "scenario_type": "daily_life|shopping|work|health|education|social|transportation|cultural|technology|problem_solving",
    "real_life_context": {{
        "situation": "Specific real-life situation (e.g., 'You are hungry after sightseeing in Madrid and found a local restaurant')",
        "location": "Exact location (e.g., 'Casa Pepe restaurant in Malasaña, Madrid')",
        "participants": ["User (Traveler)", "Carlos (Waiter)", "Background diners"],
        "cultural_context": "Important cultural background (e.g., 'Spanish dining: late hours, sharing culture, professional service')",
        "time_context": "When this happens (e.g., '8:30 PM - typical Spanish dinner time')",
        "difficulty_factors": ["What makes this challenging (e.g., 'Menu in Spanish only', 'Fast-paced service')"]
    }},
    "learning_objectives": [
        "Order a complete Spanish meal confidently",
        "Handle menu questions and dietary restrictions",
        "Navigate payment and tipping etiquette",
        "Use appropriate formal/informal language"
    ],
    "prerequisite_lessons": [
        "{cefr_level}.1: Food and Drink Vocabulary",
        "{cefr_level}.2: Polite Requests and Questions",
        "A1.4: Numbers and Prices"
    ],
    "vocabulary_focus": [
        {{
            "word": "la carta",
            "translation": "the menu",
            "pronunciation": "lah KAR-tah",
            "context": "Used when asking for or referring to restaurant menu",
            "formality": "neutral",
            "example_sentence": "¿Podría traerme la carta, por favor?",
            "cultural_notes": "Standard term in all Spanish-speaking countries"
        }},
        {{
            "word": "¿Qué me recomienda?",
            "translation": "What do you recommend?",
            "pronunciation": "keh meh reh-ko-mee-EN-dah",
            "context": "Polite way to ask waiter for recommendations",
            "formality": "formal",
            "example_sentence": "Es mi primera vez aquí, ¿qué me recomienda?",
            "cultural_notes": "Shows respect for local expertise"
        }}
    ],
    "realistic_dialogue": [
        {{
            "speaker": "Carlos (Waiter)",
            "text": "¡Buenas noches! Bienvenido a Casa Pepe. ¿Mesa para una persona?",
            "translation": "Good evening! Welcome to Casa Pepe. Table for one?",
            "audio_notes": "Professional but warm tone, slight Madrid accent, normal pace",
            "cultural_notes": "Spanish waiters are professional but not overly chatty. 'Buenas noches' used after 8 PM",
            "difficulty_level": "{cefr_level}",
            "response_triggers": ["User needs to respond about table size and be polite"]
        }},
        {{
            "speaker": "User",
            "text": "[User chooses response]",
            "translation": "[User response]",
            "response_options": [
                "Sí, una mesa, por favor. (Yes, a table please - simple and polite)",
                "Buenas noches. Sí, solo yo. (Good evening. Yes, just me - more conversational)",
                "Una mesa para uno, gracias. (A table for one, thanks - direct but polite)"
            ],
            "ai_agent_guidance": "Help user choose appropriate formality level and explain cultural context",
            "cultural_notes": "All options are acceptable, choice depends on user's comfort level"
        }}
    ],
    "conversation_branches": [
        {{
            "trigger": "If user asks about menu items",
            "dialogue_path": "Waiter explains traditional dishes and ingredients",
            "difficulty": "{cefr_level}",
            "cultural_importance": "High - shows respect for Spanish cuisine",
            "ai_agent_role": "Help user understand food terms and cultural significance",
            "real_life_frequency": "Very common - most travelers need menu help"
        }},
        {{
            "trigger": "If user has dietary restrictions",
            "dialogue_path": "Waiter suggests alternatives and explains modifications",
            "difficulty": "{cefr_level}",
            "cultural_importance": "Medium - becoming more common in Spain",
            "ai_agent_role": "Guide user through polite explanation of restrictions",
            "real_life_frequency": "Common - many people have dietary needs"
        }}
    ],
    "ai_agent_integration": {{
        "agent_role": "Experienced Spanish conversation partner who lived in Madrid",
        "intervention_points": [
            "When user struggles with menu vocabulary",
            "When cultural context is crucial (meal timing, etiquette)",
            "When pronunciation affects understanding",
            "When user needs encouragement for complex phrases"
        ],
        "encouragement_style": "Supportive and culturally informative, like a helpful local friend",
        "correction_approach": "Gentle correction with cultural explanation",
        "cultural_coaching": "Explain why certain phrases work better in Spanish culture"
    }},
    "audio_integration": {{
        "voice_characteristics": "Native Madrid Spanish speaker, male, 30s, professional waiter voice",
        "background_sounds": "Realistic restaurant: quiet conversations, clinking dishes, Spanish guitar",
        "speech_patterns": "Natural Madrid accent with restaurant pace - clear but not slow",
        "pronunciation_focus": ["Rolling R's", "Soft C sounds", "Question intonation"],
        "accent_type": "Madrid Spanish - neutral and widely understood"
    }},
    "cultural_deep_dive": {{
        "etiquette_rules": [
            "Wait to be seated, don't seat yourself",
            "Take time deciding - Spanish dining is relaxed",
            "Tipping 5-10% is appreciated but not mandatory",
            "It's normal to linger after eating"
        ],
        "common_mistakes": [
            "Ordering dinner too early (before 8:30 PM)",
            "Being too informal with waitstaff initially",
            "Not asking about daily specials",
            "Expecting immediate service"
        ],
        "regional_variations": [
            "Madrid: More formal service, traditional dishes",
            "Barcelona: More international, casual atmosphere",
            "Andalusia: Very relaxed, lots of tapas"
        ],
        "body_language": [
            "Make eye contact when ordering",
            "Slight nod when waiter explains dishes",
            "Raised hand to call waiter (not snapping)"
        ],
        "social_context": [
            "Dining is social time in Spain",
            "Meals are longer and more relaxed",
            "Conversation is important part of experience"
        ]
    }},
    "success_criteria": {{
        "conversation_completion": "Successfully order complete meal and handle payment",
        "cultural_appropriateness": "Use appropriate formality and show respect for dining culture",
        "vocabulary_usage": "Use at least 15 restaurant-specific terms naturally",
        "pronunciation_accuracy": "Achieve 75% accuracy on key food terms",
        "real_life_readiness": "Feel confident to eat at any Spanish restaurant independently"
    }},
    "follow_up_lessons": [
        "{cefr_level}.5: Advanced Restaurant Vocabulary",
        "B1.1: Complaining Politely",
        "B1.2: Making Reservations"
    ],
    "practice_suggestions": [
        "Role-play with AI agent as different restaurant types",
        "Practice pronunciation with voice recognition",
        "Record yourself ordering and compare with native audio",
        "Use AR to practice reading Spanish menus"
    ],
    "gamification_elements": {{
        "achievements": [
            "First Order: Complete your first Spanish restaurant experience",
            "Cultural Navigator: Use appropriate Spanish dining etiquette",
            "Pronunciation Pro: Perfect pronunciation of 5 food terms",
            "Confident Diner: Complete meal without AI assistance"
        ],
        "points_system": "Points for natural conversation flow, cultural appropriateness, helping others",
        "challenges": [
            "Order waiter's recommendation without seeing it first",
            "Handle situation where first choice unavailable",
            "Have brief conversation about food with another diner"
        ],
        "social_features": [
            "Share successful real-life restaurant experiences",
            "Help other learners with menu translations",
            "Rate authenticity of simulation scenarios"
        ]
    }},
    "real_life_readiness_score": 0.85
}}

Make this simulation feel like a REAL conversation that happens between REAL people in a REAL {language} environment. Include natural hesitations, cultural references, and authentic human interactions. Users should feel completely prepared for the actual situation after practicing this."""

        try:
            # Generate with Gemini (cost-effective)
            simulation = self._generate_with_gemini(language, persona_key, "social", prompt)

            if not simulation:
                logging.error("❌ Gemini generation failed")
                return None

            # Enhanced validation for realistic simulations
            validated_simulation = self._validate_realistic_simulation(simulation, language, persona_key, cefr_level)

            # Upload with enhanced fields
            success = self._upload_realistic_simulation(validated_simulation, language, persona_key)

            if success:
                self.realistic_count += 1
                self.cost_tracking["simulations_generated"] += 1
                estimated_cost = 0.001  # Very low cost with Gemini
                self.cost_tracking["total_cost"] += estimated_cost
                self.cost_tracking["cost_per_simulation"] = self.cost_tracking["total_cost"] / self.cost_tracking["simulations_generated"]

                logging.info(f"🎭 Realistic simulation complete: {validated_simulation['title']}")
                logging.info(f"💰 Cost tracking: ${self.cost_tracking['total_cost']:.3f} total, ${self.cost_tracking['cost_per_simulation']:.3f} per simulation")

                return validated_simulation

            return None

        except Exception as e:
            logging.error(f"❌ Realistic simulation generation failed: {str(e)}")
            return None

    def _validate_realistic_simulation(self, simulation: dict, language: str, persona_key: str, cefr_level: str) -> dict:
        """Enhanced validation for realistic simulations"""

        # Basic validation first
        simulation = self._basic_validation(simulation, language)

        # Ensure realistic simulation fields
        if not simulation.get("real_life_context"):
            simulation["real_life_context"] = {
                "situation": f"Practical {language} conversation scenario",
                "location": f"Authentic {language}-speaking environment",
                "participants": ["User", "Native speaker"],
                "cultural_context": f"Real {language} cultural setting"
            }

        if not simulation.get("realistic_dialogue"):
            simulation["realistic_dialogue"] = [
                {
                    "speaker": "Native Speaker",
                    "text": f"Hello in {language}",
                    "translation": "Hello",
                    "audio_notes": "Friendly tone",
                    "cultural_notes": f"Standard {language} greeting"
                }
            ]

        if not simulation.get("cefr_level"):
            simulation["cefr_level"] = cefr_level

        if not simulation.get("real_life_readiness_score"):
            simulation["real_life_readiness_score"] = 0.75

        return simulation

    def _upload_realistic_simulation(self, simulation: dict, language: str, persona_key: str) -> bool:
        """Upload realistic simulation with enhanced fields"""

        try:
            # Get IDs
            from simulation_generator import supabase

            persona_result = supabase.table("simulation_personas").select("id").eq("name", persona_key).execute()
            if not persona_result.data:
                logging.error(f"❌ Persona not found: {persona_key}")
                return False
            persona_id = persona_result.data[0]["id"]

            language_result = supabase.table("languages").select("id").eq("name", language).execute()
            if not language_result.data:
                logging.error(f"❌ Language not found: {language}")
                return False
            language_id = language_result.data[0]["id"]

            # Enhanced simulation data
            simulation_data = {
                "id": str(uuid.uuid4()),
                "persona_id": persona_id,
                "language_id": language_id,
                "title": simulation["title"],
                "description": simulation["description"],
                "difficulty_level": simulation.get("cefr_level", "A2"),
                "cefr_level": simulation.get("cefr_level", "A2"),
                "estimated_duration": simulation.get("estimated_duration", 25),
                "scenario_type": simulation.get("scenario_type", "social"),
                "learning_objectives": simulation.get("learning_objectives", []),
                "vocabulary_focus": simulation.get("vocabulary_focus", []),
                "conversation_starters": simulation.get("realistic_dialogue", [])[:3],
                "success_criteria": simulation.get("success_criteria", {}),
                "cultural_notes": simulation.get("cultural_deep_dive", {}).get("etiquette_rules", []),

                # Enhanced realistic fields
                "real_life_context": simulation.get("real_life_context", {}),
                "realistic_dialogue": simulation.get("realistic_dialogue", []),
                "conversation_branches": simulation.get("conversation_branches", []),
                "ai_agent_integration": simulation.get("ai_agent_integration", {}),
                "audio_integration": simulation.get("audio_integration", {}),
                "cultural_deep_dive": simulation.get("cultural_deep_dive", {}),
                "prerequisite_lessons": simulation.get("prerequisite_lessons", []),
                "follow_up_lessons": simulation.get("follow_up_lessons", []),
                "practice_suggestions": simulation.get("practice_suggestions", []),
                "gamification_elements": simulation.get("gamification_elements", {}),
                "real_life_readiness_score": simulation.get("real_life_readiness_score", 0.75),

                "is_active": True,
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat()
            }

            # Upload to Supabase
            result = supabase.table("simulations").insert(simulation_data).execute()

            if result.data:
                simulation_id = result.data[0]["id"]
                logging.info(f"✅ Realistic simulation uploaded: {simulation_id}")
                return True
            else:
                logging.error("❌ Supabase upload failed")
                return False

        except Exception as e:
            logging.error(f"❌ Upload error: {str(e)}")
            return False

def generate_realistic_batch(language: str, count: int = 10):
    """Generate batch of realistic simulations"""

    print(f"🎭 REALISTIC SIMULATION BATCH GENERATION")
    print(f"📊 Language: {language}")
    print(f"📊 Target: {count} realistic simulations")
    print(f"📊 Cost: ~${count * 0.001:.3f} (ultra cost-effective)")
    print(f"🎯 Focus: Human-like conversations for real-life application")
    print()

    generator = ProductionRealisticGenerator()

    personas = ["traveler", "beginner_enthusiast", "busy_professional"]
    scenarios = [
        "Ordering dinner at a traditional restaurant",
        "Shopping for groceries at local market",
        "Checking into a hotel",
        "Asking for directions in the city",
        "Visiting a pharmacy for medicine",
        "Opening a bank account",
        "Buying train tickets",
        "Ordering coffee at a café",
        "Getting a haircut at salon",
        "Booking a doctor appointment"
    ]

    cefr_levels = ["A2", "A2", "B1", "A2", "B1", "B1", "A2", "A1", "B1", "A2"]

    for i in range(count):
        persona = personas[i % len(personas)]
        scenario = scenarios[i % len(scenarios)]
        cefr_level = cefr_levels[i % len(cefr_levels)]

        logging.info(f"🎭 {language} ({i+1}/{count}): {scenario} - {cefr_level}")

        result = generator.generate_realistic_simulation_cost_effective(
            language=language,
            persona_key=persona,
            scenario=scenario,
            cefr_level=cefr_level
        )

        if result:
            logging.info(f"✅ Success: {result['title']}")
        else:
            logging.error(f"❌ Failed: {scenario}")

        # Small delay
        time.sleep(2)

    # Final report
    total_cost = generator.cost_tracking["total_cost"]
    realistic_count = generator.realistic_count
    success_rate = (realistic_count / count) * 100

    print(f"\n🎭 REALISTIC BATCH COMPLETE!")
    print(f"📊 Generated: {realistic_count}/{count}")
    print(f"📊 Success rate: {success_rate:.1f}%")
    print(f"📊 Total cost: ${total_cost:.3f}")
    print(f"📊 Cost per simulation: ${total_cost/max(1, realistic_count):.3f}")
    print(f"🎯 Real-life readiness: Enhanced with human-like conversations")

if __name__ == "__main__":
    print("🎭 PRODUCTION REALISTIC SIMULATION GENERATOR")
    print("Human-like conversations for real-life application")
    print("=" * 60)

    language = input("Enter language (default: Spanish): ") or "Spanish"
    count = int(input("Enter simulation count (default: 10): ") or "10")

    generate_realistic_batch(language, count)
