{"title": "¡Vamos a Cenar! - Ordering Dinner at Casa Pepe (A Real Madrid Restaurant Experience)", "description": "Navigate a real dinner experience at a traditional Spanish restaurant in Madrid. Learn to order food, handle menu questions, interact with waitstaff, and manage payment - exactly as you would in real life.", "cefr_level": "A2", "estimated_duration": 25, "scenario_type": "social", "real_life_context": {"situation": "You're a traveler in Madrid, hungry after a day of sightseeing. You've found a cozy local restaurant called 'Casa Pepe' that locals recommend. It's 8:30 PM (typical Spanish dinner time), the restaurant is moderately busy, and you need to order dinner without looking like a tourist.", "location": "Casa Pepe - Traditional Spanish restaurant in Malasaña neighborhood, Madrid", "participants": ["User (Traveler)", "<PERSON> (Waiter)", "<PERSON> (Another customer)", "Background diners"], "cultural_context": "Spanish dining culture: late dinner times, sharing tapas, taking time to enjoy meals, waiters are professional but not overly friendly, tipping is optional but appreciated"}, "learning_objectives": ["Order a complete Spanish meal confidently (appetizer, main course, drink)", "Ask questions about menu items and ingredients naturally", "Handle dietary restrictions or preferences politely", "Understand and respond to waiter's questions and suggestions", "Navigate payment process including tipping etiquette", "Use appropriate formal/informal language with service staff"], "prerequisite_lessons": ["A1.3: Food and Drink Vocabulary", "A1.4: Numbers and Prices", "A2.1: Polite Requests and Questions", "A2.2: Present Ten<PERSON> for Preferences"], "vocabulary_focus": [{"word": "la carta", "translation": "the menu", "pronunciation": "lah KAR-tah", "context": "Used when asking for or referring to the restaurant menu", "formality": "neutral", "example_sentence": "¿Podría traerme la carta, por favor?"}, {"word": "¿Qué me recomienda?", "translation": "What do you recommend?", "pronunciation": "keh meh reh-ko-mee-EN-dah", "context": "Polite way to ask waiter for recommendations", "formality": "formal", "example_sentence": "Es mi primera vez aquí, ¿qué me recomienda?"}, {"word": "sin gluten", "translation": "gluten-free", "pronunciation": "seen GLOO-ten", "context": "Essential for dietary restrictions", "formality": "neutral", "example_sentence": "¿Tienen opciones sin gluten?"}], "realistic_dialogue": [{"speaker": "<PERSON> (Waiter)", "text": "¡Buenas noches! Bienvenido a Casa Pepe. ¿Mesa para una persona?", "translation": "Good evening! Welcome to Casa Pepe. Table for one?", "audio_notes": "To<PERSON>: professional but warm, slight Madrid accent, normal pace", "cultural_notes": "Spanish waiters are professional but not overly chatty. 'Buenas noches' is used after 8 PM"}, {"speaker": "User", "text": "[User chooses response]", "translation": "[User response]", "response_options": ["<PERSON><PERSON>, una mesa, por favor. (Yes, a table please - polite and simple)", "Buenas noches. S<PERSON>, solo yo. (Good evening. Yes, just me - more conversational)", "Una mesa para uno, gracias. (A table for one, thanks - direct but polite)"], "ai_agent_guidance": "The AI should help user choose appropriate formality level and explain why each option works"}, {"speaker": "<PERSON> (Waiter)", "text": "Perfecto. <PERSON><PERSON><PERSON><PERSON>, por favor. *leads to table* Aquí tiene la carta. ¿Le traigo algo de beber mientras decide?", "translation": "Perfect. Follow me, please. *leads to table* Here's the menu. Shall I bring you something to drink while you decide?", "audio_notes": "Tone: efficient, includes sound of footsteps and chair being pulled out", "difficulty_notes": "Uses formal 'usted' form - typical in restaurants", "cultural_notes": "Spanish waiters often suggest drinks first, giving you time to look at the menu"}, {"speaker": "User", "text": "[User orders drink]", "translation": "[User drink order]", "response_options": ["<PERSON> cerveza, por favor. (A beer, please - casual choice)", "¿Qué vinos tienen por copas? (What wines do you have by the glass? - shows interest)", "Agua con gas, por favor. (Sparkling water, please - safe choice)"], "ai_agent_guidance": "AI explains drink culture in Spain and helps with pronunciation of drink names"}, {"speaker": "<PERSON> (Waiter)", "text": "*returns with drink* <PERSON><PERSON><PERSON><PERSON> tiene. ¿Ya sabe qué va a tomar, o necesita unos minutos más?", "translation": "*returns with drink* Here you go. Do you know what you're going to have, or do you need a few more minutes?", "audio_notes": "Sound of glass being placed on table, expectant but patient tone", "cultural_notes": "Spanish waiters don't rush you, but they're efficient"}, {"speaker": "User", "text": "[User asks about menu]", "translation": "[User menu question]", "response_options": ["¿Qué me recomienda de la casa? (What do you recommend from the house? - shows trust)", "¿Qué es exactamente el 'cocido madrileño'? (What exactly is 'cocido madrileño'? - specific question)", "¿Tienen algo sin carne? (Do you have anything without meat? - dietary preference)"], "ai_agent_guidance": "AI helps user understand menu terms and cultural significance of dishes"}], "conversation_branches": [{"trigger": "If user asks about ingredients", "dialogue_path": "Detailed ingredient discussion with waiter explaining traditional preparation", "difficulty": "A2-B1", "cultural_importance": "High - shows respect for Spanish cuisine", "ai_agent_role": "Help user understand cooking terms and express preferences clearly"}, {"trigger": "If user has dietary restrictions", "dialogue_path": "Waiter suggests alternatives and explains modifications possible", "difficulty": "A2", "cultural_importance": "Medium - dietary restrictions are becoming more common in Spain", "ai_agent_role": "Guide user through polite explanation of restrictions"}, {"trigger": "If user wants to pay", "dialogue_path": "Payment process including tip discussion and farewell", "difficulty": "A2", "cultural_importance": "High - payment etiquette varies by region", "ai_agent_role": "Explain tipping culture and payment methods in Spain"}], "ai_agent_integration": {"agent_role": "Experienced Spanish conversation partner who has lived in Madrid", "intervention_points": ["When user struggles with menu vocabulary", "When cultural context is crucial (like meal timing)", "When pronunciation affects understanding", "When user needs encouragement to try complex phrases"], "encouragement_style": "Supportive and culturally informative, like a helpful local friend", "correction_approach": "Gentle correction with cultural explanation: 'In Spain, we usually say... because...'"}, "audio_integration": {"voice_characteristics": "Native Madrid Spanish speaker, male, 30s, professional waiter voice", "background_sounds": "Realistic restaurant ambiance: quiet conversations, clinking dishes, Spanish guitar music", "speech_patterns": "Natural Madrid accent with typical restaurant pace - clear but not slow", "pronunciation_focus": ["Rolling R's in 'carta'", "Soft C in 'recomienda'", "Intonation for questions"]}, "success_criteria": {"conversation_completion": "Successfully order a complete meal and handle payment", "cultural_appropriateness": "Use appropriate formality and show respect for Spanish dining culture", "vocabulary_usage": "Use at least 15 restaurant-specific terms naturally", "pronunciation_accuracy": "Achieve 75% accuracy on key food terms", "real_life_readiness": "Feel confident to eat at any Spanish restaurant independently"}, "follow_up_lessons": ["A2.5: Advanced Restaurant Vocabulary (wine terms, cooking methods)", "B1.1: Complaining <PERSON><PERSON><PERSON> (if food isn't right)", "B1.2: Making Reservations and Special Requests"], "practice_suggestions": ["Role-play with AI agent as different types of Spanish restaurants (formal, casual, tapas bar)", "Practice with voice recognition focusing on food pronunciation", "Record yourself ordering and compare with native speaker audio", "Use AR feature to practice reading Spanish menus"], "cultural_deep_dive": {"etiquette_rules": ["Wait to be seated, don't seat yourself", "It's normal to take time deciding - don't feel rushed", "Bread is often free but not always brought automatically", "Tipping 5-10% is appreciated but not mandatory"], "common_mistakes": ["Ordering dinner too early (before 8:30 PM)", "Being too informal with waits<PERSON><PERSON> initially", "Not asking about daily specials ('platos del día')", "Expecting immediate service - Spanish dining is relaxed"], "regional_variations": ["Madrid: More formal service, traditional dishes", "Barcelona: More international, casual atmosphere", "Andalusia: Very relaxed, lots of tapas culture"], "body_language": ["Make eye contact when ordering", "Slight nod when waiter explains dishes", "Hand gesture to call waiter (raised hand, not snapping)"]}, "gamification_elements": {"achievements": ["First Order: Successfully order your first Spanish meal", "Cultural Navigator: Use appropriate cultural etiquette", "Pronunciation Pro: Perfect pronunciation of 5 food terms", "Confident Diner: Complete meal without AI assistance"], "points_system": "Points for natural conversation flow, cultural appropriateness, and helping other learners", "challenges": ["Order a meal the waiter recommends without seeing it first", "Handle a situation where your first choice isn't available", "Have a brief conversation about the food with another diner"]}}