#!/usr/bin/env python3
"""
Realistic Human-like Simulation Generator
Creates detailed, practical conversations for real-life situations
Integrates with lessons, AI agents, and audio capabilities
"""

import logging
import json
import uuid
from datetime import datetime
from simulation_generator import supabase

class RealisticSimulationGenerator:
    """Generate realistic, human-like conversation simulations"""

    def __init__(self):
        self.generated_count = 0
        self.uploaded_count = 0
        self.failed_count = 0

    def generate_realistic_simulation(self, language: str, persona_key: str, scenario: str, cefr_level: str):
        """Generate a realistic, detailed simulation with full integration"""

        logging.info(f"🎭 Generating realistic simulation: {language} - {persona_key} - {scenario} - {cefr_level}")

        # Enhanced prompt for realistic, human-like conversations
        prompt = f"""Create a realistic, detailed language learning conversation simulation for {language}.

CONTEXT:
- Language: {language}
- Persona: {persona_key}
- Scenario: {scenario}
- CEFR Level: {cefr_level}
- Purpose: Real-life practical application

REQUIREMENTS FOR REALISTIC SIMULATION:
1. NATURAL CONVERSATION FLOW - Like real humans talking
2. PRACTICAL APPLICATION - Users can use this in real situations
3. DETAILED DIALOGUE - Full conversation with natural pauses, reactions
4. CULTURAL AUTHENTICITY - Real cultural context and etiquette
5. INTEGRATION READY - Connect to lessons, AI agents, audio
6. PROGRESSIVE DIFFICULTY - Match CEFR level appropriately

OUTPUT FORMAT (JSON):
{{
    "title": "Engaging title in {language} with English subtitle",
    "description": "Detailed description of the real-life scenario",
    "cefr_level": "{cefr_level}",
    "estimated_duration": 20,
    "scenario_type": "daily_life|shopping|work|health|education|social|transportation|cultural|technology|problem_solving",
    "real_life_context": {{
        "situation": "Detailed real-life situation description",
        "location": "Specific location (e.g., 'Local supermarket in Madrid')",
        "participants": ["User", "Shop assistant", "Other customer"],
        "cultural_context": "Important cultural background for this situation"
    }},
    "learning_objectives": [
        "Specific skill 1 (e.g., 'Ask for specific products politely')",
        "Specific skill 2 (e.g., 'Understand price negotiations')",
        "Specific skill 3 (e.g., 'Handle payment methods confidently')"
    ],
    "prerequisite_lessons": [
        "A1.2: Basic Shopping Vocabulary",
        "A1.3: Numbers and Prices",
        "A2.1: Polite Requests"
    ],
    "vocabulary_focus": [
        {{
            "word": "{language} word",
            "translation": "English translation",
            "pronunciation": "IPA or phonetic",
            "context": "When and how to use this word",
            "formality": "formal|informal|neutral",
            "example_sentence": "Natural example in context"
        }}
    ],
    "realistic_dialogue": [
        {{
            "speaker": "Shop Assistant",
            "text": "Natural greeting in {language}",
            "translation": "English translation",
            "audio_notes": "Tone: friendly, pace: normal",
            "cultural_notes": "Standard greeting in {language} shops"
        }},
        {{
            "speaker": "User",
            "text": "User response in {language}",
            "translation": "English translation",
            "response_options": [
                "Option 1: Polite response",
                "Option 2: Casual response",
                "Option 3: Formal response"
            ],
            "ai_agent_guidance": "The AI should encourage polite tone here"
        }},
        {{
            "speaker": "Shop Assistant",
            "text": "Follow-up question in {language}",
            "translation": "English translation",
            "audio_notes": "Tone: helpful, includes gesture",
            "difficulty_notes": "Uses {cefr_level} level vocabulary"
        }}
    ],
    "conversation_branches": [
        {{
            "trigger": "If user asks about price",
            "dialogue_path": "Price negotiation conversation",
            "difficulty": "{cefr_level}",
            "cultural_importance": "High - price discussion etiquette"
        }},
        {{
            "trigger": "If user needs help finding item",
            "dialogue_path": "Assistance request conversation",
            "difficulty": "{cefr_level}",
            "ai_agent_role": "Guide user through polite request formation"
        }}
    ],
    "ai_agent_integration": {{
        "agent_role": "Helpful {language} conversation partner",
        "intervention_points": [
            "When user struggles with pronunciation",
            "When cultural context is important",
            "When grammar correction is needed"
        ],
        "encouragement_style": "Supportive and culturally aware",
        "correction_approach": "Gentle with explanation"
    }},
    "audio_integration": {{
        "voice_characteristics": "Native {language} speaker, {persona_key} appropriate",
        "background_sounds": "Realistic {scenario} environment",
        "speech_patterns": "Natural pace with {language} rhythm",
        "pronunciation_focus": ["Key words that learners struggle with"]
    }},
    "success_criteria": {{
        "conversation_completion": "Complete the full scenario naturally",
        "cultural_appropriateness": "Use culturally correct responses",
        "vocabulary_usage": "Use at least 80% of target vocabulary",
        "pronunciation_accuracy": "Achieve 70% pronunciation accuracy",
        "real_life_readiness": "Confident to use in actual situation"
    }},
    "follow_up_lessons": [
        "{cefr_level}.X: Advanced {scenario} vocabulary",
        "Next level scenarios for progression"
    ],
    "practice_suggestions": [
        "Role-play with AI agent in different {scenario} situations",
        "Record yourself and compare with native audio",
        "Practice with voice recognition for pronunciation"
    ],
    "cultural_deep_dive": {{
        "etiquette_rules": ["Specific cultural rules for this situation"],
        "common_mistakes": ["What learners often get wrong"],
        "regional_variations": ["How this differs across {language} regions"],
        "body_language": ["Important non-verbal communication"]
    }},
    "gamification_elements": {{
        "achievements": ["Successfully complete scenario", "Perfect pronunciation", "Cultural awareness bonus"],
        "points_system": "Based on natural conversation flow and cultural appropriateness",
        "challenges": ["Complete without AI help", "Handle unexpected responses"]
    }}
}}

Make this simulation feel like a REAL conversation that happens between REAL people. Include natural hesitations, interruptions, and authentic human interactions. The user should feel prepared for the actual real-life situation after practicing this."""

        try:
            # Import here to avoid issues if not available
            from gemini_service import GeminiService
            gemini_service = GeminiService()

            response = gemini_service.make_gemini_request(prompt)

            # Parse JSON response
            if "```json" in response:
                json_str = response.split("```json")[1].split("```")[0].strip()
            elif "```" in response:
                json_str = response.split("```")[1].strip()
            else:
                json_str = response.strip()

            simulation = json.loads(json_str)

            # Validate and enhance
            simulation = self._validate_realistic_simulation(simulation, language, persona_key, cefr_level)

            logging.info(f"✅ Realistic simulation generated: {simulation['title']}")
            return simulation

        except Exception as e:
            logging.error(f"❌ Realistic simulation generation failed: {str(e)}")
            return None

    def _validate_realistic_simulation(self, simulation: dict, language: str, persona_key: str, cefr_level: str) -> dict:
        """Validate and enhance realistic simulation data"""

        # Ensure all required fields for realistic simulation
        if not simulation.get("title"):
            simulation["title"] = f"Real-life {language} Conversation"

        if not simulation.get("cefr_level"):
            simulation["cefr_level"] = cefr_level

        if not simulation.get("real_life_context"):
            simulation["real_life_context"] = {
                "situation": f"Practical {language} conversation",
                "location": f"{language}-speaking environment",
                "participants": ["User", "Native speaker"],
                "cultural_context": f"Authentic {language} cultural setting"
            }

        if not simulation.get("realistic_dialogue"):
            simulation["realistic_dialogue"] = [
                {
                    "speaker": "Native Speaker",
                    "text": f"Hello in {language}",
                    "translation": "Hello",
                    "audio_notes": "Friendly tone",
                    "cultural_notes": f"Standard {language} greeting"
                }
            ]

        if not simulation.get("ai_agent_integration"):
            simulation["ai_agent_integration"] = {
                "agent_role": f"Helpful {language} conversation partner",
                "intervention_points": ["When user needs help"],
                "encouragement_style": "Supportive",
                "correction_approach": "Gentle"
            }

        if not simulation.get("audio_integration"):
            simulation["audio_integration"] = {
                "voice_characteristics": f"Native {language} speaker",
                "background_sounds": "Realistic environment",
                "speech_patterns": f"Natural {language} rhythm",
                "pronunciation_focus": ["Key vocabulary"]
            }

        # Ensure CEFR integration
        if not simulation.get("prerequisite_lessons"):
            simulation["prerequisite_lessons"] = [f"{cefr_level}: Basic vocabulary"]

        if not simulation.get("follow_up_lessons"):
            next_level = self._get_next_cefr_level(cefr_level)
            simulation["follow_up_lessons"] = [f"{next_level}: Advanced scenarios"]

        return simulation

    def _get_next_cefr_level(self, current_level: str) -> str:
        """Get the next CEFR level"""
        levels = ["A1", "A2", "B1", "B2", "C1", "C2"]
        try:
            current_index = levels.index(current_level)
            if current_index < len(levels) - 1:
                return levels[current_index + 1]
            return current_level
        except ValueError:
            return "A2"

def test_realistic_simulation():
    """Test realistic simulation generation"""

    print("🎭 TESTING REALISTIC SIMULATION GENERATION")
    print("=" * 60)
    print("🎯 Creating human-like, practical conversations")
    print("🎯 Full integration with lessons, AI agents, audio")
    print("🎯 Real-life application ready")
    print()

    generator = RealisticSimulationGenerator()

    # Test realistic simulation
    simulation = generator.generate_realistic_simulation(
        language="Spanish",
        persona_key="traveler",
        scenario="Ordering food at a local restaurant",
        cefr_level="A2"
    )

    if simulation:
        print("✅ REALISTIC SIMULATION GENERATED!")
        print(f"📚 Title: {simulation['title']}")
        print(f"🎯 CEFR Level: {simulation['cefr_level']}")
        print(f"📍 Context: {simulation.get('real_life_context', {}).get('situation', 'N/A')}")
        print(f"🎭 Dialogue Exchanges: {len(simulation.get('realistic_dialogue', []))}")
        print(f"🤖 AI Integration: {simulation.get('ai_agent_integration', {}).get('agent_role', 'N/A')}")
        print(f"🔊 Audio Ready: {bool(simulation.get('audio_integration'))}")
        print(f"📖 Lesson Integration: {len(simulation.get('prerequisite_lessons', []))} prerequisites")

        # Save example
        with open(f"realistic_simulation_example_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json", "w") as f:
            json.dump(simulation, f, indent=2, ensure_ascii=False)

        print(f"\n💾 Example saved to file for review")
        return True
    else:
        print("❌ Failed to generate realistic simulation")
        return False

if __name__ == "__main__":
    test_realistic_simulation()
