#!/usr/bin/env python3
"""
Retry Failed Lessons Generator
Identifies and regenerates the 334 failed lessons with improved error handling
"""

import json
import time
import logging
from typing import Dict, List, Optional, Tuple
from comprehensive_lesson_generator import ComprehensiveLessonGenerator, LessonConfig
from supabase import create_client

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'retry_failed_lessons_{int(time.time())}.log'),
        logging.StreamHandler()
    ]
)

class FailedLessonRetrier:
    def __init__(self):
        self.config = LessonConfig()
        self.generator = ComprehensiveLessonGenerator(self.config)
        self.supabase = create_client(self.config.SUPABASE_URL, self.config.SUPABASE_KEY)
        
    def identify_missing_lessons(self) -> List[Tuple[str, str, str, str, str]]:
        """Identify which lessons are missing for each language/level combination"""
        missing_lessons = []
        
        for language_name, language_code in self.config.MISSING_LANGUAGES:
            logging.info(f"🔍 Checking {language_name} ({language_code}) for missing lessons...")
            
            # Get language ID
            lang_result = self.supabase.table("languages").select("id").eq("code", language_code).execute()
            if not lang_result.data:
                logging.error(f"❌ Language {language_name} not found in database")
                continue
                
            language_id = lang_result.data[0]["id"]
            
            for level in self.config.LEVELS:
                # Get learning path for this level
                path_result = self.supabase.table("learning_paths").select("id").eq("language_id", language_id).eq("level", level).execute()
                if not path_result.data:
                    logging.warning(f"⚠️ No learning path found for {language_name} {level}")
                    continue
                
                path_id = path_result.data[0]["id"]
                
                # Get existing lessons for this path
                lessons_result = self.supabase.table("lessons").select("sequence_order").eq("path_id", path_id).execute()
                existing_orders = {lesson["sequence_order"] for lesson in lessons_result.data}
                
                # Check which lessons are missing (should be 1-15)
                expected_orders = set(range(1, 16))
                missing_orders = expected_orders - existing_orders
                
                for missing_order in missing_orders:
                    topic = self.config.TOPICS_BY_LEVEL[level][missing_order - 1]
                    missing_lessons.append((language_name, language_code, level, topic, str(missing_order)))
                    
                logging.info(f"📊 {language_name} {level}: {len(missing_orders)} missing lessons")
        
        return missing_lessons
    
    def retry_failed_lessons(self):
        """Retry generating all failed lessons"""
        missing_lessons = self.identify_missing_lessons()
        
        if not missing_lessons:
            logging.info("🎉 No missing lessons found! All lessons are complete.")
            return
        
        total_missing = len(missing_lessons)
        logging.info(f"🎯 Found {total_missing} missing lessons to regenerate")
        
        success_count = 0
        failed_count = 0
        
        for i, (language_name, language_code, level, topic, sequence_order) in enumerate(missing_lessons, 1):
            progress = (i / total_missing) * 100
            logging.info(f"📝 [{i}/{total_missing}] ({progress:.1f}%) Retrying: {language_name} {level} - {topic}")
            
            # Generate lesson content
            content = self.generator._generate_lesson_content(language_name, language_code, level, topic)
            
            if content:
                # Get language ID and upload
                lang_result = self.supabase.table("languages").select("id").eq("code", language_code).execute()
                language_id = lang_result.data[0]["id"]
                
                success = self.generator._upload_to_supabase(content, language_id, level, int(sequence_order))
                if success:
                    success_count += 1
                    logging.info(f"✅ Successfully uploaded: {content['title']}")
                else:
                    failed_count += 1
                    logging.error(f"❌ Failed to upload: {topic}")
            else:
                failed_count += 1
                logging.error(f"❌ Failed to generate: {topic}")
            
            # Rate limiting
            time.sleep(3)
        
        # Final report
        logging.info("\n" + "="*60)
        logging.info("🎯 RETRY OPERATION COMPLETE")
        logging.info("="*60)
        logging.info(f"✅ Successfully Generated: {success_count} lessons")
        logging.info(f"❌ Still Failed: {failed_count} lessons")
        logging.info(f"📊 Retry Success Rate: {(success_count/total_missing)*100:.1f}%")
        logging.info("="*60)

def main():
    """Main execution function"""
    print("🔄 NIRA Failed Lessons Retrier")
    print("=" * 40)
    
    retrier = FailedLessonRetrier()
    
    # First, identify missing lessons
    print("🔍 Identifying missing lessons...")
    missing_lessons = retrier.identify_missing_lessons()
    
    if not missing_lessons:
        print("🎉 No missing lessons found! All lessons are complete.")
        return
    
    print(f"\n📊 Found {len(missing_lessons)} missing lessons")
    
    # Show breakdown by language
    from collections import defaultdict
    by_language = defaultdict(int)
    for language_name, _, _, _, _ in missing_lessons:
        by_language[language_name] += 1
    
    print("\n📈 Missing lessons by language:")
    for language, count in by_language.items():
        print(f"   {language}: {count} lessons")
    
    confirm = input(f"\n🤔 Retry generating {len(missing_lessons)} failed lessons? (y/N): ").strip().lower()
    if confirm != 'y':
        print("❌ Retry cancelled")
        return
    
    # Start retry process
    try:
        retrier.retry_failed_lessons()
    except KeyboardInterrupt:
        print("\n⚠️ Retry interrupted by user")
    except Exception as e:
        print(f"\n❌ Retry failed: {str(e)}")
        logging.error(f"Fatal error: {str(e)}")

if __name__ == "__main__":
    main()
