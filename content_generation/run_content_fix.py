#!/usr/bin/env python3
"""
Quick runner script for NIRA content fixes
"""

import os
import sys
from comprehensive_content_fixer import NIRAContentFixer

def run_quick_test():
    """Run a quick test to verify everything works"""
    print("🧪 Running quick test...")
    
    fixer = NIRAContentFixer()
    
    # Test Gemini API connection
    try:
        test_content = fixer.generate_enhanced_lesson_content("French", "A1", "Basic Greetings")
        if test_content:
            print("✅ Gemini API connection successful")
            print(f"✅ Generated test lesson: {test_content.get('title', 'Unknown')}")
            return True
        else:
            print("❌ Failed to generate test content")
            return False
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        return False

def run_placeholder_fix_only():
    """Run only the placeholder content fix"""
    print("🔧 Running placeholder content fix only...")
    
    fixer = NIRAContentFixer()
    fixer.fix_placeholder_content()
    
    print(f"✅ Fixed {fixer.fixed_count} lessons")
    print(f"❌ Failed {fixer.failed_count} lessons")

def run_vocabulary_creation_only():
    """Run only vocabulary creation for missing languages"""
    print("📚 Running vocabulary creation only...")
    
    fixer = NIRAContentFixer()
    fixer.create_missing_vocabulary()
    
    print(f"✅ Created {fixer.created_count} vocabulary entries")
    print(f"❌ Failed {fixer.failed_count} operations")

def run_level_gaps_only():
    """Run only level gap filling"""
    print("📈 Running level gap filling only...")
    
    fixer = NIRAContentFixer()
    fixer.fill_level_gaps()
    
    print(f"✅ Created {fixer.created_count} lessons")
    print(f"❌ Failed {fixer.failed_count} operations")

def run_full_fix():
    """Run the complete comprehensive fix"""
    print("🚀 Running comprehensive content fix...")
    
    fixer = NIRAContentFixer()
    fixer.run_comprehensive_fix()

def main():
    """Main menu for running different fix operations"""
    
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == "test":
            run_quick_test()
        elif command == "placeholder":
            run_placeholder_fix_only()
        elif command == "vocabulary":
            run_vocabulary_creation_only()
        elif command == "levels":
            run_level_gaps_only()
        elif command == "full":
            run_full_fix()
        else:
            print(f"Unknown command: {command}")
            print_usage()
    else:
        print_menu()

def print_menu():
    """Print interactive menu"""
    print("🚀 NIRA Content Fixer")
    print("Choose an option:")
    print("1. Quick test (verify API connection)")
    print("2. Fix placeholder content only")
    print("3. Create missing vocabulary only")
    print("4. Fill level gaps only")
    print("5. Run comprehensive fix (all issues)")
    print("0. Exit")
    
    choice = input("\nEnter your choice (0-5): ")
    
    if choice == "1":
        run_quick_test()
    elif choice == "2":
        run_placeholder_fix_only()
    elif choice == "3":
        run_vocabulary_creation_only()
    elif choice == "4":
        run_level_gaps_only()
    elif choice == "5":
        run_full_fix()
    elif choice == "0":
        print("Goodbye!")
    else:
        print("Invalid choice. Please try again.")
        print_menu()

def print_usage():
    """Print command line usage"""
    print("Usage:")
    print("  python run_content_fix.py test        # Quick test")
    print("  python run_content_fix.py placeholder # Fix placeholder content")
    print("  python run_content_fix.py vocabulary  # Create missing vocabulary")
    print("  python run_content_fix.py levels      # Fill level gaps")
    print("  python run_content_fix.py full        # Run comprehensive fix")

if __name__ == "__main__":
    # Check if API key is set
    if os.getenv("GEMINI_API_KEY", "YOUR_GEMINI_API_KEY_HERE") == "YOUR_GEMINI_API_KEY_HERE":
        print("❌ ERROR: Please set your GEMINI_API_KEY environment variable")
        print("   export GEMINI_API_KEY='your-actual-api-key'")
        sys.exit(1)
    
    main()
