#!/usr/bin/env python3
"""
Run Tier 1 Mini Sample Generation
Generates 10 simulations for each of the 21 Tier 1 languages
Total: 210 simulations
"""

import logging
import time
from datetime import datetime
from batch_simulation_generator import BatchSimulationGenerator

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'tier1_mini_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)

# Tier 1 Languages (21 languages with full features)
TIER1_LANGUAGES = [
    # Major European Languages
    "English", "Spanish", "French", "German", "Italian", "Portuguese", "Dutch", 
    "Swedish", "Norwegian", "Danish", "Polish",
    
    # Major Asian Languages  
    "Japanese", "Korean", "Chinese", "Hindi", "Vietnamese", "Thai", "Indonesian",
    
    # Major World Languages
    "Arabic", "Russian", "Turkish", "Hebrew"
]

def run_tier1_mini():
    """Run Tier 1 mini sample generation"""
    
    print("🧪 TIER 1 MINI SAMPLE GENERATION")
    print("=" * 50)
    print(f"📊 Languages: {len(TIER1_LANGUAGES)}")
    print(f"📊 Target: {len(TIER1_LANGUAGES) * 10} simulations (10 per language)")
    print(f"📊 Estimated time: {len(TIER1_LANGUAGES) * 10 * 1.0 / 60:.1f} hours")
    print()
    
    batch_gen = BatchSimulationGenerator()
    start_time = time.time()
    
    total_generated = 0
    total_failed = 0
    completed_languages = 0
    
    logging.info(f"🚀 Starting Tier 1 mini generation")
    logging.info(f"📊 Target: {len(TIER1_LANGUAGES) * 10} simulations across {len(TIER1_LANGUAGES)} languages")
    
    # Generate 10 simulations for each Tier 1 language
    for i, language in enumerate(TIER1_LANGUAGES, 1):
        logging.info(f"\n🔥 [{i}/{len(TIER1_LANGUAGES)}] Starting {language}")
        logging.info(f"📊 Target: 10 simulations")
        
        progress = (i - 1) / len(TIER1_LANGUAGES) * 100
        logging.info(f"📈 Overall progress: {progress:.1f}%")
        
        # Reset stats for this language
        batch_gen.generator.generated_count = 0
        batch_gen.generator.reviewed_count = 0
        batch_gen.generator.uploaded_count = 0
        batch_gen.generator.failed_count = 0
        
        # Generate 10 simulations (2 per persona)
        batch_gen.generate_language_batch(language, 2)
        
        # Update totals
        language_generated = batch_gen.generator.uploaded_count
        language_failed = batch_gen.generator.failed_count
        
        total_generated += language_generated
        total_failed += language_failed
        completed_languages += 1
        
        # Language completion report
        success_rate = (language_generated / max(1, language_generated + language_failed)) * 100
        logging.info(f"✅ {language} complete!")
        logging.info(f"   Generated: {language_generated}/10")
        logging.info(f"   Success rate: {success_rate:.1f}%")
        
        # Overall progress update
        overall_progress = (completed_languages / len(TIER1_LANGUAGES)) * 100
        logging.info(f"🌍 Tier 1 progress: {completed_languages}/{len(TIER1_LANGUAGES)} languages ({overall_progress:.1f}%)")
        logging.info(f"📊 Total generated: {total_generated}/{len(TIER1_LANGUAGES) * 10}")
    
    # Final report
    elapsed_time = time.time() - start_time
    
    print("\n" + "=" * 50)
    print("🎉 TIER 1 MINI GENERATION COMPLETE!")
    print("=" * 50)
    
    logging.info(f"🎉 TIER 1 MINI GENERATION COMPLETE!")
    logging.info(f"📊 Final Statistics:")
    logging.info(f"   Languages completed: {completed_languages}/{len(TIER1_LANGUAGES)}")
    logging.info(f"   Total generated: {total_generated}")
    logging.info(f"   Total failed: {total_failed}")
    logging.info(f"   Total time: {elapsed_time/3600:.1f} hours")
    
    if total_generated > 0:
        avg_time = elapsed_time / total_generated
        overall_success_rate = (total_generated / max(1, total_generated + total_failed)) * 100
        
        logging.info(f"   Average per simulation: {avg_time:.1f} seconds")
        logging.info(f"   Overall success rate: {overall_success_rate:.1f}%")
        
        # Estimate completion percentage
        target = len(TIER1_LANGUAGES) * 10
        completion_rate = (total_generated / target) * 100
        logging.info(f"   Target completion: {completion_rate:.1f}%")
    
    print(f"\n🌟 Generated {total_generated} high-quality simulations!")
    print(f"🚀 Tier 1 languages now have sample conversation practice!")
    
    if total_failed > 0:
        logging.warning(f"⚠️  {total_failed} simulations failed. Check logs for details.")

if __name__ == "__main__":
    run_tier1_mini()
