#!/usr/bin/env python3
"""
NIRA Tier 1 Production Generation
Generates 250 simulations for each of the 21 Tier 1 languages
Total: 5,250 simulations

This is the production script for creating the complete Tier 1 simulation library.
"""

import logging
import time
from datetime import datetime
from batch_simulation_generator import BatchSimulationGenerator

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'tier1_production_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)

# Tier 1 Languages (21 languages with full features including voice/live chat)
TIER1_LANGUAGES = [
    # Major European Languages (11)
    "English", "Spanish", "French", "German", "Italian", "Portuguese", "Dutch", 
    "Swedish", "Norwegian", "Danish", "Polish",
    
    # Major Asian Languages (7)
    "Japanese", "Korean", "Chinese", "Hindi", "Vietnamese", "Thai", "Indonesian",
    
    # Major World Languages (4)
    "Arabic", "Russian", "Turkish", "Hebrew"
]

def run_tier1_production():
    """Run complete Tier 1 production generation"""
    
    print("🌍 TIER 1 PRODUCTION GENERATION")
    print("=" * 60)
    print(f"📊 Languages: {len(TIER1_LANGUAGES)}")
    print(f"📊 Target: {len(TIER1_LANGUAGES) * 250:,} simulations (250 per language)")
    print(f"📊 Estimated time: 15-20 hours")
    print(f"📊 Estimated cost: $300-500")
    print()
    print("🎯 This will create the complete Tier 1 simulation library:")
    print("   • 50 simulations per persona (5 personas)")
    print("   • 10 scenario categories per persona")
    print("   • 5 simulations per category")
    print("   • Full cultural authenticity review")
    print()
    
    # Show language breakdown
    print("📋 Tier 1 Languages:")
    print("   European:", ", ".join(TIER1_LANGUAGES[:11]))
    print("   Asian:", ", ".join(TIER1_LANGUAGES[11:18]))
    print("   World:", ", ".join(TIER1_LANGUAGES[18:22]))
    print()
    
    batch_gen = BatchSimulationGenerator()
    start_time = time.time()
    
    total_generated = 0
    total_failed = 0
    completed_languages = 0
    
    logging.info(f"🚀 Starting Tier 1 production generation")
    logging.info(f"📊 Target: {len(TIER1_LANGUAGES) * 250:,} simulations across {len(TIER1_LANGUAGES)} languages")
    
    # Generate 250 simulations for each Tier 1 language
    for i, language in enumerate(TIER1_LANGUAGES, 1):
        logging.info(f"\n🔥 [{i}/{len(TIER1_LANGUAGES)}] Starting {language}")
        logging.info(f"📊 Target: 250 simulations (50 per persona)")
        
        progress = (i - 1) / len(TIER1_LANGUAGES) * 100
        logging.info(f"📈 Overall progress: {progress:.1f}%")
        
        # Estimate remaining time
        if completed_languages > 0:
            elapsed = time.time() - start_time
            avg_time_per_language = elapsed / completed_languages
            remaining_languages = len(TIER1_LANGUAGES) - completed_languages
            estimated_remaining = (remaining_languages * avg_time_per_language) / 3600
            logging.info(f"⏱️  Estimated remaining: {estimated_remaining:.1f} hours")
        
        # Reset stats for this language
        batch_gen.generator.generated_count = 0
        batch_gen.generator.reviewed_count = 0
        batch_gen.generator.uploaded_count = 0
        batch_gen.generator.failed_count = 0
        
        # Generate full language simulations (250 total)
        batch_gen.generate_full_language_simulations(language)
        
        # Update totals
        language_generated = batch_gen.generator.uploaded_count
        language_failed = batch_gen.generator.failed_count
        
        total_generated += language_generated
        total_failed += language_failed
        completed_languages += 1
        
        # Language completion report
        success_rate = (language_generated / max(1, language_generated + language_failed)) * 100
        logging.info(f"✅ {language} complete!")
        logging.info(f"   Generated: {language_generated}/250")
        logging.info(f"   Success rate: {success_rate:.1f}%")
        
        # Overall progress update
        overall_progress = (completed_languages / len(TIER1_LANGUAGES)) * 100
        logging.info(f"🌍 Tier 1 progress: {completed_languages}/{len(TIER1_LANGUAGES)} languages ({overall_progress:.1f}%)")
        logging.info(f"📊 Total generated: {total_generated:,}/{len(TIER1_LANGUAGES) * 250:,}")
        
        # Save checkpoint
        if completed_languages % 5 == 0:
            logging.info(f"💾 Checkpoint: {completed_languages} languages completed")
    
    # Final report
    elapsed_time = time.time() - start_time
    
    print("\n" + "=" * 60)
    print("🎉 TIER 1 PRODUCTION COMPLETE!")
    print("=" * 60)
    
    logging.info(f"🎉 TIER 1 PRODUCTION COMPLETE!")
    logging.info(f"📊 Final Statistics:")
    logging.info(f"   Languages completed: {completed_languages}/{len(TIER1_LANGUAGES)}")
    logging.info(f"   Total generated: {total_generated:,}")
    logging.info(f"   Total failed: {total_failed:,}")
    logging.info(f"   Total time: {elapsed_time/3600:.1f} hours")
    
    if total_generated > 0:
        avg_time = elapsed_time / total_generated
        overall_success_rate = (total_generated / max(1, total_generated + total_failed)) * 100
        
        logging.info(f"   Average per simulation: {avg_time:.1f} seconds")
        logging.info(f"   Overall success rate: {overall_success_rate:.1f}%")
        
        # Estimate completion percentage
        target = len(TIER1_LANGUAGES) * 250
        completion_rate = (total_generated / target) * 100
        logging.info(f"   Target completion: {completion_rate:.1f}%")
    
    print(f"\n🌟 Generated {total_generated:,} high-quality simulations!")
    print(f"🚀 NIRA now has comprehensive conversation practice for all Tier 1 languages!")
    print(f"🎭 Each language has 250 simulations across 5 personas and 10 categories!")
    
    if total_failed > 0:
        logging.warning(f"⚠️  {total_failed:,} simulations failed. Check logs for details.")
    
    # Success metrics
    if total_generated >= len(TIER1_LANGUAGES) * 200:  # 80% success rate
        print("\n🏆 TIER 1 GENERATION SUCCESSFUL!")
        print("✅ Ready for production deployment")
    elif total_generated >= len(TIER1_LANGUAGES) * 150:  # 60% success rate
        print("\n⚠️  TIER 1 GENERATION PARTIALLY SUCCESSFUL")
        print("🔧 Consider running retry script for failed simulations")
    else:
        print("\n❌ TIER 1 GENERATION NEEDS ATTENTION")
        print("🔧 Review logs and retry failed languages")

def run_tier1_sample():
    """Run Tier 1 sample generation (50 per language)"""
    
    print("🧪 TIER 1 SAMPLE GENERATION")
    print("=" * 50)
    print(f"📊 Languages: {len(TIER1_LANGUAGES)}")
    print(f"📊 Target: {len(TIER1_LANGUAGES) * 50:,} simulations (50 per language)")
    print(f"📊 Estimated time: 3-4 hours")
    print()
    
    batch_gen = BatchSimulationGenerator()
    start_time = time.time()
    
    total_generated = 0
    total_failed = 0
    completed_languages = 0
    
    logging.info(f"🧪 Starting Tier 1 sample generation")
    logging.info(f"📊 Target: {len(TIER1_LANGUAGES) * 50:,} simulations across {len(TIER1_LANGUAGES)} languages")
    
    # Generate 50 simulations for each Tier 1 language
    for i, language in enumerate(TIER1_LANGUAGES, 1):
        logging.info(f"\n🔥 [{i}/{len(TIER1_LANGUAGES)}] Starting {language}")
        logging.info(f"📊 Target: 50 simulations (10 per persona)")
        
        # Reset stats for this language
        batch_gen.generator.generated_count = 0
        batch_gen.generator.reviewed_count = 0
        batch_gen.generator.uploaded_count = 0
        batch_gen.generator.failed_count = 0
        
        # Generate 50 simulations (10 per persona)
        batch_gen.generate_language_batch(language, 10)
        
        # Update totals
        language_generated = batch_gen.generator.uploaded_count
        language_failed = batch_gen.generator.failed_count
        
        total_generated += language_generated
        total_failed += language_failed
        completed_languages += 1
        
        # Language completion report
        success_rate = (language_generated / max(1, language_generated + language_failed)) * 100
        logging.info(f"✅ {language} complete!")
        logging.info(f"   Generated: {language_generated}/50")
        logging.info(f"   Success rate: {success_rate:.1f}%")
    
    # Final report
    elapsed_time = time.time() - start_time
    
    print(f"\n🌟 Generated {total_generated:,} high-quality simulations!")
    print(f"🚀 Tier 1 sample generation complete!")
    
    logging.info(f"📊 Sample generation complete: {total_generated:,} simulations in {elapsed_time/3600:.1f} hours")

if __name__ == "__main__":
    print("🎭 NIRA Tier 1 Generator")
    print("=" * 30)
    print("1. Full Production (5,250 simulations)")
    print("2. Sample Generation (1,050 simulations)")
    print("3. Exit")
    
    choice = input("\nEnter your choice (1-3): ")
    
    if choice == "1":
        print("\n⚠️  WARNING: This will generate 5,250 simulations!")
        print("⚠️  Estimated time: 15-20 hours")
        print("⚠️  Estimated cost: $300-500")
        confirm = input("\n🚀 Proceed with full production? (yes/no): ")
        if confirm.lower() == 'yes':
            run_tier1_production()
        else:
            print("❌ Production cancelled.")
    
    elif choice == "2":
        print("\n🧪 Starting sample generation...")
        run_tier1_sample()
    
    elif choice == "3":
        print("Goodbye!")
    
    else:
        print("Invalid choice.")
