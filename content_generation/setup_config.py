#!/usr/bin/env python3
"""
Configuration setup script for NIRA Comprehensive Lesson Generator
Helps you configure API keys and test connections
"""

import os
import sys
import requests
from supabase import create_client

def get_api_keys():
    """Get API keys from user input"""
    print("🔑 API Key Configuration")
    print("=" * 40)
    
    # Get Gemini API Key
    gemini_key = input("Enter your Gemini API Key: ").strip()
    if not gemini_key or len(gemini_key) < 30:
        print("❌ Invalid Gemini API key")
        return None, None
    
    # Get Supabase credentials
    supabase_url = "https://lyaojebttnqilmdosmjk.supabase.co"
    supabase_key = input("Enter your Supabase API Key: ").strip()
    if not supabase_key or len(supabase_key) < 50:
        print("❌ Invalid Supabase API key")
        return None, None
    
    return gemini_key, supabase_key

def test_gemini_connection(api_key):
    """Test Gemini API connection"""
    print("\n🧪 Testing Gemini API connection...")
    
    url = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key={api_key}"
    
    payload = {
        "contents": [{"parts": [{"text": "Say hello in JSON format: {\"message\": \"hello\"}"}]}],
        "generationConfig": {
            "temperature": 0.1,
            "maxOutputTokens": 100,
            "responseMimeType": "application/json"
        }
    }
    
    try:
        response = requests.post(url, json=payload, timeout=10)
        if response.ok:
            print("✅ Gemini API connection successful!")
            return True
        else:
            print(f"❌ Gemini API error: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Gemini API connection failed: {str(e)}")
        return False

def test_supabase_connection(url, key):
    """Test Supabase connection"""
    print("\n🧪 Testing Supabase connection...")
    
    try:
        supabase = create_client(url, key)
        result = supabase.table("languages").select("count").execute()
        print("✅ Supabase connection successful!")
        return True
    except Exception as e:
        print(f"❌ Supabase connection failed: {str(e)}")
        return False

def update_config_file(gemini_key, supabase_key):
    """Update the configuration in the main script"""
    config_file = "comprehensive_lesson_generator.py"
    
    try:
        with open(config_file, 'r') as f:
            content = f.read()
        
        # Replace API keys
        content = content.replace(
            'GEMINI_API_KEY: str = "AIzaSyAYasKJOJOJOJOJOJOJOJOJOJOJOJOJOJO"',
            f'GEMINI_API_KEY: str = "{gemini_key}"'
        )
        content = content.replace(
            'SUPABASE_KEY: str = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."',
            f'SUPABASE_KEY: str = "{supabase_key}"'
        )
        
        with open(config_file, 'w') as f:
            f.write(content)
        
        print(f"✅ Updated {config_file} with your API keys")
        return True
        
    except Exception as e:
        print(f"❌ Failed to update config file: {str(e)}")
        return False

def main():
    """Main setup function"""
    print("🚀 NIRA Lesson Generator Setup")
    print("=" * 40)
    
    # Get API keys
    gemini_key, supabase_key = get_api_keys()
    if not gemini_key or not supabase_key:
        print("❌ Setup failed - invalid API keys")
        return
    
    # Test connections
    gemini_ok = test_gemini_connection(gemini_key)
    supabase_ok = test_supabase_connection("https://lyaojebttnqilmdosmjk.supabase.co", supabase_key)
    
    if not gemini_ok or not supabase_ok:
        print("\n❌ Setup failed - API connections failed")
        return
    
    # Update config file
    if update_config_file(gemini_key, supabase_key):
        print("\n🎉 Setup complete!")
        print("You can now run: python comprehensive_lesson_generator.py")
    else:
        print("\n❌ Setup failed - could not update config file")

if __name__ == "__main__":
    main()
