#!/usr/bin/env python3
"""
Show examples of generated simulations
"""

import os
os.environ['SUPABASE_URL'] = 'https://lyaojebttnqilmdosmjk.supabase.co'
os.environ['SUPABASE_ANON_KEY'] = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c'

from simulation_generator import supabase
import json

def show_simulation_examples():
    """Show examples of generated simulations"""
    
    print("🎭 NIRA SIMULATION EXAMPLES")
    print("=" * 60)
    print("Generated using Gemini → OpenAI → Supabase pipeline")
    print()
    
    try:
        # Get recent simulations
        result = supabase.table('simulations').select('*').order('created_at', desc=True).limit(8).execute()
        
        if not result.data:
            print("❌ No simulations found in database")
            return
        
        print(f"📊 Found {len(result.data)} recent simulations:")
        print()
        
        for i, sim in enumerate(result.data, 1):
            print(f"🎯 SIMULATION {i}")
            print("-" * 40)
            print(f"📚 Title: {sim['title']}")
            print(f"📝 Description: {sim['description']}")
            print(f"🎭 Category: {sim['scenario_type']}")
            print(f"⏱️  Duration: {sim['estimated_duration']} minutes")
            print(f"📈 Difficulty: {sim['difficulty_level']}")
            
            # Show learning objectives
            objectives = sim.get('learning_objectives', [])
            if objectives:
                print(f"🎯 Learning Objectives ({len(objectives)}):")
                for obj in objectives[:3]:  # Show first 3
                    print(f"   • {obj}")
                if len(objectives) > 3:
                    print(f"   • ... and {len(objectives) - 3} more")
            
            # Show vocabulary focus
            vocab = sim.get('vocabulary_focus', [])
            if vocab:
                print(f"📚 Vocabulary Focus ({len(vocab)} words):")
                for word in vocab[:3]:  # Show first 3
                    if isinstance(word, dict):
                        print(f"   • {word.get('word', 'N/A')} - {word.get('translation', 'N/A')}")
                if len(vocab) > 3:
                    print(f"   • ... and {len(vocab) - 3} more words")
            
            # Show conversation starters
            starters = sim.get('conversation_starters', [])
            if starters:
                print(f"💬 Conversation Starters ({len(starters)}):")
                for starter in starters[:2]:  # Show first 2
                    print(f"   • \"{starter}\"")
                if len(starters) > 2:
                    print(f"   • ... and {len(starters) - 2} more")
            
            # Show cultural notes
            cultural_notes = sim.get('cultural_notes', '')
            if cultural_notes:
                print(f"🌍 Cultural Notes:")
                print(f"   {cultural_notes[:150]}{'...' if len(cultural_notes) > 150 else ''}")
            
            print(f"🗓️  Created: {sim['created_at'][:19]}")
            print()
        
        # Show statistics
        print("📊 GENERATION STATISTICS")
        print("-" * 30)
        
        # Count by persona
        persona_counts = {}
        for sim in result.data:
            persona_id = sim.get('persona_id', 'unknown')
            persona_counts[persona_id] = persona_counts.get(persona_id, 0) + 1
        
        print(f"✅ Total simulations shown: {len(result.data)}")
        print(f"🎭 Unique personas: {len(persona_counts)}")
        
        # Get persona names
        try:
            personas_result = supabase.table('simulation_personas').select('id, display_name').execute()
            persona_names = {p['id']: p['display_name'] for p in personas_result.data}
            
            print("👥 Simulations by persona:")
            for persona_id, count in persona_counts.items():
                name = persona_names.get(persona_id, f"ID: {persona_id[:8]}...")
                print(f"   • {name}: {count} simulations")
        except:
            print("   (Persona details not available)")
        
        print()
        print("🎉 All simulations include:")
        print("   ✅ Gemini-generated authentic conversations")
        print("   ✅ OpenAI cultural authenticity review")
        print("   ✅ Comprehensive vocabulary and grammar focus")
        print("   ✅ Cultural context and etiquette notes")
        print("   ✅ Multiple response options for practice")
        print("   ✅ Progressive difficulty levels")
        
    except Exception as e:
        print(f"❌ Error retrieving simulations: {str(e)}")

def show_detailed_example():
    """Show one detailed example"""
    
    print("\n🔍 DETAILED SIMULATION EXAMPLE")
    print("=" * 50)
    
    try:
        # Get one simulation with full details
        result = supabase.table('simulations').select('*').order('created_at', desc=True).limit(1).execute()
        
        if not result.data:
            print("❌ No simulations found")
            return
        
        sim = result.data[0]
        
        print(f"📚 {sim['title']}")
        print(f"📝 {sim['description']}")
        print()
        
        print("🎯 LEARNING OBJECTIVES:")
        for i, obj in enumerate(sim.get('learning_objectives', []), 1):
            print(f"   {i}. {obj}")
        print()
        
        print("📚 VOCABULARY FOCUS:")
        for word in sim.get('vocabulary_focus', [])[:5]:
            if isinstance(word, dict):
                print(f"   • {word.get('word', 'N/A')}")
                print(f"     Translation: {word.get('translation', 'N/A')}")
                print(f"     Context: {word.get('context', 'N/A')}")
                print()
        
        print("💬 CONVERSATION STARTERS:")
        for i, starter in enumerate(sim.get('conversation_starters', []), 1):
            print(f"   {i}. \"{starter}\"")
        print()
        
        print("🌍 CULTURAL NOTES:")
        print(f"   {sim.get('cultural_notes', 'No cultural notes available')}")
        print()
        
        print("📊 SUCCESS CRITERIA:")
        criteria = sim.get('success_criteria', {})
        for key, value in criteria.items():
            print(f"   • {key.replace('_', ' ').title()}: {value}")
        
    except Exception as e:
        print(f"❌ Error retrieving detailed example: {str(e)}")

if __name__ == "__main__":
    show_simulation_examples()
    show_detailed_example()
