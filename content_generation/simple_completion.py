#!/usr/bin/env python3
"""
Simple Tier 1 Completion Script
Complete remaining simulations using existing infrastructure
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from production_realistic_generator import ProductionRealisticGenerator
import time

def complete_remaining():
    """Complete remaining simulations"""
    
    print("🎯 COMPLETING TIER 1 SIMULATIONS")
    print("=" * 50)
    
    generator = ProductionRealisticGenerator()
    
    # Languages that need 1-2 more simulations
    completion_tasks = [
        ("English", "Getting emergency dental treatment", "beginner_enthusiast", "B1"),
        ("Italian", "Getting emergency dental treatment", "beginner_enthusiast", "B1"),
        ("Chinese", "Getting emergency dental treatment", "beginner_enthusiast", "B1"),
        ("Korean", "Getting emergency dental treatment", "beginner_enthusiast", "B1"),
        ("Arabic", "Getting emergency dental treatment", "beginner_enthusiast", "B1"),
        ("Indonesian", "Getting emergency dental treatment", "beginner_enthusiast", "B1"),
        ("Vietnamese", "Getting emergency dental treatment", "beginner_enthusiast", "B1"),
        ("Portuguese", "Reporting noise complaint to authorities", "traveler", "B1"),
        ("Portuguese", "Getting emergency dental treatment", "beginner_enthusiast", "B1"),
        ("Dutch", "Reporting noise complaint to authorities", "traveler", "B1"),
        ("Dutch", "Getting emergency dental treatment", "beginner_enthusiast", "B1"),
        ("Swedish", "Reporting noise complaint to authorities", "traveler", "B1"),
        ("Swedish", "Getting emergency dental treatment", "beginner_enthusiast", "B1"),
    ]
    
    # Missing languages - full 50 simulations each
    missing_languages = ["Turkish", "Danish", "Polish", "Hebrew"]
    
    scenarios = [
        "Ordering dinner at a traditional restaurant",
        "Shopping for groceries at local market",
        "Checking into hotel and handling room issues",
        "Asking for directions in busy city center",
        "Buying train tickets and understanding schedules",
        "Shopping for clothes and asking about sizes",
        "Opening bank account as foreigner",
        "Visiting pharmacy for medicine and advice",
        "Job interview for local position",
        "Reporting theft to police station"
    ]
    
    personas = ["traveler", "beginner_enthusiast", "busy_professional"]
    cefr_levels = ["A1", "A2", "B1"]
    
    completed = 0
    
    # Step 1: Complete existing languages
    print("🔥 STEP 1: COMPLETING EXISTING LANGUAGES")
    for language, scenario, persona, cefr in completion_tasks:
        print(f"📝 {language}: {scenario}")
        
        result = generator.generate_realistic_simulation_cost_effective(
            language=language,
            persona_key=persona,
            scenario=scenario,
            cefr_level=cefr
        )
        
        if result:
            print(f"✅ Success: {result['title']}")
            completed += 1
        else:
            print(f"❌ Failed: {scenario}")
        
        time.sleep(2)
    
    # Step 2: Generate missing languages (first 10 simulations each)
    print(f"\n🔥 STEP 2: GENERATING MISSING LANGUAGES (10 each)")
    for language in missing_languages:
        print(f"\n🌍 {language}: Generating 10 simulations")
        
        for i in range(10):
            scenario = scenarios[i % len(scenarios)]
            persona = personas[i % len(personas)]
            cefr = cefr_levels[i % len(cefr_levels)]
            
            print(f"📝 {language} ({i+1}/10): {scenario}")
            
            result = generator.generate_realistic_simulation_cost_effective(
                language=language,
                persona_key=persona,
                scenario=scenario,
                cefr_level=cefr
            )
            
            if result:
                print(f"✅ Success: {result['title']}")
                completed += 1
            else:
                print(f"❌ Failed: {scenario}")
            
            time.sleep(2)
    
    print(f"\n🎉 COMPLETION SUMMARY")
    print(f"📊 Simulations added: {completed}")
    print(f"💰 Total cost: ${generator.cost_tracking['total_cost']:.3f}")
    print("✅ Tier 1 completion in progress!")

if __name__ == "__main__":
    complete_remaining()
