#!/usr/bin/env python3
"""
Simple NIRA Content Fixer
Fixes critical issues identified in the database audit using working Supabase client
"""

import json
import os
import time
import logging
import requests
from datetime import datetime
from typing import Dict, List, Optional

# Configuration
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY", "YOUR_GEMINI_API_KEY_HERE")

# Logging setup
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'simple_fix_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)

class SimpleContentFixer:
    """Simple content fixer using direct HTTP requests"""
    
    def __init__(self):
        self.fixed_count = 0
        self.created_count = 0
        self.failed_count = 0
        self.gemini_url = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key={GEMINI_API_KEY}"
        
        # Supabase configuration
        self.supabase_url = "https://lyaojebttnqilmdosmjk.supabase.co"
        self.supabase_key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"
        self.headers = {
            "apikey": self.supabase_key,
            "Authorization": f"Bearer {self.supabase_key}",
            "Content-Type": "application/json",
            "Prefer": "return=representation"
        }

    def test_connection(self) -> bool:
        """Test Gemini API and Supabase connections"""
        try:
            # Test Gemini API
            test_content = self.generate_lesson_content("French", "A1", "Basic Greetings")
            if test_content:
                logging.info("✅ Gemini API connection successful")
                logging.info(f"✅ Generated test lesson: {test_content.get('title', 'Unknown')}")
                return True
            else:
                logging.error("❌ Failed to generate test content")
                return False
        except Exception as e:
            logging.error(f"❌ Connection test failed: {str(e)}")
            return False

    def fix_placeholder_content(self):
        """Fix lessons with placeholder content"""
        try:
            # Get lessons with placeholder content using direct SQL query
            url = f"{self.supabase_url}/rest/v1/rpc/get_placeholder_lessons"
            response = requests.post(url, headers=self.headers)
            
            if response.status_code == 404:
                # Fallback: get all lessons and filter manually
                logging.info("Using fallback method to find placeholder content...")
                url = f"{self.supabase_url}/rest/v1/lessons"
                params = {"select": "*", "limit": "1000"}
                response = requests.get(url, headers=self.headers, params=params)
                response.raise_for_status()
                
                all_lessons = response.json()
                placeholder_lessons = []
                
                for lesson in all_lessons:
                    content_str = json.dumps(lesson.get("content_metadata", {}))
                    if "Option A" in content_str or "placeholder" in content_str.lower():
                        placeholder_lessons.append(lesson)
            else:
                response.raise_for_status()
                placeholder_lessons = response.json()
            
            logging.info(f"Found {len(placeholder_lessons)} lessons with placeholder content")
            
            for lesson in placeholder_lessons[:10]:  # Start with first 10 for testing
                self.fix_single_lesson(lesson)
                time.sleep(2)  # Rate limiting
                
        except Exception as e:
            logging.error(f"Error fixing placeholder content: {str(e)}")

    def fix_single_lesson(self, lesson: Dict):
        """Fix a single lesson with placeholder content"""
        try:
            # Generate new content
            new_content = self.generate_lesson_content("French", "A1", lesson.get("title", "Language Lesson"))
            
            if new_content:
                # Update lesson
                update_data = {
                    "content_metadata": new_content,
                    "description": new_content.get("description", lesson.get("description")),
                    "learning_objectives": new_content.get("learning_objectives", []),
                    "vocabulary_focus": [vocab["word"] for vocab in new_content.get("vocabulary", [])],
                    "grammar_concepts": [gp["concept"] for gp in new_content.get("grammar_points", [])],
                    "cultural_notes": "; ".join([cc["description"] for cc in new_content.get("cultural_context", [])]),
                    "updated_at": datetime.now().isoformat()
                }
                
                url = f"{self.supabase_url}/rest/v1/lessons"
                params = {"id": f"eq.{lesson['id']}"}
                response = requests.patch(url, headers=self.headers, json=update_data, params=params)
                response.raise_for_status()
                
                self.fixed_count += 1
                logging.info(f"✅ Fixed lesson: {lesson.get('title', 'Unknown')}")
                
        except Exception as e:
            self.failed_count += 1
            logging.error(f"❌ Failed to fix lesson {lesson.get('title', 'Unknown')}: {str(e)}")

    def generate_lesson_content(self, language: str, level: str, topic: str) -> Optional[Dict]:
        """Generate lesson content using Gemini AI"""
        
        prompt = f"""
        Create a comprehensive {language} language lesson for {level} level on the topic: {topic}
        
        REQUIREMENTS:
        1. VOCABULARY: Exactly 10 words with proper {language} spelling, English translation, pronunciation, part of speech, example sentences
        2. GRAMMAR POINTS: 2-3 key grammar concepts with clear explanations and examples
        3. CULTURAL CONTEXT: Real-world scenarios with cultural etiquette and customs
        4. DIALOGUES: 3-4 realistic conversations with cultural context notes
        5. EXERCISES: Interactive practice including multiple choice, fill-in-blank, matching
        6. LEARNING OBJECTIVES: 4 clear, measurable learning goals
        
        OUTPUT FORMAT: Valid JSON with this structure:
        {{
            "title": "Engaging lesson title in English",
            "description": "Brief lesson description (50-100 words)",
            "learning_objectives": ["objective1", "objective2", "objective3", "objective4"],
            "vocabulary": [
                {{
                    "word": "word_in_{language}",
                    "translation": "english_translation",
                    "pronunciation": "/pronunciation/",
                    "part_of_speech": "noun/verb/etc",
                    "example": "Example sentence in {language}",
                    "example_translation": "English translation of example",
                    "cultural_context": "Cultural notes when relevant"
                }}
            ],
            "grammar_points": [
                {{
                    "concept": "Grammar concept name",
                    "explanation": "Clear explanation",
                    "examples": ["example1", "example2"],
                    "tips": "Practice tips"
                }}
            ],
            "cultural_context": [
                {{
                    "topic": "Cultural topic",
                    "description": "Description of cultural aspect",
                    "dos": ["do1", "do2"],
                    "donts": ["dont1", "dont2"]
                }}
            ],
            "dialogues": [
                {{
                    "speakers": ["Speaker1", "Speaker2"],
                    "exchanges": [
                        {{
                            "speaker": "Speaker1",
                            "text": "Text in {language}",
                            "translation": "English translation"
                        }}
                    ],
                    "cultural_notes": "Cultural context for dialogue"
                }}
            ],
            "exercises": [
                {{
                    "type": "multiple_choice",
                    "question": "Question text",
                    "options": ["option1", "option2", "option3", "option4"],
                    "correct_answer": 0,
                    "explanation": "Explanation of correct answer",
                    "points": 10
                }}
            ]
        }}
        """
        
        try:
            response = self.call_gemini_api(prompt)
            content = self.parse_ai_response(response)
            
            if self.validate_content(content):
                return content
            else:
                logging.error("Content validation failed")
                return None
                
        except Exception as e:
            logging.error(f"Failed to generate content: {str(e)}")
            return None

    def call_gemini_api(self, prompt: str) -> Dict:
        """Call Gemini API with the given prompt"""
        headers = {"Content-Type": "application/json"}
        
        data = {
            "contents": [{"parts": [{"text": prompt}]}],
            "generationConfig": {
                "temperature": 0.8,
                "topK": 40,
                "topP": 0.95,
                "maxOutputTokens": 4096
            }
        }
        
        response = requests.post(self.gemini_url, headers=headers, json=data)
        response.raise_for_status()
        return response.json()

    def parse_ai_response(self, response: Dict) -> Dict:
        """Parse AI response and extract JSON content"""
        try:
            text = response["candidates"][0]["content"]["parts"][0]["text"]
            
            if "```json" in text:
                json_start = text.find("```json") + 7
                json_end = text.find("```", json_start)
                json_text = text[json_start:json_end].strip()
            elif "{" in text and "}" in text:
                json_start = text.find("{")
                json_end = text.rfind("}") + 1
                json_text = text[json_start:json_end]
            else:
                raise ValueError("No JSON content found in response")
            
            return json.loads(json_text)
            
        except Exception as e:
            logging.error(f"Failed to parse AI response: {str(e)}")
            raise

    def validate_content(self, content: Dict) -> bool:
        """Validate generated content structure"""
        required_fields = ["title", "description", "vocabulary", "grammar_points", "cultural_context", "dialogues", "exercises"]
        
        for field in required_fields:
            if field not in content:
                logging.error(f"Missing required field: {field}")
                return False
        
        if not isinstance(content["vocabulary"], list) or len(content["vocabulary"]) == 0:
            logging.error("Invalid vocabulary structure")
            return False
        
        return True

    def print_report(self):
        """Print final report"""
        logging.info("🎉 CONTENT FIX COMPLETE!")
        logging.info(f"   Fixed lessons: {self.fixed_count}")
        logging.info(f"   Created content: {self.created_count}")
        logging.info(f"   Failed operations: {self.failed_count}")

def main():
    """Main execution function"""
    print("🚀 Simple NIRA Content Fixer")
    print("This will fix placeholder content in lessons using Gemini Flash 2.0")
    print()
    
    if GEMINI_API_KEY == "YOUR_GEMINI_API_KEY_HERE":
        print("❌ ERROR: Please set your GEMINI_API_KEY environment variable")
        print("   export GEMINI_API_KEY='your-actual-api-key'")
        return
    
    fixer = SimpleContentFixer()
    
    # Test connection first
    if not fixer.test_connection():
        print("❌ Connection test failed. Please check your API key.")
        return
    
    confirm = input("Connection successful! Do you want to proceed with fixing placeholder content? (yes/no): ")
    if confirm.lower() != 'yes':
        print("Operation cancelled.")
        return
    
    # Fix placeholder content
    fixer.fix_placeholder_content()
    fixer.print_report()

if __name__ == "__main__":
    main()
