#!/usr/bin/env python3
"""
Simple Lesson Generator Test
Test basic functionality with a minimal lesson structure
"""

import json
import logging
import requests
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class SimpleLessonTest:
    def __init__(self):
        self.gemini_api_key = os.getenv('GEMINI_API_KEY')
        self.gemini_url = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key={self.gemini_api_key}"
        
    def test_simple_generation(self):
        """Test simple lesson generation"""
        
        prompt = """
Generate a simple Tamil A1 lesson about greetings. Return ONLY valid JSON with this exact structure:

{
    "title": "Tamil Greetings Lesson",
    "description": "Learn basic Tamil greetings",
    "vocabulary": [
        {
            "word": "வணக்கம்",
            "translation": "Hello",
            "pronunciation": "/vanakkam/"
        },
        {
            "word": "நன்றி",
            "translation": "Thank you", 
            "pronunciation": "/nandri/"
        },
        {
            "word": "மன்னிக்கவும்",
            "translation": "Excuse me",
            "pronunciation": "/mannikkavum/"
        }
    ],
    "conversations": [
        {
            "title": "Meeting Someone",
            "exchanges": [
                {
                    "speaker": "Person A",
                    "text": "வணக்கம்!",
                    "translation": "Hello!"
                },
                {
                    "speaker": "Person B", 
                    "text": "வணக்கம்! நீங்கள் எப்படி இருக்கிறீர்கள்?",
                    "translation": "Hello! How are you?"
                }
            ]
        }
    ]
}

Return only this JSON structure, nothing else.
"""
        
        try:
            headers = {"Content-Type": "application/json"}
            data = {
                "contents": [{"parts": [{"text": prompt}]}],
                "generationConfig": {
                    "temperature": 0.3,
                    "topK": 20,
                    "topP": 0.8,
                    "maxOutputTokens": 1024
                }
            }
            
            logging.info("🔄 Calling Gemini API...")
            response = requests.post(self.gemini_url, headers=headers, json=data)
            response.raise_for_status()
            
            result = response.json()
            generated_text = result['candidates'][0]['content']['parts'][0]['text']
            
            logging.info("📝 Raw response received")
            print("Raw Response:")
            print("=" * 50)
            print(generated_text)
            print("=" * 50)
            
            # Try to parse JSON
            try:
                # Clean the response
                if "```json" in generated_text:
                    json_text = generated_text.split("```json")[1].split("```")[0].strip()
                elif "```" in generated_text:
                    json_text = generated_text.split("```")[1].strip()
                else:
                    json_text = generated_text.strip()
                
                lesson_data = json.loads(json_text)
                
                logging.info("✅ JSON parsing successful!")
                print("\nParsed Lesson:")
                print("=" * 50)
                print(f"Title: {lesson_data['title']}")
                print(f"Description: {lesson_data['description']}")
                print(f"Vocabulary items: {len(lesson_data['vocabulary'])}")
                print(f"Conversations: {len(lesson_data['conversations'])}")
                
                return lesson_data
                
            except json.JSONDecodeError as e:
                logging.error(f"❌ JSON parsing failed: {str(e)}")
                logging.error(f"Problematic text: {json_text[:200]}...")
                return None
                
        except Exception as e:
            logging.error(f"❌ API call failed: {str(e)}")
            return None

if __name__ == "__main__":
    tester = SimpleLessonTest()
    result = tester.test_simple_generation()
    
    if result:
        print("\n🎉 Test successful! Basic lesson generation works.")
    else:
        print("\n❌ Test failed. Need to debug the generation process.")
