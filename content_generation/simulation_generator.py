#!/usr/bin/env python3
"""
NIRA Simulation Generator
Uses Gemini for base scenario generation and OpenAI for cultural review
Generates 250 simulations per language (50 per persona × 5 personas)
"""

import json
import os
import time
import logging
import requests
from openai import OpenAI
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from supabase import create_client, Client
import uuid

# Configuration - Use environment variables or fallback to hardcoded values
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY") or "AIzaSyAYasSocME9nPr7HP625EEx4cofwm5MH3Q"
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY") or "***********************************************************************************************************************************************************************"
SUPABASE_URL = os.getenv("SUPABASE_URL") or "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_KEY = os.getenv("SUPABASE_ANON_KEY") or "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"

# Initialize clients
supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)
openai_client = OpenAI(api_key=OPENAI_API_KEY)

# Logging setup
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'simulation_generation_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)

# Simulation Categories (10 categories × 5 simulations per persona = 50 per persona)
SCENARIO_CATEGORIES = {
    "daily_life": {
        "name": "Daily Life & Home",
        "icon": "🏠",
        "scenarios": [
            "Morning routine conversation",
            "Household chores discussion",
            "Family dinner conversation",
            "Weekend plans chat",
            "Roommate coordination"
        ]
    },
    "shopping": {
        "name": "Shopping & Services",
        "icon": "🛒",
        "scenarios": [
            "Grocery store interaction",
            "Restaurant ordering",
            "Bank transaction",
            "Post office visit",
            "Pharmacy consultation"
        ]
    },
    "transportation": {
        "name": "Transportation & Travel",
        "icon": "🚗",
        "scenarios": [
            "Public transport navigation",
            "Taxi conversation",
            "Airport check-in",
            "Hotel booking",
            "Asking for directions"
        ]
    },
    "work": {
        "name": "Work & Professional",
        "icon": "💼",
        "scenarios": [
            "Job interview",
            "Team meeting",
            "Client presentation",
            "Colleague collaboration",
            "Performance review"
        ]
    },
    "health": {
        "name": "Health & Wellness",
        "icon": "🏥",
        "scenarios": [
            "Doctor appointment",
            "Pharmacy visit",
            "Gym conversation",
            "Emergency situation",
            "Wellness consultation"
        ]
    },
    "education": {
        "name": "Education & Learning",
        "icon": "🎓",
        "scenarios": [
            "Classroom discussion",
            "Library interaction",
            "Study group",
            "Academic presentation",
            "Course enrollment"
        ]
    },
    "social": {
        "name": "Social & Entertainment",
        "icon": "🎉",
        "scenarios": [
            "Party conversation",
            "Movie discussion",
            "Sports event",
            "Hobby sharing",
            "Event planning"
        ]
    },
    "technology": {
        "name": "Technology & Digital",
        "icon": "📱",
        "scenarios": [
            "Tech support call",
            "Online shopping",
            "Social media discussion",
            "Video call setup",
            "Digital payment"
        ]
    },
    "cultural": {
        "name": "Cultural & Community",
        "icon": "🌍",
        "scenarios": [
            "Cultural festival",
            "Community event",
            "Traditional celebration",
            "Local customs",
            "Neighborhood interaction"
        ]
    },
    "problem_solving": {
        "name": "Problem Solving & Emergencies",
        "icon": "⚠️",
        "scenarios": [
            "Product return",
            "Complaint resolution",
            "Emergency assistance",
            "Conflict mediation",
            "Help request"
        ]
    }
}

# Agent Personas (matching AgentModels.swift)
AGENT_PERSONAS = {
    "beginner_enthusiast": {
        "name": "Beginner Enthusiast",
        "icon": "🌟",
        "description": "Fun, gamified learning for newcomers",
        "focus": "Basic vocabulary, pronunciation, confidence building"
    },
    "busy_professional": {
        "name": "Busy Professional",
        "icon": "💼",
        "description": "Business vocabulary and professional scenarios",
        "focus": "Business language, professional emails, career focus"
    },
    "traveler": {
        "name": "Traveler",
        "icon": "✈️",
        "description": "Essential travel phrases and cultural navigation",
        "focus": "Travel phrases, cultural tips, local customs"
    },
    "cultural_seeker": {
        "name": "Cultural Seeker",
        "icon": "🏛️",
        "description": "Literature, traditions, cultural heritage",
        "focus": "Literature, history, traditions, cultural heritage"
    },
    "social_learner": {
        "name": "Social Learner",
        "icon": "👥",
        "description": "Social interactions and community integration",
        "focus": "Conversations, social media, local slang, community"
    }
}

# Languages (50 total)
LANGUAGES = [
    "Spanish", "French", "English", "Japanese", "Korean", "German", "Italian", "Hindi",
    "Chinese", "Russian", "Dutch", "Swedish", "Norwegian", "Turkish", "Greek", "Ukrainian",
    "Vietnamese", "Thai", "Indonesian", "Swahili", "Tagalog", "Hebrew", "Bengali", "Tamil",
    "Portuguese", "Arabic", "Telugu", "Yoruba", "Marathi", "Punjabi", "Gujarati", "Danish",
    "Kannada", "Malayalam", "Odia", "Bhojpuri", "Assamese", "Sindhi", "Maithili", "Zulu",
    "Amharic", "Konkani", "Xhosa", "Quechua", "Hawaiian", "Navajo", "Maori", "Cherokee",
    "Inuktitut", "Polish"
]

class SimulationGenerator:
    """Generate simulations using Gemini + OpenAI pipeline"""

    def __init__(self):
        self.generated_count = 0
        self.reviewed_count = 0
        self.uploaded_count = 0
        self.failed_count = 0
        self.gemini_url = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key={GEMINI_API_KEY}"

    def generate_simulation_batch(self, language: str, persona_key: str, category_key: str, scenario: str) -> Optional[Dict]:
        """Generate a simulation using Gemini, review with OpenAI, upload to Supabase"""

        try:
            # Step 1: Generate base simulation with Gemini
            logging.info(f"🤖 Generating simulation: {language} - {persona_key} - {scenario}")
            base_simulation = self._generate_with_gemini(language, persona_key, category_key, scenario)

            if not base_simulation:
                return None

            # Step 2: Cultural review with OpenAI
            logging.info(f"🔍 Cultural review: {base_simulation['title']}")
            reviewed_simulation = self._review_with_openai(base_simulation, language)

            if not reviewed_simulation:
                return None

            # Step 3: Upload to Supabase
            logging.info(f"📤 Uploading: {reviewed_simulation['title']}")
            success = self._upload_to_supabase(reviewed_simulation, language, persona_key, category_key)

            if success:
                self.uploaded_count += 1
                logging.info(f"✅ Complete: {reviewed_simulation['title']}")
                return reviewed_simulation
            else:
                return None

        except Exception as e:
            self.failed_count += 1
            logging.error(f"❌ Failed simulation generation: {str(e)}")
            return None

    def _generate_with_gemini(self, language: str, persona_key: str, category_key: str, scenario: str) -> Optional[Dict]:
        """Generate base simulation content using Gemini"""

        persona = AGENT_PERSONAS[persona_key]
        category = SCENARIO_CATEGORIES[category_key]

        prompt = f"""
Create a realistic conversation simulation for {language} language learning.

CONTEXT:
- Language: {language}
- Persona: {persona['name']} ({persona['description']})
- Category: {category['name']}
- Scenario: {scenario}
- Focus: {persona['focus']}

REQUIREMENTS:
1. Create a natural, authentic conversation (6-8 exchanges)
2. Include cultural context and etiquette
3. Provide vocabulary highlights (8-12 words)
4. Add grammar focus points (2-3 concepts)
5. Include response options for learner practice
6. Ensure cultural authenticity and appropriateness

OUTPUT FORMAT (JSON):
{{
    "title": "Engaging simulation title",
    "description": "Brief description of the scenario",
    "scenario_context": "Detailed context and setting",
    "difficulty_level": "A2/B1/B2 (appropriate for persona)",
    "estimated_duration": 15,
    "learning_objectives": ["objective1", "objective2", "objective3"],
    "cultural_notes": "Important cultural context and etiquette",
    "vocabulary_focus": [
        {{
            "word": "word_in_{language}",
            "translation": "english_translation",
            "pronunciation": "/pronunciation/",
            "context": "usage_context"
        }}
    ],
    "grammar_focus": [
        {{
            "concept": "grammar_concept",
            "explanation": "clear_explanation",
            "examples": ["example1", "example2"]
        }}
    ],
    "dialogue": [
        {{
            "speaker": "Speaker1_Name",
            "text": "Text in {language}",
            "translation": "English translation",
            "cultural_context": "Cultural notes if relevant"
        }}
    ],
    "response_options": [
        {{
            "situation": "When to respond",
            "options": [
                {{
                    "text": "Response in {language}",
                    "translation": "English translation",
                    "appropriateness": "formal/informal/neutral",
                    "cultural_note": "Cultural context"
                }}
            ]
        }}
    ]
}}
"""

        try:
            response = self._call_gemini_api(prompt)
            content = self._parse_gemini_response(response)

            if self._validate_simulation_content(content):
                self.generated_count += 1
                return content
            else:
                raise ValueError("Content validation failed")

        except Exception as e:
            logging.error(f"Gemini generation failed: {str(e)}")
            return None

    def _review_with_openai(self, simulation: Dict, language: str) -> Optional[Dict]:
        """Review and enhance simulation for cultural authenticity using OpenAI"""

        review_prompt = f"""
You are a cultural expert and language educator reviewing a {language} conversation simulation for authenticity and appropriateness.

SIMULATION TO REVIEW:
{json.dumps(simulation, indent=2)}

REVIEW CRITERIA:
1. Cultural Authenticity: Are the interactions culturally appropriate and realistic?
2. Language Accuracy: Is the {language} natural and correct?
3. Educational Value: Does it effectively teach the target concepts?
4. Cultural Sensitivity: Are there any cultural stereotypes or inappropriate content?
5. Practical Relevance: Is this a realistic, useful scenario?

TASKS:
1. Review the simulation for cultural accuracy and appropriateness
2. Suggest improvements for language naturalness
3. Enhance cultural context and etiquette notes
4. Verify vocabulary and grammar focus are appropriate
5. Ensure the scenario is realistic and educational

OUTPUT: Return the IMPROVED simulation in the same JSON format, with:
- Enhanced cultural notes and context
- More natural dialogue if needed
- Improved vocabulary explanations
- Better cultural etiquette guidance
- Any necessary corrections

If the simulation is culturally inappropriate or inaccurate, explain the issues and provide corrections.
"""

        try:
            response = openai_client.chat.completions.create(
                model="gpt-4-turbo",
                messages=[
                    {"role": "system", "content": f"You are an expert {language} language and culture consultant."},
                    {"role": "user", "content": review_prompt}
                ],
                temperature=0.3,
                max_tokens=3000
            )

            reviewed_content = response.choices[0].message.content

            # Parse the improved simulation
            if "```json" in reviewed_content:
                json_start = reviewed_content.find("```json") + 7
                json_end = reviewed_content.find("```", json_start)
                json_text = reviewed_content[json_start:json_end].strip()
            elif "{" in reviewed_content:
                json_start = reviewed_content.find("{")
                json_end = reviewed_content.rfind("}") + 1
                json_text = reviewed_content[json_start:json_end]
            else:
                # If no JSON, return original with review notes
                simulation["review_notes"] = reviewed_content
                return simulation

            improved_simulation = json.loads(json_text)
            improved_simulation["review_status"] = "reviewed"
            improved_simulation["review_timestamp"] = datetime.now().isoformat()

            self.reviewed_count += 1
            return improved_simulation

        except Exception as e:
            logging.error(f"OpenAI review failed: {str(e)}")
            # Return original simulation if review fails
            simulation["review_status"] = "review_failed"
            simulation["review_error"] = str(e)
            return simulation

    def _upload_to_supabase(self, simulation: Dict, language: str, persona_key: str, category_key: str) -> bool:
        """Upload simulation to Supabase database"""

        try:
            # Get or create persona ID
            persona_id = self._get_or_create_persona(persona_key)
            language_id = self._get_language_id(language)

            # Prepare simulation data to match existing schema
            simulation_data = {
                "id": str(uuid.uuid4()),
                "persona_id": persona_id,
                "language_id": language_id,
                "title": simulation["title"],
                "description": simulation["description"],
                "difficulty_level": simulation["difficulty_level"],
                "estimated_duration": simulation["estimated_duration"],
                "scenario_type": category_key,  # Map to scenario_type
                "learning_objectives": simulation["learning_objectives"],
                "vocabulary_focus": simulation["vocabulary_focus"],
                "conversation_starters": [
                    exchange["text"] for exchange in simulation.get("dialogue", [])[:3]
                ],  # First 3 dialogue exchanges as conversation starters
                "success_criteria": {
                    "vocabulary_mastery": 0.3,
                    "cultural_appropriateness": 0.3,
                    "conversation_flow": 0.4,
                    "min_accuracy": 0.7,
                    "required_exchanges": len(simulation.get("dialogue", []))
                },
                "cultural_notes": simulation["cultural_notes"],
                "is_active": True,
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat()
            }

            # Upload to simulations table
            result = supabase.table("simulations").insert(simulation_data).execute()

            if result.data:
                logging.info(f"✅ Successfully uploaded simulation to Supabase: {simulation_data['id']}")
                return True
            else:
                raise Exception("No data returned from simulation insert")

        except Exception as e:
            logging.error(f"Supabase upload failed: {str(e)}")
            return False

    def _upload_dialogue_entries(self, simulation_id: str, dialogue: List[Dict]) -> None:
        """Upload individual dialogue entries"""

        try:
            dialogue_entries = []
            for i, exchange in enumerate(dialogue):
                entry = {
                    "id": str(uuid.uuid4()),
                    "simulation_id": simulation_id,
                    "sequence_order": i + 1,
                    "speaker_role": "participant" if i % 2 == 0 else "learner",
                    "speaker_name": exchange.get("speaker", f"Speaker{i+1}"),
                    "dialogue_text": exchange["text"],
                    "dialogue_translation": exchange["translation"],
                    "audio_url": None,  # To be added later
                    "response_options": [],  # Individual response options
                    "cultural_context": exchange.get("cultural_context", ""),
                    "vocabulary_highlights": [],  # Extract from vocabulary_focus
                    "grammar_notes": "",
                    "difficulty_level": "B1",  # Default
                    "is_critical_path": True,
                    "created_at": datetime.now().isoformat()
                }
                dialogue_entries.append(entry)

            # Batch insert dialogue entries
            if dialogue_entries:
                supabase.table("simulation_dialogues").insert(dialogue_entries).execute()

        except Exception as e:
            logging.error(f"Dialogue upload failed: {str(e)}")

    def _get_or_create_persona(self, persona_key: str) -> str:
        """Get or create persona in database"""

        try:
            # Check if persona exists
            result = supabase.table("simulation_personas").select("id").eq("name", persona_key).execute()

            if result.data:
                return result.data[0]["id"]
            else:
                # Create new persona
                persona = AGENT_PERSONAS[persona_key]
                persona_data = {
                    "id": str(uuid.uuid4()),
                    "name": persona_key,
                    "display_name": persona["name"],
                    "description": persona["description"],
                    "target_audience": "all",
                    "difficulty_range": "A1-C2",
                    "color_theme": "#007AFF",
                    "icon_name": persona["icon"],
                    "learning_objectives": [persona["focus"]],
                    "is_active": True,
                    "sort_order": len(AGENT_PERSONAS),
                    "created_at": datetime.now().isoformat(),
                    "updated_at": datetime.now().isoformat()
                }

                result = supabase.table("simulation_personas").insert(persona_data).execute()
                return result.data[0]["id"]

        except Exception as e:
            logging.error(f"Persona creation failed: {str(e)}")
            return str(uuid.uuid4())  # Fallback

    def _get_language_id(self, language: str) -> str:
        """Get actual language UUID from the languages table"""
        try:
            # Check if language exists in database
            result = supabase.table("languages").select("id").eq("name", language).execute()

            if result.data:
                return result.data[0]["id"]
            else:
                # If language doesn't exist, create it
                logging.info(f"Creating new language entry for {language}")

                # Language code mapping
                language_codes = {
                    "French": "fr", "Spanish": "es", "German": "de", "Italian": "it",
                    "Portuguese": "pt", "English": "en", "Japanese": "ja", "Korean": "ko",
                    "Chinese": "zh", "Hindi": "hi", "Arabic": "ar", "Russian": "ru",
                    "Dutch": "nl", "Swedish": "sv", "Norwegian": "no", "Danish": "da",
                    "Polish": "pl", "Turkish": "tr", "Greek": "el", "Hebrew": "he",
                    "Thai": "th", "Vietnamese": "vi", "Indonesian": "id", "Malay": "ms",
                    "Tamil": "ta", "Telugu": "te", "Bengali": "bn", "Marathi": "mr",
                    "Gujarati": "gu", "Kannada": "kn", "Malayalam": "ml", "Punjabi": "pa"
                }

                new_language = {
                    "name": language,
                    "code": language_codes.get(language, language.lower()[:2]),
                    "native_name": language,  # Simplified for now
                    "is_active": True
                }

                create_result = supabase.table("languages").insert(new_language).execute()
                if create_result.data:
                    return create_result.data[0]["id"]
                else:
                    raise Exception(f"Failed to create language {language}")

        except Exception as e:
            logging.error(f"Error getting language ID for {language}: {str(e)}")
            # Fallback to a default language ID (English)
            result = supabase.table("languages").select("id").eq("name", "English").execute()
            if result.data:
                return result.data[0]["id"]
            else:
                raise Exception("No fallback language found")

    def _call_gemini_api(self, prompt: str) -> Dict:
        """Call Gemini API"""

        headers = {"Content-Type": "application/json"}
        data = {
            "contents": [{"parts": [{"text": prompt}]}],
            "generationConfig": {
                "temperature": 0.8,
                "topK": 40,
                "topP": 0.95,
                "maxOutputTokens": 4096
            }
        }

        response = requests.post(self.gemini_url, headers=headers, json=data)
        response.raise_for_status()
        return response.json()

    def _parse_gemini_response(self, response: Dict) -> Dict:
        """Parse Gemini response"""

        text = response["candidates"][0]["content"]["parts"][0]["text"]

        if "```json" in text:
            json_start = text.find("```json") + 7
            json_end = text.find("```", json_start)
            json_text = text[json_start:json_end].strip()
        else:
            json_start = text.find("{")
            json_end = text.rfind("}") + 1
            json_text = text[json_start:json_end]

        return json.loads(json_text)

    def _validate_simulation_content(self, content: Dict) -> bool:
        """Validate simulation content structure"""

        required_fields = ["title", "description", "dialogue", "vocabulary_focus"]
        return all(field in content for field in required_fields)

    def generate_language_simulations(self, language: str, max_simulations: int = 10) -> None:
        """Generate simulations for a specific language (testing with limited count)"""

        logging.info(f"🚀 Starting simulation generation for {language}")
        logging.info(f"📊 Target: {max_simulations} simulations")

        generated = 0

        for persona_key in AGENT_PERSONAS.keys():
            if generated >= max_simulations:
                break

            for category_key, category in SCENARIO_CATEGORIES.items():
                if generated >= max_simulations:
                    break

                for scenario in category["scenarios"][:2]:  # Limit to 2 scenarios per category for testing
                    if generated >= max_simulations:
                        break

                    logging.info(f"📝 Generating {generated+1}/{max_simulations}: {persona_key} - {scenario}")

                    result = self.generate_simulation_batch(language, persona_key, category_key, scenario)

                    if result:
                        generated += 1
                        logging.info(f"✅ Success! Generated: {result['title']}")
                    else:
                        logging.error(f"❌ Failed to generate simulation")

                    # Rate limiting
                    time.sleep(3)

        self._print_generation_report(language)

    def generate_full_scale_simulations(self, languages: List[str] = None) -> None:
        """Generate full scale simulations (250 per language)"""

        if not languages:
            languages = LANGUAGES[:5]  # Start with first 5 languages for testing

        logging.info(f"🌍 FULL SCALE GENERATION: {len(languages)} languages")
        logging.info(f"📊 Target: {len(languages) * 250} total simulations")

        for language in languages:
            logging.info(f"\n🔥 Starting {language} - Target: 250 simulations")

            generated = 0
            for persona_key in AGENT_PERSONAS.keys():
                for category_key, category in SCENARIO_CATEGORIES.items():
                    for scenario in category["scenarios"]:

                        result = self.generate_simulation_batch(language, persona_key, category_key, scenario)

                        if result:
                            generated += 1
                            logging.info(f"✅ {language} Progress: {generated}/250")

                        # Rate limiting - important for API limits
                        time.sleep(2)

            logging.info(f"🎉 {language} Complete! Generated: {generated}/250 simulations")

        self._print_final_report()

    def test_single_simulation(self, language: str = "French", persona: str = "beginner_enthusiast") -> Dict:
        """Test generation of a single simulation"""

        logging.info(f"🧪 Testing single simulation: {language} - {persona}")

        category_key = "daily_life"
        scenario = "Morning routine conversation"

        result = self.generate_simulation_batch(language, persona, category_key, scenario)

        if result:
            logging.info(f"✅ Test successful!")
            logging.info(f"📝 Title: {result['title']}")
            logging.info(f"📝 Description: {result['description']}")
            logging.info(f"📝 Dialogue exchanges: {len(result.get('dialogue', []))}")
            logging.info(f"📝 Vocabulary items: {len(result.get('vocabulary_focus', []))}")
            return result
        else:
            logging.error(f"❌ Test failed!")
            return {}

    def _print_generation_report(self, language: str) -> None:
        """Print generation report for a language"""

        logging.info(f"\n📊 {language} Generation Report:")
        logging.info(f"   Generated: {self.generated_count}")
        logging.info(f"   Reviewed: {self.reviewed_count}")
        logging.info(f"   Uploaded: {self.uploaded_count}")
        logging.info(f"   Failed: {self.failed_count}")

        success_rate = (self.uploaded_count / max(1, self.generated_count)) * 100
        logging.info(f"   Success Rate: {success_rate:.1f}%")

    def _print_final_report(self) -> None:
        """Print final generation statistics"""

        logging.info("\n🎉 SIMULATION GENERATION COMPLETE!")
        logging.info(f"📊 Final Statistics:")
        logging.info(f"   Total Generated: {self.generated_count}")
        logging.info(f"   Total Reviewed: {self.reviewed_count}")
        logging.info(f"   Total Uploaded: {self.uploaded_count}")
        logging.info(f"   Total Failed: {self.failed_count}")

        if self.uploaded_count > 0:
            logging.info(f"🚀 Successfully created {self.uploaded_count} simulations!")

        if self.failed_count > 0:
            logging.warning(f"⚠️  {self.failed_count} simulations failed. Check logs for details.")

def test_pipeline():
    """Test the complete Gemini -> OpenAI -> Supabase pipeline"""

    print("🧪 Testing NIRA Simulation Generation Pipeline")
    print("=" * 50)

    generator = SimulationGenerator()

    # Test single simulation
    print("\n1. Testing single simulation generation...")
    result = generator.test_single_simulation("French", "beginner_enthusiast")

    if result:
        print(f"✅ Pipeline test successful!")
        print(f"📝 Generated simulation: {result.get('title', 'Unknown')}")
        return True
    else:
        print("❌ Pipeline test failed!")
        return False

def generate_sample_batch():
    """Generate a small batch for testing"""

    print("🚀 Generating Sample Batch")
    print("=" * 30)

    generator = SimulationGenerator()

    # Generate 10 simulations for French
    generator.generate_language_simulations("French", max_simulations=10)

def generate_production_batch():
    """Generate production-scale simulations"""

    print("🌍 PRODUCTION SIMULATION GENERATION")
    print("=" * 40)
    print("⚠️  This will generate 1,250 simulations (5 languages × 250 each)")
    print("⚠️  Estimated time: 2-3 hours")
    print("⚠️  API costs: ~$50-100")

    confirm = input("\nContinue? (yes/no): ")

    if confirm.lower() == 'yes':
        generator = SimulationGenerator()

        # Start with Tier 1 languages
        tier1_languages = ["Spanish", "French", "English", "Japanese", "German"]
        generator.generate_full_scale_simulations(tier1_languages)
    else:
        print("Generation cancelled.")

def main():
    """Main function with options"""

    print("🎭 NIRA Simulation Generator")
    print("Gemini → OpenAI → Supabase Pipeline")
    print("=" * 40)
    print("1. Test single simulation")
    print("2. Generate sample batch (10 simulations)")
    print("3. Generate production batch (1,250 simulations)")
    print("4. Exit")

    choice = input("\nEnter your choice (1-4): ")

    if choice == "1":
        test_pipeline()
    elif choice == "2":
        generate_sample_batch()
    elif choice == "3":
        generate_production_batch()
    elif choice == "4":
        print("Goodbye!")
    else:
        print("Invalid choice. Please try again.")
        main()

if __name__ == "__main__":
    main()
