#!/usr/bin/env python3
"""
Supabase Uploader for NIRA
Uploads generated lesson content to Supabase database
"""

import json
import logging
import requests
import os
import uuid
from datetime import datetime
from dotenv import load_dotenv
from typing import Dict, List, Optional, Any

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class SupabaseUploader:
    """Upload lesson content to Supabase database"""
    
    def __init__(self):
        self.supabase_url = os.getenv('SUPABASE_URL')
        self.supabase_key = os.getenv('SUPABASE_ANON_KEY')
        
        if not all([self.supabase_url, self.supabase_key]):
            raise ValueError("Supabase credentials not found. Check your .env file.")
        
        self.base_url = f"{self.supabase_url}/rest/v1"
        self.headers = {
            "apikey": self.supabase_key,
            "Authorization": f"Bearer {self.supabase_key}",
            "Content-Type": "application/json",
            "Prefer": "return=representation"
        }
        
        # Upload statistics
        self.upload_stats = {
            "lessons_uploaded": 0,
            "audio_files_linked": 0,
            "failed_uploads": 0
        }
        
        logging.info("📤 Supabase Uploader initialized")
    
    def upload_complete_lesson(self, lesson_data: Dict, audio_data: Dict, language: str = "Tamil") -> Optional[str]:
        """
        Upload a complete lesson with all components to Supabase
        Returns the lesson ID if successful
        """
        
        logging.info(f"📤 Uploading lesson: {lesson_data.get('title', 'Untitled')}")
        
        try:
            # Step 1: Get language ID
            language_id = self._get_language_id(language)
            if not language_id:
                raise ValueError(f"Language '{language}' not found in database")
            
            # Step 2: Get or create learning path
            learning_path_id = self._get_or_create_learning_path(
                language_id, 
                lesson_data.get('level', 'A1')
            )
            
            # Step 3: Create lesson record
            lesson_id = self._create_lesson_record(lesson_data, learning_path_id, audio_data)
            
            if lesson_id:
                self.upload_stats["lessons_uploaded"] += 1
                self.upload_stats["audio_files_linked"] += len(audio_data.get('vocabulary', [])) + \
                                                         len(audio_data.get('conversations', [])) + \
                                                         len(audio_data.get('exercises', []))
                
                logging.info(f"✅ Lesson uploaded successfully with ID: {lesson_id}")
                return lesson_id
            else:
                raise ValueError("Failed to create lesson record")
                
        except Exception as e:
            self.upload_stats["failed_uploads"] += 1
            logging.error(f"❌ Failed to upload lesson: {str(e)}")
            return None
    
    def _get_language_id(self, language_name: str) -> Optional[str]:
        """Get language ID from database"""
        
        try:
            url = f"{self.base_url}/languages"
            params = {"name": f"eq.{language_name}"}
            
            response = requests.get(url, headers=self.headers, params=params)
            response.raise_for_status()
            
            languages = response.json()
            if languages:
                language_id = languages[0]['id']
                logging.info(f"✅ Found language ID: {language_id}")
                return language_id
            else:
                logging.error(f"❌ Language '{language_name}' not found")
                return None
                
        except Exception as e:
            logging.error(f"❌ Error getting language ID: {str(e)}")
            return None
    
    def _get_or_create_learning_path(self, language_id: str, level: str) -> Optional[str]:
        """Get existing learning path or create new one"""
        
        try:
            # First, try to find existing path
            url = f"{self.base_url}/learning_paths"
            params = {
                "language_id": f"eq.{language_id}",
                "level": f"eq.{level}"
            }
            
            response = requests.get(url, headers=self.headers, params=params)
            response.raise_for_status()
            
            paths = response.json()
            if paths:
                path_id = paths[0]['id']
                logging.info(f"✅ Found existing learning path: {path_id}")
                return path_id
            
            # Create new learning path if not found
            path_data = {
                "id": str(uuid.uuid4()),
                "language_id": language_id,
                "name": f"Tamil {level}: Beginner Fundamentals" if level == "A1" else f"Tamil {level}: Advanced",
                "description": f"Complete {level} level Tamil learning path",
                "level": level,
                "sequence_order": 1,
                "estimated_duration": 300,  # 5 hours
                "is_active": True
            }
            
            response = requests.post(url, headers=self.headers, json=path_data)
            response.raise_for_status()
            
            created_path = response.json()
            if created_path:
                path_id = created_path[0]['id']
                logging.info(f"✅ Created new learning path: {path_id}")
                return path_id
            
        except Exception as e:
            logging.error(f"❌ Error with learning path: {str(e)}")
            return None
    
    def _create_lesson_record(self, lesson_data: Dict, learning_path_id: str, audio_data: Dict) -> Optional[str]:
        """Create the main lesson record with all content"""
        
        try:
            lesson_id = str(uuid.uuid4())
            
            # Prepare content metadata with audio URLs
            content_metadata = self._prepare_content_metadata(lesson_data, audio_data)
            
            lesson_record = {
                "id": lesson_id,
                "path_id": learning_path_id,
                "title": lesson_data.get('title', 'Untitled Lesson'),
                "description": lesson_data.get('description', ''),
                "lesson_type": "comprehensive",
                "difficulty_level": self._get_difficulty_number(lesson_data.get('level', 'A1')),
                "estimated_duration": lesson_data.get('estimated_duration_minutes', 25),
                "sequence_order": lesson_data.get('lesson_number', 1),
                "learning_objectives": [
                    "Master vocabulary and pronunciation",
                    "Understand grammar concepts",
                    "Practice conversations",
                    "Complete exercises successfully"
                ],
                "vocabulary_focus": [],
                "grammar_concepts": [point.get('rule', '') for point in lesson_data.get('grammar', [])],
                "cultural_notes": f"Authentic {lesson_data.get('language', 'Tamil')} cultural context",
                "prerequisite_lessons": [],
                "content_metadata": content_metadata,
                "is_active": True,
                "has_audio": True,
                "audio_metadata": {
                    "total_audio_files": len(audio_data.get('vocabulary', [])) + 
                                       len(audio_data.get('conversations', [])) + 
                                       len(audio_data.get('exercises', [])),
                    "generated_at": audio_data.get('generation_metadata', {}).get('generated_at'),
                    "voice_id": audio_data.get('generation_metadata', {}).get('voice_id')
                }
            }
            
            # Upload to Supabase
            url = f"{self.base_url}/lessons"
            response = requests.post(url, headers=self.headers, json=lesson_record)

            if response.status_code != 201:
                logging.error(f"❌ Supabase error: {response.status_code}")
                logging.error(f"❌ Response: {response.text}")
                return None

            created_lesson = response.json()
            if created_lesson:
                logging.info(f"✅ Lesson record created successfully")
                return lesson_id
            
        except Exception as e:
            logging.error(f"❌ Error creating lesson record: {str(e)}")
            return None
    
    def _prepare_content_metadata(self, lesson_data: Dict, audio_data: Dict) -> Dict:
        """Prepare comprehensive content metadata with audio links"""
        
        # Map audio files to content
        vocab_with_audio = []
        for i, vocab in enumerate(lesson_data.get('vocabulary', [])):
            audio_info = None
            if i < len(audio_data.get('vocabulary', [])):
                audio_info = audio_data['vocabulary'][i]
            
            vocab_item = {
                **vocab,
                "word_audio_url": audio_info.get('word_audio_url') if audio_info else None,
                "example_audio_url": audio_info.get('example_audio_url') if audio_info else None
            }
            vocab_with_audio.append(vocab_item)
        
        # Map conversation audio
        conversations_with_audio = []
        for i, conv in enumerate(lesson_data.get('conversations', [])):
            audio_info = None
            if i < len(audio_data.get('conversations', [])):
                audio_info = audio_data['conversations'][i]
            
            conv_item = {
                **conv,
                "audio_exchanges": audio_info.get('exchanges', []) if audio_info else []
            }
            conversations_with_audio.append(conv_item)
        
        # Map exercise audio
        exercises_with_audio = []
        for exercise in lesson_data.get('exercises', []):
            # Find matching audio by exercise ID
            audio_info = None
            for audio_ex in audio_data.get('exercises', []):
                if audio_ex.get('exercise_id') == exercise.get('id'):
                    audio_info = audio_ex
                    break
            
            exercise_item = {
                **exercise,
                "question_audio_url": audio_info.get('audio_url') if audio_info else None
            }
            exercises_with_audio.append(exercise_item)
        
        return {
            "level": lesson_data.get('level', 'A1'),
            "topic": lesson_data.get('topic', ''),
            "vocabulary": vocab_with_audio,
            "conversations": conversations_with_audio,
            "grammar": lesson_data.get('grammar', []),
            "exercises": exercises_with_audio,
            "generation_metadata": lesson_data.get('generation_metadata', {}),
            "audio_metadata": audio_data.get('generation_metadata', {}),
            "complexity": "beginner" if lesson_data.get('level') == 'A1' else "intermediate",
            "skillFocus": ["vocabulary", "conversation", "grammar", "exercises"],
            "authenticity": "ai_generated_validated",
            "qualityAssured": True
        }
    
    def _get_difficulty_number(self, level: str) -> int:
        """Convert CEFR level to difficulty number"""
        level_map = {
            'A1': 1, 'A2': 2, 'B1': 3, 
            'B2': 4, 'C1': 5, 'C2': 6
        }
        return level_map.get(level, 1)
    
    def get_upload_statistics(self) -> Dict:
        """Get upload statistics"""
        return self.upload_stats

# Test function
if __name__ == "__main__":
    # Load the generated lesson and audio data
    try:
        with open('test_lesson_output.json', 'r', encoding='utf-8') as f:
            lesson_data = json.load(f)
        
        with open('lesson_audio_files.json', 'r', encoding='utf-8') as f:
            audio_data = json.load(f)
        
        uploader = SupabaseUploader()
        
        # Upload the lesson
        lesson_id = uploader.upload_complete_lesson(lesson_data, audio_data, "Tamil")
        
        if lesson_id:
            print("🎉 Upload Results:")
            print("=" * 50)
            print(f"Lesson ID: {lesson_id}")
            print(f"Upload Statistics: {uploader.get_upload_statistics()}")
            print("\n✅ Lesson successfully uploaded to Supabase!")
            print("🔗 You can now test it in the UI!")
        else:
            print("❌ Upload failed")
        
    except FileNotFoundError as e:
        print(f"❌ Required files not found: {str(e)}")
        print("Run the previous generators first.")
    except Exception as e:
        print(f"❌ Upload test failed: {str(e)}")
