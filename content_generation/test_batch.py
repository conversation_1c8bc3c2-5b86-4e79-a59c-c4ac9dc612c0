#!/usr/bin/env python3
"""
Test batch simulation generation
"""

import logging
from batch_simulation_generator import BatchSimulationGenerator

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_sample_batch():
    """Test generating a small sample batch"""
    
    print("🧪 Testing Sample Batch Generation")
    print("=" * 40)
    
    batch_gen = BatchSimulationGenerator()
    
    # Generate 5 simulations for French
    batch_gen.generate_sample_batch("French", 5)
    
    print("\n✅ Sample batch test complete!")

if __name__ == "__main__":
    test_sample_batch()
