#!/usr/bin/env python3
"""
Test Cost-Optimized Generation
Generate 10 Spanish simulations with 90% cost savings
"""

import logging
from datetime import datetime
from cost_optimized_generator import CostOptimizedBatchGenerator

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_cost_optimized():
    """Test cost-optimized generation"""
    
    print("💰 TESTING COST-OPTIMIZED GENERATION")
    print("=" * 50)
    print("🎯 Language: Spanish")
    print("🎯 Target: 10 simulations")
    print("🎯 Expected cost: ~$0.01 (vs $5.60 with OpenAI)")
    print("🎯 Method: Gemini + Basic validation (no OpenAI)")
    print()
    
    # Create cost-optimized batch generator
    batch_gen = CostOptimizedBatchGenerator(max_cost_limit=1.0)
    
    # Generate 10 Spanish simulations
    batch_gen.generate_cost_controlled_batch("Spanish", 10)
    
    print("\n✅ Cost-optimized test complete!")

if __name__ == "__main__":
    test_cost_optimized()
