#!/usr/bin/env python3
"""
Test script for NIRA Simulation Generator
Tests the Gemini -> OpenAI -> Supabase pipeline with a single simulation
"""

import json
import os
import logging
from datetime import datetime

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# API Configuration from comprehensive_lesson_generator.py
GEMINI_API_KEY = "AIzaSyAYasSocME9nPr7HP625EEx4cofwm5MH3Q"
OPENAI_API_KEY = "***********************************************************************************************************************************************************************"
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"

def test_gemini_generation():
    """Test Gemini API for simulation generation"""

    import requests

    logging.info("🤖 Testing Gemini API...")

    prompt = """
Create a realistic French conversation simulation for language learning.

CONTEXT:
- Language: French
- Persona: Beginner Enthusiast (Fun, gamified learning for newcomers)
- Category: Daily Life & Home
- Scenario: Morning routine conversation
- Focus: Basic vocabulary, pronunciation, confidence building

REQUIREMENTS:
1. Create a natural, authentic conversation (6-8 exchanges)
2. Include cultural context and etiquette
3. Provide vocabulary highlights (8-12 words)
4. Add grammar focus points (2-3 concepts)
5. Include response options for learner practice
6. Ensure cultural authenticity and appropriateness

OUTPUT FORMAT (JSON):
{
    "title": "Engaging simulation title",
    "description": "Brief description of the scenario",
    "scenario_context": "Detailed context and setting",
    "difficulty_level": "A2",
    "estimated_duration": 15,
    "learning_objectives": ["objective1", "objective2", "objective3"],
    "cultural_notes": "Important cultural context and etiquette",
    "vocabulary_focus": [
        {
            "word": "word_in_french",
            "translation": "english_translation",
            "pronunciation": "/pronunciation/",
            "context": "usage_context"
        }
    ],
    "grammar_focus": [
        {
            "concept": "grammar_concept",
            "explanation": "clear_explanation",
            "examples": ["example1", "example2"]
        }
    ],
    "dialogue": [
        {
            "speaker": "Speaker1_Name",
            "text": "Text in French",
            "translation": "English translation",
            "cultural_context": "Cultural notes if relevant"
        }
    ],
    "response_options": [
        {
            "situation": "When to respond",
            "options": [
                {
                    "text": "Response in French",
                    "translation": "English translation",
                    "appropriateness": "formal/informal/neutral",
                    "cultural_note": "Cultural context"
                }
            ]
        }
    ]
}
"""

    try:
        gemini_url = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key={GEMINI_API_KEY}"

        headers = {"Content-Type": "application/json"}
        data = {
            "contents": [{"parts": [{"text": prompt}]}],
            "generationConfig": {
                "temperature": 0.8,
                "topK": 40,
                "topP": 0.95,
                "maxOutputTokens": 4096
            }
        }

        response = requests.post(gemini_url, headers=headers, json=data)
        response.raise_for_status()

        result = response.json()
        text = result["candidates"][0]["content"]["parts"][0]["text"]

        # Extract JSON
        if "```json" in text:
            json_start = text.find("```json") + 7
            json_end = text.find("```", json_start)
            json_text = text[json_start:json_end].strip()
        else:
            json_start = text.find("{")
            json_end = text.rfind("}") + 1
            json_text = text[json_start:json_end]

        simulation = json.loads(json_text)

        logging.info("✅ Gemini generation successful!")
        logging.info(f"📝 Title: {simulation.get('title', 'Unknown')}")
        logging.info(f"📝 Dialogue exchanges: {len(simulation.get('dialogue', []))}")
        logging.info(f"📝 Vocabulary items: {len(simulation.get('vocabulary_focus', []))}")

        return simulation

    except Exception as e:
        logging.error(f"❌ Gemini generation failed: {str(e)}")
        return None

def test_openai_review(simulation):
    """Test OpenAI cultural review"""

    from openai import OpenAI

    logging.info("🔍 Testing OpenAI cultural review...")

    try:
        client = OpenAI(api_key=OPENAI_API_KEY)

        review_prompt = f"""
You are a cultural expert and language educator reviewing a French conversation simulation for authenticity and appropriateness.

SIMULATION TO REVIEW:
{json.dumps(simulation, indent=2)}

REVIEW CRITERIA:
1. Cultural Authenticity: Are the interactions culturally appropriate and realistic?
2. Language Accuracy: Is the French natural and correct?
3. Educational Value: Does it effectively teach the target concepts?
4. Cultural Sensitivity: Are there any cultural stereotypes or inappropriate content?
5. Practical Relevance: Is this a realistic, useful scenario?

TASKS:
1. Review the simulation for cultural accuracy and appropriateness
2. Suggest improvements for language naturalness
3. Enhance cultural context and etiquette notes
4. Verify vocabulary and grammar focus are appropriate
5. Ensure the scenario is realistic and educational

OUTPUT: Return the IMPROVED simulation in the same JSON format, with:
- Enhanced cultural notes and context
- More natural dialogue if needed
- Improved vocabulary explanations
- Better cultural etiquette guidance
- Any necessary corrections

If the simulation is culturally appropriate, you can return it with minimal changes but add review_status: "approved".
"""

        response = client.chat.completions.create(
            model="gpt-4",
            messages=[
                {"role": "system", "content": "You are an expert French language and culture consultant."},
                {"role": "user", "content": review_prompt}
            ],
            temperature=0.3,
            max_tokens=3000
        )

        reviewed_content = response.choices[0].message.content

        # Parse the improved simulation
        if "```json" in reviewed_content:
            json_start = reviewed_content.find("```json") + 7
            json_end = reviewed_content.find("```", json_start)
            json_text = reviewed_content[json_start:json_end].strip()
        elif "{" in reviewed_content:
            json_start = reviewed_content.find("{")
            json_end = reviewed_content.rfind("}") + 1
            json_text = reviewed_content[json_start:json_end]
        else:
            # If no JSON, return original with review notes
            simulation["review_notes"] = reviewed_content
            return simulation

        improved_simulation = json.loads(json_text)
        improved_simulation["review_status"] = "reviewed"
        improved_simulation["review_timestamp"] = datetime.now().isoformat()

        logging.info("✅ OpenAI review successful!")
        logging.info(f"📝 Review status: {improved_simulation.get('review_status', 'unknown')}")

        return improved_simulation

    except Exception as e:
        logging.error(f"❌ OpenAI review failed: {str(e)}")
        # Return original simulation if review fails
        simulation["review_status"] = "review_failed"
        simulation["review_error"] = str(e)
        return simulation

def test_supabase_upload(simulation):
    """Test Supabase upload"""

    from supabase import create_client
    import uuid

    logging.info("📤 Testing Supabase upload...")

    try:
        supabase = create_client(SUPABASE_URL, SUPABASE_KEY)

        # Test connection first
        result = supabase.table("simulation_personas").select("id").limit(1).execute()
        logging.info(f"✅ Supabase connection successful! Found {len(result.data)} personas")

        # For testing, just log what we would upload
        logging.info("📝 Would upload simulation:")
        logging.info(f"   Title: {simulation.get('title', 'Unknown')}")
        logging.info(f"   Description: {simulation.get('description', 'No description')}")
        logging.info(f"   Dialogue exchanges: {len(simulation.get('dialogue', []))}")
        logging.info(f"   Vocabulary items: {len(simulation.get('vocabulary_focus', []))}")

        # Save to file for inspection
        with open(f"test_simulation_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json", "w") as f:
            json.dump(simulation, f, indent=2, ensure_ascii=False)

        logging.info("✅ Simulation saved to file for inspection")
        return True

    except Exception as e:
        logging.error(f"❌ Supabase test failed: {str(e)}")
        return False

def main():
    """Test the complete pipeline"""

    print("🧪 Testing NIRA Simulation Generation Pipeline")
    print("=" * 50)

    # Step 1: Test Gemini generation
    print("\n1. Testing Gemini generation...")
    simulation = test_gemini_generation()

    if not simulation:
        print("❌ Pipeline test failed at Gemini generation")
        return

    # Step 2: Test OpenAI review
    print("\n2. Testing OpenAI cultural review...")
    reviewed_simulation = test_openai_review(simulation)

    if not reviewed_simulation:
        print("❌ Pipeline test failed at OpenAI review")
        return

    # Step 3: Test Supabase upload
    print("\n3. Testing Supabase upload...")
    upload_success = test_supabase_upload(reviewed_simulation)

    if upload_success:
        print("\n🎉 Pipeline test successful!")
        print("✅ Gemini generation: Working")
        print("✅ OpenAI review: Working")
        print("✅ Supabase connection: Working")
        print("\n📝 Ready for production simulation generation!")
    else:
        print("❌ Pipeline test failed at Supabase upload")

if __name__ == "__main__":
    main()
