#!/usr/bin/env python3
"""
Test script to generate a single lesson for verification
"""

import json
from comprehensive_lesson_generator import Comprehensive<PERSON>essonGenerator, LessonConfig

def test_single_lesson():
    """Test generating a single lesson"""
    print("🧪 Testing Single Lesson Generation")
    print("=" * 40)
    
    # Initialize
    config = LessonConfig()
    generator = ComprehensiveLessonGenerator(config)
    
    # Test with Korean A1 lesson
    language_name = "Korean"
    language_code = "ko"
    level = "A1"
    topic = "Basic Greetings and Introductions"
    
    print(f"📚 Generating: {language_name} {level} - {topic}")
    
    # Generate content
    content = generator._generate_lesson_content(language_name, language_code, level, topic)
    
    if content:
        print("✅ Content generated successfully!")
        print(f"📝 Title: {content['title']}")
        print(f"📖 Description: {content['description']}")
        print(f"📚 Vocabulary items: {len(content.get('vocabulary', []))}")
        print(f"📝 Grammar points: {len(content.get('grammar_points', []))}")
        print(f"💬 Dialogues: {len(content.get('dialogues', []))}")
        print(f"🎯 Exercises: {len(content.get('exercises', []))}")
        
        # Save to file for inspection
        with open(f"test_lesson_{language_code}_{level}_{topic.replace(' ', '_')}.json", 'w', encoding='utf-8') as f:
            json.dump(content, f, indent=2, ensure_ascii=False)
        
        print(f"💾 Saved to test_lesson_{language_code}_{level}_{topic.replace(' ', '_')}.json")
        
        # Test upload (optional)
        upload_test = input("\n🤔 Test upload to Supabase? (y/N): ").strip().lower()
        if upload_test == 'y':
            language_id = generator._ensure_language_exists(language_name, language_code)
            if language_id:
                success = generator._upload_to_supabase(content, language_id, level, 1)
                if success:
                    print("✅ Upload successful!")
                else:
                    print("❌ Upload failed!")
            else:
                print("❌ Could not create language entry")
        
    else:
        print("❌ Content generation failed!")

if __name__ == "__main__":
    test_single_lesson()
