#!/usr/bin/env python3
"""
Test single simulation generation and upload
"""

import logging
from simulation_generator import SimulationGenerator

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_single_upload():
    """Test generating and uploading a single simulation"""
    
    print("🧪 Testing Single Simulation Upload")
    print("=" * 40)
    
    generator = SimulationGenerator()
    
    # Generate 1 simulation for French
    result = generator.generate_simulation_batch(
        language="French",
        persona_key="beginner_enthusiast", 
        category_key="daily_life",
        scenario="Morning routine conversation"
    )
    
    if result:
        print(f"✅ Success! Generated and uploaded: {result['title']}")
        print(f"📝 Description: {result['description']}")
        print(f"📝 Dialogue exchanges: {len(result.get('dialogue', []))}")
        print(f"📝 Vocabulary items: {len(result.get('vocabulary_focus', []))}")
        return True
    else:
        print("❌ Failed to generate simulation")
        return False

if __name__ == "__main__":
    success = test_single_upload()
    if success:
        print("\n🎉 Single upload test successful!")
    else:
        print("\n❌ Single upload test failed!")
