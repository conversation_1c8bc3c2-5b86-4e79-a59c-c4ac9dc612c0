#!/usr/bin/env python3
"""
Test small batch simulation generation
"""

import logging
from batch_simulation_generator import BatchSimulationGenerator

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_small_batch():
    """Test generating a small batch of simulations"""
    
    print("🧪 Testing Small Batch Generation")
    print("=" * 40)
    
    batch_gen = BatchSimulationGenerator()
    
    # Generate 3 simulations for French
    batch_gen.generate_sample_batch("French", 3)
    
    print("\n✅ Small batch test complete!")

if __name__ == "__main__":
    test_small_batch()
