#!/usr/bin/env python3
"""
NIRA Tier 1 Production Simulation Generator
Generates 250 simulations for each of the 21 Tier 1 languages
Total: 5,250 simulations (21 languages × 250 each)
"""

import logging
import time
from datetime import datetime
from batch_simulation_generator import BatchSimulationGenerator

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'tier1_production_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)

# Tier 1 Languages (21 languages with full features including voice/live chat)
TIER1_LANGUAGES = [
    # Major European Languages
    "English", "Spanish", "French", "German", "Italian", "Portuguese", "Dutch", 
    "Swedish", "Norwegian", "Danish", "Polish",
    
    # Major Asian Languages  
    "Japanese", "Korean", "Chinese", "Hindi", "Vietnamese", "Thai", "Indonesian",
    
    # Major World Languages
    "Arabic", "Russian", "Turkish", "Hebrew"
]

class Tier1ProductionGenerator:
    """Production-scale generator for Tier 1 languages"""
    
    def __init__(self):
        self.batch_gen = BatchSimulationGenerator()
        self.total_languages = len(TIER1_LANGUAGES)
        self.total_target = self.total_languages * 250  # 5,250 simulations
        self.completed_languages = 0
        self.total_generated = 0
        self.total_failed = 0
        self.start_time = None
        
    def generate_tier1_complete(self):
        """Generate complete Tier 1 simulation library"""
        
        print("🌍 TIER 1 PRODUCTION SIMULATION GENERATION")
        print("=" * 60)
        print(f"📊 Languages: {self.total_languages}")
        print(f"📊 Target: {self.total_target:,} simulations (250 per language)")
        print(f"📊 Estimated time: 15-20 hours")
        print(f"📊 Estimated cost: $300-500")
        print()
        
        # Confirm before starting
        print("⚠️  This is a production-scale operation!")
        print("⚠️  It will generate 5,250 high-quality simulations")
        print("⚠️  Please ensure stable internet connection")
        print()
        
        confirm = input("🚀 Start Tier 1 production generation? (yes/no): ")
        
        if confirm.lower() != 'yes':
            print("❌ Generation cancelled.")
            return
        
        self.start_time = time.time()
        
        logging.info(f"🚀 Starting Tier 1 production generation")
        logging.info(f"📊 Target: {self.total_target:,} simulations across {self.total_languages} languages")
        
        # Generate simulations for each Tier 1 language
        for i, language in enumerate(TIER1_LANGUAGES, 1):
            self._generate_language_complete(language, i)
        
        self._print_final_tier1_report()
    
    def generate_tier1_sample(self, simulations_per_language: int = 50):
        """Generate sample batch for Tier 1 languages (for testing)"""
        
        total_target = self.total_languages * simulations_per_language
        
        print("🧪 TIER 1 SAMPLE GENERATION")
        print("=" * 40)
        print(f"📊 Languages: {self.total_languages}")
        print(f"📊 Target: {total_target:,} simulations ({simulations_per_language} per language)")
        print(f"📊 Estimated time: {total_target * 1.0 / 60:.1f} hours")
        print()
        
        confirm = input("🚀 Start Tier 1 sample generation? (yes/no): ")
        
        if confirm.lower() != 'yes':
            print("❌ Generation cancelled.")
            return
        
        self.start_time = time.time()
        
        logging.info(f"🧪 Starting Tier 1 sample generation")
        logging.info(f"📊 Target: {total_target:,} simulations across {self.total_languages} languages")
        
        # Generate sample simulations for each Tier 1 language
        for i, language in enumerate(TIER1_LANGUAGES, 1):
            self._generate_language_sample(language, i, simulations_per_language)
        
        self._print_final_tier1_report()
    
    def _generate_language_complete(self, language: str, language_num: int):
        """Generate complete 250 simulations for a language"""
        
        logging.info(f"\n🔥 [{language_num}/{self.total_languages}] Starting {language}")
        logging.info(f"📊 Target: 250 simulations")
        
        progress = (language_num - 1) / self.total_languages * 100
        logging.info(f"📈 Overall progress: {progress:.1f}%")
        
        # Reset batch generator stats for this language
        self.batch_gen.generator.generated_count = 0
        self.batch_gen.generator.reviewed_count = 0
        self.batch_gen.generator.uploaded_count = 0
        self.batch_gen.generator.failed_count = 0
        
        # Generate full language simulations
        self.batch_gen.generate_full_language_simulations(language)
        
        # Update totals
        language_generated = self.batch_gen.generator.uploaded_count
        language_failed = self.batch_gen.generator.failed_count
        
        self.total_generated += language_generated
        self.total_failed += language_failed
        self.completed_languages += 1
        
        # Language completion report
        success_rate = (language_generated / max(1, language_generated + language_failed)) * 100
        logging.info(f"✅ {language} complete!")
        logging.info(f"   Generated: {language_generated}/250")
        logging.info(f"   Success rate: {success_rate:.1f}%")
        
        # Overall progress update
        overall_progress = (self.completed_languages / self.total_languages) * 100
        logging.info(f"🌍 Tier 1 progress: {self.completed_languages}/{self.total_languages} languages ({overall_progress:.1f}%)")
        logging.info(f"📊 Total generated: {self.total_generated:,}/{self.total_target:,}")
    
    def _generate_language_sample(self, language: str, language_num: int, simulations_per_language: int):
        """Generate sample simulations for a language"""
        
        logging.info(f"\n🧪 [{language_num}/{self.total_languages}] Starting {language} sample")
        logging.info(f"📊 Target: {simulations_per_language} simulations")
        
        # Reset batch generator stats for this language
        self.batch_gen.generator.generated_count = 0
        self.batch_gen.generator.reviewed_count = 0
        self.batch_gen.generator.uploaded_count = 0
        self.batch_gen.generator.failed_count = 0
        
        # Generate language batch
        max_per_persona = simulations_per_language // 5  # 5 personas
        self.batch_gen.generate_language_batch(language, max_per_persona)
        
        # Update totals
        language_generated = self.batch_gen.generator.uploaded_count
        language_failed = self.batch_gen.generator.failed_count
        
        self.total_generated += language_generated
        self.total_failed += language_failed
        self.completed_languages += 1
        
        # Language completion report
        success_rate = (language_generated / max(1, language_generated + language_failed)) * 100
        logging.info(f"✅ {language} sample complete!")
        logging.info(f"   Generated: {language_generated}/{simulations_per_language}")
        logging.info(f"   Success rate: {success_rate:.1f}%")
    
    def _print_final_tier1_report(self):
        """Print final Tier 1 generation report"""
        
        elapsed_time = time.time() - self.start_time
        
        print("\n" + "=" * 60)
        print("🎉 TIER 1 GENERATION COMPLETE!")
        print("=" * 60)
        
        logging.info(f"🎉 TIER 1 GENERATION COMPLETE!")
        logging.info(f"📊 Final Statistics:")
        logging.info(f"   Languages completed: {self.completed_languages}/{self.total_languages}")
        logging.info(f"   Total generated: {self.total_generated:,}")
        logging.info(f"   Total failed: {self.total_failed:,}")
        logging.info(f"   Total time: {elapsed_time/3600:.1f} hours")
        
        if self.total_generated > 0:
            avg_time = elapsed_time / self.total_generated
            overall_success_rate = (self.total_generated / max(1, self.total_generated + self.total_failed)) * 100
            
            logging.info(f"   Average per simulation: {avg_time:.1f} seconds")
            logging.info(f"   Overall success rate: {overall_success_rate:.1f}%")
            
            # Estimate completion percentage
            completion_rate = (self.total_generated / self.total_target) * 100
            logging.info(f"   Target completion: {completion_rate:.1f}%")
        
        print(f"\n🌟 Generated {self.total_generated:,} high-quality simulations!")
        print(f"🚀 NIRA now has comprehensive conversation practice for Tier 1 languages!")
        
        if self.total_failed > 0:
            logging.warning(f"⚠️  {self.total_failed:,} simulations failed. Check logs for details.")

def main():
    """Main function with Tier 1 generation options"""
    
    print("🎭 NIRA Tier 1 Production Generator")
    print("Gemini → OpenAI → Supabase Pipeline")
    print("=" * 50)
    print("1. Complete Tier 1 (5,250 simulations)")
    print("2. Tier 1 Sample (1,050 simulations - 50 per language)")
    print("3. Tier 1 Mini Sample (210 simulations - 10 per language)")
    print("4. Single language test")
    print("5. Exit")
    
    choice = input("\nEnter your choice (1-5): ")
    
    generator = Tier1ProductionGenerator()
    
    if choice == "1":
        generator.generate_tier1_complete()
        
    elif choice == "2":
        generator.generate_tier1_sample(50)
        
    elif choice == "3":
        generator.generate_tier1_sample(10)
        
    elif choice == "4":
        language = input("Enter language (default: Spanish): ") or "Spanish"
        print(f"🧪 Testing {language}...")
        generator.batch_gen.generate_sample_batch(language, 5)
        
    elif choice == "5":
        print("Goodbye!")
        
    else:
        print("Invalid choice. Please try again.")
        main()

if __name__ == "__main__":
    main()
