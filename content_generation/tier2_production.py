#!/usr/bin/env python3
"""
Tier 2 Production Generator
Generate 50 simulations for each Tier 2 language (partial features, no voice)
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from production_realistic_generator import ProductionRealisticGenerator
import time

def generate_tier2():
    """Generate Tier 2 simulations"""
    
    print("🎯 TIER 2 PRODUCTION - PARTIAL FEATURES")
    print("=" * 60)
    print("📊 Target: 18 languages × 50 simulations = 900 total")
    print("🎭 Features: Partial features, no voice support")
    print("💰 Estimated cost: ~$0.90")
    print()
    
    generator = ProductionRealisticGenerator()
    
    # Tier 2 languages (18 languages with partial features)
    tier2_languages = [
        "Bulgarian", "Croatian", "Czech", "Estonian", "Finnish", 
        "Greek", "Hungarian", "Latvian", "Lithuanian", "Romanian",
        "Serbian", "Slovak", "Slovenian", "Ukrainian", "Catalan",
        "Basque", "Welsh", "Irish"
    ]
    
    # Enhanced realistic scenarios for Tier 2
    scenarios = [
        "Ordering dinner at a traditional restaurant",
        "Shopping for groceries at local market",
        "Checking into hotel and handling room issues",
        "Asking for directions in busy city center",
        "Buying train tickets and understanding schedules",
        "Shopping for clothes and asking about sizes",
        "Opening bank account as foreigner",
        "Visiting pharmacy for medicine and advice",
        "Job interview for local position",
        "Reporting theft to police station",
        "Asking about menu items and dietary restrictions",
        "Handling payment and tipping at restaurant",
        "Ordering coffee and pastries at local café",
        "Asking for cooking advice at food market",
        "Ordering takeout food by phone",
        "Complaining politely about food quality",
        "Making restaurant reservation for special occasion",
        "Asking for restaurant recommendations from locals",
        "Dealing with flight delays at airport",
        "Renting a car and understanding insurance",
        "Taking taxi and explaining destination",
        "Using public transportation during rush hour",
        "Asking for help with heavy luggage",
        "Booking tours and activities with guide",
        "Handling lost passport at embassy",
        "Returning defective item to store",
        "Getting haircut at local salon",
        "Buying phone plan and understanding options",
        "Negotiating price at local market",
        "Getting car repaired at mechanic",
        "Booking appointment with doctor",
        "Sending package at post office",
        "Participating in business meeting",
        "Networking at professional event",
        "Handling workplace conflict diplomatically",
        "Presenting ideas to international team",
        "Negotiating contract terms politely",
        "Asking for raise or promotion",
        "Training new colleague from abroad",
        "Attending client dinner meeting",
        "Giving presentation to local audience",
        "Seeking help when lost in unfamiliar area",
        "Handling medical emergency at hospital",
        "Dealing with landlord about apartment issues",
        "Resolving billing dispute with utility company",
        "Getting help with broken down car",
        "Handling insurance claim after accident",
        "Seeking legal advice for visa issues",
        "Reporting noise complaint to authorities",
        "Getting emergency dental treatment"
    ]
    
    personas = ["traveler", "beginner_enthusiast", "busy_professional"]
    cefr_levels = ["A1", "A2", "B1"]
    
    total_completed = 0
    
    for language in tier2_languages:
        print(f"\n🌍 Starting {language}: 50 simulations")
        
        for i in range(50):
            scenario = scenarios[i % len(scenarios)]
            persona = personas[i % len(personas)]
            cefr = cefr_levels[i % len(cefr_levels)]
            
            print(f"📝 {language} ({i+1}/50): {scenario}")
            
            result = generator.generate_realistic_simulation_cost_effective(
                language=language,
                persona_key=persona,
                scenario=scenario,
                cefr_level=cefr
            )
            
            if result:
                print(f"✅ Success: {result['title']}")
                total_completed += 1
            else:
                print(f"❌ Failed: {scenario}")
            
            # Progress update every 10 simulations
            if (i + 1) % 10 == 0:
                progress = ((i + 1) / 50) * 100
                print(f"📊 {language} Progress: {i+1}/50 ({progress:.1f}%)")
            
            time.sleep(2)
        
        print(f"✅ {language} completed!")
    
    # Final report
    total_cost = generator.cost_tracking["total_cost"]
    
    print(f"\n🎉 TIER 2 PRODUCTION COMPLETE!")
    print("=" * 60)
    print(f"📊 Total simulations: {total_completed}")
    print(f"📊 Total cost: ${total_cost:.3f}")
    print(f"📊 Cost per simulation: ${total_cost/max(1, total_completed):.3f}")
    print("🎭 Features: Partial support, text-based learning")

if __name__ == "__main__":
    generate_tier2()
