#!/usr/bin/env python3
"""
Upload Audio Files to Supabase Storage and Update Database URLs
This script uploads all local audio files to Supabase Storage and updates the lesson database with the new URLs.
"""

import os
import json
import requests
import time
from pathlib import Path
from typing import Dict, List, Optional, Tuple

# Supabase Configuration
SUPABASE_PROJECT_ID = "lyaojebttnqilmdosmjk"
SUPABASE_URL = f"https://{SUPABASE_PROJECT_ID}.supabase.co"
SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"
STORAGE_BUCKET = "lesson-audio"

# Local audio directory
LOCAL_AUDIO_DIR = "/Users/<USER>/Documents/NIRA/Assets/Audio"

class SupabaseAudioUploader:
    def __init__(self):
        self.supabase_url = SUPABASE_URL
        self.headers = {
            "apikey": SUPABASE_ANON_KEY,
            "Authorization": f"Bearer {SUPABASE_ANON_KEY}",
            "Content-Type": "application/json"
        }
        self.uploaded_files = []
        self.failed_uploads = []
        
    def upload_audio_file(self, local_file_path: str, storage_file_path: str) -> Optional[str]:
        """Upload a single audio file to Supabase Storage."""
        try:
            # Read the file
            with open(local_file_path, 'rb') as f:
                file_data = f.read()
            
            # Upload to Supabase Storage
            upload_url = f"{self.supabase_url}/storage/v1/object/{STORAGE_BUCKET}/{storage_file_path}"
            
            upload_headers = {
                "apikey": SUPABASE_ANON_KEY,
                "Authorization": f"Bearer {SUPABASE_ANON_KEY}",
                "Content-Type": "audio/mpeg"
            }
            
            print(f"📤 Uploading: {os.path.basename(local_file_path)}")
            response = requests.post(upload_url, data=file_data, headers=upload_headers, timeout=60)
            
            if response.status_code in [200, 201]:
                # Get the public URL
                public_url = f"{self.supabase_url}/storage/v1/object/public/{STORAGE_BUCKET}/{storage_file_path}"
                print(f"✅ Uploaded successfully: {public_url}")
                return public_url
            else:
                print(f"❌ Upload failed: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ Error uploading {local_file_path}: {e}")
            return None
    
    def upload_all_audio_files(self) -> Dict[str, str]:
        """Upload all audio files and return a mapping of filename to public URL."""
        print(f"🚀 Starting upload of audio files from: {LOCAL_AUDIO_DIR}")
        
        if not os.path.exists(LOCAL_AUDIO_DIR):
            print(f"❌ Audio directory not found: {LOCAL_AUDIO_DIR}")
            return {}
        
        audio_files = [f for f in os.listdir(LOCAL_AUDIO_DIR) if f.endswith('.mp3')]
        print(f"📁 Found {len(audio_files)} audio files to upload")
        
        url_mapping = {}
        
        for i, filename in enumerate(audio_files, 1):
            local_path = os.path.join(LOCAL_AUDIO_DIR, filename)
            storage_path = f"tamil/a1/{filename}"  # Organize by language and level
            
            print(f"\n[{i}/{len(audio_files)}] Processing: {filename}")
            
            public_url = self.upload_audio_file(local_path, storage_path)
            
            if public_url:
                url_mapping[filename] = public_url
                self.uploaded_files.append({
                    'filename': filename,
                    'local_path': local_path,
                    'storage_path': storage_path,
                    'public_url': public_url
                })
            else:
                self.failed_uploads.append({
                    'filename': filename,
                    'local_path': local_path,
                    'storage_path': storage_path
                })
            
            # Rate limiting - wait between uploads
            if i < len(audio_files):
                time.sleep(1)
        
        print(f"\n📊 Upload Summary:")
        print(f"✅ Successful uploads: {len(self.uploaded_files)}")
        print(f"❌ Failed uploads: {len(self.failed_uploads)}")
        
        return url_mapping
    
    def update_lesson_audio_urls(self, url_mapping: Dict[str, str]) -> bool:
        """Update the lesson database with new Supabase Storage URLs."""
        print(f"\n🔄 Updating lesson database with new audio URLs...")
        
        try:
            # Get the current lesson data
            query_url = f"{self.supabase_url}/rest/v1/lessons?id=eq.c890a530-7d2e-4a5f-9bbd-491daaca823a&select=content_metadata"
            response = requests.get(query_url, headers=self.headers)
            
            if response.status_code != 200:
                print(f"❌ Failed to fetch lesson data: {response.status_code}")
                return False
            
            lesson_data = response.json()
            if not lesson_data:
                print("❌ No lesson data found")
                return False
            
            content_metadata = lesson_data[0]['content_metadata']
            
            # Update vocabulary audio URLs
            if 'vocabulary' in content_metadata:
                for vocab_item in content_metadata['vocabulary']:
                    if 'word_audio_url' in vocab_item:
                        old_url = vocab_item['word_audio_url']
                        filename = os.path.basename(old_url)
                        if filename in url_mapping:
                            vocab_item['word_audio_url'] = url_mapping[filename]
                            print(f"✅ Updated vocab word audio: {filename}")
                    
                    if 'example_audio_url' in vocab_item:
                        old_url = vocab_item['example_audio_url']
                        filename = os.path.basename(old_url)
                        if filename in url_mapping:
                            vocab_item['example_audio_url'] = url_mapping[filename]
                            print(f"✅ Updated vocab example audio: {filename}")
            
            # Update conversation audio URLs
            if 'conversations' in content_metadata:
                for conversation in content_metadata['conversations']:
                    if 'audio_exchanges' in conversation:
                        for exchange in conversation['audio_exchanges']:
                            if 'audio_url' in exchange:
                                old_url = exchange['audio_url']
                                filename = os.path.basename(old_url)
                                if filename in url_mapping:
                                    exchange['audio_url'] = url_mapping[filename]
                                    print(f"✅ Updated conversation audio: {filename}")
            
            # Update the lesson in the database
            update_url = f"{self.supabase_url}/rest/v1/lessons?id=eq.c890a530-7d2e-4a5f-9bbd-491daaca823a"
            update_data = {"content_metadata": content_metadata}
            
            response = requests.patch(update_url, json=update_data, headers=self.headers)
            
            if response.status_code in [200, 204]:
                print(f"✅ Lesson database updated successfully!")
                return True
            else:
                print(f"❌ Failed to update lesson database: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Error updating lesson database: {e}")
            return False
    
    def generate_report(self) -> Dict:
        """Generate a summary report of the upload process."""
        report = {
            'upload_summary': {
                'total_files': len(self.uploaded_files) + len(self.failed_uploads),
                'successful_uploads': len(self.uploaded_files),
                'failed_uploads': len(self.failed_uploads),
                'success_rate': len(self.uploaded_files) / (len(self.uploaded_files) + len(self.failed_uploads)) * 100 if (len(self.uploaded_files) + len(self.failed_uploads)) > 0 else 0
            },
            'uploaded_files': self.uploaded_files,
            'failed_uploads': self.failed_uploads
        }
        
        # Save report to file
        report_filename = f"supabase_audio_upload_report_{int(time.time())}.json"
        with open(report_filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"\n📄 Upload report saved: {report_filename}")
        return report

def main():
    """Main function to upload audio files and update database."""
    print("🎵 Supabase Audio Upload & Database Update")
    print("=" * 60)
    
    uploader = SupabaseAudioUploader()
    
    # Upload all audio files
    url_mapping = uploader.upload_all_audio_files()
    
    if not url_mapping:
        print("❌ No files were uploaded successfully. Exiting.")
        return
    
    # Update lesson database with new URLs
    update_success = uploader.update_lesson_audio_urls(url_mapping)
    
    # Generate report
    report = uploader.generate_report()
    
    # Final summary
    print("\n" + "=" * 60)
    print("📊 FINAL SUMMARY")
    print("=" * 60)
    print(f"📤 Files uploaded: {report['upload_summary']['successful_uploads']}")
    print(f"❌ Upload failures: {report['upload_summary']['failed_uploads']}")
    print(f"📈 Success rate: {report['upload_summary']['success_rate']:.1f}%")
    print(f"🔄 Database updated: {'✅ Yes' if update_success else '❌ No'}")
    
    if update_success and report['upload_summary']['success_rate'] >= 90:
        print("\n🎉 Audio upload and database update completed successfully!")
        print("📱 Your app should now be able to play audio from anywhere!")
        print(f"🌐 Audio files are now available at: {SUPABASE_URL}/storage/v1/object/public/{STORAGE_BUCKET}/tamil/a1/")
    else:
        print("\n⚠️ Upload completed with some issues. Check the report for details.")

if __name__ == "__main__":
    main()
