-- Migration: Add Vietnamese, Indonesian, and Arabic languages with AI agents
-- Date: December 24, 2024
-- Description: Expanding NIRA to support 15 languages total

-- Add the 3 new languages
INSERT INTO languages (id, name, native_name, code, difficulty_level, writing_system, created_at, updated_at) VALUES
(
    '550e8400-e29b-41d4-a716-446655440013',
    'Vietnamese',
    'Tiếng Việt',
    'vi',
    'intermediate',
    'latin',
    NOW(),
    NOW()
),
(
    '550e8400-e29b-41d4-a716-446655440014',
    'Indonesian',
    'Bahasa Indonesia',
    'id',
    'beginner',
    'latin',
    NOW(),
    NOW()
),
(
    '550e8400-e29b-41d4-a716-446655440015',
    'Arabic',
    'العربية',
    'ar',
    'advanced',
    'arabic',
    NOW(),
    NOW()
);

-- Add language levels for Vietnamese (A1-C2)
INSERT INTO language_levels (id, language_id, level_code, level_name, description, order_index, created_at, updated_at) VALUES
('550e8400-e29b-41d4-a716-446655440130', '550e8400-e29b-41d4-a716-446655440013', 'A1', 'Beginner Vietnamese', 'Basic Vietnamese phrases and tones', 1, NOW(), NOW()),
('550e8400-e29b-41d4-a716-446655440131', '550e8400-e29b-41d4-a716-446655440013', 'A2', 'Elementary Vietnamese', 'Simple conversations and family vocabulary', 2, NOW(), NOW()),
('550e8400-e29b-41d4-a716-446655440132', '550e8400-e29b-41d4-a716-446655440013', 'B1', 'Intermediate Vietnamese', 'Daily life conversations and cultural context', 3, NOW(), NOW()),
('550e8400-e29b-41d4-a716-446655440133', '550e8400-e29b-41d4-a716-446655440013', 'B2', 'Upper Intermediate Vietnamese', 'Complex topics and business Vietnamese', 4, NOW(), NOW()),
('550e8400-e29b-41d4-a716-446655440134', '550e8400-e29b-41d4-a716-446655440013', 'C1', 'Advanced Vietnamese', 'Fluent communication and literature', 5, NOW(), NOW()),
('550e8400-e29b-41d4-a716-446655440135', '550e8400-e29b-41d4-a716-446655440013', 'C2', 'Mastery Vietnamese', 'Native-like proficiency and cultural nuances', 6, NOW(), NOW());

-- Add language levels for Indonesian (A1-C2)
INSERT INTO language_levels (id, language_id, level_code, level_name, description, order_index, created_at, updated_at) VALUES
('550e8400-e29b-41d4-a716-446655440140', '550e8400-e29b-41d4-a716-446655440014', 'A1', 'Beginner Indonesian', 'Basic Indonesian greetings and numbers', 1, NOW(), NOW()),
('550e8400-e29b-41d4-a716-446655440141', '550e8400-e29b-41d4-a716-446655440014', 'A2', 'Elementary Indonesian', 'Simple conversations and daily activities', 2, NOW(), NOW()),
('550e8400-e29b-41d4-a716-446655440142', '550e8400-e29b-41d4-a716-446655440014', 'B1', 'Intermediate Indonesian', 'Cultural diversity and gotong royong concepts', 3, NOW(), NOW()),
('550e8400-e29b-41d4-a716-446655440143', '550e8400-e29b-41d4-a716-446655440014', 'B2', 'Upper Intermediate Indonesian', 'Regional variations and formal Indonesian', 4, NOW(), NOW()),
('550e8400-e29b-41d4-a716-446655440144', '550e8400-e29b-41d4-a716-446655440014', 'C1', 'Advanced Indonesian', 'Literature and complex cultural topics', 5, NOW(), NOW()),
('550e8400-e29b-41d4-a716-446655440145', '550e8400-e29b-41d4-a716-446655440014', 'C2', 'Mastery Indonesian', 'Professional and academic Indonesian', 6, NOW(), NOW());

-- Add language levels for Arabic (A1-C2)
INSERT INTO language_levels (id, language_id, level_code, level_name, description, order_index, created_at, updated_at) VALUES
('550e8400-e29b-41d4-a716-446655440150', '550e8400-e29b-41d4-a716-446655440015', 'A1', 'Beginner Arabic', 'Arabic alphabet and basic phrases', 1, NOW(), NOW()),
('550e8400-e29b-41d4-a716-446655440151', '550e8400-e29b-41d4-a716-446655440015', 'A2', 'Elementary Arabic', 'Simple conversations and Islamic greetings', 2, NOW(), NOW()),
('550e8400-e29b-41d4-a716-446655440152', '550e8400-e29b-41d4-a716-446655440015', 'B1', 'Intermediate Arabic', 'Cultural traditions and hospitality customs', 3, NOW(), NOW()),
('550e8400-e29b-41d4-a716-446655440153', '550e8400-e29b-41d4-a716-446655440015', 'B2', 'Upper Intermediate Arabic', 'Classical Arabic and modern dialects', 4, NOW(), NOW()),
('550e8400-e29b-41d4-a716-446655440154', '550e8400-e29b-41d4-a716-446655440015', 'C1', 'Advanced Arabic', 'Literature and religious texts', 5, NOW(), NOW()),
('550e8400-e29b-41d4-a716-446655440155', '550e8400-e29b-41d4-a716-446655440015', 'C2', 'Mastery Arabic', 'Scholarly Arabic and poetry', 6, NOW(), NOW());

-- Add AI agents for the new languages
INSERT INTO agents (id, name, personality, expertise, language_id, cultural_background, teaching_style, avatar_url, is_active, created_at, updated_at) VALUES
(
    '550e8400-e29b-41d4-a716-446655440213',
    'Linh',
    'energetic',
    'Vietnamese tones, family values, and cultural traditions',
    '550e8400-e29b-41d4-a716-446655440013',
    'Vietnamese culture with emphasis on respect for elders and community harmony',
    'Interactive and encouraging, specializing in tone practice and cultural context',
    'https://example.com/avatars/linh.png',
    true,
    NOW(),
    NOW()
),
(
    '550e8400-e29b-41d4-a716-446655440214',
    'Sari',
    'warm',
    'Indonesian diversity, gotong royong, and Bhinneka Tunggal Ika philosophy',
    '550e8400-e29b-41d4-a716-446655440014',
    'Indonesian multicultural heritage celebrating unity in diversity',
    'Inclusive and patient, focusing on cultural diversity and mutual assistance concepts',
    'https://example.com/avatars/sari.png',
    true,
    NOW(),
    NOW()
),
(
    '550e8400-e29b-41d4-a716-446655440215',
    'Ahmed',
    'knowledgeable',
    'Arabic script, Islamic culture, and Middle Eastern hospitality traditions',
    '550e8400-e29b-41d4-a716-446655440015',
    'Arab culture with deep knowledge of Islamic traditions and hospitality customs',
    'Scholarly and respectful, emphasizing proper pronunciation and cultural sensitivity',
    'https://example.com/avatars/ahmed.png',
    true,
    NOW(),
    NOW()
);

-- Create indexes for performance optimization
CREATE INDEX IF NOT EXISTS idx_languages_code ON languages(code);
CREATE INDEX IF NOT EXISTS idx_language_levels_language_id ON language_levels(language_id);
CREATE INDEX IF NOT EXISTS idx_agents_language_id ON agents(language_id);
CREATE INDEX IF NOT EXISTS idx_agents_is_active ON agents(is_active); 