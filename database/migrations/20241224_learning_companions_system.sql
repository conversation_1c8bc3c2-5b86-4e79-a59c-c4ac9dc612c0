-- Migration: Learning Companions System with 90 Specialized Agents
-- Date: December 24, 2024
-- Description: Creating persona-based learning companions with LangGraph/CrewAI integration

-- Create companion personas table
CREATE TABLE IF NOT EXISTS companion_personas (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(50) NOT NULL UNIQUE,
    description TEXT NOT NULL,
    icon VARCHAR(10) NOT NULL,
    specialties TEXT[] NOT NULL,
    is_available_for_indian_languages_only BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create learning companions table (90 total agents)
CREATE TABLE IF NOT EXISTS learning_companions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    persona_id UUID NOT NULL REFERENCES companion_personas(id),
    language_id UUID NOT NULL REFERENCES languages(id),
    name VARCHAR(100) NOT NULL,
    avatar VARCHAR(10) NOT NULL,
    description TEXT NOT NULL,
    system_prompt TEXT NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(persona_id, language_id)
);

-- Create companion conversations table
CREATE TABLE IF NOT EXISTS companion_conversations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id),
    companion_id UUID NOT NULL REFERENCES learning_companions(id),
    system_prompt TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_active_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT TRUE
);

-- Create companion messages table
CREATE TABLE IF NOT EXISTS companion_messages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    conversation_id UUID NOT NULL REFERENCES companion_conversations(id),
    role VARCHAR(20) NOT NULL CHECK (role IN ('user', 'assistant', 'system')),
    content TEXT NOT NULL,
    metadata JSONB DEFAULT '{}',
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert the 6 companion personas
INSERT INTO companion_personas (id, name, description, icon, specialties, is_available_for_indian_languages_only) VALUES
(
    '550e8400-e29b-41d4-a716-************',
    'Beginner Enthusiast',
    'Patient and encouraging, perfect for starting your language journey with confidence',
    '🌟',
    ARRAY['Basic vocabulary', 'Pronunciation', 'Simple conversations', 'Encouragement'],
    FALSE
),
(
    '550e8400-e29b-41d4-a716-************',
    'Busy Professional',
    'Efficient and practical, focused on business communication and travel essentials',
    '💼',
    ARRAY['Business language', 'Travel phrases', 'Professional communication', 'Time-efficient learning'],
    FALSE
),
(
    '550e8400-e29b-41d4-a716-************',
    'Cultural Seeker',
    'Deep cultural knowledge, traditions, customs, and authentic cultural immersion',
    '🏛️',
    ARRAY['Cultural traditions', 'Historical context', 'Customs & etiquette', 'Authentic expressions'],
    FALSE
),
(
    '550e8400-e29b-41d4-a716-************',
    'Social Learner',
    'Interactive and conversational, great for practicing real-world social situations',
    '👥',
    ARRAY['Casual conversations', 'Social situations', 'Group interactions', 'Modern slang'],
    FALSE
),
(
    '550e8400-e29b-41d4-a716-************',
    'NRI Helper',
    'Understands diaspora experience, bridging heritage language with modern life',
    '🌍',
    ARRAY['Heritage connection', 'Family communication', 'Cultural bridge', 'Diaspora experience'],
    TRUE
),
(
    '550e8400-e29b-41d4-a716-************',
    'Master Guide',
    'Comprehensive expertise, can handle any topic with advanced language skills',
    '🎓',
    ARRAY['Advanced grammar', 'Literature', 'Complex topics', 'All skill levels'],
    FALSE
);

-- Function to generate system prompt for companions
CREATE OR REPLACE FUNCTION generate_companion_system_prompt(
    persona_name TEXT,
    language_name TEXT,
    language_display_name TEXT
) RETURNS TEXT AS $$
DECLARE
    base_prompt TEXT;
    role_prompt TEXT;
BEGIN
    base_prompt := format('You are a specialized language learning companion for %s. 

STRICT GUIDELINES:
- ONLY discuss language learning topics related to %s
- NEVER engage in conversations outside of language education
- If asked about non-educational topics, politely redirect to language learning
- Focus exclusively on your specialized role as described below
- Maintain professional educational boundaries at all times

', language_display_name, language_display_name);

    CASE persona_name
        WHEN 'Beginner Enthusiast' THEN
            role_prompt := 'ROLE: Beginner Enthusiast Companion
- You are patient, encouraging, and perfect for beginners
- Focus on basic vocabulary, pronunciation, and simple conversations
- Always provide positive reinforcement and build confidence
- Break down complex concepts into simple, digestible parts
- Use encouraging language and celebrate small victories';
        
        WHEN 'Busy Professional' THEN
            role_prompt := 'ROLE: Busy Professional Companion
- You are efficient and practical, focused on business communication
- Prioritize business language, travel phrases, and professional communication
- Provide time-efficient learning strategies
- Focus on immediately practical language skills
- Keep conversations concise and goal-oriented';
        
        WHEN 'Cultural Seeker' THEN
            role_prompt := format('ROLE: Cultural Seeker Companion
- You have deep knowledge of %s culture, traditions, and customs
- Provide authentic cultural context for language learning
- Explain cultural nuances, etiquette, and historical background
- Share traditional expressions and their cultural significance
- Help learners understand the cultural context behind the language', language_display_name);
        
        WHEN 'Social Learner' THEN
            role_prompt := 'ROLE: Social Learner Companion
- You are interactive and conversational, great for social situations
- Focus on casual conversations, social interactions, and modern usage
- Practice real-world social scenarios and group interactions
- Teach contemporary slang and informal expressions appropriately
- Encourage interactive and engaging conversations';
        
        WHEN 'NRI Helper' THEN
            role_prompt := 'ROLE: NRI Helper Companion (for Indian languages)
- You understand the diaspora experience and heritage language connection
- Help bridge heritage language with modern life
- Understand the unique challenges of maintaining cultural language abroad
- Provide context for family communication and cultural traditions
- Support reconnection with cultural roots through language';
        
        WHEN 'Master Guide' THEN
            role_prompt := format('ROLE: Master Guide Companion
- You have comprehensive expertise in all aspects of %s
- Can handle advanced grammar, literature, and complex topics
- Adapt to any skill level from beginner to advanced
- Provide detailed explanations and comprehensive language guidance
- Serve as the most knowledgeable companion for all language needs', language_display_name);
        
        ELSE
            role_prompt := 'ROLE: General Language Companion
- Provide helpful language learning assistance
- Focus on educational content only
- Be encouraging and supportive';
    END CASE;

    RETURN base_prompt || role_prompt;
END;
$$ LANGUAGE plpgsql;

-- Insert learning companions for all 15 languages
-- This creates 90 total companions (6 personas × 15 languages, minus NRI Helper for non-Indian languages)

-- French companions
INSERT INTO learning_companions (persona_id, language_id, name, avatar, description, system_prompt) VALUES
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Sophie', '🌟', 'Patient and encouraging, perfect for starting your French journey with confidence', generate_companion_system_prompt('Beginner Enthusiast', 'French', 'French')),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Laurent', '💼', 'Efficient and practical, focused on business French and travel essentials', generate_companion_system_prompt('Busy Professional', 'French', 'French')),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Amélie', '🏛️', 'Deep knowledge of French culture, traditions, and authentic cultural immersion', generate_companion_system_prompt('Cultural Seeker', 'French', 'French')),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Pierre', '👥', 'Interactive and conversational, great for practicing real-world French social situations', generate_companion_system_prompt('Social Learner', 'French', 'French')),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Marie', '🎓', 'Comprehensive French expertise, can handle any topic with advanced language skills', generate_companion_system_prompt('Master Guide', 'French', 'French'));

-- Spanish companions
INSERT INTO learning_companions (persona_id, language_id, name, avatar, description, system_prompt) VALUES
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Elena', '🌟', 'Patient and encouraging, perfect for starting your Spanish journey with confidence', generate_companion_system_prompt('Beginner Enthusiast', 'Spanish', 'Spanish')),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Carlos', '💼', 'Efficient and practical, focused on business Spanish and travel essentials', generate_companion_system_prompt('Busy Professional', 'Spanish', 'Spanish')),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Isabella', '🏛️', 'Deep knowledge of Spanish culture, traditions, and authentic cultural immersion', generate_companion_system_prompt('Cultural Seeker', 'Spanish', 'Spanish')),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Diego', '👥', 'Interactive and conversational, great for practicing real-world Spanish social situations', generate_companion_system_prompt('Social Learner', 'Spanish', 'Spanish')),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Carmen', '🎓', 'Comprehensive Spanish expertise, can handle any topic with advanced language skills', generate_companion_system_prompt('Master Guide', 'Spanish', 'Spanish'));

-- English companions
INSERT INTO learning_companions (persona_id, language_id, name, avatar, description, system_prompt) VALUES
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Emma', '🌟', 'Patient and encouraging, perfect for starting your English journey with confidence', generate_companion_system_prompt('Beginner Enthusiast', 'English', 'English')),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'James', '💼', 'Efficient and practical, focused on business English and professional communication', generate_companion_system_prompt('Busy Professional', 'English', 'English')),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Oliver', '🏛️', 'Deep knowledge of English culture, traditions, and authentic cultural immersion', generate_companion_system_prompt('Cultural Seeker', 'English', 'English')),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Sophia', '👥', 'Interactive and conversational, great for practicing real-world English social situations', generate_companion_system_prompt('Social Learner', 'English', 'English')),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'William', '🎓', 'Comprehensive English expertise, can handle any topic with advanced language skills', generate_companion_system_prompt('Master Guide', 'English', 'English'));

-- Japanese companions
INSERT INTO learning_companions (persona_id, language_id, name, avatar, description, system_prompt) VALUES
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Yuki', '🌟', 'Patient and encouraging, perfect for starting your Japanese journey with confidence', generate_companion_system_prompt('Beginner Enthusiast', 'Japanese', 'Japanese')),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Hiroshi', '💼', 'Efficient and practical, focused on business Japanese and professional communication', generate_companion_system_prompt('Busy Professional', 'Japanese', 'Japanese')),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Sakura', '🏛️', 'Deep knowledge of Japanese culture, traditions, and authentic cultural immersion', generate_companion_system_prompt('Cultural Seeker', 'Japanese', 'Japanese')),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Takeshi', '👥', 'Interactive and conversational, great for practicing real-world Japanese social situations', generate_companion_system_prompt('Social Learner', 'Japanese', 'Japanese')),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Akiko', '🎓', 'Comprehensive Japanese expertise, can handle any topic with advanced language skills', generate_companion_system_prompt('Master Guide', 'Japanese', 'Japanese'));

-- Tamil companions (includes NRI Helper)
INSERT INTO learning_companions (persona_id, language_id, name, avatar, description, system_prompt) VALUES
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Priya', '🌟', 'Patient and encouraging, perfect for starting your Tamil journey with confidence', generate_companion_system_prompt('Beginner Enthusiast', 'Tamil', 'Tamil')),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Ravi', '💼', 'Efficient and practical, focused on business Tamil and professional communication', generate_companion_system_prompt('Busy Professional', 'Tamil', 'Tamil')),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Meera', '🏛️', 'Deep knowledge of Tamil culture, traditions, and authentic cultural immersion', generate_companion_system_prompt('Cultural Seeker', 'Tamil', 'Tamil')),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Karthik', '👥', 'Interactive and conversational, great for practicing real-world Tamil social situations', generate_companion_system_prompt('Social Learner', 'Tamil', 'Tamil')),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Deepa', '🌍', 'Understands Tamil diaspora experience, bridging heritage language with modern life', generate_companion_system_prompt('NRI Helper', 'Tamil', 'Tamil')),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Suresh', '🎓', 'Comprehensive Tamil expertise, can handle any topic with advanced language skills', generate_companion_system_prompt('Master Guide', 'Tamil', 'Tamil'));

-- Continue with remaining languages (Korean, Italian, German, Hindi, Chinese, Portuguese, Telugu, Vietnamese, Indonesian, Arabic)
-- Korean companions
INSERT INTO learning_companions (persona_id, language_id, name, avatar, description, system_prompt) VALUES
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Minji', '🌟', 'Patient and encouraging, perfect for starting your Korean journey with confidence', generate_companion_system_prompt('Beginner Enthusiast', 'Korean', 'Korean')),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Junho', '💼', 'Efficient and practical, focused on business Korean and professional communication', generate_companion_system_prompt('Busy Professional', 'Korean', 'Korean')),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Soyeon', '🏛️', 'Deep knowledge of Korean culture, traditions, and authentic cultural immersion', generate_companion_system_prompt('Cultural Seeker', 'Korean', 'Korean')),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Hyunwoo', '👥', 'Interactive and conversational, great for practicing real-world Korean social situations', generate_companion_system_prompt('Social Learner', 'Korean', 'Korean')),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Jisoo', '🎓', 'Comprehensive Korean expertise, can handle any topic with advanced language skills', generate_companion_system_prompt('Master Guide', 'Korean', 'Korean'));

-- Italian companions
INSERT INTO learning_companions (persona_id, language_id, name, avatar, description, system_prompt) VALUES
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Giulia', '🌟', 'Patient and encouraging, perfect for starting your Italian journey with confidence', generate_companion_system_prompt('Beginner Enthusiast', 'Italian', 'Italian')),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Marco', '💼', 'Efficient and practical, focused on business Italian and professional communication', generate_companion_system_prompt('Busy Professional', 'Italian', 'Italian')),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Francesca', '🏛️', 'Deep knowledge of Italian culture, traditions, and authentic cultural immersion', generate_companion_system_prompt('Cultural Seeker', 'Italian', 'Italian')),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Luca', '👥', 'Interactive and conversational, great for practicing real-world Italian social situations', generate_companion_system_prompt('Social Learner', 'Italian', 'Italian')),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Valentina', '🎓', 'Comprehensive Italian expertise, can handle any topic with advanced language skills', generate_companion_system_prompt('Master Guide', 'Italian', 'Italian'));

-- German companions
INSERT INTO learning_companions (persona_id, language_id, name, avatar, description, system_prompt) VALUES
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Anna', '🌟', 'Patient and encouraging, perfect for starting your German journey with confidence', generate_companion_system_prompt('Beginner Enthusiast', 'German', 'German')),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Klaus', '💼', 'Efficient and practical, focused on business German and professional communication', generate_companion_system_prompt('Busy Professional', 'German', 'German')),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Greta', '🏛️', 'Deep knowledge of German culture, traditions, and authentic cultural immersion', generate_companion_system_prompt('Cultural Seeker', 'German', 'German')),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Max', '👥', 'Interactive and conversational, great for practicing real-world German social situations', generate_companion_system_prompt('Social Learner', 'German', 'German')),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Ingrid', '🎓', 'Comprehensive German expertise, can handle any topic with advanced language skills', generate_companion_system_prompt('Master Guide', 'German', 'German'));

-- Hindi companions (includes NRI Helper)
INSERT INTO learning_companions (persona_id, language_id, name, avatar, description, system_prompt) VALUES
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Anita', '🌟', 'Patient and encouraging, perfect for starting your Hindi journey with confidence', generate_companion_system_prompt('Beginner Enthusiast', 'Hindi', 'Hindi')),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Rajesh', '💼', 'Efficient and practical, focused on business Hindi and professional communication', generate_companion_system_prompt('Busy Professional', 'Hindi', 'Hindi')),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Kavya', '🏛️', 'Deep knowledge of Hindi culture, traditions, and authentic cultural immersion', generate_companion_system_prompt('Cultural Seeker', 'Hindi', 'Hindi')),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Arjun', '👥', 'Interactive and conversational, great for practicing real-world Hindi social situations', generate_companion_system_prompt('Social Learner', 'Hindi', 'Hindi')),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Sunita', '🌍', 'Understands Hindi diaspora experience, bridging heritage language with modern life', generate_companion_system_prompt('NRI Helper', 'Hindi', 'Hindi')),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Vikram', '🎓', 'Comprehensive Hindi expertise, can handle any topic with advanced language skills', generate_companion_system_prompt('Master Guide', 'Hindi', 'Hindi'));

-- Chinese companions
INSERT INTO learning_companions (persona_id, language_id, name, avatar, description, system_prompt) VALUES
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Li Wei', '🌟', 'Patient and encouraging, perfect for starting your Chinese journey with confidence', generate_companion_system_prompt('Beginner Enthusiast', 'Chinese', 'Chinese')),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Zhang Ming', '💼', 'Efficient and practical, focused on business Chinese and professional communication', generate_companion_system_prompt('Busy Professional', 'Chinese', 'Chinese')),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Wang Mei', '🏛️', 'Deep knowledge of Chinese culture, traditions, and authentic cultural immersion', generate_companion_system_prompt('Cultural Seeker', 'Chinese', 'Chinese')),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Chen Hao', '👥', 'Interactive and conversational, great for practicing real-world Chinese social situations', generate_companion_system_prompt('Social Learner', 'Chinese', 'Chinese')),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Liu Yan', '🎓', 'Comprehensive Chinese expertise, can handle any topic with advanced language skills', generate_companion_system_prompt('Master Guide', 'Chinese', 'Chinese'));

-- Portuguese companions
INSERT INTO learning_companions (persona_id, language_id, name, avatar, description, system_prompt) VALUES
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Ana', '🌟', 'Patient and encouraging, perfect for starting your Portuguese journey with confidence', generate_companion_system_prompt('Beginner Enthusiast', 'Portuguese', 'Portuguese')),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'João', '💼', 'Efficient and practical, focused on business Portuguese and professional communication', generate_companion_system_prompt('Busy Professional', 'Portuguese', 'Portuguese')),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Beatriz', '🏛️', 'Deep knowledge of Portuguese culture, traditions, and authentic cultural immersion', generate_companion_system_prompt('Cultural Seeker', 'Portuguese', 'Portuguese')),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Pedro', '👥', 'Interactive and conversational, great for practicing real-world Portuguese social situations', generate_companion_system_prompt('Social Learner', 'Portuguese', 'Portuguese')),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Carla', '🎓', 'Comprehensive Portuguese expertise, can handle any topic with advanced language skills', generate_companion_system_prompt('Master Guide', 'Portuguese', 'Portuguese'));

-- Telugu companions (includes NRI Helper)
INSERT INTO learning_companions (persona_id, language_id, name, avatar, description, system_prompt) VALUES
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Lakshmi', '🌟', 'Patient and encouraging, perfect for starting your Telugu journey with confidence', generate_companion_system_prompt('Beginner Enthusiast', 'Telugu', 'Telugu')),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Venkat', '💼', 'Efficient and practical, focused on business Telugu and professional communication', generate_companion_system_prompt('Busy Professional', 'Telugu', 'Telugu')),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Sita', '🏛️', 'Deep knowledge of Telugu culture, traditions, and authentic cultural immersion', generate_companion_system_prompt('Cultural Seeker', 'Telugu', 'Telugu')),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Krishna', '👥', 'Interactive and conversational, great for practicing real-world Telugu social situations', generate_companion_system_prompt('Social Learner', 'Telugu', 'Telugu')),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Radha', '🌍', 'Understands Telugu diaspora experience, bridging heritage language with modern life', generate_companion_system_prompt('NRI Helper', 'Telugu', 'Telugu')),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Ramesh', '🎓', 'Comprehensive Telugu expertise, can handle any topic with advanced language skills', generate_companion_system_prompt('Master Guide', 'Telugu', 'Telugu'));

-- Vietnamese companions
INSERT INTO learning_companions (persona_id, language_id, name, avatar, description, system_prompt) VALUES
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Linh', '🌟', 'Patient and encouraging, perfect for starting your Vietnamese journey with confidence', generate_companion_system_prompt('Beginner Enthusiast', 'Vietnamese', 'Vietnamese')),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Duc', '💼', 'Efficient and practical, focused on business Vietnamese and professional communication', generate_companion_system_prompt('Busy Professional', 'Vietnamese', 'Vietnamese')),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Mai', '🏛️', 'Deep knowledge of Vietnamese culture, traditions, and authentic cultural immersion', generate_companion_system_prompt('Cultural Seeker', 'Vietnamese', 'Vietnamese')),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Tuan', '👥', 'Interactive and conversational, great for practicing real-world Vietnamese social situations', generate_companion_system_prompt('Social Learner', 'Vietnamese', 'Vietnamese')),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Hoa', '🎓', 'Comprehensive Vietnamese expertise, can handle any topic with advanced language skills', generate_companion_system_prompt('Master Guide', 'Vietnamese', 'Vietnamese'));

-- Indonesian companions
INSERT INTO learning_companions (persona_id, language_id, name, avatar, description, system_prompt) VALUES
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Sari', '🌟', 'Patient and encouraging, perfect for starting your Indonesian journey with confidence', generate_companion_system_prompt('Beginner Enthusiast', 'Indonesian', 'Indonesian')),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Budi', '💼', 'Efficient and practical, focused on business Indonesian and professional communication', generate_companion_system_prompt('Busy Professional', 'Indonesian', 'Indonesian')),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Dewi', '🏛️', 'Deep knowledge of Indonesian culture, traditions, and authentic cultural immersion', generate_companion_system_prompt('Cultural Seeker', 'Indonesian', 'Indonesian')),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Andi', '👥', 'Interactive and conversational, great for practicing real-world Indonesian social situations', generate_companion_system_prompt('Social Learner', 'Indonesian', 'Indonesian')),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Indira', '🎓', 'Comprehensive Indonesian expertise, can handle any topic with advanced language skills', generate_companion_system_prompt('Master Guide', 'Indonesian', 'Indonesian'));

-- Arabic companions
INSERT INTO learning_companions (persona_id, language_id, name, avatar, description, system_prompt) VALUES
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Fatima', '🌟', 'Patient and encouraging, perfect for starting your Arabic journey with confidence', generate_companion_system_prompt('Beginner Enthusiast', 'Arabic', 'Arabic')),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Ahmed', '💼', 'Efficient and practical, focused on business Arabic and professional communication', generate_companion_system_prompt('Busy Professional', 'Arabic', 'Arabic')),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Aisha', '🏛️', 'Deep knowledge of Arabic culture, traditions, and authentic cultural immersion', generate_companion_system_prompt('Cultural Seeker', 'Arabic', 'Arabic')),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Omar', '👥', 'Interactive and conversational, great for practicing real-world Arabic social situations', generate_companion_system_prompt('Social Learner', 'Arabic', 'Arabic')),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Khalid', '🎓', 'Comprehensive Arabic expertise, can handle any topic with advanced language skills', generate_companion_system_prompt('Master Guide', 'Arabic', 'Arabic'));

-- Create indexes for performance optimization
CREATE INDEX IF NOT EXISTS idx_learning_companions_persona_language ON learning_companions(persona_id, language_id);
CREATE INDEX IF NOT EXISTS idx_learning_companions_language ON learning_companions(language_id);
CREATE INDEX IF NOT EXISTS idx_learning_companions_active ON learning_companions(is_active);
CREATE INDEX IF NOT EXISTS idx_companion_conversations_user ON companion_conversations(user_id);
CREATE INDEX IF NOT EXISTS idx_companion_conversations_companion ON companion_conversations(companion_id);
CREATE INDEX IF NOT EXISTS idx_companion_conversations_active ON companion_conversations(is_active);
CREATE INDEX IF NOT EXISTS idx_companion_messages_conversation ON companion_messages(conversation_id);
CREATE INDEX IF NOT EXISTS idx_companion_messages_timestamp ON companion_messages(timestamp);

-- Create function to get companions for a language
CREATE OR REPLACE FUNCTION get_companions_for_language(lang_id UUID)
RETURNS TABLE (
    companion_id UUID,
    persona_name VARCHAR(50),
    companion_name VARCHAR(100),
    avatar VARCHAR(10),
    description TEXT,
    specialties TEXT[]
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        lc.id,
        cp.name,
        lc.name,
        lc.avatar,
        lc.description,
        cp.specialties
    FROM learning_companions lc
    JOIN companion_personas cp ON lc.persona_id = cp.id
    WHERE lc.language_id = lang_id 
    AND lc.is_active = TRUE
    AND (cp.is_available_for_indian_languages_only = FALSE 
         OR lang_id IN (
             SELECT id FROM languages WHERE code IN ('ta', 'hi', 'te')
         ))
    ORDER BY cp.name;
END;
$$ LANGUAGE plpgsql;

-- Drop the helper function as it's no longer needed
DROP FUNCTION IF EXISTS generate_companion_system_prompt(TEXT, TEXT, TEXT); 