# NIRA Agentic AI Technical Architecture

## Overview

This document defines the technical architecture for transforming NIRA into an agentic AI-powered language learning platform. The system leverages multiple specialized AI agents working collaboratively to provide personalized, culturally-aware learning experiences, backed by a comprehensive database schema that supports Duolingo-level features and beyond.

## System Architecture

### High-Level Component Diagram

```
┌─────────────────────────────────────────────────────────────────┐
│                        NIRA iOS App                            │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   Chat UI       │  │  Learning UI    │  │   Progress UI   │ │
│  │   Components    │  │   Components    │  │   Components    │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │            Agent Communication Layer                        │ │
│  │         (WebSocket + REST API Client)                      │ │
│  └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
                                  ↕️ WSS/HTTPS
┌─────────────────────────────────────────────────────────────────┐
│                      Vapor Server                              │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                Agent Orchestration Layer                    │ │
│  │  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │ │
│  │  │  Agent Manager  │  │ Conversation    │  │  Session    │ │ │
│  │  │   & Registry    │  │    Router       │  │  Manager    │ │ │
│  │  └─────────────────┘  └─────────────────┘  └─────────────┘ │ │
│  └─────────────────────────────────────────────────────────────┘ │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │               Learning Content System                       │ │
│  │  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │ │
│  │  │ Progress    │ │ Vocabulary  │ │ Cultural    │          │ │
│  │  │ Tracker     │ │ Manager     │ │ Context     │          │ │
│  │  └─────────────┘ └─────────────┘ └─────────────┘          │ │
│  └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
                                  ↕️
┌─────────────────────────────────────────────────────────────────┐
│                     Database & AI Services                     │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   OpenAI GPT-4  │  │   Supabase      │  │  Content CDN    │ │
│  │   (Agent AI)    │  │   (Learning DB) │  │  (Media)        │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

## Database Schema Architecture

### Core Learning Database (Supabase PostgreSQL)

Our comprehensive database schema supports a full-featured language learning platform that meets and exceeds Duolingo's capabilities:

```sql
-- Core Language & Agent System
✅ languages (5 languages: French, Spanish, English, Japanese, Tamil)
✅ agents (3 AI tutors with distinct personalities and expertise)
✅ users (Enhanced profiles with learning goals and preferences)

-- Learning Content System  
✅ learning_paths (Agent-curated learning journeys)
✅ lessons (Structured content with cultural focus)
✅ exercises (Multi-modal: text, audio, conversation, pronunciation)
✅ vocabulary (Rich database with pronunciation, cultural context)

-- Progress & Analytics
✅ user_progress (Granular lesson tracking with mastery levels)
✅ user_vocabulary (Spaced repetition with forgetting curves)
✅ user_learning_analytics (Daily metrics for personalization)

-- Agent Integration
✅ agent_sessions (Enhanced conversation sessions)
✅ conversation_turns (Detailed AI interaction analysis)
✅ cultural_contexts (Agent-specific cultural explanations)

-- Advanced Features
✅ ai_generated_content (Dynamic content creation)
✅ user_achievements (XP, streaks, badges)
✅ user_session_state (Real-time learning adaptation)
```

### Database-Agent Integration Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                    Agent Data Flow                              │
│                                                                 │
│  User Progress → Agent Analysis → Content Selection → Response  │
│       ↓                ↓                ↓              ↓       │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ │
│  │user_progress│ │conversation │ │ vocabulary  │ │ cultural    │ │
│  │user_vocab   │ │ _turns      │ │ exercises   │ │ _contexts   │ │
│  │analytics    │ │ sessions    │ │ lessons     │ │ achievements│ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

## Agent Ecosystem Design

### Enhanced Agent Personalities (Database-Driven)

#### 1. Marie - French Tutor Agent 🇫🇷
**Database Profile**:
- **Personality**: `{"patient": true, "detail_oriented": true, "culturally_aware": true}`
- **Expertise**: `["grammar", "pronunciation", "culture", "formal_language"]`
- **Teaching Style**: `"gentle"`
- **Cultural Focus**: French etiquette, dining culture, daily life

**Enhanced Capabilities**:
- Accesses user vocabulary progress from `user_vocabulary` table
- Provides cultural context from `cultural_contexts` with French etiquette
- Generates exercises dynamically based on user progress
- Tracks conversation turns with detailed analysis

#### 2. Carlos - Spanish Tutor Agent 🇪🇸  
**Database Profile**:
- **Personality**: `{"enthusiastic": true, "warm": true, "motivational": true}`
- **Expertise**: `["conversation", "pronunciation", "culture", "informal_language"]`
- **Teaching Style**: `"encouraging"`
- **Cultural Focus**: Latino warmth, family values, celebrations

**Enhanced Capabilities**:
- Emphasizes cultural warmth through `cultural_contexts` integration
- Adapts difficulty based on `user_learning_analytics`
- Creates personalized learning paths in `user_learning_paths`
- Provides pronunciation feedback stored in `conversation_turns`

#### 3. Pierre - Conversation Partner Agent 🗣️
**Database Profile**:
- **Personality**: `{"friendly": true, "natural": true, "authentic": true}`
- **Expertise**: `["conversation", "slang", "culture", "everyday_language"]`
- **Teaching Style**: `"casual"`
- **Cultural Focus**: French slang, regional differences, modern culture

**Enhanced Capabilities**:
- Analyzes conversation context from `agent_sessions`
- Provides natural corrections stored in `conversation_turns.corrections_made`
- Teaches colloquial expressions from vocabulary database
- Maintains authentic personality state across sessions

### Agent Technical Implementation

```swift
// Enhanced Agent with Database Integration
class DatabaseDrivenAgent: AgentProtocol {
    let agentId: UUID
    let supabaseClient: SupabaseClient
    let openAIService: OpenAIService
    
    // Load agent personality and expertise from database
    func loadAgentProfile() async throws -> AgentProfile {
        return try await supabaseClient
            .from("agents")
            .select("*")
            .eq("id", agentId)
            .single()
            .execute()
    }
    
    // Get user's learning context for personalized responses
    func getUserLearningContext(userId: UUID) async throws -> LearningContext {
        // Load user progress, vocabulary, recent sessions
        let progress = try await supabaseClient
            .from("user_progress")
            .select("*, lessons(*)")
            .eq("user_id", userId)
            .execute()
            
        let vocabulary = try await supabaseClient
            .from("user_vocabulary")
            .select("*, vocabulary(*)")
            .eq("user_id", userId)
            .eq("next_review_date", Date.now)
            .execute()
            
        return LearningContext(progress: progress, vocabulary: vocabulary)
    }
    
    // Generate AI response with cultural and learning context
    func generateResponse(
        userMessage: String,
        learningContext: LearningContext
    ) async throws -> AgentResponse {
        let agentProfile = try await loadAgentProfile()
        let culturalContext = try await getCulturalContext(for: userMessage)
        
        let prompt = buildEducationalPrompt(
            agent: agentProfile,
            userMessage: userMessage,
            learningContext: learningContext,
            culturalContext: culturalContext
        )
        
        let response = try await openAIService.generateResponse(prompt: prompt)
        
        // Store conversation turn with detailed analysis
        try await storeConversationTurn(
            response: response,
            userMessage: userMessage,
            analysis: learningContext
        )
        
        return response
    }
}
```

## Learning Content Integration

### Dynamic Content Generation

```swift
class ContentGenerationService {
    // Generate exercises based on user progress and gaps
    func generatePersonalizedExercise(
        userId: UUID,
        targetVocabulary: [VocabularyWord],
        difficulty: DifficultyLevel
    ) async throws -> Exercise {
        
        // Analyze user's weak areas from analytics
        let analytics = try await getUserAnalytics(userId: userId)
        let mistakes = analytics.commonMistakes
        
        // Create targeted exercise addressing weaknesses
        let exercise = try await aiGenerateExercise(
            vocabulary: targetVocabulary,
            mistakes: mistakes,
            difficulty: difficulty
        )
        
        // Store in ai_generated_content for tracking
        try await supabaseClient
            .from("ai_generated_content")
            .insert(exercise.toDatabaseModel())
            .execute()
            
        return exercise
    }
}
```

### Spaced Repetition Integration

```swift
class VocabularyReviewService {
    // Update vocabulary strength based on SuperMemo algorithm
    func updateVocabularyStrength(
        userId: UUID,
        vocabularyId: UUID,
        wasCorrect: Bool
    ) async throws {
        
        let userVocab = try await getUserVocabulary(userId: userId, vocabularyId: vocabularyId)
        
        // Calculate new strength and review date
        let newStrength = calculateSpacedRepetition(
            currentStrength: userVocab.strength,
            wasCorrect: wasCorrect,
            easinessFactor: userVocab.easinessFactor
        )
        
        let nextReviewDate = calculateNextReview(
            intervalDays: newStrength.intervalDays
        )
        
        // Update in database
        try await supabaseClient
            .from("user_vocabulary")
            .update([
                "strength": newStrength.strength,
                "next_review_date": nextReviewDate,
                "review_count": userVocab.reviewCount + 1,
                "correct_count": wasCorrect ? userVocab.correctCount + 1 : userVocab.correctCount
            ])
            .eq("user_id", userId)
            .eq("vocabulary_id", vocabularyId)
            .execute()
    }
}
```

## Framework Integration

### CrewAI Integration

```swift
// CrewAI Wrapper
class CrewAIOrchestrator {
    let crew: Crew
    let agents: [CrewAgent]
    
    func initializeCrew(with agents: [AgentDefinition]) async
    func executeTask(task: LearningTask) async -> TaskResult
    func coordinateAgents(for scenario: ImmersiveScenario) async -> ScenarioResult
}

// Agent Task Definition
struct LearningTask {
    let type: TaskType
    let description: String
    let context: LearningContext
    let expectedOutput: OutputType
    let assignedAgent: AgentID
    let collaboratingAgents: [AgentID]
}
```

### LangGraph Integration

```swift
// LangGraph Workflow
class LearningWorkflow {
    let graph: ConversationGraph
    let nodes: [WorkflowNode]
    let edges: [WorkflowEdge]
    
    func executeWorkflow(input: UserInput) async -> WorkflowResult
    func adaptPath(based on: UserPerformance) async -> PathAdaptation
    func visualizeProgress() -> ProgressVisualization
}

// Workflow Nodes
enum WorkflowNode {
    case agentInteraction(AgentID)
    case userChoice(ChoiceOptions)
    case assessment(AssessmentType)
    case scenarioTransition(ScenarioChange)
    case culturalInsight(CulturalMoment)
}
```

## API Design

### Agent Interaction Endpoints

```swift
// REST API
POST /api/v1/agents/conversation/start
POST /api/v1/agents/conversation/{sessionId}/message
GET  /api/v1/agents/conversation/{sessionId}/history
POST /api/v1/agents/scenario/create
GET  /api/v1/agents/available
POST /api/v1/agents/{agentId}/configure

// WebSocket Events
// Client → Server
{
    "type": "user_message",
    "sessionId": "uuid",
    "message": "Bonjour!",
    "context": {...}
}

// Server → Client  
{
    "type": "agent_response",
    "agentId": "tutor_agent",
    "sessionId": "uuid", 
    "response": "Bonjour! Comment allez-vous?",
    "context": {...},
    "metadata": {...}
}
```

### Agent Management API

```swift
// Agent Configuration
struct AgentConfiguration {
    let agentId: AgentID
    let personality: AgentPersonality
    let capabilities: [AgentCapability]
    let availability: AgentAvailability
    let languageSupport: [Language]
}

// Agent Status
struct AgentStatus {
    let agentId: AgentID
    let isActive: Bool
    let currentSessions: [SessionID]
    let performance: AgentPerformanceMetrics
    let lastUpdate: Date
}
```

## Infrastructure Requirements

### Server-Side Components

1. **Agent Registry Service**
   - Agent discovery and instantiation
   - Capability matching
   - Load balancing

2. **Conversation Manager**
   - Session lifecycle management
   - Context preservation
   - Message routing

3. **Real-time Communication**
   - WebSocket connection management
   - Message queuing
   - Connection pooling

4. **Agent Memory Store**
   - Redis for session state
   - PostgreSQL for persistent memory
   - Vector store for semantic memory

### Performance Considerations

1. **Latency Optimization**
   - Agent response caching
   - Predictive agent loading
   - Context pre-computation

2. **Scalability**
   - Horizontal agent scaling
   - Session sharding
   - Resource pooling

3. **Reliability**
   - Agent health monitoring
   - Graceful degradation
   - Fallback mechanisms

## Security & Privacy

### Data Protection
- End-to-end encryption for conversations
- User data anonymization for agent training
- GDPR-compliant data handling
- Secure agent-to-agent communication

### Agent Security
- Agent capability sandboxing
- Input validation and sanitization
- Rate limiting and abuse prevention
- Agent behavior monitoring

## Monitoring & Analytics

### Agent Performance Metrics
- Response time and quality
- User satisfaction ratings
- Learning effectiveness scores
- Agent collaboration success

### User Experience Analytics
- Conversation engagement levels
- Learning progression tracking
- Cultural scenario effectiveness
- Agent preference patterns

## Integration with Existing System

### Compatibility Layer
- Seamless fallback to traditional lessons
- Existing user data migration
- Current API backward compatibility
- Gradual feature rollout

### Migration Strategy
1. Add agent endpoints alongside existing APIs
2. Create hybrid UI supporting both modes
3. A/B test agent vs. traditional experiences
4. Gradually increase agent feature exposure
5. Monitor performance and user adoption

---

**Next Steps**: Review `PHASE_IMPLEMENTATION.md` for detailed development phases and implementation timeline. 