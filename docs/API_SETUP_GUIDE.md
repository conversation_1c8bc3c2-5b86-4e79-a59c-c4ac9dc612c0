# 🔐 API Keys Setup Guide

## 🚨 **SECURITY NOTICE**
**Never commit real API keys to version control!** This guide shows you how to set up your development environment securely.

---

## 📋 **Quick Setup (5 minutes)**

### **Step 1: Copy the Template**
```bash
cd NIRA/Config
cp APIKeys.swift.template APIKeys.swift
```

### **Step 2: Get Your API Keys**

#### **🤖 Gemini AI API Key** (Required)
1. Go to [Google AI Studio](https://ai.google.dev/tutorials/setup)
2. Click "Get API Key"
3. Create a new project or select existing
4. Copy your API key

#### **🗄️ Supabase Configuration** (Required)
1. Go to [Supabase Dashboard](https://app.supabase.com)
2. Create a new project or select existing
3. Go to Settings → API
4. Copy:
   - Project URL (e.g., `https://your-project-id.supabase.co`)
   - Anon/Public Key

#### **🔧 OpenAI API Key** (Optional)
1. Go to [OpenAI Platform](https://platform.openai.com/api-keys)
2. Create new secret key
3. Copy the key (starts with `sk-`)

### **Step 3: Configure APIKeys.swift**
Open `NIRA/Config/APIKeys.swift` and replace:

```swift
struct APIKeys {
    static let geminiAPIKey = "YOUR_ACTUAL_GEMINI_KEY_HERE"
    static let supabaseURL = "https://your-actual-project-id.supabase.co"
    static let supabaseAnonKey = "YOUR_ACTUAL_SUPABASE_ANON_KEY_HERE"
    static let openAIAPIKey = "YOUR_ACTUAL_OPENAI_KEY_HERE" // Optional
}
```

### **Step 4: Verify Setup**
Build and run the app. You should see:
- ✅ No "API keys not configured" errors
- ✅ AI chat responses work
- ✅ Supabase connection successful

---

## 🔒 **Security Best Practices**

### **✅ DO**
- Use the template system provided
- Keep `APIKeys.swift` in `.gitignore`
- Use environment variables in production
- Rotate keys regularly
- Use different keys for development/production

### **❌ DON'T**
- Commit real API keys to git
- Share keys in chat/email
- Use production keys in development
- Hardcode keys in source code
- Leave keys in screenshots/logs

---

## 🛠️ **Advanced Setup (Production)**

### **Environment Variables (Recommended)**
For production deployments, use environment variables:

```swift
// In APIKeys.swift
static let geminiAPIKey = ProcessInfo.processInfo.environment["GEMINI_API_KEY"] ?? "fallback_key"
```

### **CI/CD Configuration**
Add to your CI/CD secrets:
```bash
GEMINI_API_KEY=your_key_here
SUPABASE_URL=your_url_here
SUPABASE_ANON_KEY=your_key_here
```

---

## 🧪 **Testing Your Setup**

### **1. API Key Validation**
The app will automatically validate your keys on startup:
```swift
do {
    try APIKeys.validateConfiguration()
    print("✅ API keys configured correctly")
} catch {
    print("❌ API configuration error: \(error)")
}
```

### **2. Gemini AI Test**
Try chatting with any AI tutor:
- Open the app
- Go to "AI Agents"
- Select any language/tutor
- Send a message
- You should get an AI response

### **3. Supabase Test**
Check the console for:
```
✅ Supabase client initialized successfully
✅ User profile synced
```

---

## 🚨 **Troubleshooting**

### **"API keys not configured" Error**
- Verify you copied the template correctly
- Check that you replaced ALL placeholder values
- Ensure no typos in your keys

### **"Network connection error"**
- Check your internet connection
- Verify API keys are valid and active
- Check if you've exceeded API quotas

### **"Supabase connection failed"**
- Verify your Supabase project is active
- Check the URL format (must include https://)
- Ensure the anon key is correct

### **Gemini API Errors**
- Verify your API key is active
- Check if you've enabled the Gemini API
- Ensure you have sufficient quota

---

## 📞 **Support**

If you're still having issues:
1. Check the [Troubleshooting Guide](TROUBLESHOOTING.md)
2. Review the [Developer Handoff](DEVELOPER_HANDOFF.md)
3. Create an issue with:
   - Error messages (without API keys!)
   - Steps to reproduce
   - Your development environment

---

## 🔄 **Key Rotation**

### **When to Rotate**
- Suspected key compromise
- Team member changes
- Regular security maintenance (quarterly)
- Moving from development to production

### **How to Rotate**
1. Generate new keys from provider
2. Update `APIKeys.swift`
3. Test functionality
4. Revoke old keys
5. Update team/documentation

---

**Remember: Security is everyone's responsibility! 🛡️** 