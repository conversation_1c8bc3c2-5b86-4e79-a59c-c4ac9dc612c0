# Agent Details for Tier 1 Languages

## Overview
This document provides an in-depth look at the 5 unique agents per language for the 22 Tier 1 languages in the language learning app. Tier 1 languages have full feature support, including voice (text-to-speech and speech-to-text), live chat, and all agent functionalities, powered by Gemini AI and supplemented with Google Cloud TTS/STT for languages like Tamil. Each language has 5 agents (110 agents total for Tier 1), each tied to a persona: <PERSON><PERSON>ner Enthusiast, Busy Professional, Traveler, Cultural Immersion Seeker, and Social Learner. These agents support speaking, writing, reading, and interaction with a Supabase knowledge base, with special attention to non-Latin scripts (e.g., Tamil, Arabic, Hindi).

## Tier 1 Languages (22 Total)
These languages have full support for all features:
1. Spanish (45.6M learners)
2. French (26M learners)
3. English (20M learners, for non-native speakers)
4. Portuguese (5.5M learners)
5. German (17.4M learners)
6. Japanese (21.5M learners)
7. Korean (17.7M learners)
8. Hindi (11.2M learners)
9. Arabic (4.5M learners)
10. Italian (12.2M learners)
11. Chinese (Mandarin) (8.3M learners)
12. Indonesian (0.9M learners)
13. Bengali (0.6M learners)
14. Tamil (0.5M learners, moved to Tier 1 as requested)
15. Russian (6.7M learners)
16. Dutch (3.8M learners)
17. Swedish (3.2M learners)
18. Thai (1.0M learners)
19. Turkish (2.2M learners)
20. Farsi (Persian) (0.5M learners)
21. Tagalog (Filipino) (0.8M learners)
22. Yoruba (0.4M learners)

## Agent Structure and Functionalities
Each language has 5 agents, each tied to a persona, designed to support speaking, writing, reading, and interaction with the Supabase knowledge base. Agents leverage Gemini AI for NLP, content generation, and script rendering, with Google Cloud TTS/STT for voice support in languages like Tamil. The Supabase knowledge base stores user-uploaded content (books, PDFs, attachments), memory, and conversation history.

### 1. Beginner Enthusiast Agent
**Persona**: A youthful, energetic learner passionate about pop culture and gamified learning (e.g., "Anime Fan Aki" for Japanese, "K-Drama Star Soo-jin" for Korean).

**Focus**: Fun, engaging lessons tied to cultural phenomena (e.g., anime for Japanese, Bollywood for Hindi).

**Speaking**:
- **Technology**: Uses Gemini AI TTS for natural speech, supplemented by Google Cloud TTS for languages like Tamil.
- **Example (Japanese)**: Aki says, “Let’s say ‘konnichiwa’ like an anime character!” using Gemini AI TTS. The user repeats, and Gemini AI STT provides feedback: “Great tone, but add more energy!”
- **Example (Tamil)**: Aki says, “Say ‘வணக்கம்’ [vanakkam] like a Tamil movie star!” using Google Cloud TTS. Feedback: “Perfect ‘வ’ [va], but soften the ‘ம்’ [m].”
- **Non-Latin Script Support**: For languages like Arabic, Aki ensures correct pronunciation of guttural sounds (e.g., “ع” [ayn]).

**Writing**:
- **Technology**: Gemini AI analyzes user input, supports non-Latin scripts via Unicode fonts (e.g., Noto Sans Tamil, Noto Serif Arabic).
- **Example (Hindi)**: Aki prompts, “Write ‘मैं स्कूल जाता हूँ’ [Main school jaata hoon] (I go to school).” The user types using a Devanagari keyboard, and Aki corrects: “Good, but ‘हूँ’ needs the nasal dot.”
- **Example (Korean)**: Aki asks, “Write ‘안녕’ [annyeong] (hello).” Feedback: “Correct, but adjust the spacing before ‘녕’.”

**Reading**:
- **Technology**: Gemini AI generates quizzes, renders scripts with transliteration/translation.
- **Example (Spanish)**: Aki presents a sentence: “Hola, ¿cómo estás?” with a quiz: “What does this mean?” (Answer: “Hello, how are you?”).
- **Example (Arabic)**: Aki shows “مرحبا” [marhaban] (hello) with transliteration and translation, asking, “What does this mean?”

**Knowledge Base Interaction**:
- **Example (Japanese)**: A user uploads a manga PDF. Aki extracts a page via OCR (Google Cloud Vision API), stores it in Supabase, and creates a reading exercise: “Read this speech bubble: ‘私は元気です’ [Watashi wa genki desu].”
- **Memory/Conversation History**: Aki recalls, “Last time, you struggled with Japanese ‘は’ [wa]. Let’s practice more.”

### 2. Busy Professional Agent
**Persona**: A career-focused individual needing practical language skills (e.g., "Consultant Priya" for Hindi, "Manager Hans" for German).

**Focus**: Business vocabulary, professional scenarios (e.g., meetings, emails).

**Speaking**:
- **Technology**: Gemini AI TTS/STT, Google Cloud TTS for Tamil.
- **Example (German)**: Hans says, “Let’s practice a meeting intro: ‘Guten Tag, mein Name ist...’” The user responds, and Hans provides feedback: “Good, but stress ‘Tag’ more.”
- **Example (Tamil)**: Priya says, “Introduce yourself in a meeting: ‘என் பெயர் பிரியா’ [En peyar Priya].” Feedback: “Excellent ‘பெயர்’ [peyar], but soften ‘ர’ [ra].”

**Writing**:
- **Technology**: Gemini AI for grammar/spelling correction, Unicode for non-Latin scripts.
- **Example (Hindi)**: Priya prompts, “Write an email: ‘प्रिय महोदय, मैं एक बैठक अनुरोध करना चाहता हूँ’ [Priya Mahoday, Main ek baithak anurodh karna chahta hoon].” Feedback: “Correct, but use ‘चाहता हूँ’ with the nasal dot.”
- **Example (Arabic)**: Priya asks, “Write a formal request: ‘السيد محمد، أود تحديد موعد’ [Al-Sayyid Mohammed, Awad tahdid maw‘id].” Feedback: “Good, but adjust the hamza on ‘أود’.”

**Reading**:
- **Technology**: Gemini AI extracts text from uploaded documents, generates comprehension tasks.
- **Example (French)**: Priya extracts a business letter from a user-uploaded PDF: “Cher Monsieur, nous vous remercions...” The user answers, “Who is this letter addressed to?”
- **Example (Bengali)**: Priya presents a contract excerpt: “আমরা সম্মত হয়েছি” [Amra sammata hayechi], asking, “What does this mean?” (Answer: “We have agreed.”)

**Knowledge Base Interaction**:
- **Example (Tamil)**: A user uploads a Tamil business document. Priya extracts the text, stores it in Supabase, and creates a reading task: “What does ‘ஒப்பந்தம்’ [oppandham] (contract) mean in this context?”
- **Memory/Conversation History**: Priya notes, “You often mispronounce Tamil ‘ழ’ [zha]. Let’s focus on that today.”

### 3. Traveler Agent
**Persona**: An adventurous traveler needing practical phrases (e.g., "Explorer Marco" for Italian, "Wanderer Aisha" for Arabic).

**Focus**: Travel-related vocabulary, cultural tips (e.g., ordering food, asking directions).

**Speaking**:
- **Technology**: Gemini AI TTS/STT, Google Cloud TTS for Tamil.
- **Example (Italian)**: Marco says, “Ask for directions: ‘Dov’è la stazione?’” The user repeats, and Marco responds, “Great, but stress ‘stazione’ on the third syllable.”
- **Example (Tamil)**: Marco says, “Order food: ‘சா�ப்பாடு வேண்டும்’ [saapadu vendum].” Feedback: “Nice ‘சா’ [saa], but ‘வேண்டும்’ [vendum] needs a softer ‘ம்’ [m].”

**Writing**:
- **Technology**: Gemini AI for feedback, Unicode for script rendering.
- **Example (Thai)**: Marco prompts, “Write ‘สวัสดี’ [sawasdee] (hello).” Feedback: “Correct, but adjust the tone mark on ‘ดี’.”
- **Example (Hindi)**: Marco asks, “Write ‘मुझे पानी चाहिए’ [Mujhe paani chahiye] (I need water).” Feedback: “Good, but ‘चाहिए’ should have the correct vowel marker.”

**Reading**:
- **Technology**: Gemini AI processes uploaded images, generates reading tasks.
- **Example (Spanish)**: Marco shows a menu: “Ensalada - 5€.” The user answers, “What is the price?” (Answer: “5 euros.”)
- **Example (Arabic)**: Marco presents a sign: “ممنوع التدخين” [mamnu‘ at-tadkhin] (no smoking), asking, “What does this mean?”

**Knowledge Base Interaction**:
- **Example (Tamil)**: A user uploads an image of a Tamil street sign: “விமான நிலையம்” [Vimaana Nilayam] (airport). Marco creates a reading task: “What does this sign say?”
- **Memory/Conversation History**: Marco recalls, “You struggled with Tamil ‘வி’ [vi] last time. Let’s practice more signs.”

### 4. Cultural Immersion Seeker Agent
**Persona**: A culture enthusiast diving into literature and traditions (e.g., "Poet Rani" for Bengali, "Scholar Li" for Chinese).

**Focus**: Cultural content, literature, media (e.g., poetry, historical texts).

**Speaking**:
- **Technology**: Gemini AI TTS/STT, Google Cloud TTS for Tamil.
- **Example (Bengali)**: Rani recites, “Say this Tagore line: ‘আমার সোনার বাংলা’ [Amar sonar Bangla].” Feedback: “Great, but emphasize ‘সোনার’ [sonar].”
- **Example (Tamil)**: Rani says, “Recite this Thirukkural: ‘அறிவினுள் எல்லாம் தலை’ [Arivinul ellaam thalai].” Feedback: “Perfect ‘அறிவினுள்’ [Arivinul], but soften ‘தலை’ [thalai].”

**Writing**:
- **Technology**: Gemini AI for feedback, Unicode for scripts.
- **Example (Chinese)**: Li prompts, “Write a short poem: ‘月亮很美’ [Yuèliàng hěn měi] (The moon is beautiful).” Feedback: “Correct characters, but adjust tone on ‘美’.”
- **Example (Arabic)**: Rani asks, “Write a line of poetry: ‘الحب نور’ [Al-hubb noor] (Love is light).” Feedback: “Good, but connect ‘الحب’ properly.”

**Reading**:
- **Technology**: Gemini AI extracts text from uploaded books, generates tasks.
- **Example (French)**: Rani extracts a passage from “Le Petit Prince”: “On ne voit bien qu’avec le cœur.” The user answers, “What does this mean?” (Answer: “One sees clearly only with the heart.”)
- **Example (Tamil)**: Rani presents a Thirukkural line: “அறிவினுள் எல்லாம் தலை” [Arivinul ellaam thalai], asking, “What is the theme?” (Answer: “Wisdom is supreme.”)

**Knowledge Base Interaction**:
- **Example (Bengali)**: A user uploads a Tagore book. Rani extracts a poem, stores it in Supabase, and creates a reading task: “What does ‘আমার সোনার বাংলা’ mean?”
- **Memory/Conversation History**: Rani notes, “You enjoyed Tamil poetry last time. Let’s explore more Thirukkural.”

### 5. Social Learner Agent
**Persona**: An expat or social butterfly aiming to connect with locals (e.g., "Expat Sam" for Punjabi, "Friend Nia" for Yoruba).

**Focus**: Social integration, local phrases (e.g., greetings, small talk).

**Speaking**:
- **Technology**: Gemini AI TTS/STT, Google Cloud TTS for Tamil.
- **Example (Punjabi)**: Sam says, “Greet a friend: ‘ਸਤ ਸ੍ਰੀ ਅਕਾਲ’ [Sat Sri Akaal].” Feedback: “Good ‘ਸਤ’ [Sat], but stress ‘ਅਕਾਲ’ [Akaal].”
- **Example (Tamil)**: Sam says, “Say ‘நண்பர்களை சந்திக்கிறேன்’ [Nanbargalai sandhikkiren] (I’m meeting friends).” Feedback: “Nice ‘நண்பர்கள்’ [nanbargal], but ‘சந்திக்கிறேன்’ [sandhikkiren] needs a softer ‘ற’ [ra].”

**Writing**:
- **Technology**: Gemini AI for feedback, Unicode for scripts.
- **Example (Yoruba)**: Nia prompts, “Write a greeting: ‘Ẹ káàbọ̀’ (welcome).” Feedback: “Correct, but adjust the tone mark on ‘bọ̀’.”
- **Example (Hindi)**: Sam asks, “Write a text: ‘आप कैसे हैं?’ [Aap kaise hain?] (How are you?).” Feedback: “Good, but ‘हैं’ needs the nasal dot.”

**Reading**:
- **Technology**: Gemini AI processes uploaded community texts, generates tasks.
- **Example (Portuguese)**: Sam shows a flyer: “Festa na praia às 18h.” The user answers, “What time is the party?” (Answer: “6 PM.”)
- **Example (Tamil)**: Sam presents a notice: “கூட்டம் நாளை’ [Kootam naalai] (Meeting tomorrow), asking, “When is the meeting?”

**Knowledge Base Interaction**:
- **Example (Tamil)**: A user uploads a Tamil community notice. Sam extracts the text, stores it in Supabase, and creates a reading task: “What does ‘கூட்டம்’ [kootam] mean?” (Answer: “Meeting.”)
- **Memory/Conversation History**: Sam recalls, “You practiced Tamil greetings last session. Let’s try small talk today.”

## Non-Latin Script Support
Tier 1 languages with non-Latin scripts include Hindi (Devanagari), Arabic (Arabic script), Japanese (kana/kanji), Korean (Hangul), Chinese (hanzi), Bengali (Bengali script), Tamil (Tamil script), Thai (Thai script), and Farsi (Persian script). Implementation details:
- **Rendering**: Use Unicode fonts (e.g., Noto Sans Tamil, Noto Serif Arabic).
- **Input**: Virtual keyboards (e.g., Gboard for Tamil, Arabic).
- **Feedback**: Gemini AI corrects script-specific errors (e.g., Tamil conjunct consonants, Arabic letter connections).
- **Reading**: Provide transliteration/translation (e.g., Tamil “வணக்கம்” [vanakkam], Arabic “مرحبا” [marhaban]).

## Supabase Knowledge Base Integration
- **Uploads**: Users upload books, PDFs, or images (e.g., a Tamil novel, an Arabic sign). Supabase stores these in the Uploads Table.
- **Extraction**: Gemini AI uses Google Cloud Vision API for OCR (e.g., extracting Tamil script from a PDF).
- **Interaction**: Agents create tasks from uploaded content (e.g., Traveler agent uses a Tamil menu for a reading exercise).
- **Memory/Conversation History**: Supabase logs interactions (e.g., user’s Tamil pronunciation practice) and tracks progress (e.g., mastered 10 Tamil words).

## Example Workflow (Tamil, Tier 1)
**User**: Uploads a Tamil PDF of a Thirukkural chapter and interacts with all 5 agents:
- **Beginner Enthusiast (Aki)**: “Say ‘வணக்கம்’ [vanakkam] like a Tamil movie star!” (Google Cloud TTS/STT).
- **Busy Professional (Priya)**: “Write an email: ‘திரு. ராஜுக்கு, வணக்கம்’ [Thiru. Rajukku, Vanakkam].” Feedback on spacing.
- **Traveler (Marco)**: “Read this menu item: ‘இட்லி’ [idli].” Extracted from an uploaded image.
- **Cultural Immersion Seeker (Rani)**: “Recite this Thirukkural: ‘அறிவினுள் எல்லாம் தலை’ [Arivinul ellaam thalai].” Feedback on pronunciation.
- **Social Learner (Sam)**: “Say ‘நண்பர்களை சந்திக்கிறேன்’ [Nanbargalai sandhikkiren].” Feedback on softening ‘ற’ [ra].

## Conclusion
The 5 agents for Tier 1 languages provide a comprehensive, personalized learning experience, fully supporting speaking, writing, reading, and knowledge base interaction. Special attention to non-Latin scripts ensures accessibility, while Supabase and Gemini AI enable a seamless, scalable solution for 110 agents across 22 languages.