# Agent Details for Tier 2 Languages

## Overview
This document provides an in-depth look at the 5 unique agents per language for the 17 Tier 2 languages in the language learning app. Tier 2 languages have partial feature support, lacking voice capabilities (text-to-speech and speech-to-text) and offering limited live chat (pre-scripted dialogues only), due to limitations in Gemini AI's support. However, they fully support text-based interactions, writing, reading, and integration with a Supabase knowledge base. Each language has 5 agents (85 agents total for Tier 2), each tied to a persona: <PERSON><PERSON>ner Enthusiast, Busy Professional, Traveler, Cultural Immersion Seeker, and Social Learner. These agents support writing, reading, and interaction with user-uploaded content, with special attention to non-Latin scripts (e.g., Telugu, Gujarati, Swahili).

## Tier 2 Languages (17 Total)
These languages have partial support, focusing on text-based interactions:
1. Vietnamese (1.2M learners)
2. Telugu (0.4M learners)
3. Kannada (0.2M learners)
4. Malayalam (0.2M learners)
5. Marathi (0.3M learners)
6. Punjabi (0.3M learners)
7. Gujarati (0.3M learners)
8. Odia (0.2M learners)
9. Assamese (0.1M learners)
10. Sindhi (0.1M learners)
11. <PERSON><PERSON><PERSON><PERSON><PERSON> (0.2M learners)
12. <PERSON><PERSON><PERSON> (0.1M learners)
13. Swahili (0.8M learners)
14. Hebrew (0.7M learners)
15. Greek (2.0M learners)
16. Ukrainian (1.4M learners)
17. Danish (0.5M learners)

## Agent Structure and Functionalities
Each language has 5 agents, each tied to a persona, designed to support writing, reading, and text-based interactions with the Supabase knowledge base. Since Tier 2 languages lack voice support, agents provide phonetic guides and text-based pronunciation instructions. Live chat is limited to pre-scripted dialogues, but agents can still engage users dynamically through text. The Supabase knowledge base stores user-uploaded content (books, PDFs, attachments), memory, and conversation history, powered by Gemini AI for NLP, content generation, and script rendering.

### 1. Beginner Enthusiast Agent
**Persona**: A youthful, energetic learner passionate about pop culture and gamified learning (e.g., "Movie Buff Ravi" for Telugu, "Folktale Fan Esther" for Swahili).

**Focus**: Fun, engaging lessons tied to cultural phenomena (e.g., Telugu cinema, Swahili folktales).

**Speaking (Text-Based Pronunciation Guides)**:
- **Technology**: Since TTS/STT is unavailable, agents provide phonetic guides and pronunciation tips in text form.
- **Example (Telugu)**: Ravi instructs, “Say ‘హాయ్’ [haay] (hi). Pronounce it as ‘haa-yi’ with a soft ‘yi’ at the end.” The user can follow the guide to practice independently.
- **Example (Swahili)**: Esther suggests, “Say ‘Habari’ (hello). It’s pronounced ‘Ha-ba-ree’ with a rolled ‘r’.” The user reads and practices the pronunciation.

**Writing**:
- **Technology**: Gemini AI analyzes user input, supports non-Latin scripts via Unicode fonts (e.g., Noto Sans Telugu, Noto Sans Hebrew).
- **Example (Vietnamese)**: Ravi prompts, “Write ‘Xin chào’ (hello).” The user types, and Ravi corrects: “Good, but add the tone mark on ‘chào’ to make it ‘chào’.”
- **Example (Gujarati)**: Ravi asks, “Write ‘હેલો’ [helo] (hello).” Feedback: “Correct, but ensure the ‘લો’ [lo] has the right vowel marker.”

**Reading**:
- **Technology**: Gemini AI generates quizzes, renders scripts with transliteration/translation.
- **Example (Greek)**: Ravi presents a sentence: “Γεια σου” [Yia sou] (hello) with transliteration and translation, asking, “What does this mean?”
- **Example (Malayalam)**: Ravi shows “നമസ്തേ” [namaste] (hello) with transliteration, asking, “What does this mean?” (Answer: “Hello.”)

**Knowledge Base Interaction**:
- **Example (Telugu)**: A user uploads a Telugu movie script PDF. Ravi extracts a line via OCR (Google Cloud Vision API), stores it in Supabase, and creates a reading exercise: “Read this dialogue: ‘నీవు ఎలా ఉన్నావు?’ [Neevu ela unnaru?] (How are you?).”
- **Memory/Conversation History**: Ravi recalls, “Last time, you struggled with Telugu ‘నీ’ [nee]. Let’s practice more.”

### 2. Busy Professional Agent
**Persona**: A career-focused individual needing practical language skills (e.g., "Entrepreneur Anil" for Marathi, "Consultant Yael" for Hebrew).

**Focus**: Business vocabulary, professional scenarios (e.g., meetings, emails).

**Speaking (Text-Based Pronunciation Guides)**:
- **Technology**: Phonetic guides for pronunciation practice.
- **Example (Marathi)**: Anil instructs, “Say ‘मी एक बैठक आयोजित करतो’ [Mi ek baithak ayojit karto] (I’m organizing a meeting). Pronounce it as ‘Mee ek by-thak a-yo-jit kar-to’ with a soft ‘th’.”
- **Example (Hebrew)**: Yael suggests, “Say ‘אני רוצה לקבוע פגישה’ [Ani rotzeh likvo’a pgisha] (I want to schedule a meeting). Pronounce it as ‘A-nee rot-zeh lik-vo-ah pgee-sha’.”

**Writing**:
- **Technology**: Gemini AI for grammar/spelling correction, Unicode for non-Latin scripts.
- **Example (Punjabi)**: Anil prompts, “Write an email: ‘ਸਤ ਸ੍ਰੀ ਅਕਾਲ, ਮੈਂ ਮੀਟਿੰਗ ਲਈ ਸਮਾਂ ਮੰਗਦਾ ਹਾਂ’ [Sat Sri Akaal, Main meeting layi samay mangda haan] (Hello, I request a meeting time).” Feedback: “Good, but ‘ਮੰਗਦਾ’ [mangda] should be ‘ਮੰਗਦਾ ਹਾਂ’ [mangda haan].”
- **Example (Swahili)**: Yael asks, “Write a formal request: ‘Ninapenda kuweka miadi’ (I’d like to book an appointment).” Feedback: “Correct, well done!”

**Reading**:
- **Technology**: Gemini AI extracts text from uploaded documents, generates comprehension tasks.
- **Example (Danish)**: Anil extracts a business email: “Kære Hr. Jensen, tak for din tid...” The user answers, “Who is this email addressed to?” (Answer: “Mr. Jensen.”)
- **Example (Kannada)**: Yael presents a contract excerpt: “ನಾವು ಒಪ್ಪಿಗೆ ಮಾಡಿದ್ದೇವೆ” [Naavu oppige maadiddeve] (We have agreed), asking, “What does this mean?”

**Knowledge Base Interaction**:
- **Example (Marathi)**: A user uploads a Marathi business document. Anil extracts the text, stores it in Supabase, and creates a reading task: “What does ‘बैठक’ [baithak] (meeting) mean in this context?”
- **Memory/Conversation History**: Anil notes, “You often misspell Marathi ‘बै’ [bai]. Let’s focus on that today.”

### 3. Traveler Agent
**Persona**: An adventurous traveler needing practical phrases (e.g., "Explorer Kiran" for Gujarati, "Wanderer Olena" for Ukrainian).

**Focus**: Travel-related vocabulary, cultural tips (e.g., ordering food, asking directions).

**Speaking (Text-Based Pronunciation Guides)**:
- **Technology**: Phonetic guides for pronunciation practice.
- **Example (Gujarati)**: Kiran instructs, “Say ‘હું પાણી માંગું છું’ [Hun paani maangu chhu] (I want water). Pronounce it as ‘Hoon paa-nee maan-goo chhoo’ with a soft ‘chh’.”
- **Example (Ukrainian)**: Olena suggests, “Say ‘Де вокзал?’ [De vokzal?] (Where is the station?). Pronounce it as ‘Deh vok-zal’ with a rolled ‘r’.”

**Writing**:
- **Technology**: Gemini AI for feedback, Unicode for script rendering.
- **Example (Vietnamese)**: Kiran prompts, “Write ‘Tôi cần nước’ (I need water).” Feedback: “Good, but add the tone mark on ‘nước’ to make it ‘nước’.”
- **Example (Odia)**: Kiran asks, “Write ‘ମୁଁ ପାଣି ଚାହୁଁ’ [Mu paani chaahu] (I want water).” Feedback: “Correct, but ensure the ‘ଚା’ [cha] has the right vowel marker.”

**Reading**:
- **Technology**: Gemini AI processes uploaded images, generates reading tasks.
- **Example (Greek)**: Kiran shows a sign: “Απαγορεύεται το κάπνισμα” [Apagorevetai to kapnisma] (No smoking), asking, “What does this mean?”
- **Example (Telugu)**: Olena presents a menu: “ఇడ్లీ” [idli], asking, “What is this item?” (Answer: “Idli, a steamed rice cake.”)

**Knowledge Base Interaction**:
- **Example (Gujarati)**: A user uploads an image of a Gujarati street sign: “એરપોર્ટ” [Airport]. Kiran creates a reading task: “What does this sign say?”
- **Memory/Conversation History**: Kiran recalls, “You struggled with Gujarati ‘એ’ [e] last time. Let’s practice more signs.”

### 4. Cultural Immersion Seeker Agent
**Persona**: A culture enthusiast diving into literature and traditions (e.g., "Storyteller Meera" for Malayalam, "Historian Danylo" for Ukrainian).

**Focus**: Cultural content, literature, media (e.g., poetry, historical texts).

**Speaking (Text-Based Pronunciation Guides)**:
- **Technology**: Phonetic guides for pronunciation practice.
- **Example (Malayalam)**: Meera instructs, “Say ‘ഞാൻ മലയാളം സംസാരിക്കുന്നു’ [Njaan Malayalam samsaarikkunnu] (I speak Malayalam). Pronounce it as ‘Nyaan Ma-la-yaa-lam sam-saa-rik-koon-noo’ with a soft ‘n’.”
- **Example (Hebrew)**: Danylo suggests, “Say ‘אני אוהב ספרים’ [Ani ohev sfarim] (I love books). Pronounce it as ‘A-nee o-hev sfa-reem’.”

**Writing**:
- **Technology**: Gemini AI for feedback, Unicode for scripts.
- **Example (Bhojpuri)**: Meera prompts, “Write a short line: ‘हम किताब पढ़ी’ [Hum kitaab padhi] (I read a book).” Feedback: “Correct, good job!”
- **Example (Swahili)**: Danylo asks, “Write a cultural note: ‘Hadithi ni muhimu’ (Stories are important).” Feedback: “Well done!”

**Reading**:
- **Technology**: Gemini AI extracts text from uploaded books, generates tasks.
- **Example (Ukrainian)**: Danylo extracts a passage from a user-uploaded book: “Україна – моя батьківщина” [Ukraina – moya batkivshchyna] (Ukraine is my homeland). The user answers, “What does this mean?”
- **Example (Kannada)**: Meera presents a poem: “ನಾನು ಕನ್ನಡಿಗ” [Naanu Kannadiga] (I am a Kannadiga), asking, “What is the theme?” (Answer: “Pride in Kannada identity.”)

**Knowledge Base Interaction**:
- **Example (Malayalam)**: A user uploads a Malayalam poetry book. Meera extracts a poem, stores it in Supabase, and creates a reading task: “What does ‘കവിത’ [kavita] (poetry) mean in this context?”
- **Memory/Conversation History**: Meera notes, “You enjoyed Malayalam literature last time. Let’s explore more poems.”

### 5. Social Learner Agent
**Persona**: An expat or social butterfly aiming to connect with locals (e.g., "Friend Arjun" for Odia, "Neighbor Sofia" for Danish).

**Focus**: Social integration, local phrases (e.g., greetings, small talk).

**Speaking (Text-Based Pronunciation Guides)**:
- **Technology**: Phonetic guides for pronunciation practice.
- **Example (Odia)**: Arjun instructs, “Say ‘ନମସ୍କାର’ [Namaskar] (hello). Pronounce it as ‘Na-ma-skaar’ with a soft ‘s’.”
- **Example (Danish)**: Sofia suggests, “Say ‘Hej venner’ (Hi friends). Pronounce it as ‘Hay ven-ner’ with a soft ‘r’.”

**Writing**:
- **Technology**: Gemini AI for feedback, Unicode for scripts.
- **Example (Assamese)**: Arjun prompts, “Write a greeting: ‘নমস্কাৰ’ [Nomoskar] (hello).” Feedback: “Correct, well done!”
- **Example (Greek)**: Sofia asks, “Write a text: ‘Πώς είσαι;’ [Pos eisai?] (How are you?).” Feedback: “Good, but ensure the semicolon ‘;’ is correct for Greek punctuation.”

**Reading**:
- **Technology**: Gemini AI processes uploaded community texts, generates tasks.
- **Example (Swahili)**: Arjun shows a notice: “Mkutano kesho” (Meeting tomorrow). The user answers, “When is the meeting?” (Answer: “Tomorrow.”)
- **Example (Punjabi)**: Sofia presents a flyer: “ਮੇਲਾ ਕੱਲ੍ਹ’ [Mela kallh] (Festival tomorrow), asking, “What event is this?”

**Knowledge Base Interaction**:
- **Example (Odia)**: A user uploads an Odia community notice. Arjun extracts the text, stores it in Supabase, and creates a reading task: “What does ‘ସଭା’ [sabha] (meeting) mean?”
- **Memory/Conversation History**: Arjun recalls, “You practiced Odia greetings last session. Let’s try small talk today.”

## Non-Latin Script Support
Tier 2 languages with non-Latin scripts include Vietnamese (Latin-based but with diacritics), Telugu (Telugu script), Kannada (Kannada script), Malayalam (Malayalam script), Marathi (Devanagari), Punjabi (Gurmukhi), Gujarati (Gujarati script), Odia (Odia script), Assamese (Assamese script), Sindhi (Devanagari/Arabic script), Bhojpuri (Devanagari), Maithili (Devanagari), Hebrew (Hebrew script), Greek (Greek script), and Ukrainian (Cyrillic). Implementation details:
- **Rendering**: Use Unicode fonts (e.g., Noto Sans Telugu, Noto Sans Hebrew, Noto Serif Cyrillic).
- **Input**: Virtual keyboards (e.g., Gboard for Telugu, Hebrew).
- **Feedback**: Gemini AI corrects script-specific errors (e.g., Telugu conjunct consonants, Hebrew vowel placement).
- **Reading**: Provide transliteration/translation (e.g., Telugu “హాయ్” [haay], Greek “Γεια σου” [Yia sou]).

## Supabase Knowledge Base Integration
- **Uploads**: Users upload books, PDFs, or images (e.g., a Telugu script PDF, a Swahili notice). Supabase stores these in the Uploads Table.
- **Extraction**: Gemini AI uses Google Cloud Vision API for OCR (e.g., extracting Telugu script from a PDF).
- **Interaction**: Agents create tasks from uploaded content (e.g., Traveler agent uses a Gujarati sign for a reading exercise).
- **Memory/Conversation History**: Supabase logs interactions (e.g., user’s Telugu reading practice) and tracks progress (e.g., mastered 5 Telugu words).

## Example Workflow (Telugu, Tier 2)
**User**: Uploads a Telugu PDF of a movie script and interacts with all 5 agents:
- **Beginner Enthusiast (Ravi)**: “Read this dialogue: ‘నీవు ఎలా ఉన్నావు?’ [Neevu ela unnaru?] (How are you?).”
- **Busy Professional (Anil)**: “Write a formal email: ‘ప్రియమైన సార్, నాకు ఒక సమావేశం కావాలి’ [Priyamaina Sir, Naaku oka samavesham kaavaali] (Dear Sir, I need a meeting).” Feedback on spacing.
- **Traveler (Kiran)**: “Read this menu item: ‘ఇడ్లీ’ [idli].” Extracted from an uploaded image.
- **Cultural Immersion Seeker (Meera)**: “Read this poem line: ‘ప్రేమ చాలా గొప్పది’ [Prema chaala goppadi] (Love is great).” Feedback on understanding.
- **Social Learner (Arjun)**: “Write a greeting: ‘హాయ్’ [haay] (hi).” Feedback: “Great job!”

## Conclusion
The 5 agents for Tier 2 languages provide a robust, text-based learning experience, supporting writing, reading, and knowledge base interaction. Despite the lack of voice support, phonetic guides ensure users can practice pronunciation, while pre-scripted dialogues maintain engagement. Special attention to non-Latin scripts ensures accessibility, and the Supabase-Gemini AI integration enables a seamless, scalable solution for 85 agents across 17 languages.