# Agent Details for Tier 3 Languages

## Overview
This document provides an in-depth look at the 5 unique agents per language for the 11 Tier 3 languages in the language learning app. Tier 3 languages have basic feature support, limited to text-only interactions with no voice capabilities (text-to-speech or speech-to-text) and no live chat (not even pre-scripted dialogues), due to significant limitations in Gemini AI's support for these languages. However, they fully support text-based interactions, writing, reading, and integration with a Supabase knowledge base. Each language has 5 agents (55 agents total for Tier 3), each tied to a persona: <PERSON><PERSON>ner Enthusiast, <PERSON>y Professional, Traveler, Cultural Immersion Seeker, and Social Learner. These agents support writing, reading, and interaction with user-uploaded content, with special attention to non-Latin scripts (e.g., Cherokee, Inuktitut, <PERSON>ri).

## Tier 3 Languages (11 Total)
These languages are limited to text-only interactions, focusing on basic learning support:
1. Norwegian (2.8M learners)
2. <PERSON><PERSON><PERSON> (0.05M learners)
3. Xhosa (0.05M learners)
4. Zulu (0.1M learners)
5. Amharic (0.1M learners)
6. Quechua (0.05M learners)
7. <PERSON><PERSON> (0.01M learners)
8. Cherokee (0.01M learners)
9. Navajo (0.02M learners)
10. Hawaiian (0.03M learners)
11. Inuktitut (0.01M learners)

## Agent Structure and Functionalities
Each language has 5 agents, each tied to a persona, designed to support writing, reading, and text-based interactions with the Supabase knowledge base. Since Tier 3 languages lack voice support and live chat, agents provide phonetic guides for pronunciation and focus on static, text-based exercises. The Supabase knowledge base stores user-uploaded content (books, PDFs, attachments), memory, and conversation history, powered by Gemini AI for NLP, content generation, and script rendering.

### 1. Beginner Enthusiast Agent
**Persona**: A youthful, energetic learner passionate about cultural phenomena (e.g., "Folktale Fan Thandi" for Xhosa, "Island Dreamer Koa" for Hawaiian).

**Focus**: Fun, engaging lessons tied to cultural elements (e.g., Xhosa folktales, Hawaiian hula traditions).

**Speaking (Text-Based Pronunciation Guides)**:
- **Technology**: Since TTS/STT is unavailable, agents provide phonetic guides and pronunciation tips in text form.
- **Example (Xhosa)**: Thandi instructs, “Say ‘Molo’ (hello). Pronounce it as ‘Mo-lo’ with a click on the ‘l’.” The user can follow the guide to practice independently.
- **Example (Hawaiian)**: Koa suggests, “Say ‘Aloha’ (hello). It’s pronounced ‘Ah-loh-ha’ with a soft ‘h’.” The user reads and practices the pronunciation.

**Writing**:
- **Technology**: Gemini AI analyzes user input, supports non-Latin scripts via Unicode fonts (e.g., Noto Sans Cherokee, Noto Sans Ethiopic for Amharic).
- **Example (Cherokee)**: Thandi prompts, “Write ‘ᎣᏏᏲ’ [osiyo] (hello).” The user types using a Cherokee syllabary keyboard, and Thandi corrects: “Good, but ensure the ‘Ᏺ’ [yo] is clear.”
- **Example (Norwegian)**: Koa asks, “Write ‘Hei’ (hello).” Feedback: “Correct, well done!”

**Reading**:
- **Technology**: Gemini AI generates quizzes, renders scripts with transliteration/translation.
- **Example (Maori)**: Thandi presents a greeting: “Kia ora” (hello) with transliteration and translation, asking, “What does this mean?”
- **Example (Inuktitut)**: Koa shows “ᐊᐃᓐᖓᐃ” [ainngai] (hello) with transliteration, asking, “What does this mean?” (Answer: “Hello.”)

**Knowledge Base Interaction**:
- **Example (Xhosa)**: A user uploads a Xhosa folktale PDF. Thandi extracts a line via OCR (Google Cloud Vision API), stores it in Supabase, and creates a reading exercise: “Read this sentence: ‘Bhota, ndiyavuya!’ (Hello, I’m happy!).”
- **Memory/Conversation History**: Thandi recalls, “Last time, you struggled with Xhosa clicks. Let’s practice more.”

### 2. Busy Professional Agent
**Persona**: A career-focused individual needing practical language skills (e.g., "Trader Sipho" for Zulu, "Consultant Tala" for Quechua).

**Focus**: Business vocabulary, professional scenarios (e.g., meetings, emails).

**Speaking (Text-Based Pronunciation Guides)**:
- **Technology**: Phonetic guides for pronunciation practice.
- **Example (Zulu)**: Sipho instructs, “Say ‘Sawubona, ngifuna ukubhuka intlanganiso’ (Hello, I want to book a meeting). Pronounce it as ‘Sa-woo-bo-na, ngee-foo-na oo-koo-bhoo-ka int-lan-ga-nee-so’ with a soft ‘n’.”
- **Example (Quechua)**: Tala suggests, “Say ‘Allinllachu, huk reunionta munani’ (Hello, I want a meeting). Pronounce it as ‘Al-leen-ya-chu, huk re-oo-nyon-ta moo-na-nee’.”

**Writing**:
- **Technology**: Gemini AI for grammar/spelling correction, Unicode for non-Latin scripts.
- **Example (Amharic)**: Sipho prompts, “Write an email: ‘ሰላም፣ እባክህ ስብሰባ እንደምንሰብስብ’ [Selam, ibakih sibsiba enideminsebsib] (Hello, please let’s arrange a meeting).” Feedback: “Good, but ensure the ‘ስብሰባ’ [sibsiba] has the correct form.”
- **Example (Norwegian)**: Tala asks, “Write a formal request: ‘Jeg ønsker et møte’ (I’d like a meeting).” Feedback: “Correct, well done!”

**Reading**:
- **Technology**: Gemini AI extracts text from uploaded documents, generates comprehension tasks.
- **Example (Norwegian)**: Sipho extracts a business email: “Kjære Herr Olsen, takk for din tid...” The user answers, “Who is this email addressed to?” (Answer: “Mr. Olsen.”)
- **Example (Navajo)**: Tala presents a contract excerpt: “Shí éí Diné bizaad baa níníłtaʼ” (I agree in the Navajo language), asking, “What does this mean?”

**Knowledge Base Interaction**:
- **Example (Zulu)**: A user uploads a Zulu business document. Sipho extracts the text, stores it in Supabase, and creates a reading task: “What does ‘intlanganiso’ (meeting) mean in this context?”
- **Memory/Conversation History**: Sipho notes, “You often misspell Zulu ‘intlanganiso’. Let’s focus on that today.”

### 3. Traveler Agent
**Persona**: An adventurous traveler needing practical phrases (e.g., "Explorer Hemi" for Maori, "Wanderer Awinita" for Cherokee).

**Focus**: Travel-related vocabulary, cultural tips (e.g., ordering food, asking directions).

**Speaking (Text-Based Pronunciation Guides)**:
- **Technology**: Phonetic guides for pronunciation practice.
- **Example (Maori)**: Hemi instructs, “Say ‘Kei hea te wharepaku?’ (Where is the bathroom?). Pronounce it as ‘Kay heh-ah teh far-eh-pah-koo’ with a soft ‘r’.”
- **Example (Cherokee)**: Awinita suggests, “Say ‘ᎯᎠ ᎢᏳᎵᏍᏓᏁᎵ?’ [Hia iyulisdaneli?] (Where is the station?). Pronounce it as ‘Hee-ah ee-yoo-lis-da-neh-lee’.”

**Writing**:
- **Technology**: Gemini AI for feedback, Unicode for script rendering.
- **Example (Hawaiian)**: Hemi prompts, “Write ‘He wai kaʻu e inu ai’ (I’d like water to drink).” Feedback: “Good, but add the glottal stop in ‘kaʻu’.”
- **Example (Konkani)**: Awinita asks, “Write ‘म्हाका पाणी जाय’ [Mhaaka paani jaay] (I want water).” Feedback: “Correct, but ensure the ‘जाय’ [jaay] has the right vowel marker.”

**Reading**:
- **Technology**: Gemini AI processes uploaded images, generates reading tasks.
- **Example (Norwegian)**: Hemi shows a sign: “Røyking forbudt” (No smoking), asking, “What does this mean?”
- **Example (Navajo)**: Awinita presents a menu: “Tó éí shí kááʼ” (Water is my choice), asking, “What is this item?” (Answer: “Water.”)

**Knowledge Base Interaction**:
- **Example (Cherokee)**: A user uploads an image of a Cherokee street sign: “ᏓᏂᏍᏛᎢ” [Danisdvi] (airport). Awinita creates a reading task: “What does this sign say?”
- **Memory/Conversation History**: Awinita recalls, “You struggled with Cherokee ‘Ꮣ’ [da] last time. Let’s practice more signs.”

### 4. Cultural Immersion Seeker Agent
**Persona**: A culture enthusiast diving into literature and traditions (e.g., "Storyteller Mere" for Maori, "Historian T’áá" for Navajo).

**Focus**: Cultural content, literature, media (e.g., oral histories, traditional stories).

**Speaking (Text-Based Pronunciation Guides)**:
- **Technology**: Phonetic guides for pronunciation practice.
- **Example (Maori)**: Mere instructs, “Say ‘He tangata ahau’ (I am a person). Pronounce it as ‘Heh tan-ga-ta ah-ow’ with a soft ‘t’.”
- **Example (Inuktitut)**: T’áá suggests, “Say ‘ᐃᓄᒃᑎᑐᑦ ᐅᖃᓕᒪᕆᒃ’ [Inuktitut uqalimarik] (I speak Inuktitut). Pronounce it as ‘Ee-nook-tee-toot oo-qa-lee-ma-rik’.”

**Writing**:
- **Technology**: Gemini AI for feedback, Unicode for scripts.
- **Example (Amharic)**: Mere prompts, “Write a short line: ‘እኔ መጽሐፍ አነበብኩ’ [Enē mets’haf anebebkhu] (I read a book).” Feedback: “Correct, good job!”
- **Example (Quechua)**: T’áá asks, “Write a cultural note: ‘Ñuqa kawsayta munani’ (I want to live).” Feedback: “Well done!”

**Reading**:
- **Technology**: Gemini AI extracts text from uploaded books, generates tasks.
- **Example (Norwegian)**: Mere extracts a passage from a user-uploaded book: “Norge er mitt hjemland” (Norway is my homeland). The user answers, “What does this mean?”
- **Example (Cherokee)**: T’áá presents a story: “ᎠᏂᏴᏫᏯ ᎤᎾᏓᏅᏛ” [Aniyvwiya unadanvdv] (The Cherokee people’s story), asking, “What is the theme?” (Answer: “Cherokee history.”)

**Knowledge Base Interaction**:
- **Example (Maori)**: A user uploads a Maori oral history book. Mere extracts a story, stores it in Supabase, and creates a reading task: “What does ‘whakapapa’ (genealogy) mean in this context?”
- **Memory/Conversation History**: Mere notes, “You enjoyed Maori stories last time. Let’s explore more traditions.”

### 5. Social Learner Agent
**Persona**: An expat or social butterfly aiming to connect with locals (e.g., "Friend Nia" for Zulu, "Neighbor Kanti" for Konkani).

**Focus**: Social integration, local phrases (e.g., greetings, small talk).

**Speaking (Text-Based Pronunciation Guides)**:
- **Technology**: Phonetic guides for pronunciation practice.
- **Example (Zulu)**: Nia instructs, “Say ‘Sawubona’ (hello). Pronounce it as ‘Sa-woo-bo-na’ with a soft ‘n’.”
- **Example (Konkani)**: Kanti suggests, “Say ‘देव बरें करूं’ [Dev baren karun] (May God bless). Pronounce it as ‘Dev ba-ren ka-roon’.”

**Writing**:
- **Technology**: Gemini AI for feedback, Unicode for scripts.
- **Example (Hawaiian)**: Nia prompts, “Write a greeting: ‘Aloha’ (hello).” Feedback: “Correct, well done!”
- **Example (Inuktitut)**: Kanti asks, “Write a greeting: ‘ᐊᐃᓐᖓᐃ’ [ainngai] (hello).” Feedback: “Good job!”

**Reading**:
- **Technology**: Gemini AI processes uploaded community texts, generates tasks.
- **Example (Xhosa)**: Nia shows a notice: “Intlanganiso ngomso” (Meeting tomorrow). The user answers, “When is the meeting?” (Answer: “Tomorrow.”)
- **Example (Konkani)**: Kanti presents a flyer: “उत्सव काल’ [Utsav kaal] (Festival tomorrow), asking, “What event is this?”

**Knowledge Base Interaction**:
- **Example (Zulu)**: A user uploads a Zulu community notice. Nia extracts the text, stores it in Supabase, and creates a reading task: “What does ‘umhlangano’ (meeting) mean?”
- **Memory/Conversation History**: Nia recalls, “You practiced Zulu greetings last session. Let’s try small talk today.”

## Non-Latin Script Support
Tier 3 languages with non-Latin scripts include Konkani (Devanagari), Amharic (Ethiopic script), Quechua (Latin-based but with regional variations), Maori (Latin-based with macrons), Cherokee (Cherokee syllabary), Navajo (Latin-based with diacritics), Hawaiian (Latin-based with glottal stops and macrons), and Inuktitut (Inuktitut syllabics). Implementation details:
- **Rendering**: Use Unicode fonts (e.g., Noto Sans Cherokee, Noto Sans Ethiopic, Noto Sans Inuktitut).
- **Input**: Virtual keyboards (e.g., Gboard for Cherokee, Inuktitut).
- **Feedback**: Gemini AI corrects script-specific errors (e.g., Cherokee syllabary characters, Inuktitut syllabics).
- **Reading**: Provide transliteration/translation (e.g., Cherokee “ᎣᏏᏲ” [osiyo], Inuktitut “ᐊᐃᓐᖓᐃ” [ainngai]).

## Supabase Knowledge Base Integration
- **Uploads**: Users upload books, PDFs, or images (e.g., a Cherokee story PDF, a Maori notice). Supabase stores these in the Uploads Table.
- **Extraction**: Gemini AI uses Google Cloud Vision API for OCR (e.g., extracting Cherokee syllabary from a PDF).
- **Interaction**: Agents create tasks from uploaded content (e.g., Traveler agent uses a Hawaiian sign for a reading exercise).
- **Memory/Conversation History**: Supabase logs interactions (e.g., user’s Cherokee reading practice) and tracks progress (e.g., mastered 5 Cherokee words).

## Example Workflow (Cherokee, Tier 3)
**User**: Uploads a Cherokee PDF of a traditional story and interacts with all 5 agents:
- **Beginner Enthusiast (Thandi)**: “Read this greeting: ‘ᎣᏏᏲ’ [osiyo] (hello).”
- **Busy Professional (Sipho)**: “Write a formal note: ‘ᎣᏏᏲ, ᏓᏂᏍᏛᎢ ᏥᏳᎪᏗ’ [Osiyo, danisdvi tsi-yu-go-di] (Hello, I need a meeting).” Feedback on character clarity.
- **Traveler (Awinita)**: “Read this sign: ‘ᏓᏂᏍᏛᎢ’ [Danisdvi] (airport).” Extracted from an uploaded image.
- **Cultural Immersion Seeker (Mere)**: “Read this story line: ‘ᎠᏂᏴᏫᏯ ᎤᎾᏓᏅᏛ’ [Aniyvwiya unadanvdv] (The Cherokee people’s story).” Feedback on understanding.
- **Social Learner (Kanti)**: “Write a greeting: ‘ᎣᏏᏲ’ [osiyo] (hello).” Feedback: “Great job!”

## Conclusion
The 5 agents for Tier 3 languages provide a robust, text-only learning experience, supporting writing, reading, and knowledge base interaction. Despite the lack of voice support and live chat, phonetic guides ensure users can practice pronunciation, while static exercises maintain engagement. Special attention to non-Latin scripts ensures accessibility, and the Supabase-Gemini AI integration enables a seamless, scalable solution for 55 agents across 11 languages.