# Cherokee Agent Details (Tier 3 Language)

## Overview
This document provides an in-depth exploration of Cherokee, a Tier 3 language in the language learning app, with basic feature support limited to text-only interactions, lacking voice capabilities (TTS/STT) and live chat functionality. Cherokee is supported by 5 unique agents (<PERSON><PERSON><PERSON> Enthusiast, <PERSON>y Professional, Traveler, Cultural Immersion Seeker, Social Learner), each tailored to a persona, focusing on text-based interactions, writing, reading, and knowledge base integration. We’ll examine Cherokee’s linguistic and cultural context, its implementation in the app, and how the Supabase-Gemini AI tech stack enhances the learning experience, with special attention to its non-Latin script.

## Cherokee: Linguistic and Cultural Context
- **Speakers**: Approximately 20,000 speakers, primarily in Oklahoma and North Carolina (USA), with efforts to revitalize the language among the Cherokee Nation.
- **Script**: Cherokee syllabary, a writing system invented by Sequoyah in 1821, consisting of 85 characters, each representing a syllable (e.g., Ꭰ [a], Ꮳ [tsa]). It’s a non-Latin script, unique to the Cherokee language.
- **Cultural Significance**: Cherokee is a key part of Cherokee identity, with a rich oral tradition including stories, songs, and ceremonies. The language is tied to the Cherokee Nation’s history, including the Trail of Tears, and is actively being preserved through education programs.
- **Linguistic Features**: Polysynthetic grammar, where words are formed by combining multiple morphemes (e.g., ᎤᏪᏯᏛ [u-we-ya-dv] “river”). It uses tones and vowel length to distinguish meaning (e.g., ᎠᎹ [ama] “water” vs. ᎠᎹ [aama] with a different tone for “salt”).
- **Learner Appeal**: Popular among Cherokee Nation members, language revitalization advocates, and cultural enthusiasts interested in Indigenous languages and histories.

## Implementation in the App (Tier 3)
As a Tier 3 language, Cherokee supports text-only interactions with no voice or live chat support:
- **Voice Support**: Unavailable due to Gemini AI limitations. Agents provide phonetic guides for pronunciation (e.g., ᎣᏏᏲ [osiyo] as “oh-see-yoh”).
- **Live Chat**: Not supported; all interactions are static, text-based exercises.
- **Script Support**: Cherokee syllabary is rendered using Unicode (Noto Sans Cherokee font) with a virtual keyboard for user input.
- **Knowledge Base**: Supabase stores user-uploaded content (e.g., Cherokee PDFs, images), memory, and conversation history, integrated with Gemini AI for processing.

## The 5 Agents for Cherokee
Each agent is tailored to a persona, supporting text-based interactions, writing, reading, and knowledge base integration. They leverage Cherokee’s cultural depth and linguistic uniqueness to create an engaging learning experience, despite the lack of voice and live chat support.

### 1. Beginner Enthusiast Agent: "Storyteller Awinita"
**Persona**: A young Cherokee enthusiast passionate about traditional stories and cultural games.

**Focus**: Fun, engaging lessons tied to Cherokee culture, especially oral traditions and community activities.

**Speaking (Text-Based Pronunciation Guides)**:
- **Technology**: Phonetic guides provided since TTS/STT is unavailable.
- **Example**: Awinita instructs, “Say ‘ᎣᏏᏲ’ [osiyo] (hello) like you’re greeting a friend at a Cherokee festival. Pronounce it as ‘oh-see-yoh’ with a soft ‘yoh’ at the end.” The user practices independently, following the guide.
- **Feature**: Awinita introduces simple Cherokee phrases from traditional games, like “ᎤᏁᎦ” [unega] (white), used in Cherokee stickball, with pronunciation tips: “Say ‘unega’ as ‘oo-neh-gah’ with a light ‘gah’.”

**Writing**:
- **Technology**: Gemini AI analyzes user input, renders Cherokee syllabary using Noto Sans Cherokee.
- **Example**: Awinita prompts, “Write a greeting: ‘ᎣᏏᏲ’ [osiyo] (hello).” The user types using a Cherokee keyboard, and Awinita corrects: “Good, but ensure the ‘Ᏺ’ [yo] character is clear and not confused with ‘Ᏹ’ [yi].”
- **Feature**: Awinita provides character formation guidance (e.g., “For ‘Ꭳ’ [o], start with the top curve, then draw the downward stroke”).

**Reading**:
- **Technology**: Gemini AI generates quizzes, renders Cherokee syllabary with transliteration/translation.
- **Example**: Awinita presents a phrase from a Cherokee story: “ᎠᏂᏴᏫᏯ” [Aniyvwiya] (Cherokee people), asking, “What does this mean?” The user answers, and Awinita confirms: “Correct! It refers to the Cherokee people.”
- **Feature**: Includes transliteration (“Aniyvwiya”) and translation for beginners, with an option to toggle them off for advanced learners.

**Knowledge Base Interaction**:
- **Example**: A user uploads a Cherokee folktale PDF. Awinita extracts a line via OCR (Google Cloud Vision API), stores it in Supabase, and creates a reading exercise: “Read this: ‘ᏣᎳᎩ ᎤᏪᏯᏛ’ [Tsalagi uwe-ya-dv] (Cherokee river). What does it mean?”
- **Memory/Conversation History**: Awinita recalls, “Last time, you struggled with Cherokee ‘Ꮳ’ [tsa]. Let’s practice more words with that character.”

### 2. Busy Professional Agent: "Leader Tsisqua"
**Persona**: A Cherokee professional helping users navigate the language in formal and community leadership contexts.

**Focus**: Practical vocabulary, professional scenarios (e.g., community meetings, formal letters).

**Speaking (Text-Based Pronunciation Guides)**:
- **Technology**: Phonetic guides for pronunciation practice.
- **Example**: Tsisqua instructs, “Say ‘ᎣᏏᏲ, ᏓᏂᏍᏛᎢ ᏥᏳᎪᏗ’ [Osiyo, danisdvi tsi-yu-go-di] (Hello, I need a meeting). Pronounce it as ‘oh-see-yoh, da-nees-dvee tsee-yoo-go-dee’ with a soft ‘dee’.” The user follows the guide to practice.
- **Feature**: Tsisqua provides etiquette tips: “In Cherokee meetings, start with ‘ᎣᏏᏲ’ [osiyo] to show respect, pronounced ‘oh-see-yoh’.”

**Writing**:
- **Technology**: Gemini AI for grammar/spelling correction, Unicode for Cherokee syllabary.
- **Example**: Tsisqua prompts, “Write a formal request: ‘ᎣᏏᏲ, ᏓᏂᏍᏛᎢ ᏥᏳᎪᏗ’ [Osiyo, danisdvi tsi-yu-go-di] (Hello, I need a meeting).” Feedback: “Good, but ensure ‘ᏥᏳᎪᏗ’ [tsi-yu-go-di] uses the correct syllabary characters.”
- **Feature**: Tsisqua provides templates for formal phrases (e.g., “ᎥᎿ” [v-hna] for “please”) and explains their use in Cherokee community contexts.

**Reading**:
- **Technology**: Gemini AI extracts text from uploaded documents, generates comprehension tasks.
- **Example**: Tsisqua extracts a Cherokee community notice from a user-uploaded PDF: “ᏓᏂᏍᏛᎢ ᎤᎾᏙᏓᏆ” [Danisdvi unadodaqua] (Meeting next week). The user answers, “When is the meeting?” (Answer: “Next week.”)
- **Feature**: Highlights key vocabulary (e.g., “ᏓᏂᏍᏛᎢ” [danisdvi] for meeting) with definitions.

**Knowledge Base Interaction**:
- **Example**: A user uploads a Cherokee community agreement PDF. Tsisqua extracts a clause, stores it in Supabase, and creates a reading task: “What does ‘ᏧᎾᏓᎴᏅᏓ’ [tsunadalen-vda] (agreement) mean in this context?”
- **Memory/Conversation History**: Tsisqua notes, “You often confuse Cherokee ‘Ꮷ’ [tsu] and ‘Ꮳ’ [tsa]. Let’s practice more formal terms with these characters.”

### 3. Traveler Agent: "Wanderer Hiawassee"
**Persona**: An adventurous traveler exploring Cherokee lands in Oklahoma and North Carolina.

**Focus**: Travel-related vocabulary, cultural tips (e.g., asking directions, visiting cultural sites).

**Speaking (Text-Based Pronunciation Guides)**:
- **Technology**: Phonetic guides for pronunciation practice.
- **Example**: Hiawassee instructs, “Say ‘ᎯᎠ ᎢᏳᎵᏍᏓᏁᎵ?’ [Hia iyulisdaneli?] (Where is the station?) while traveling in Cherokee Nation. Pronounce it as ‘Hee-ah ee-yoo-lis-da-neh-lee’ with a soft ‘lee’.” The user practices following the guide.
- **Feature**: Hiawassee provides cultural context: “When asking directions in Cherokee communities, it’s polite to start with ‘ᎣᏏᏲ’ [osiyo] (hello), pronounced ‘oh-see-yoh’.”

**Writing**:
- **Technology**: Gemini AI for feedback, Unicode for Cherokee syllabary.
- **Example**: Hiawassee prompts, “Write a request for water: ‘ᎠᎹ ᏍᎩᏁᏍᏗ’ [Ama sgin-esdi] (Water, please).” Feedback: “Correct, but ensure ‘ᏍᎩᏁᏍᏗ’ [sgin-esdi] uses the right syllabary characters.”
- **Feature**: Hiawassee teaches common travel phrases with script and transliteration (e.g., “ᎦᏙ ᎤᏛᏅ?” [Gado udvnv?] – “Where is it?”).

**Reading**:
- **Technology**: Gemini AI processes uploaded images, generates reading tasks.
- **Example**: Hiawassee shows a Cherokee sign from an uploaded image: “ᏓᏂᏍᏛᎢ” [Danisdvi] (airport). The user answers, “What does this sign say?” (Answer: “Airport.”)
- **Feature**: Provides cultural tips with readings (e.g., “Cherokee Nation has many cultural sites—let’s learn place names like ‘ᏣᎳᎩ’ [Tsalagi] for Cherokee!”).

**Knowledge Base Interaction**:
- **Example**: A user uploads an image of a Cherokee cultural center sign: “ᏣᎳᎩ ᎤᏂᏃᎮᏓ” [Tsalagi unin-oheda] (Cherokee museum). Hiawassee creates a reading task: “What does this sign mean?” (Answer: “Cherokee museum.”)
- **Memory/Conversation History**: Hiawassee recalls, “You struggled with Cherokee ‘Ꮳ’ [tsa] last time. Let’s practice more signs with that character.”

### 4. Cultural Immersion Seeker Agent: "Historian T’áá"
**Persona**: A culture enthusiast passionate about Cherokee history, oral traditions, and ceremonies.

**Focus**: Cultural content, oral histories, and traditions (e.g., Cherokee stories, ceremonies like the Green Corn Ceremony).

**Speaking (Text-Based Pronunciation Guides)**:
- **Technology**: Phonetic guides for pronunciation practice.
- **Example**: T’áá instructs, “Say a line from a Cherokee story: ‘ᎠᏂᏴᏫᏯ ᎤᎾᏓᏅᏛ’ [Aniyvwiya unadanvdv] (The Cherokee people’s story). Pronounce it as ‘Ah-nee-yuh-wee-yah oo-nah-dahn-v-duh’ with a soft ‘v’.” The user follows the guide to practice.
- **Feature**: T’áá explains the cultural significance: “This phrase introduces many Cherokee oral histories, often told during ceremonies like the Green Corn Ceremony.”

**Writing**:
- **Technology**: Gemini AI for feedback, Unicode for Cherokee syllabary.
- **Example**: T’áá prompts, “Write a short story opener: ‘ᎤᏪᏯᏛ ᎤᏬᏚ’ [Uwe-ya-dv uwodu] (The river is beautiful).” Feedback: “Beautiful! But ensure ‘ᎤᏬᏚ’ [uwodu] has the correct syllabary character for ‘Ꮪ’ [du].”
- **Feature**: T’áá provides historical context: “Cherokee stories often feature nature, like rivers, as central elements of creation narratives.”

**Reading**:
- **Technology**: Gemini AI extracts text from uploaded books, generates tasks.
- **Example**: T’áá extracts a Cherokee story from a user-uploaded PDF: “ᎤᏁᎳᏅᎯ ᎤᏪᏥ ᎤᏬᏗᎨ” [Unelanvhi uwetsi uwodige] (The Creator’s child is beautiful). The user answers, “What is the theme?” (Answer: “Celebrating creation.”)
- **Feature**: Highlights cultural vocabulary (e.g., “ᎤᏁᎳᏅᎯ” [Unelanvhi] for Creator) with explanations.

**Knowledge Base Interaction**:
- **Example**: A user uploads a Cherokee oral history book. T’áá extracts a story, stores it in Supabase, and creates a reading task: “What does ‘ᎤᏪᏯᏛ’ [u-we-ya-dv] (river) symbolize in this story?”
- **Memory/Conversation History**: T’áá notes, “You enjoyed Cherokee stories last time. Let’s explore more about the Green Corn Ceremony today.”

### 5. Social Learner Agent: "Friend Gadugi"
**Persona**: A community member helping users connect with Cherokee-speaking groups.

**Focus**: Social integration, local phrases (e.g., greetings, small talk, community events).

**Speaking (Text-Based Pronunciation Guides)**:
- **Technology**: Phonetic guides for pronunciation practice.
- **Example**: Gadugi instructs, “Greet a friend: ‘ᏓᎾᏓᎩᏍᎪᎢ?’ [Danadagisgoi?] (How are you?). Pronounce it as ‘Da-nah-da-gees-go-ee’ with a soft ‘go’.” The user practices following the guide.
- **Feature**: Gadugi teaches social norms: “In Cherokee culture, ‘ᎦᏙᎯ’ [gadohi] (friend) is a common term, pronounced ‘ga-doh-hee’.”

**Writing**:
- **Technology**: Gemini AI for feedback, Unicode for Cherokee syllabary.
- **Example**: Gadugi prompts, “Write a text to a friend: ‘ᏓᎾᏓᎩᏍᎪᎢ?’ [Danadagisgoi?] (How are you?).” Feedback: “Correct, great job!”
- **Feature**: Gadugi introduces community phrases (e.g., “ᎣᏍᏓ” [osda] for “good”) used in casual Cherokee conversations.

**Reading**:
- **Technology**: Gemini AI processes uploaded community texts, generates tasks.
- **Example**: Gadugi shows a Cherokee community notice: “ᏓᏂᏍᏛᎢ ᎤᎾᏙᏓᏆ” [Danisdvi unadodaqua] (Meeting next week). The user answers, “When is the meeting?” (Answer: “Next week.”)
- **Feature**: Explains cultural context: “Cherokee communities often hold ‘ᏓᏂᏍᏛᎢ’ [danisdvi] (meetings) for events like the Cherokee National Holiday—let’s learn related vocabulary!”

**Knowledge Base Interaction**:
- **Example**: A user uploads a Cherokee festival notice. Gadugi extracts the text, stores it in Supabase, and creates a reading task: “What does ‘ᏣᎳᎩ ᎤᏙᏢᏒ’ [Tsalagi udotlvsv] (Cherokee holiday) mean?”
- **Memory/Conversation History**: Gadugi recalls, “You practiced Cherokee greetings last session. Let’s try small talk about community events today.”

## Non-Latin Script Support: Cherokee Syllabary
Cherokee uses a non-Latin script with unique features:
- **Rendering**: Noto Sans Cherokee font ensures accurate display of Cherokee characters (e.g., Ꭰ [a], Ꮳ [tsa]).
- **Input**: Virtual keyboard allows users to type Cherokee syllabary directly (e.g., typing “ᎣᏏᏲ” [osiyo]).
- **Feedback**: Gemini AI corrects script-specific errors (e.g., “You wrote ‘ᎣᏏᏲ’ [osiyo] correctly, but ensure ‘Ᏺ’ [yo] isn’t confused with ‘Ᏹ’ [yi]”).
- **Reading**: Provides transliteration (e.g., “osiyo”) and translation (e.g., “hello”) for beginners, with toggle options.

## Supabase Knowledge Base Integration
- **Uploads**: Users upload Cherokee content (e.g., a traditional story PDF, a cultural center sign image, a festival notice). Supabase stores these in the Uploads Table.
- **Extraction**: Gemini AI uses Google Cloud Vision API for OCR to extract Cherokee syllabary (e.g., from a scanned storybook).
- **Interaction**: Agents create tasks from uploaded content (e.g., Traveler agent uses a Cherokee sign for a reading exercise).
- **Memory/Conversation History**: Supabase logs interactions (e.g., user’s Cherokee reading practice) and tracks progress (e.g., mastered 5 Cherokee words).

## Example Workflow: A Day with Cherokee Agents
**User**: A learner uploads a Cherokee PDF of a traditional story, an image of a Cherokee cultural center sign, and a festival notice. They spend a day interacting with all 5 agents:
- **Morning (Beginner Enthusiast - Awinita)**: Awinita uses the story PDF to teach a phrase: “Read ‘ᎠᏂᏴᏫᏯ’ [Aniyvwiya] (Cherokee people).” Provides pronunciation guide: “Say ‘Ah-nee-yuh-wee-yah’.”
- **Mid-Morning (Busy Professional - Tsisqua)**: Tsisqua creates a writing task: “Write a meeting request: ‘ᎣᏏᏲ, ᏓᏂᏍᏛᎢ ᏥᏳᎪᏗ’ [Osiyo, danisdvi tsi-yu-go-di].” Feedback on syllabary characters.
- **Afternoon (Traveler - Hiawassee)**: Hiawassee uses the sign image: “Read ‘ᏣᎳᎩ ᎤᏂᏃᎮᏓ’ [Tsalagi unin-oheda] (Cherokee museum).” Teaches travel phrases like “ᎦᏙ ᎤᏛᏅ?” [Gado udvnv?] (Where is it?).
- **Evening (Cultural Immersion Seeker - T’áá)**: T’áá extracts a story line: “ᎠᏂᏴᏫᏯ ᎤᎾᏓᏅᏛ” [Aniyvwiya unadanvdv]. Asks, “What’s the theme?” and provides cultural context.
- **Night (Social Learner - Gadugi)**: Gadugi uses the festival notice: “Read ‘ᏣᎳᎩ ᎤᏙᏢᏒ’ [Tsalagi udotlvsv].” Teaches a greeting: “Write ‘ᏓᎾᏓᎩᏍᎪᎢ?’ [Danadagisgoi?] (How are you?).” Feedback: “Great job!”

## Challenges and Mitigations
- **Lack of Voice Support**: Since TTS/STT is unavailable, agents provide detailed phonetic guides (e.g., “ᎣᏏᏲ” [osiyo] as “oh-see-yoh”) to help users practice pronunciation independently.
- **Syllabary Complexity**: Cherokee’s syllabary has 85 characters, which can be daunting. Agents break down character formation (e.g., “For ‘Ꭳ’ [o], start with the top curve”) and provide visual guides.
- **Tonal Distinctions**: Cherokee uses tones to distinguish meaning. Agents explain these in text (e.g., “ᎠᎹ [ama] with a high tone means ‘water,’ while a low tone means ‘salt’”).

## Conclusion
The 5 agents for Cherokee provide a robust, text-only learning experience, leveraging Tier 3 features to support writing, reading, and knowledge base interaction. Despite the lack of voice and live chat support, phonetic guides and static exercises ensure engagement, while Gemini AI and Supabase enable deep cultural immersion through Cherokee stories, community events, and travel contexts, making Cherokee learning accessible and meaningful.