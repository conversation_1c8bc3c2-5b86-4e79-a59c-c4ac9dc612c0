# Agentic Architecture for Language Learning App

## Overview
This document details the architecture for a language learning app supporting 50 languages, with a focus on inclusivity by including minority languages. The app leverages an agentic framework powered by Gemini AI and Supabase, aiming to be the world's first app with 50-language support, each with 5 unique agents (250 agents total) based on personas: <PERSON><PERSON><PERSON> Enthusiast, <PERSON>y Professional, Traveler, Cultural Immersion Seeker, and Social Learner. The app supports speaking, writing, and reading, with special attention to non sustained-Latin scripts (e.g., Tamil, Arabic, Cherokee), and integrates a knowledge base for user-uploaded content (books, PDFs, attachments), memory, and conversation history.

## Key Features
- **Languages Supported**: 50 languages, including major (e.g., Spanish, Mandarin), regional (e.g., Swahili, Tagalog), and minority/endangered languages (e.g., Cherokee, Inuktitut).
- **Agents**: 5 unique agents per language (250 total), each tied to a persona, providing personalized learning experiences.
- **Tiers**: Languages are divided into three tiers based on feature support due to Gemini AI limitations:
  - **Tier 1 (Full Features)**: Voice, live chat, all agents fully functional.
  - **Tier 2 (Partial Features)**: No voice, limited live chat.
  - **Tier 3 (Basic Features)**: Text-only, no live chat.
- **Skill Support**: Speaking, writing, and reading, with specific handling for non-Latin scripts.
- **Knowledge Base**: Integrated with Supabase to store user-uploaded books, PDFs, attachments, memory, and conversation history.
- **Tech Stack**:
  - **AI**: Gemini AI for content generation, NLP, OCR, TTS/STT.
  - **Backend**: Supabase for database, storage, and real-time updates.

## Language List (50 Total)
### Tier 1: Full Features (22 Languages)
Full support for voice (TTS/STT), live chat, and all agent functionalities:
1. Spanish (45.6M learners)
2. French (26M learners)
3. English (20M learners, for non-native speakers)
4. Portuguese (5.5M learners)
5. German (17.4M learners)
6. Japanese (21.5M learners)
7. Korean (17.7M learners)
8. Hindi (11.2M learners)
9. Arabic (4.5M learners)
10. Italian (12.2M learners)
11. Chinese (Mandarin) (8.3M learners)
12. Indonesian (0.9M learners)
13. Bengali (0.6M learners)
14. Tamil (0.5M learners, moved to Tier 1 as requested)
15. Russian (6.7M learners)
16. Dutch (3.8M learners)
17. Swedish (3.2M learners)
18. Thai (1.0M learners)
19. Turkish (2.2M learners)
20. Farsi (Persian) (0.5M learners)
21. Tagalog (Filipino) (0.8M learners)
22. Yoruba (0.4M learners)

### Tier 2: Partial Features (17 Languages)
No voice support, limited live chat (pre-scripted dialogues):
1. Vietnamese (1.2M learners)
2. Telugu (0.4M learners)
3. Kannada (0.2M learners)
4. Malayalam (0.2M learners)
5. Marathi (0.3M learners)
6. Punjabi (0.3M learners)
7. Gujarati (0.3M learners)
8. Odia (0.2M learners)
9. Assamese (0.1M learners)
10. Sindhi (0.1M learners)
11. Bhojpuri (0.2M learners)
12. Maithili (0.1M learners)
13. Swahili (0.8M learners)
14. Hebrew (0.7M learners)
15. Greek (2.0M learners)
16. Ukrainian (1.4M learners)
17. Danish (0.5M learners)

### Tier 3: Basic Features (11 Languages)
Text-only, no live chat:
1. Norwegian (2.8M learners)
2. Konkani (0.05M learners)
3. Xhosa (0.05M learners)
4. Zulu (0.1M learners)
5. Amharic (0.1M learners)
6. Quechua (0.05M learners)
7. Maori (0.01M learners)
8. Cherokee (0.01M learners)
9. Navajo (0.02M learners)
10. Hawaiian (0.03M learners)
11. Inuktitut (0.01M learners)

## Agent Structure
Each language has 5 agents (250 total), each tied to a persona, designed to support speaking, writing, and reading, with adaptations for non-Latin scripts.

### Persona-Based Agents
1. **Beginner Enthusiast Agent** (e.g., "Anime Fan Aki" for Japanese)
   - Focus: Fun, gamified learning (e.g., Japanese anime phrases).
   - Speaking: Voice exercises (e.g., “Say ‘konnichiwa’ like an anime character”) using Gemini AI TTS (Tier 1), phonetic guides for Tier 2/3.
   - Writing: Script practice (e.g., kana for Japanese, Devanagari for Hindi) with real-time feedback.
   - Reading: Interactive quizzes with script rendering (e.g., reading manga snippets).

2. **Busy Professional Agent** (e.g., "Consultant Priya" for Hindi)
   - Focus: Business vocabulary, professional scenarios.
   - Speaking: Role-playing dialogues (e.g., Hindi business meeting) with TTS.
   - Writing: Email drafting in Hindi with Devanagari script support.
   - Reading: Analyzing business documents from user-uploaded PDFs.

3. **Traveler Agent** (e.g., "Explorer Marco" for Tamil)
   - Focus: Travel phrases, cultural tips.
   - Speaking: Phrase practice (e.g., “சாப்பாடு வேண்டும்” [saapadu vendum] in Tamil) with Google Cloud TTS.
   - Writing: Writing messages in Tamil script with feedback.
   - Reading: Reading Tamil menus or signs from uploaded images.

4. **Cultural Immersion Seeker Agent** (e.g., "Poet Rani" for Bengali)
   - Focus: Cultural content, literature.
   - Speaking: Reciting Bengali poetry with TTS (Tier 1), phonetic guides for Tier 2.
   - Writing: Writing stories in Bengali script with feedback.
   - Reading: Reading Tagore’s works from user-uploaded PDFs.

5. **Social Learner Agent** (e.g., "Expat Sam" for Punjabi)
   - Focus: Social integration, local phrases.
   - Speaking: Conversational practice (e.g., Punjabi greetings) with TTS (Tier 1).
   - Writing: Writing social media posts in Gurmukhi script.
   - Reading: Reading community notices from uploaded PDFs.

### Non-Latin Script Support
Languages with non-Latin scripts (e.g., Tamil, Arabic, Cherokee) require special handling:
- **Script Rendering**: Use Unicode fonts (e.g., Noto Sans Tamil, Noto Serif Arabic, Noto Sans Cherokee).
- **Input Support**: Integrate virtual keyboards (e.g., Gboard for Tamil, Arabic) for user input.
- **Writing Feedback**: Gemini AI provides feedback on script accuracy (e.g., Tamil’s conjunct consonants, Arabic’s right-to-left writing).
- **Reading Exercises**: Display texts with transliteration/translation (e.g., Cherokee “ᏣᎳᎩ” [tsa-la-gi] with English translation).

#### Tamil in Tier 1: Specific Implementation
- **Speaking**:
  - Use Google Cloud TTS for Tamil to generate natural-sounding speech (e.g., “வணக்கம்” [vanakkam]).
  - Google Cloud STT for speech recognition, providing feedback (e.g., “You pronounced ‘ழ’ [zha] correctly”).
- **Writing**:
  - Render Tamil script using Noto Sans Tamil font.
  - Virtual keyboard for input (e.g., typing “நான் தமிழ் பேசுகிறேன்” [Naan Tamil pesugiren]).
  - Gemini AI offers feedback (e.g., “Correct use of ‘பேசுகிறேன்’, but adjust spacing after ‘நான்’”).
- **Reading**:
  - Display Tamil texts (e.g., Thirukkural) with transliteration (e.g., “அறிவினுள் [Arivinul]”).
  - Generate quizzes (e.g., “What does ‘எனக்கு புத்தகம் பிடிக்கும்’ mean?”).

## Knowledge Base Integration with Supabase
### Supabase Setup
- **Database Schema**:
  - **Users Table**: User profiles, language preferences, progress.
  - **Uploads Table**: User-uploaded books, PDFs, attachments.
  - **Conversation History Table**: Logs all interactions with agents.
  - **Memory Table**: Stores user-specific data (e.g., vocabulary learned).
  - **Knowledge Base Table**: Indexes content from uploads for querying.
- **Storage**: Supabase Storage for large files (books, PDFs, images, audio).
- **Authentication**: Supabase Auth for secure user access.
- **Realtime**: Supabase Realtime for syncing conversation history across devices.

### Gemini AI Integration
- **Content Extraction**: Gemini AI uses OCR (via Google Cloud Vision API) to extract text from PDFs/images (e.g., Tamil novel).
- **Chat with Uploaded Content**: Users query the knowledge base (e.g., “Summarize this Tamil book chapter”). Gemini AI retrieves content from Supabase and responds.
- **Memory and Personalization**: Gemini AI accesses the Memory Table (e.g., “You struggled with Tamil ‘ழ’ last time, let’s practice”).
- **Conversation History**: Logs all interactions in Supabase (e.g., past Tamil writing exercises).

### Attachments Integration
- **Upload Handling**: Users upload images (e.g., Tamil street sign), PDFs, or books via Supabase Storage.
- **Processing**: Gemini AI analyzes attachments (e.g., OCR for Tamil script, image recognition for script identification).
- **Example**: A user uploads a Tamil PDF. The Traveler agent creates a reading exercise: “Read this menu item: ‘இட்லி’ [idli].”

## Tech Stack Implementation
### Supabase
- **Database**: PostgreSQL for structured data (user profiles, conversation history).
- **Storage**: Store uploads and audio files.
- **API**: REST API for Gemini AI to query Supabase data.

### Gemini AI
- **NLP**: Processes user inputs, generates responses, handles script rendering.
- **OCR**: Extracts text from PDFs/images for the knowledge base.
- **TTS/STT**: Supports voice for Tier 1 languages (e.g., Tamil via Google Cloud TTS/STT).

### Frontend
- **Script Rendering**: Use React Native (mobile) or React (web) with Unicode fonts.
- **Chat Interface**: Supports text, voice (Tier 1), and attachments.
- **Virtual Keyboards**: Integrate Gboard or custom keyboards for non-Latin scripts.

## Example Workflow (Tamil, Tier 1)
1. **User Action**: A user uploads a Tamil PDF of a Thirukkural chapter and starts a session with the Cultural Immersion Seeker agent.
2. **Agent Response**:
   - Gemini AI extracts the text using OCR, stores it in Supabase.
   - The agent presents a reading exercise: “Read this line: ‘அறிவினுள் எல்லாம் தலை’ [Arivinul ellaam thalai].”
   - Speaking: The agent says the line using Google Cloud TTS and asks the user to repeat, using STT for feedback.
   - Writing: The user writes a summary in Tamil script; Gemini AI corrects: “Good, but ‘தலை’ needs a space before.”
   - Conversation history and progress are saved in Supabase.

## Challenges and Mitigations
- **Tamil Voice Support**: If Gemini AI lacks robust Tamil TTS/STT, Google Cloud TTS/STT ensures Tier 1 support.
- **Non-Latin Script Rendering**: Use Noto Fonts for accurate display.
- **Scalability**: Supabase and Gemini AI scale to support 250 agents and user uploads.

## Conclusion
This architecture supports 50 languages with 250 agents, ensuring personalized learning for speaking, writing, and reading, with robust support for non-Latin scripts like Tamil (Tier 1). The Supabase-Gemini AI integration enables a powerful knowledge base, memory, and conversation history, positioning the app as a market leader.