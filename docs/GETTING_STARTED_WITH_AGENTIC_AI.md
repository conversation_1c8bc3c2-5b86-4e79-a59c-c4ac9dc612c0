# 🚀 Getting Started with NIRA Agentic AI

## 🎯 **Current Status: Phase 1 Complete ✅**
**Last Updated**: May 23, 2024  
**Next Step**: Phase 2 Single Agent Implementation (4 weeks)

---

## ⚡ **Quick Start for New Developers** (5 minutes)

### **Step 1: Clone and Setup**
```bash
# Clone the repository
git clone <repository-url>
cd NIRA

# Navigate to server directory
cd server
```

### **Step 2: Environment Configuration**
```bash
# Your .env file is already created with dummy keys
# Edit it to add your real API keys
nano .env

# CRITICAL: Replace these dummy values:
# OPENAI_API_KEY=sk-DUMMY... → Your real OpenAI key
# GEMINI_API_KEY=AIzaSy... → Your real Gemini key  
# PINECONE_API_KEY=123456... → Your real Pinecone key
```

### **Step 3: Test Server**
```bash
# Build and run the server
swift build
swift run App serve --env development

# Test agent endpoints (in another terminal)
curl http://localhost:8080/api/v1/agents/available
```

### **Step 4: iOS App**
```bash
# Open iOS project
cd ..
open NIRA.xcodeproj

# Build and run (Cmd+R in Xcode)
```

**✅ You're ready for Phase 2 development!**

---

## 🔑 **API Keys You Need**

### **Essential for Development**

| Service | Purpose | Where to Get | 
|---------|---------|--------------|
| **OpenAI** | GPT-4 agent responses | https://platform.openai.com/api-keys |
| **Google Gemini** | Content generation | https://ai.google.dev/tutorials/setup |
| **Pinecone** | Vector database | https://app.pinecone.io/ |

### **Optional for Phase 2**
- **Anthropic Claude**: Fallback AI model
- **OpenRouter**: Multi-model access

### **Generate Security Keys**
```bash
# Generate JWT secret
openssl rand -base64 32

# Add to .env file
JWT_SECRET=<generated-value>
```

---

## 🏗️ **What's Already Built (Phase 1)**

### ✅ **Server Foundation Complete**
```
server/Sources/App/
├── Controllers/Agent/
│   └── AgentController.swift        ← API endpoints ready
├── Models/Agent/
│   ├── AgentSession.swift           ← Database model
│   └── ConversationTurn.swift       ← Message tracking
├── Services/
│   └── AgentOrchestrationService.swift ← Core service (mock responses)
└── Migrations/
    ├── CreateAgentSession.swift     ← DB setup
    └── CreateConversationTurn.swift ← DB setup
```

### ✅ **Database Ready**
- **Tables**: `agent_sessions`, `conversation_turns`
- **Relationships**: User → Sessions → Conversation turns
- **Fields**: Agent IDs, context, metadata tracking

### ✅ **API Endpoints Active**
- `POST /api/v1/agents/conversation/start` - Start agent session
- `POST /api/v1/agents/conversation/:id/message` - Send message  
- `GET /api/v1/agents/available` - List available agents
- `WebSocket /api/v1/agents/conversation/:id/ws` - Real-time chat

### ✅ **Environment & Security**
- `.env` files with API key management
- JWT authentication ready
- Database migrations completed
- WebSocket support enabled

---

## 🎯 **Phase 2: Your Development Tasks** (Next 4 Weeks)

### **Week 1-2: AI Integration**

#### **Task 1: Replace Mock Agent Response**
**File**: `server/Sources/App/Services/AgentOrchestrationService.swift`
**Line**: 45-60

```swift
// CURRENT (mock):
private func generateAgentResponse(
    message: String,
    context: AgentContext,
    agentId: String
) async throws -> String {
    return "Mock response to: \(message)"
}

// YOUR TASK: Replace with real AI integration
private func generateAgentResponse(
    message: String,
    context: AgentContext,
    agentId: String
) async throws -> String {
    
    // Get API key from environment
    guard let openAIKey = Environment.get("OPENAI_API_KEY") else {
        throw Abort(.internalServerError, reason: "OpenAI API key not configured")
    }
    
    // TODO: Implement OpenAI/Gemini API call
    // 1. Build conversation history from context
    // 2. Add agent personality prompt
    // 3. Call AI API
    // 4. Return response
}
```

#### **Task 2: Add Agent Personality System**
**Create**: `server/Sources/App/Models/Agent/AgentPersonality.swift`

```swift
enum AgentPersonality: String, Codable {
    case patient = "patient"
    case encouraging = "encouraging"
    case challenging = "challenging"
    
    var systemPrompt: String {
        switch self {
        case .patient:
            return "You are a patient language tutor..."
        case .encouraging:
            return "You are an enthusiastic tutor..."
        case .challenging:
            return "You are a challenging tutor..."
        }
    }
}
```

### **Week 3-4: iOS Integration**

#### **Task 3: Create Agent Chat UI**
**Create**: `NIRA/Views/Agent/AgentConversationView.swift`

```swift
import SwiftUI

struct AgentConversationView: View {
    @StateObject private var viewModel = AgentConversationViewModel()
    
    var body: some View {
        VStack {
            // Chat messages
            ScrollView {
                LazyVStack {
                    ForEach(viewModel.messages) { message in
                        AgentMessageBubble(message: message)
                    }
                }
            }
            
            // Input field
            HStack {
                TextField("Type your message...", text: $viewModel.currentMessage)
                Button("Send") {
                    viewModel.sendMessage()
                }
            }
        }
        .onAppear {
            viewModel.startAgentSession()
        }
    }
}
```

#### **Task 4: WebSocket Communication**
**Create**: `NIRA/Services/Agent/AgentCommunicationService.swift`

```swift
import Foundation
import Combine

class AgentCommunicationService: ObservableObject {
    @Published var connectionStatus: ConnectionStatus = .disconnected
    @Published var messages: [AgentMessage] = []
    
    private var webSocketTask: URLSessionWebSocketTask?
    
    func connect(sessionId: String) {
        // TODO: Implement WebSocket connection
    }
    
    func sendMessage(_ message: String) {
        // TODO: Send message via WebSocket
    }
}
```

---

## 🧪 **Testing Your Implementation**

### **Server Testing**
```bash
# Test AI integration
curl -X POST http://localhost:8080/api/v1/agents/conversation/start \
  -H "Content-Type: application/json" \
  -d '{
    "agents": ["tutor"],
    "language": "french",
    "userProficiency": "beginner"
  }'

# Test message sending
curl -X POST http://localhost:8080/api/v1/agents/conversation/{session-id}/message \
  -H "Content-Type: application/json" \
  -d '{
    "message": "Bonjour! Comment allez-vous?",
    "context": {"currentTopic": "greetings"}
  }'
```

### **iOS Testing**
```swift
// Test agent conversation in iOS simulator
// 1. Start app
// 2. Navigate to agent chat
// 3. Send message "Hello"
// 4. Verify AI response appears
// 5. Check conversation memory persists
```

---

## 🚨 **Common Issues & Solutions**

### **Issue: OpenAI API Key Error**
```bash
# Error: "OpenAI API key not configured"
# Solution: Check your .env file
cat server/.env | grep OPENAI_API_KEY
# Should show: OPENAI_API_KEY=sk-your-real-key
```

### **Issue: Pinecone Connection Failed**
```bash
# Error: Cannot connect to Pinecone
# Solution: Verify URL and API key
curl -H "Api-Key: your-pinecone-key" https://your-index.svc.pinecone.io/describe
```

### **Issue: Database Migration Error**
```bash
# Error: Cannot run migrations
# Solution: Reset and re-run
swift run App migrate --revert --env development
swift run App migrate --env development
```

### **Issue: WebSocket Connection Failed**
```bash
# Error: WebSocket won't connect
# Solution: Test with wscat
npm install -g wscat
wscat -c ws://localhost:8080/api/v1/agents/conversation/test/ws
```

---

## 📋 **Phase 2 Success Criteria**

### **Week 1-2 Goals**
- [ ] AI agent responds naturally to user messages
- [ ] Agent personality affects response style
- [ ] Conversation context preserved between messages
- [ ] Server handles multiple concurrent agent sessions

### **Week 3-4 Goals**  
- [ ] iOS app displays real-time agent conversations
- [ ] WebSocket communication works smoothly
- [ ] User can start/stop agent sessions from iOS
- [ ] Conversation history persists in database

### **Final Phase 2 Demo**
- User opens iOS app
- Taps "Chat with AI Tutor"  
- Types "Bonjour!" in French mode
- AI tutor responds naturally in character
- Conversation continues with context memory
- Session persists across app restarts

---

## 🔄 **After Phase 2: Next Steps**

### **Phase 3: Multi-Agent Coordination** (4 weeks)
- Add ConversationPartner and CulturalGuide agents
- Implement agent handoff and coordination
- Create immersive cultural scenarios

### **Phase 4: Advanced Features** (6 weeks)  
- Voice conversation with speech recognition
- Real-time learning analytics
- Advanced personality adaptation

### **Phase 5: Production** (8 weeks)
- Cultural scenario library
- Production deployment
- User testing and optimization

---

## 📞 **Need Help?**

### **Documentation References**
- **Technical Details**: [docs/DEVELOPMENT.md](docs/DEVELOPMENT.md)
- **Architecture**: [docs/AGENTIC_ARCHITECTURE.md](docs/AGENTIC_ARCHITECTURE.md)  
- **Implementation Plan**: [PHASE_IMPLEMENTATION.md](PHASE_IMPLEMENTATION.md)

### **Key Files to Study**
1. `server/Sources/App/Services/AgentOrchestrationService.swift` - Start here
2. `server/Sources/App/Controllers/Agent/AgentController.swift` - API endpoints
3. `server/Sources/App/Models/Agent/` - Database models

### **Development Workflow**
1. **Server changes** → Test with curl commands
2. **iOS changes** → Test in simulator  
3. **Integration** → Test full flow
4. **Documentation** → Update this file with discoveries

---

**🎯 Ready to build the future of AI language learning!** 