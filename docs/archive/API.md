# NIRA Hybrid Architecture API Documentation

## Overview

The NIRA API provides a comprehensive set of endpoints for the hybrid architecture, supporting intelligent content caching, server-side content generation, and personalized learning experiences.

**Base URL**: `https://api.nira-app.com` (Production) / `http://localhost:8080` (Development)

**API Version**: `v1`

**Authentication**: Bearer token (JWT) required for all protected endpoints

## Table of Contents

1. [Authentication](#authentication)
2. [Content Management](#content-management)
3. [Lesson Bundles](#lesson-bundles)
4. [Cache Management](#cache-management)
5. [Cultural Context](#cultural-context)
6. [Real-time Features](#real-time-features)
7. [Error Handling](#error-handling)
8. [Rate Limiting](#rate-limiting)

---

## Authentication

### Headers
All protected endpoints require authentication via JWT token:

```http
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

### Token Refresh
Tokens expire after 24 hours. Refresh using Auth0 SDK on the client side.

---

## Content Management

### Get Personalized Lessons

Retrieve personalized lesson recommendations based on user profile and learning patterns.

```http
GET /api/v1/content/lessons/personalized
```

**Query Parameters:**
- `language` (required): Target language (french, spanish, japanese, tamil, english)
- `count` (optional): Number of lessons to return (default: 10, max: 50)
- `difficulty` (optional): Filter by difficulty (beginner, elementary, intermediate, advanced)
- `category` (optional): Filter by category (vocabulary, grammar, conversation, culture)

**Example Request:**
```bash
curl -X GET "https://api.nira-app.com/api/v1/content/lessons/personalized?language=french&count=5&difficulty=intermediate" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
```

**Response:**
```json
{
  "lessons": [
    {
      "id": "lesson_123",
      "title": "French Café Conversations",
      "description": "Learn how to order coffee and pastries in French cafés",
      "difficulty": "intermediate",
      "estimatedDuration": 15,
      "category": "conversation"
    }
  ],
  "totalAvailable": 47,
  "cacheRecommendations": ["lesson_123", "lesson_124", "lesson_125"]
}
```

### Get Individual Lesson

Retrieve a specific lesson by ID.

```http
GET /api/v1/content/lesson/:lessonId
```

**Path Parameters:**
- `lessonId` (required): Unique lesson identifier

**Example Request:**
```bash
curl -X GET "https://api.nira-app.com/api/v1/content/lesson/lesson_123" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
```

**Response:**
```json
{
  "lesson": {
    "id": "lesson_123",
    "title": "French Café Conversations",
    "description": "Learn how to order coffee and pastries in French cafés",
    "content": "In this lesson, you'll learn essential phrases for French café experiences...",
    "vocabulary": [
      {
        "word": "un café",
        "translation": "a coffee",
        "partOfSpeech": "noun",
        "context": "Je voudrais un café, s'il vous plaît.",
        "difficulty": "beginner"
      }
    ],
    "exercises": [
      {
        "type": "multiple_choice",
        "question": "How do you say 'I would like a coffee' in French?",
        "options": [
          "Je voudrais un café",
          "Je veux café",
          "Un café, moi",
          "Café pour moi"
        ],
        "correctAnswer": "Je voudrais un café",
        "points": 10,
        "explanation": "This is the polite way to order coffee in French."
      }
    ],
    "culturalContext": {
      "id": "cultural_456",
      "language": "french",
      "region": "France",
      "topic": "Café Culture",
      "complexity": "intermediate",
      "tags": ["social", "food", "etiquette"]
    },
    "language": "french",
    "difficulty": "intermediate",
    "category": "conversation",
    "estimatedDuration": 15,
    "topics": ["ordering", "politeness", "food vocabulary"],
    "learningObjectives": [
      "Order coffee and pastries politely",
      "Understand café etiquette",
      "Use conditional tense for requests"
    ]
  }
}
```

### Generate Custom Lesson

Request generation of a custom lesson based on specific parameters.

```http
POST /api/v1/content/lesson/generate
```

**Request Body:**
```json
{
  "language": "spanish",
  "difficulty": "intermediate",
  "category": "vocabulary",
  "culturalRegion": "Mexico",
  "focusAreas": ["food", "restaurants"],
  "avoidTopics": ["politics", "religion"],
  "estimatedDuration": 20
}
```

**Response:**
```json
{
  "lesson": {
    "id": "lesson_789",
    "title": "Mexican Restaurant Vocabulary",
    "description": "Essential vocabulary for dining in Mexican restaurants",
    "content": "...",
    "vocabulary": [...],
    "exercises": [...],
    "culturalContext": {...},
    "language": "spanish",
    "difficulty": "intermediate",
    "category": "vocabulary",
    "estimatedDuration": 20,
    "topics": ["food", "restaurants", "ordering"],
    "learningObjectives": [...]
  }
}
```

---

## Lesson Bundles

### Get Lesson Bundle

Retrieve a personalized lesson bundle for intelligent caching.

```http
GET /api/v1/content/lesson-bundle/:language
```

**Path Parameters:**
- `language` (required): Target language

**Example Request:**
```bash
curl -X GET "https://api.nira-app.com/api/v1/content/lesson-bundle/spanish" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
```

**Response:**
```json
{
  "bundleId": "bundle_abc123",
  "lessonIds": [
    "lesson_001",
    "lesson_002",
    "lesson_003"
  ],
  "priorityLessons": [
    {
      "id": "lesson_001",
      "title": "Spanish Greetings",
      "description": "Basic greetings and introductions in Spanish",
      "content": "...",
      "vocabulary": [...],
      "exercises": [...],
      "culturalContext": {...},
      "language": "spanish",
      "difficulty": "beginner",
      "category": "conversation",
      "estimatedDuration": 10,
      "topics": ["greetings", "introductions"],
      "learningObjectives": [...]
    }
  ],
  "estimatedSize": 15360,
  "expiresAt": "2024-01-15T10:00:00Z",
  "cacheRecommendations": [
    {
      "lessonId": "lesson_001",
      "priority": "high",
      "estimatedSize": 2048,
      "reason": "High relevance to user profile"
    },
    {
      "lessonId": "lesson_002",
      "priority": "medium",
      "estimatedSize": 1536,
      "reason": "Next in learning sequence"
    }
  ]
}
```

### Request Lesson Bundle Generation

Request asynchronous generation of a lesson bundle.

```http
POST /api/v1/content/lesson-bundle/request
```

**Request Body:**
```json
{
  "language": "french",
  "bundleSize": 10,
  "preferences": {
    "focusAreas": ["conversation", "vocabulary"],
    "avoidTopics": ["business", "politics"],
    "preferredExerciseTypes": ["multiple_choice", "fill_blank"],
    "culturalRegions": ["France", "Quebec"],
    "difficultyRange": ["intermediate", "advanced"]
  }
}
```

**Response:**
```json
{
  "jobId": "job_def456",
  "status": "processing",
  "estimatedCompletionTime": "2024-01-01T10:05:00Z"
}
```

### Get Bundle Generation Status

Check the status of a bundle generation job.

```http
GET /api/v1/content/lesson-bundle/:bundleId/status
```

**Response:**
```json
{
  "status": "completed",
  "progress": 1.0,
  "completedLessons": 10,
  "totalLessons": 10,
  "downloadUrl": "https://api.nira-app.com/api/v1/content/lesson-bundle/bundle_abc123"
}
```

**Status Values:**
- `pending`: Job queued but not started
- `processing`: Content generation in progress
- `completed`: Bundle ready for download
- `failed`: Generation failed (check error field)

---

## Cache Management

### Get Cache Recommendations

Retrieve intelligent caching recommendations based on user patterns.

```http
GET /api/v1/content/cache/recommendations
```

**Query Parameters:**
- `language` (required): Target language
- `currentCacheSize` (optional): Current cache size in KB
- `deviceCapacity` (optional): Available device storage in MB

**Example Request:**
```bash
curl -X GET "https://api.nira-app.com/api/v1/content/cache/recommendations?language=japanese&currentCacheSize=10240" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
```

**Response:**
```json
{
  "recommendations": [
    {
      "lessonId": "lesson_jp001",
      "priority": "high",
      "estimatedSize": 2048,
      "reason": "Based on learning patterns"
    },
    {
      "lessonId": "lesson_jp002",
      "priority": "medium",
      "estimatedSize": 1536,
      "reason": "Upcoming in curriculum"
    }
  ],
  "optimalCacheSize": 30720,
  "refreshInterval": 259200
}
```

### Request Content Prefetch

Get optimized content for prefetching.

```http
POST /api/v1/content/cache/prefetch
```

**Request Body:**
```json
{
  "language": "tamil",
  "lessonCount": 5,
  "deviceCapacity": 64000,
  "connectionType": "wifi",
  "batteryLevel": 0.8
}
```

**Response:**
```json
{
  "prefetchId": "prefetch_789",
  "data": {
    "lessonIds": ["lesson_ta001", "lesson_ta002"],
    "priority": "high",
    "estimatedSize": 10240,
    "expiresAt": "2024-01-08T10:00:00Z"
  },
  "downloadUrls": [
    "https://api.nira-app.com/api/v1/content/lesson/lesson_ta001/download",
    "https://api.nira-app.com/api/v1/content/lesson/lesson_ta002/download"
  ],
  "cacheStrategy": {
    "type": "intelligent",
    "maxSize": 51200,
    "refreshPolicy": "weekly"
  }
}
```

---

## Cultural Context

### Get Cultural Context by Language

Retrieve cultural context information for a specific language.

```http
GET /api/v1/content/cultural/:language
```

**Path Parameters:**
- `language` (required): Target language

**Query Parameters:**
- `count` (optional): Number of items to return (default: 10)

**Example Request:**
```bash
curl -X GET "https://api.nira-app.com/api/v1/content/cultural/japanese?count=5" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
```

**Response:**
```json
{
  "content": [
    {
      "id": "cultural_jp001",
      "language": "japanese",
      "region": "Japan",
      "topic": "Bowing Etiquette",
      "complexity": "intermediate",
      "tags": ["social", "respect", "greetings"]
    },
    {
      "id": "cultural_jp002",
      "language": "japanese",
      "region": "Japan",
      "topic": "Business Card Exchange",
      "complexity": "advanced",
      "tags": ["business", "respect", "protocol"]
    }
  ]
}
```

### Get Regional Cultural Context

Retrieve cultural context for a specific language and region.

```http
GET /api/v1/content/cultural/:language/:region
```

**Path Parameters:**
- `language` (required): Target language
- `region` (required): Specific region or country

**Example Request:**
```bash
curl -X GET "https://api.nira-app.com/api/v1/content/cultural/spanish/mexico" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
```

**Response:**
```json
{
  "content": [
    {
      "id": "cultural_mx001",
      "language": "spanish",
      "region": "Mexico",
      "topic": "Day of the Dead Traditions",
      "complexity": "intermediate",
      "tags": ["tradition", "family", "celebration"]
    }
  ]
}
```

---

## Real-time Features

### WebSocket Connection

Connect to real-time lesson synchronization.

**Endpoint:** `wss://api.nira-app.com/ws/lessons/sync` (Production)
**Endpoint:** `ws://localhost:8080/ws/lessons/sync` (Development)

**Authentication:** Include JWT token in connection headers

**Example Connection:**
```javascript
const ws = new WebSocket('wss://api.nira-app.com/ws/lessons/sync', [], {
  headers: {
    'Authorization': 'Bearer ' + jwtToken
  }
});
```

**Message Types:**

#### Lesson Progress Sync
```json
{
  "type": "progress_update",
  "data": {
    "lessonId": "lesson_123",
    "userId": "user_456",
    "progress": 0.75,
    "timeSpent": 450,
    "exercisesCompleted": 3
  }
}
```

#### Cache Invalidation
```json
{
  "type": "cache_invalidate",
  "data": {
    "lessonIds": ["lesson_123", "lesson_124"],
    "reason": "content_updated"
  }
}
```

#### New Content Available
```json
{
  "type": "content_available",
  "data": {
    "bundleId": "bundle_new123",
    "language": "french",
    "lessonCount": 5
  }
}
```

---

## Error Handling

### Standard Error Response Format

```json
{
  "error": {
    "code": "LESSON_NOT_FOUND",
    "message": "The requested lesson could not be found",
    "details": {
      "lessonId": "lesson_invalid",
      "suggestion": "Check lesson ID or request personalized lessons"
    }
  },
  "timestamp": "2024-01-01T10:00:00Z",
  "path": "/api/v1/content/lesson/lesson_invalid"
}
```

### Common Error Codes

| Status Code | Error Code | Description |
|-------------|------------|-------------|
| 400 | `INVALID_REQUEST` | Request parameters are invalid |
| 401 | `UNAUTHORIZED` | Invalid or missing authentication token |
| 403 | `FORBIDDEN` | Insufficient permissions |
| 404 | `LESSON_NOT_FOUND` | Requested lesson does not exist |
| 404 | `BUNDLE_NOT_FOUND` | Requested bundle does not exist |
| 409 | `GENERATION_IN_PROGRESS` | Lesson generation already in progress |
| 429 | `RATE_LIMIT_EXCEEDED` | Too many requests |
| 500 | `INTERNAL_ERROR` | Server error |
| 502 | `EXTERNAL_SERVICE_ERROR` | External API (Pinecone/Gemini) error |
| 503 | `SERVICE_UNAVAILABLE` | Service temporarily unavailable |

### Error Examples

#### Invalid Language Parameter
```json
{
  "error": {
    "code": "INVALID_REQUEST",
    "message": "Invalid language parameter",
    "details": {
      "field": "language",
      "value": "klingon",
      "allowed": ["french", "spanish", "japanese", "tamil", "english"]
    }
  },
  "timestamp": "2024-01-01T10:00:00Z",
  "path": "/api/v1/content/lessons/personalized"
}
```

#### Rate Limit Exceeded
```json
{
  "error": {
    "code": "RATE_LIMIT_EXCEEDED",
    "message": "Rate limit exceeded: 100 requests per hour",
    "details": {
      "limit": 100,
      "window": "1h",
      "resetTime": "2024-01-01T11:00:00Z"
    }
  },
  "timestamp": "2024-01-01T10:30:00Z",
  "path": "/api/v1/content/lesson/generate"
}
```

---

## Rate Limiting

### Standard Limits

| Endpoint Type | Limit | Window |
|---------------|-------|--------|
| Content Retrieval | 1000 requests | 1 hour |
| Lesson Generation | 10 requests | 1 hour |
| Bundle Requests | 5 requests | 1 hour |
| Cache Operations | 500 requests | 1 hour |
| WebSocket Messages | 100 messages | 1 minute |

### Rate Limit Headers

All responses include rate limiting information:

```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1640995200
X-RateLimit-Window: 3600
```

### Handling Rate Limits

When rate limited, implement exponential backoff:

```javascript
async function makeAPIRequest(url, options, retries = 3) {
  try {
    const response = await fetch(url, options);
    
    if (response.status === 429) {
      const resetTime = response.headers.get('X-RateLimit-Reset');
      const delay = Math.min(Math.pow(2, 3 - retries) * 1000, 60000);
      
      if (retries > 0) {
        await new Promise(resolve => setTimeout(resolve, delay));
        return makeAPIRequest(url, options, retries - 1);
      }
    }
    
    return response;
  } catch (error) {
    throw error;
  }
}
```

---

## API Status

### Health Check

```http
GET /api/v1/health
```

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T10:00:00Z",
  "version": "1.0.0",
  "services": {
    "database": "connected",
    "pinecone": "connected",
    "gemini": "connected",
    "redis": "connected"
  }
}
```

### Feature Status

```http
GET /api/v1/status
```

**Response:**
```json
{
  "server": "running",
  "database": "connected",
  "pinecone": "connected",
  "features": {
    "content_generation": true,
    "intelligent_caching": true,
    "vector_search": true,
    "real_time_sync": true,
    "cultural_context": true
  }
}
```

---

## SDK Examples

### iOS Swift Example

```swift
import Foundation

class NIRAAPIClient {
    private let baseURL = "https://api.nira-app.com/api/v1"
    private let session = URLSession.shared
    
    func getPersonalizedLessons(language: String) async throws -> PersonalizedLessonsResponse {
        let url = URL(string: "\(baseURL)/content/lessons/personalized?language=\(language)")!
        var request = URLRequest(url: url)
        request.setValue("Bearer \(jwtToken)", forHTTPHeaderField: "Authorization")
        
        let (data, response) = try await session.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw APIError.requestFailed
        }
        
        return try JSONDecoder().decode(PersonalizedLessonsResponse.self, from: data)
    }
    
    func requestLessonBundle(language: String, bundleSize: Int) async throws -> BundleGenerationResponse {
        let url = URL(string: "\(baseURL)/content/lesson-bundle/request")!
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("Bearer \(jwtToken)", forHTTPHeaderField: "Authorization")
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        
        let requestBody = LessonBundleRequest(language: language, bundleSize: bundleSize)
        request.httpBody = try JSONEncoder().encode(requestBody)
        
        let (data, response) = try await session.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw APIError.requestFailed
        }
        
        return try JSONDecoder().decode(BundleGenerationResponse.self, from: data)
    }
}
```

### WebSocket Swift Example

```swift
import Network

class NIRAWebSocketClient {
    private var webSocketTask: URLSessionWebSocketTask?
    
    func connect() {
        let url = URL(string: "wss://api.nira-app.com/ws/lessons/sync")!
        var request = URLRequest(url: url)
        request.setValue("Bearer \(jwtToken)", forHTTPHeaderField: "Authorization")
        
        webSocketTask = URLSession.shared.webSocketTask(with: request)
        webSocketTask?.resume()
        
        receiveMessage()
    }
    
    private func receiveMessage() {
        webSocketTask?.receive { [weak self] result in
            switch result {
            case .success(let message):
                switch message {
                case .string(let text):
                    self?.handleMessage(text)
                case .data(let data):
                    self?.handleData(data)
                @unknown default:
                    break
                }
                self?.receiveMessage()
            case .failure(let error):
                print("WebSocket error: \(error)")
            }
        }
    }
    
    private func handleMessage(_ text: String) {
        // Handle incoming WebSocket messages
        if let data = text.data(using: .utf8),
           let message = try? JSONDecoder().decode(WebSocketMessage.self, from: data) {
            // Process message based on type
        }
    }
}
```

---

This API documentation provides comprehensive coverage of all hybrid architecture endpoints, enabling efficient development and integration of the intelligent caching system with server-side content generation. 