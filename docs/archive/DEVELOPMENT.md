# NIRA Hybrid Architecture Development Guide

## Prerequisites

### Required Software
- **Xcode 15.0+** with iOS 17.0+ SDK
- **Swift 5.9+**
- **macOS 14.0+** (for iOS development)
- **Git** for version control
- **Homebrew** (recommended for package management)

### Optional but Recommended
- **Vapor Toolbox** for backend development
- **PostgreSQL** for local database development
- **Redis** for local caching
- **Postman** or **Insomnia** for API testing
- **Pinecone CLI** for vector database management

## Environment Setup

### 🎯 **Current Status: Phase 1 Complete ✅**
As of May 23, 2024, the agentic AI foundation is implemented and ready for Phase 2 development.

### 1. Clone the Repository

```bash
git clone https://github.com/yourusername/nira-language-app.git
cd NIRA
```

### 2. Install Development Dependencies

```bash
# Install Vapor toolbox
brew install vapor

# Install PostgreSQL (if running backend locally)
brew install postgresql
brew services start postgresql

# Install Redis (for caching and session management)
brew install redis
brew services start redis
```

### 3. Configure Environment Variables ✅

**✅ ALREADY CREATED**: The `.env` file is already created in `server/.env` with dummy API keys.

**⚠️ CRITICAL**: Replace dummy API keys with real ones:

```bash
# Navigate to server directory
cd server

# Edit the .env file
nano .env

# Replace these dummy values with real API keys:
OPENAI_API_KEY=sk-DUMMY1234567890abcdefghijklmnopqrstuvwxyzABCDEF
GEMINI_API_KEY=AIzaSyDUMMY-1234567890-abcdefghijklmnopqrstuvwxyz
PINECONE_API_KEY=12345678-abcd-1234-efgh-567890123456
PINECONE_URL=https://your-index-dummy123.svc.pinecone.io
JWT_SECRET=your-super-secure-jwt-secret-key-replace-this-dummy-value
PORT=8080
LOG_LEVEL=debug
```

### 4. Current Environment Configuration

The `server/.env` file contains the following configuration:

```bash
# === NIRA AGENTIC AI CONFIGURATION ===
NODE_ENV=development
ENVIRONMENT=development

# === AI SERVICE API KEYS (REPLACE WITH REAL KEYS) ===
OPENAI_API_KEY=sk-DUMMY1234567890abcdefghijklmnopqrstuvwxyzABCDEF
GEMINI_API_KEY=AIzaSyDUMMY-1234567890-abcdefghijklmnopqrstuvwxyz
PINECONE_API_KEY=12345678-abcd-1234-efgh-567890123456
PINECONE_URL=https://your-index-dummy123.svc.pinecone.io
JWT_SECRET=your-super-secure-jwt-secret-key-replace-this-dummy-value
PORT=8080
LOG_LEVEL=debug

# Additional agent-specific configurations included...
```

### 5. Environment-Specific Files ✅

Environment-specific files are already created:
- `server/.env` - Main configuration
- `server/.env.development` - Development environment
- `server/.env.production` - Production environment  
- `server/.env.testing` - Testing environment

### 6. Test Your Setup

```bash
# Start the server
cd server
swift run App serve --env development

# Test agent endpoints (in another terminal)
curl http://localhost:8080/api/v1/agents/available

# Should return: {"available_agents": ["tutor"], "status": "foundation_ready"}
```

## iOS App Development

### 1. Open the Xcode Project

```bash
open NIRA.xcodeproj
```

### 2. Install Dependencies via Swift Package Manager

The following dependencies should be added via Xcode → File → Add Package Dependencies:

```
Auth0.swift: https://github.com/auth0/Auth0.swift
Lottie: https://github.com/airbnb/lottie-ios
FirebaseSDK: https://github.com/firebase/firebase-ios-sdk
Sentry: https://github.com/getsentry/sentry-cocoa
```

### 3. Hybrid Architecture Project Structure

Create the following directory structure in your iOS project:

```
NIRA/
├── App/
│   ├── NIRAApp.swift
│   └── Info.plist
├── Views/
│   ├── Authentication/
│   │   ├── LoginView.swift
│   │   ├── SignupView.swift
│   │   └── ProfileView.swift
│   ├── Lessons/
│   │   ├── LessonListView.swift
│   │   ├── LessonDetailView.swift
│   │   ├── ExerciseView.swift
│   │   └── OfflineContentView.swift      # 🆕 Offline content management
│   ├── Cache/                           # 🆕 Cache management views
│   │   ├── CacheStatusView.swift
│   │   └── DownloadProgressView.swift
│   └── Common/
│       ├── LoadingView.swift
│       └── ErrorView.swift
├── ViewModels/
│   ├── AuthViewModel.swift
│   ├── LessonViewModel.swift
│   ├── CacheViewModel.swift             # 🆕 Cache management
│   └── ProfileViewModel.swift
├── Models/
│   ├── User.swift
│   ├── Lesson.swift
│   ├── Exercise.swift
│   ├── Progress.swift
│   ├── CachedLesson.swift              # 🆕 Cache models
│   └── LessonBundle.swift              # 🆕 Bundle models
├── Services/
│   ├── APIClient.swift                 # 🆕 Unified API client
│   ├── ContentCacheService.swift       # 🆕 Intelligent caching
│   ├── LessonService.swift             # 🔄 Updated for hybrid approach
│   ├── AuthService.swift
│   └── NetworkMonitor.swift            # 🆕 Network monitoring
├── Utils/
│   ├── Config.swift
│   ├── Extensions/
│   │   ├── View+Cache.swift            # 🆕 Cache-related view extensions
│   │   └── UserDefaults+Cache.swift    # 🆕 Cache preferences
│   └── Constants.swift
└── Resources/
    ├── Localizations/
    └── Assets.xcassets/
```

### 4. Build and Run

1. Select your target device or simulator
2. Press `Cmd+R` to build and run
3. The app should launch with hybrid caching enabled

### 5. Testing Cache Functionality

```swift
// Test intelligent caching in your development environment
ContentCacheService.shared.requestLessonBundle(
    language: "french",
    bundleSize: 5
)

// Monitor cache status
ContentCacheService.shared.$cacheStatus
    .sink { status in
        print("Cache utilization: \(status.utilizationPercentage)%")
    }
```

## Backend Development

### 1. Set Up Hybrid Architecture Server

If the server directory doesn't exist, create it:

```bash
mkdir server
cd server
vapor new . --no-commit
```

### 2. Configure Package.swift for Hybrid Architecture

Update `server/Package.swift`:

```swift
// swift-tools-version:5.9
import PackageDescription

let package = Package(
    name: "nira-api",
    platforms: [
       .macOS(.v13)
    ],
    dependencies: [
        .package(url: "https://github.com/vapor/vapor.git", from: "4.89.0"),
        .package(url: "https://github.com/vapor/fluent.git", from: "4.8.0"),
        .package(url: "https://github.com/vapor/fluent-postgres-driver.git", from: "2.7.2"),
        .package(url: "https://github.com/vapor/redis.git", from: "4.10.0"),
        .package(url: "https://github.com/vapor/jwt.git", from: "4.2.2"),
        .package(url: "https://github.com/vapor/websocket-kit.git", from: "2.6.1"),
    ],
    targets: [
        .executableTarget(
            name: "App",
            dependencies: [
                .product(name: "Vapor", package: "vapor"),
                .product(name: "Fluent", package: "fluent"),
                .product(name: "FluentPostgresDriver", package: "fluent-postgres-driver"),
                .product(name: "Redis", package: "redis"),
                .product(name: "JWT", package: "jwt"),
                .product(name: "WebSocketKit", package: "websocket-kit"),
            ]
        ),
        .testTarget(name: "AppTests", dependencies: [
            .target(name: "App"),
            .product(name: "XCTVapor", package: "vapor"),
        ])
    ]
)
```

### 3. Hybrid Architecture Implementation

#### Implemented Services ✅

**PineconeService** (`server/Sources/App/Services/PineconeService.swift`):
```swift
// Vector operations and content management
// - Lesson content indexing with embeddings
// - Personalized content retrieval
// - Cultural context management
// - Related content discovery
```

**ContentGenerationService** (`server/Sources/App/Services/ContentGenerationService.swift`):
```swift
// Server-side AI content generation
// - Gemini AI integration for lesson creation
// - Personalized batch lesson generation
// - Adaptive lesson creation based on performance
// - Quality consistency across all users
```

**ContentController** (`server/Sources/App/Controllers/ContentController.swift`):
```swift
// API endpoints for content delivery and caching coordination
// - GET /api/content/lesson-bundle/:language
// - POST /api/content/lesson-bundle/request
// - GET /api/content/lessons/personalized
// - POST /api/content/cache/prefetch
```

### 4. Configure Server Environment

Create `server/.env`:

```bash
# Database
DATABASE_URL=postgresql://username:password@localhost:5432/nira_dev

# Pinecone Vector Database
PINECONE_API_KEY=your-pinecone-api-key
PINECONE_URL=https://your-index.pinecone.io

# AI Services
GEMINI_API_KEY=your-gemini-api-key
OPENAI_API_KEY=your-openai-api-key

# Redis
REDIS_URL=redis://localhost:6379

# Server Configuration
PORT=8080
LOG_LEVEL=debug
ENVIRONMENT=development
```

### 5. Run the Server

```bash
cd server
vapor build
vapor run
```

The server will start on `http://localhost:8080` with the following endpoints:

```
GET     /                           # Health check
GET     /api/v1/health             # API health status
GET     /api/content/lesson-bundle/:language
POST    /api/content/lesson-bundle/request
GET     /api/content/lessons/personalized
WebSocket /ws/lessons/sync          # Real-time synchronization
```

## Development Workflow

### 1. Hybrid Architecture Development Process

```bash
# 1. Start backend services
cd server
vapor run &

# 2. Start Redis (for caching)
redis-server &

# 3. Open iOS project
open NIRA.xcodeproj

# 4. Build and run iOS app
# Cmd+R in Xcode
```

### 2. Testing Content Generation

```bash
# Test server-side content generation
curl -X POST http://localhost:8080/api/content/lesson-bundle/request \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <jwt_token>" \
  -d '{
    "language": "french",
    "bundleSize": 5,
    "preferences": {
      "focusAreas": ["conversation", "vocabulary"],
      "avoidTopics": ["business"]
    }
  }'
```

### 3. Testing Intelligent Caching

```swift
// In your iOS app development
Task {
    do {
        let bundle = try await ContentCacheService.shared.requestLessonBundle(
            language: "spanish",
            bundleSize: 10
        )
        print("Bundle created with \(bundle.lessonIds.count) lessons")
        print("Cache status: \(ContentCacheService.shared.cacheStatus)")
    } catch {
        print("Error: \(error)")
    }
}
```

### 4. Monitor Cache Performance

```swift
// Add cache monitoring to your development builds
ContentCacheService.shared.$cacheStatus
    .receive(on: DispatchQueue.main)
    .sink { status in
        print("""
        Cache Status:
        - Size: \(status.formattedCurrentSize) / \(status.formattedMaxSize)
        - Utilization: \(status.utilizationPercentage)%
        - Lesson Count: \(status.lessonCount)
        """)
    }
    .store(in: &cancellables)
```

## Testing and Debugging

### 1. Backend API Testing

```bash
# Test content bundle request
curl -X GET http://localhost:8080/api/content/lesson-bundle/french \
  -H "Authorization: Bearer <jwt_token>"

# Test cache recommendations
curl -X GET "http://localhost:8080/api/content/cache/recommendations?language=spanish&currentCacheSize=10240" \
  -H "Authorization: Bearer <jwt_token>"

# Test cultural context
curl -X GET http://localhost:8080/api/content/cultural/japanese \
  -H "Authorization: Bearer <jwt_token>"
```

### 2. iOS Cache Testing

```swift
// Test cache hit rates in development
let cacheService = ContentCacheService.shared

// Preload content
try await cacheService.prefetchRecommendedContent(for: currentUser)

// Test offline access
let offlineContent = try await cacheService.getOfflineContent(for: "french")
print("Available offline: \(offlineContent.count) lessons")

// Test cache cleanup
try await cacheService.cleanupCache()
```

### 3. Performance Monitoring

```swift
// Monitor cache performance metrics
struct CacheMetrics {
    let hitRate: Double
    let averageLoadTime: TimeInterval
    let prefetchSuccess: Double
    let storageEfficiency: Double
}

// Implement in your development builds
```

## Debugging Common Issues

### 1. Cache Not Working
```swift
// Check cache directory permissions
let cacheDir = ContentCacheService.shared.cacheDirectory
print("Cache directory: \(cacheDir)")
print("Directory exists: \(FileManager.default.fileExists(atPath: cacheDir.path))")
```

### 2. Server Connection Issues
```bash
# Check server status
curl http://localhost:8080/api/v1/health

# Check environment variables
vapor run --env development --verbose
```

### 3. Pinecone Integration Issues
```bash
# Test Pinecone connection
curl -X GET https://your-index.pinecone.io/describe \
  -H "Api-Key: your-api-key"
```

## Production Deployment

### 1. Server Deployment
```bash
# Build for production
vapor build --configuration release

# Deploy to your production environment
# (Railway, Heroku, AWS, etc.)
```

### 2. iOS App Store Build
```bash
# Archive for App Store
# Product → Archive in Xcode

# Upload to App Store Connect
# Use Xcode Organizer or altool
```

### 3. Environment Configuration
```bash
# Production environment variables
ENVIRONMENT=production
DATABASE_URL=postgresql://prod-db-url
PINECONE_API_KEY=prod-pinecone-key
GEMINI_API_KEY=prod-gemini-key
```

---

## Agentic AI Development Workflow

### Prerequisites for Agentic Features

Before starting agentic AI development, ensure you have completed the hybrid architecture setup above. The agentic system builds on top of the existing infrastructure.

### 1. Agent Framework Setup

#### Install CrewAI Dependencies (Server)

```bash
# Add to server Package.swift dependencies
cd server

# Install Python dependencies for CrewAI integration
pip install crewai langchain openai pinecone-client

# Create Python bridge for Swift-Python communication
# Add to server dependencies:
.package(url: "https://github.com/pvieito/PythonKit", from: "0.3.1")
```

#### LangGraph Alternative Setup

```bash
# If choosing LangGraph instead
pip install langgraph langchain-core langchain-openai

# Create graph visualization dependencies
pip install matplotlib networkx
```

### 2. Agent Development Environment

#### Server-Side Agent Development

```bash
# Create agent development structure
cd server/Sources/App
mkdir -p Agents/{Core,Orchestration,Communication}
mkdir -p Models/Agent
mkdir -p Controllers/Agent

# Agent development files
touch Agents/Core/TutorAgent.swift
touch Agents/Core/ConversationPartnerAgent.swift
touch Agents/Core/CulturalGuideAgent.swift
touch Agents/Orchestration/AgentOrchestrator.swift
touch Controllers/Agent/AgentController.swift
```

#### iOS Agent Development

```bash
# Create agent UI components
cd NIRA
mkdir -p Views/Agent/{Chat,Scenarios,Configuration}
mkdir -p Services/Agent
mkdir -p Models/Agent

# Agent UI files
touch Views/Agent/Chat/AgentConversationView.swift
touch Views/Agent/Chat/AgentBubbleView.swift
touch Views/Agent/Scenarios/ScenarioSelectionView.swift
touch Services/Agent/AgentCommunicationService.swift
```

### 3. Development Workflow for Agents

#### Phase 1: Foundation Development

```bash
# 1. Start with database migrations
cd server
vapor run migrate --revert  # If needed
vapor run migrate

# 2. Create basic agent endpoints
# Edit routes.swift to add agent routes

# 3. Test basic agent infrastructure
curl -X GET http://localhost:8080/api/v1/agents/available
```

#### Phase 2: Single Agent Development

```swift
// Development testing for TutorAgent
class TutorAgentTests: XCTestCase {
    func testAgentPersonality() async throws {
        let agent = TutorAgent(personality: .patient, language: .french)
        let response = try await agent.generateResponse(
            to: "Bonjour!",
            context: ConversationContext.beginnerFrench
        )
        
        XCTAssertNotNil(response)
        XCTAssertTrue(response.message.contains("Bonjour"))
        XCTAssertEqual(response.agentId, "tutor_french")
    }
    
    func testContextPreservation() async throws {
        let agent = TutorAgent(personality: .patient, language: .french)
        
        // First message
        let context1 = ConversationContext.beginnerFrench
        let response1 = try await agent.generateResponse(to: "Bonjour!", context: context1)
        
        // Second message should reference first
        let context2 = context1.addingTurn(response1)
        let response2 = try await agent.generateResponse(to: "Comment allez-vous?", context: context2)
        
        XCTAssertTrue(agent.hasContextOf(previousConversation: true))
    }
}
```

#### Phase 3: Multi-Agent Development

```swift
// Development testing for agent coordination
class AgentCoordinationTests: XCTestCase {
    func testScenarioOrchestration() async throws {
        let orchestrator = AgentOrchestrator()
        let scenario = ImmersiveScenario.frenchCafe
        
        let execution = try await orchestrator.orchestrateScenario(
            scenario,
            for: User.testUser
        )
        
        XCTAssertEqual(execution.participatingAgents.count, 3) // Tutor, Partner, Cultural Guide
        XCTAssertTrue(execution.isActive)
    }
    
    func testAgentHandoff() async throws {
        let orchestrator = AgentOrchestrator()
        
        let handoff = try await orchestrator.coordinateAgentHandoff(
            from: .tutor,
            to: .conversationPartner,
            context: HandoffContext.scenarioTransition
        )
        
        XCTAssertTrue(handoff.wasSuccessful)
        XCTAssertNotNil(handoff.contextTransferred)
    }
}
```

### 4. Agent Testing and Debugging

#### Server-Side Agent Testing

```bash
# Test agent endpoint responses
curl -X POST http://localhost:8080/api/v1/agents/conversation/start \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <jwt_token>" \
  -d '{
    "agents": ["tutor", "cultural_guide"],
    "language": "french",
    "scenario": "cafe_conversation",
    "userProficiency": "beginner"
  }'

# Test agent message handling
curl -X POST http://localhost:8080/api/v1/agents/conversation/{sessionId}/message \
  -H "Content-Type: application/json" \
  -d '{
    "message": "Bonjour! Je voudrais un café, s'il vous plaît.",
    "context": {
      "currentTopic": "ordering_food",
      "culturalSetting": "french_cafe"
    }
  }'

# Test multi-agent coordination
curl -X GET http://localhost:8080/api/v1/agents/conversation/{sessionId}/participants
```

#### iOS Agent Testing

```swift
// Test agent communication service
class AgentCommunicationTests: XCTestCase {
    func testAgentConnection() async throws {
        let service = AgentCommunicationService()
        
        let session = try await service.startConversation(
            with: [.tutor, .culturalGuide],
            language: .french,
            scenario: .cafePractice
        )
        
        XCTAssertNotNil(session)
        XCTAssertEqual(session.participants.count, 2)
        XCTAssertTrue(service.isConnected)
    }
    
    func testMessageSending() async throws {
        let service = AgentCommunicationService()
        let session = try await service.startTestConversation()
        
        let response = try await service.sendMessage("Bonjour!")
        
        XCTAssertNotNil(response)
        XCTAssertTrue(response.message.count > 0)
        XCTAssertEqual(response.agentType, .tutor)
    }
}
```

### 5. Real-time Development and WebSocket Testing

#### WebSocket Development Setup

```swift
// Test WebSocket connection for real-time agent interactions
class AgentWebSocketTests: XCTestCase {
    func testRealTimeAgentCommunication() async throws {
        let webSocket = try await WebSocketConnection.connect(to: "ws://localhost:8080/ws/agents")
        
        // Send user message
        let userMessage = AgentWebSocketMessage(
            type: .userMessage,
            content: "Bonjour!",
            sessionId: testSessionId
        )
        
        try await webSocket.send(userMessage)
        
        // Receive agent response
        let response = try await webSocket.receive()
        XCTAssertEqual(response.type, .agentResponse)
        XCTAssertNotNil(response.agentId)
    }
}
```

#### WebSocket Development Monitoring

```bash
# Monitor WebSocket connections during development
npm install -g wscat

# Connect to agent WebSocket endpoint
wscat -c ws://localhost:8080/ws/agents

# Send test messages
{"type":"user_message","sessionId":"test","message":"Hello!"}
```

### 6. Agent Performance Monitoring

#### Development Performance Metrics

```swift
// Monitor agent response times during development
class AgentPerformanceMonitor {
    @Published var responseMetrics: [AgentMetric] = []
    
    func measureAgentResponse<T>(
        _ operation: @escaping () async throws -> T
    ) async rethrows -> T {
        let startTime = Date()
        let result = try await operation()
        let duration = Date().timeIntervalSince(startTime)
        
        responseMetrics.append(AgentMetric(
            duration: duration,
            timestamp: Date(),
            operation: String(describing: T.self)
        ))
        
        return result
    }
}

// Usage in development
let monitor = AgentPerformanceMonitor()
let response = try await monitor.measureAgentResponse {
    return try await tutorAgent.generateResponse(to: userMessage, context: context)
}
```

#### Cache Performance for Agents

```swift
// Monitor agent response caching
class AgentCacheMonitor {
    func logCachePerformance() {
        let metrics = AgentResponseCache.shared.getMetrics()
        print("""
        Agent Cache Metrics:
        - Hit Rate: \(metrics.hitRate * 100)%
        - Average Response Time: \(metrics.averageResponseTime)ms
        - Cache Size: \(metrics.cacheSize) responses
        - Memory Usage: \(metrics.memoryUsage)MB
        """)
    }
}
```

### 7. Cultural Scenario Development

#### Scenario Testing Framework

```swift
// Test cultural scenario authenticity
class CulturalScenarioTests: XCTestCase {
    func testFrenchCafeScenario() async throws {
        let scenario = CulturalScenario.frenchCafe
        let culturalGuide = CulturalGuideAgent(region: .france)
        
        let validation = try await culturalGuide.validateScenario(scenario)
        
        XCTAssertTrue(validation.isAuthentic)
        XCTAssertTrue(validation.culturallyAppropriate)
        XCTAssertFalse(validation.containsStereotypes)
    }
    
    func testScenarioAdaptation() async throws {
        let scenario = CulturalScenario.japaneseTeaCeremony
        let user = User.testUser(proficiency: .beginner)
        
        let adaptedScenario = try await scenario.adaptForUser(user)
        
        XCTAssertEqual(adaptedScenario.complexity, .beginner)
        XCTAssertTrue(adaptedScenario.includesGuidance)
    }
}
```

### 8. Development Best Practices for Agents

#### Agent Personality Consistency

```swift
// Ensure agent personality remains consistent
class AgentPersonalityValidator {
    func validatePersonalityConsistency(
        agent: AgentProtocol,
        responses: [AgentResponse]
    ) -> PersonalityConsistencyReport {
        
        let personalityTraits = agent.personality.traits
        let consistencyScore = responses.reduce(0.0) { score, response in
            return score + response.personalityAlignment(with: personalityTraits)
        } / Double(responses.count)
        
        return PersonalityConsistencyReport(
            score: consistencyScore,
            inconsistencies: findInconsistencies(in: responses),
            recommendations: generatePersonalityRecommendations()
        )
    }
}
```

#### Agent Development Debugging

```swift
// Comprehensive agent debugging tools
class AgentDebugger {
    func debugAgentResponse(
        agent: AgentProtocol,
        input: String,
        context: ConversationContext
    ) async throws -> AgentDebugReport {
        
        let startTime = Date()
        
        // Track agent decision process
        let decisionProcess = try await agent.getDecisionProcess(for: input, context: context)
        
        // Generate response
        let response = try await agent.generateResponse(to: input, context: context)
        
        let processingTime = Date().timeIntervalSince(startTime)
        
        return AgentDebugReport(
            input: input,
            response: response,
            decisionProcess: decisionProcess,
            processingTime: processingTime,
            contextUtilization: analyzeContextUsage(context, decisionProcess),
            personalityExpression: analyzePersonalityInResponse(response, agent.personality)
        )
    }
}
```

### 9. Integration Testing

#### End-to-End Agent Testing

```swift
// Test complete user journey through agent system
class AgentIntegrationTests: XCTestCase {
    func testCompleteConversationFlow() async throws {
        // 1. Start conversation
        let session = try await AgentCommunicationService.shared.startConversation(
            with: [.tutor, .conversationPartner],
            language: .french,
            scenario: .cafePractice
        )
        
        // 2. Send series of messages
        let messages = ["Bonjour!", "Je voudrais un café", "Merci beaucoup"]
        var responses: [AgentResponse] = []
        
        for message in messages {
            let response = try await AgentCommunicationService.shared.sendMessage(message)
            responses.append(response)
        }
        
        // 3. Validate learning progression
        let progression = try await session.assessLearningProgression()
        XCTAssertTrue(progression.hasImprovement)
        XCTAssertGreaterThan(progression.confidenceIncrease, 0.1)
        
        // 4. Test scenario completion
        let completion = try await session.completeScenario()
        XCTAssertTrue(completion.wasSuccessful)
        XCTAssertNotNil(completion.culturalInsights)
    }
}
```

This comprehensive agentic AI development workflow ensures you can build, test, and deploy the agent system methodically while maintaining the existing hybrid architecture functionality. 

# Development environment (your local work)
cp .env .env.development

# Production environment (when deployed)  
cp .env .env.production

# Testing environment
cp .env .env.testing 