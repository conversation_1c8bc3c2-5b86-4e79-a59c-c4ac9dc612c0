# Archive Documentation

This directory contains outdated documentation from the previous Vapor backend implementation and old architectural decisions that are no longer relevant to the current Supabase-based architecture.

## Contents

### Vapor Backend Documentation (Deprecated)
- `API.md` - Old Vapor API documentation
- `ARCHITECTURE.md` - Previous server architecture
- `DEVELOPMENT.md` - Old development setup guide

### Outdated Architecture Documents
- `DATA_STRATEGY.md` - Previous data strategy (replaced by Supabase)
- `HYBRID_ARCHITECTURE.md` - Old hybrid architecture approach
- `IMPLEMENTATION.md` - Previous implementation details

## Note

These files are kept for historical reference only. For current documentation, see:
- Main docs directory: `../`
- Supabase documentation: `../supabase/`
- Project README: `../../README.md`

**Migration Date**: January 2025  
**Reason**: Migrated from Vapor backend to Supabase 