# NIRA Supabase Migration Guide

## Overview
This guide documents the migration from Vapor backend to Supabase for the NIRA language learning app.

## Migration Steps

### Phase 1: Database Schema Migration
- [x] Export current PostgreSQL schema from Vapor
- [ ] Import schema to Supabase
- [ ] Enable Row Level Security
- [ ] Set up user authentication schema

### Phase 2: Backend Migration
- [ ] Create Supabase Edge Functions to replace Vapor controllers
- [ ] Migrate AI services (Gemini, Pinecone) to Edge Functions
- [ ] Set up real-time subscriptions

### Phase 3: iOS App Migration
- [ ] Replace APIClient with Supabase Swift client
- [ ] Update authentication flow
- [ ] Implement real-time features
- [ ] Update all service classes

### Phase 4: Deployment & Testing
- [ ] Deploy Edge Functions
- [ ] Test all functionality
- [ ] Performance optimization

## Database Schema

### Tables to Migrate:
1. `users` - User profiles and authentication
2. `lessons` - Language learning content
3. `progress` - User progress tracking
4. `learning_sessions` - Session data
5. `agents` - AI agent configurations
6. `content_cache` - Cached content

### Supabase Features Used:
- ✅ PostgreSQL Database
- ✅ Row Level Security (RLS)
- ✅ Real-time subscriptions
- ✅ Edge Functions
- ✅ Authentication
- ✅ Storage (for lesson media)

## Cost Comparison
- **Current Vapor**: $30-55/month
- **Supabase Pro**: $25/month
- **Savings**: $5-30/month + reduced maintenance

## Timeline
- Week 1: Database migration
- Week 2: Edge Functions
- Week 3: iOS app updates
- Week 4: Testing & deployment 