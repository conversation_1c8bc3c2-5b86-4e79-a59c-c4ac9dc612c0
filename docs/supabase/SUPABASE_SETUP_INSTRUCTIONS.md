# NIRA Supabase Setup Instructions - Complete Language Learning Platform

## Overview

This guide sets up the complete NIRA language learning database with 16 tables supporting Duolingo-level features including:
- **Multi-language support** (French, Spanish, English, Japanese, Tamil)
- **AI agent personalities** with cultural expertise  
- **Comprehensive learning content** (vocabulary, exercises, lessons)
- **Progress tracking** with spaced repetition
- **Cultural context** and achievements system

## Prerequisites

1. **Node.js** (for Supabase CLI): Install from [nodejs.org](https://nodejs.org/)
2. **Supabase CLI**: 
   ```bash
   npm install -g @supabase/cli
   ```
3. **Supabase Account**: Create at [supabase.com](https://supabase.com)

## Step 1: Create Supabase Project

1. **Create project on Supabase Dashboard**:
   - Go to [app.supabase.com](https://app.supabase.com)
   - Click "New Project"
   - Name: "NIRA-Language-Learning"
   - Choose region (US East recommended)
   - Generate a strong database password

2. **Get project credentials**:
   - Note your **Project URL**: `https://your-project-id.supabase.co`
   - Note your **API Key**: Found in Settings → API
   - Save the **database password** for later use

## Step 2: Deploy Complete Database Schema

Our schema includes 16 comprehensive tables. Apply them using the Supabase SQL editor:

### Core Database Schema (Copy-paste into SQL Editor)

```sql
-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 1. Languages table (5 languages with difficulty levels)
CREATE TABLE languages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    code VARCHAR(10) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    native_name VARCHAR(100) NOT NULL,
    difficulty_level INTEGER DEFAULT 1,
    writing_system VARCHAR(50),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. AI Agents table (3 tutors with personalities)
CREATE TABLE agents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    personality_type VARCHAR(50) NOT NULL,
    personality_traits JSONB NOT NULL,
    expertise_areas TEXT[],
    teaching_style VARCHAR(50),
    avatar_url TEXT,
    background_story TEXT,
    signature_phrases TEXT[],
    supported_languages UUID[] NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. Enhanced Users table
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    native_language_id UUID REFERENCES languages(id),
    target_languages UUID[] DEFAULT '{}',
    preferred_agent_id UUID REFERENCES agents(id),
    learning_goals JSONB,
    proficiency_levels JSONB,
    learning_preferences JSONB,
    timezone VARCHAR(50) DEFAULT 'UTC',
    streak_count INTEGER DEFAULT 0,
    total_xp INTEGER DEFAULT 0,
    is_premium BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Continue with remaining 13 tables...
-- [Full schema available in migrations]
```

### Apply Complete Schema
1. Go to your Supabase project dashboard
2. Navigate to **SQL Editor**
3. Create a new query and paste the complete schema (available in your project)
4. Run the migration to create all 16 tables

## Step 3: Populate Initial Data

### Sample Data Population
```sql
-- Insert 5 languages
INSERT INTO languages (code, name, native_name, difficulty_level, writing_system) VALUES
('en', 'English', 'English', 1, 'latin'),
('fr', 'French', 'Français', 3, 'latin'),
('es', 'Spanish', 'Español', 2, 'latin'),
('ja', 'Japanese', '日本語', 5, 'hiragana'),
('ta', 'Tamil', 'தமிழ்', 4, 'tamil');

-- Insert 3 AI agents with personalities
-- [Full insert statements available in migrations]
```

### Verify Data Population
```sql
-- Check the ecosystem
SELECT 
    l.name as language,
    l.native_name,
    COUNT(DISTINCT a.id) as agent_count,
    COUNT(DISTINCT v.id) as vocabulary_count
FROM languages l
LEFT JOIN agents a ON l.id = ANY(a.supported_languages)  
LEFT JOIN vocabulary v ON l.id = v.language_id
GROUP BY l.id, l.name, l.native_name;
```

## Step 4: Update iOS App Configuration

### 1. Add Supabase Dependency
In Xcode:
- File → Add Package Dependencies
- Add: `https://github.com/supabase/supabase-swift.git`
- Select version `2.5.1` or later

### 2. Configure Supabase Client
```swift
// SupabaseClient.swift
import Supabase

class SupabaseManager: ObservableObject {
    static let shared = SupabaseManager()
    
    let client: SupabaseClient
    
    init() {
        client = SupabaseClient(
            supabaseURL: URL(string: "https://your-project-id.supabase.co")!,
            supabaseKey: "your-anon-key"
        )
    }
}
```

### 3. Environment Configuration
Create `Config.swift`:
```swift
struct Config {
    static let supabaseURL = "https://your-project-id.supabase.co"
    static let supabaseAnonKey = "your-anon-key"
    static let openAIAPIKey = "your-openai-key"
}
```

## Step 5: Update Vapor Server Integration

### 1. Replace Database Models
Update your Vapor models to work with Supabase:

```swift
// Replace existing models with Supabase-compatible versions
struct Agent: Content {
    let id: UUID
    let name: String
    let personalityTraits: [String: Bool]
    let expertiseAreas: [String]
    let teachingStyle: String
    let supportedLanguages: [UUID]
}
```

### 2. Update Service Layer
```swift
// AgentOrchestrationService.swift
class AgentOrchestrationService {
    private let supabaseClient: SupabaseClient
    
    func getAgentProfile(agentId: UUID) async throws -> Agent {
        return try await supabaseClient
            .from("agents")
            .select("*")
            .eq("id", agentId)
            .single()
            .execute()
    }
    
    func getUserLearningContext(userId: UUID) async throws -> LearningContext {
        // Load user progress, vocabulary, analytics
        let progress = try await supabaseClient
            .from("user_progress")
            .select("*, lessons(*)")
            .eq("user_id", userId)
            .execute()
            
        return LearningContext(progress: progress)
    }
}
```

## Step 6: Database Schema Overview

### 16 Tables Created:
1. **`languages`** - 5 languages with difficulty levels
2. **`agents`** - 3 AI tutors with personalities  
3. **`users`** - Enhanced user profiles
4. **`learning_paths`** - Agent-curated learning journeys
5. **`lessons`** - Structured content with cultural focus
6. **`exercises`** - Multi-modal exercise types
7. **`vocabulary`** - Rich word database with pronunciation
8. **`user_progress`** - Granular lesson tracking
9. **`user_vocabulary`** - Spaced repetition system
10. **`cultural_contexts`** - Agent-specific cultural explanations
11. **`user_achievements`** - XP, streaks, badges
12. **`agent_sessions`** - Enhanced conversation sessions
13. **`conversation_turns`** - Detailed AI interaction analysis
14. **`user_learning_analytics`** - Daily learning metrics
15. **`ai_generated_content`** - Dynamic content tracking
16. **`user_session_state`** - Real-time learning adaptation

### Key Features Supported:
- ✅ **Multi-language vocabulary** with pronunciation guides
- ✅ **Cultural context** with regional explanations  
- ✅ **Spaced repetition** vocabulary review system
- ✅ **Agent personalities** with teaching styles
- ✅ **Progress analytics** for personalization
- ✅ **Dynamic content** generation and tracking
- ✅ **Achievement system** with gamification

## Step 7: Testing the Setup

### 1. Verify Database Schema
```sql
-- Check all tables exist
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' 
ORDER BY table_name;

-- Verify relationships
SELECT COUNT(*) as total_relationships 
FROM information_schema.table_constraints 
WHERE constraint_type = 'FOREIGN KEY';
```

### 2. Test Agent Data
```sql
-- View agent personalities
SELECT name, personality_type, teaching_style, expertise_areas 
FROM agents;

-- Check vocabulary with cultural context
SELECT word, pronunciation, cultural_context 
FROM vocabulary v
JOIN languages l ON v.language_id = l.id
WHERE l.code = 'fr';
```

### 3. Test iOS Integration
```swift
// Test basic connection
func testSupabaseConnection() async {
    do {
        let languages = try await SupabaseManager.shared.client
            .from("languages")
            .select("*")
            .execute()
        print("✅ Connected to Supabase: \(languages.count) languages loaded")
    } catch {
        print("❌ Supabase connection failed: \(error)")
    }
}
```

## Step 8: Production Deployment

### 1. Environment Variables
Set in Supabase dashboard under Settings → Edge Functions:
```bash
OPENAI_API_KEY=your_openai_api_key_here
```

### 2. RLS Policies (Row Level Security)
The schema includes appropriate RLS policies for:
- User data privacy
- Agent access control  
- Learning content protection

### 3. Performance Optimization
Indexes are automatically created for:
- User progress queries
- Vocabulary reviews
- Conversation history
- Language filtering

## Troubleshooting

### Common Issues:
1. **"Table does not exist"**: Ensure all 16 tables were created successfully
2. **"Permission denied"**: Check RLS policies are correctly applied
3. **"Foreign key violation"**: Verify language and agent data was inserted first

### Debug Commands:
```sql
-- Check table creation
\dt

-- Verify data population  
SELECT COUNT(*) FROM languages;
SELECT COUNT(*) FROM agents;
SELECT COUNT(*) FROM vocabulary;
```

## Next Steps

With the database setup complete, you now have:
- ✅ **Comprehensive language learning database**
- ✅ **AI agent personality system** 
- ✅ **Progress tracking and analytics**
- ✅ **Cultural context integration**
- ✅ **Spaced repetition for vocabulary**
- ✅ **Achievement and gamification system**

Ready to build the next generation of AI-powered language learning! 🚀 