import Foundation
import SwiftUI

// MARK: - Core Data Models Aligned with Backend Strategy

// MARK: - User Identity & Privacy Domain

struct User: Identifiable, Codable {
    let id: UUID
    let emailHash: String
    let authProvider: AuthProvider
    let createdAt: Date
    let updatedAt: Date
    let privacySettings: PrivacySettings
    let dataRetentionDays: Int
    let accountStatus: AccountStatus
    
    enum AuthProvider: String, Codable, CaseIterable {
        case local, google, apple, facebook, microsoft
        
        var displayName: String {
            switch self {
            case .local: return "Email"
            case .google: return "Google"
            case .apple: return "Apple"
            case .facebook: return "Facebook"
            case .microsoft: return "Microsoft"
            }
        }
        
        var icon: String {
            switch self {
            case .local: return "envelope.circle.fill"
            case .google: return "g.circle.fill"
            case .apple: return "apple.logo"
            case .facebook: return "f.circle.fill"
            case .microsoft: return "m.circle.fill"
            }
        }
    }
    
    enum AccountStatus: String, Codable, CaseIterable {
        case active, suspended, pendingVerification = "pending_verification", deactivated, deleted
        
        var displayName: String {
            switch self {
            case .active: return "Active"
            case .suspended: return "Suspended"
            case .pendingVerification: return "Pending Verification"
            case .deactivated: return "Deactivated"
            case .deleted: return "Deleted"
            }
        }
        
        var color: Color {
            switch self {
            case .active: return .green
            case .suspended: return .orange
            case .pendingVerification: return .blue
            case .deactivated: return .gray
            case .deleted: return .red
            }
        }
    }
}

struct UserProfile: Identifiable, Codable {
    let userId: UUID
    var displayName: String?
    let nativeLanguage: String
    var targetLanguages: [String]
    var learningGoals: [LearningGoal]
    var proficiencyLevels: [String: CEFRLevel]
    var timezone: String
    var preferredLessonLength: TimeInterval
    var dailyGoalMinutes: Int
    var streakFreezeCount: Int
    var personalityPreferences: PersonalityPreferences
    var culturalBackground: String?
    let createdAt: Date
    let updatedAt: Date
    
    var id: UUID { userId }
    
    enum LearningGoal: String, Codable, CaseIterable {
        case conversational, business, academic, travel, culture
        
        var displayName: String {
            switch self {
            case .conversational: return "Conversational"
            case .business: return "Business"
            case .academic: return "Academic"
            case .travel: return "Travel"
            case .culture: return "Cultural Understanding"
            }
        }
        
        var icon: String {
            switch self {
            case .conversational: return "bubble.left.and.bubble.right.fill"
            case .business: return "briefcase.fill"
            case .academic: return "graduationcap.fill"
            case .travel: return "airplane"
            case .culture: return "globe"
            }
        }
    }
}

struct PersonalityPreferences: Codable {
    var patience: PatientLevel = .medium
    var correctionStyle: CorrectionStyle = .gentle
    var encouragementLevel: EncouragementLevel = .medium
    var culturalCuriosity: CulturalCuriosity = .high
    
    enum PatientLevel: String, Codable, CaseIterable {
        case low, medium, high
        
        var displayName: String {
            switch self {
            case .low: return "Fast-paced"
            case .medium: return "Balanced"
            case .high: return "Patient & thorough"
            }
        }
    }
    
    enum CorrectionStyle: String, Codable, CaseIterable {
        case gentle, direct, encouraging
        
        var displayName: String {
            switch self {
            case .gentle: return "Gentle corrections"
            case .direct: return "Direct feedback"
            case .encouraging: return "Encouraging guidance"
            }
        }
    }
    
    enum EncouragementLevel: String, Codable, CaseIterable {
        case minimal, medium, high
        
        var displayName: String {
            switch self {
            case .minimal: return "Minimal encouragement"
            case .medium: return "Balanced feedback"
            case .high: return "High encouragement"
            }
        }
    }
    
    enum CulturalCuriosity: String, Codable, CaseIterable {
        case low, medium, high
        
        var displayName: String {
            switch self {
            case .low: return "Focus on language"
            case .medium: return "Some cultural context"
            case .high: return "Rich cultural immersion"
            }
        }
    }
}

struct PrivacySettings: Codable {
    var dataSharing: Bool = false
    var analytics: Bool = false
    var conversationRecording: Bool = false
    var voiceDataTraining: Bool = false
    var personalizationAnalytics: Bool = true
    var marketingCommunications: Bool = false
    var aiTrainingConsent: Bool = false
    
    var conversationRetentionDays: Int = 365
    var voiceDataRetentionDays: Int = 90
}

// MARK: - Language & Content Domain

struct Language: Identifiable, Codable {
    let code: String
    let name: String
    let nativeName: String
    let script: ScriptType
    let textDirection: TextDirection
    let complexityScore: Int
    let culturalRegions: [String]
    let aiModelSupport: AIModelSupport
    let speakerPopulation: Int64?
    let isActive: Bool
    let createdAt: Date
    
    var id: String { code }
    
    enum ScriptType: String, Codable, CaseIterable {
        case latin, cyrillic, arabic, chinese, japanese, korean, devanagari, thai, hebrew
        
        var displayName: String {
            switch self {
            case .latin: return "Latin"
            case .cyrillic: return "Cyrillic"
            case .arabic: return "Arabic"
            case .chinese: return "Chinese"
            case .japanese: return "Japanese"
            case .korean: return "Korean"
            case .devanagari: return "Devanagari"
            case .thai: return "Thai"
            case .hebrew: return "Hebrew"
            }
        }
    }
    
    enum TextDirection: String, Codable {
        case ltr, rtl, ttb
        
        var alignment: TextAlignment {
            switch self {
            case .ltr: return .leading
            case .rtl: return .trailing
            case .ttb: return .center
            }
        }
    }
    
    struct AIModelSupport: Codable {
        let gemini: Bool
        let speech: Bool
        let translation: Bool
        let culturalContext: Bool
        
        var supportLevel: Double {
            let features = [gemini, speech, translation, culturalContext]
            return Double(features.filter { $0 }.count) / Double(features.count)
        }
    }
    
    var difficultyColor: Color {
        switch complexityScore {
        case 1...3: return .green
        case 4...6: return .orange
        case 7...8: return .red
        case 9...10: return .purple
        default: return .gray
        }
    }
    
    var speakerPopulationFormatted: String {
        guard let population = speakerPopulation else { return "Unknown" }
        
        if population >= 1_000_000_000 {
            return String(format: "%.1fB speakers", Double(population) / 1_000_000_000)
        } else if population >= 1_000_000 {
            return String(format: "%.1fM speakers", Double(population) / 1_000_000)
        } else if population >= 1_000 {
            return String(format: "%.1fK speakers", Double(population) / 1_000)
        } else {
            return "\(population) speakers"
        }
    }
}

struct CEFRLevel: Codable, CaseIterable, Comparable {
    static let allCases: [CEFRLevel] = [.A1, .A2, .B1, .B2, .C1, .C2]
    
    let rawValue: String
    
    static let A1 = CEFRLevel(rawValue: "A1")
    static let A2 = CEFRLevel(rawValue: "A2")
    static let B1 = CEFRLevel(rawValue: "B1")
    static let B2 = CEFRLevel(rawValue: "B2")
    static let C1 = CEFRLevel(rawValue: "C1")
    static let C2 = CEFRLevel(rawValue: "C2")
    
    var displayName: String {
        switch rawValue {
        case "A1": return "Beginner"
        case "A2": return "Elementary"
        case "B1": return "Intermediate"
        case "B2": return "Upper Intermediate"
        case "C1": return "Advanced"
        case "C2": return "Proficient"
        default: return rawValue
        }
    }
    
    var description: String {
        switch rawValue {
        case "A1": return "Can understand and use familiar everyday expressions"
        case "A2": return "Can communicate in simple and routine tasks"
        case "B1": return "Can deal with most situations in areas where the language is spoken"
        case "B2": return "Can interact with a degree of fluency and spontaneity"
        case "C1": return "Can use language flexibly and effectively"
        case "C2": return "Can understand virtually everything with ease"
        default: return "Unknown level"
        }
    }
    
    var color: Color {
        switch rawValue {
        case "A1", "A2": return .green
        case "B1", "B2": return .blue
        case "C1", "C2": return .purple
        default: return .gray
        }
    }
    
    var progressValue: Double {
        switch rawValue {
        case "A1": return 1.0 / 6.0
        case "A2": return 2.0 / 6.0
        case "B1": return 3.0 / 6.0
        case "B2": return 4.0 / 6.0
        case "C1": return 5.0 / 6.0
        case "C2": return 6.0 / 6.0
        default: return 0.0
        }
    }
    
    static func < (lhs: CEFRLevel, rhs: CEFRLevel) -> Bool {
        return lhs.progressValue < rhs.progressValue
    }
}

struct LessonContent: Identifiable, Codable {
    let id: UUID
    let languageCode: String
    let contentTypeId: UUID
    let level: CEFRLevel
    let topic: String
    let title: [String: String] // Multilingual titles
    let description: [String: String]?
    let contentText: String?
    let filePath: String?
    let fileSizeBytes: Int64?
    let durationSeconds: Int?
    let checksum: String?
    let metadata: ContentMetadata
    let semanticTags: [String]
    let culturalContext: CulturalContext
    let spacedRepetitionInterval: TimeInterval
    let aiEmbeddingsId: UUID?
    let isPublished: Bool
    let createdBy: UUID?
    let createdAt: Date
    let updatedAt: Date
    
    struct ContentMetadata: Codable {
        let difficulty: Double
        let prerequisites: [UUID]
        let estimatedTime: TimeInterval
        let interactionTypes: [InteractionType]
        let skillsFocused: [LanguageSkill]
        
        enum InteractionType: String, Codable, CaseIterable {
            case reading, listening, speaking, writing, interactive, video, audio, quiz
            
            var icon: String {
                switch self {
                case .reading: return "book.fill"
                case .listening: return "ear.fill"
                case .speaking: return "mic.fill"
                case .writing: return "pencil"
                case .interactive: return "hand.tap.fill"
                case .video: return "video.fill"
                case .audio: return "speaker.wave.2.fill"
                case .quiz: return "questionmark.circle.fill"
                }
            }
        }
        
        enum LanguageSkill: String, Codable, CaseIterable {
            case speaking, listening, reading, writing, vocabulary, grammar, pronunciation, culture
            
            var displayName: String {
                switch self {
                case .speaking: return "Speaking"
                case .listening: return "Listening"
                case .reading: return "Reading"
                case .writing: return "Writing"
                case .vocabulary: return "Vocabulary"
                case .grammar: return "Grammar"
                case .pronunciation: return "Pronunciation"
                case .culture: return "Cultural Understanding"
                }
            }
            
            var icon: String {
                switch self {
                case .speaking: return "mic.fill"
                case .listening: return "ear.fill"
                case .reading: return "book.fill"
                case .writing: return "pencil"
                case .vocabulary: return "textformat.abc"
                case .grammar: return "textformat.size"
                case .pronunciation: return "waveform"
                case .culture: return "globe"
                }
            }
            
            var color: Color {
                switch self {
                case .speaking: return .red
                case .listening: return .blue
                case .reading: return .green
                case .writing: return .purple
                case .vocabulary: return .orange
                case .grammar: return .pink
                case .pronunciation: return .yellow
                case .culture: return .indigo
                }
            }
        }
    }
    
    struct CulturalContext: Codable {
        let region: String?
        let formality: FormalityLevel?
        let context: [CulturalNote]
        
        enum FormalityLevel: String, Codable {
            case informal, neutral, formal, veryFormal = "very_formal"
            
            var displayName: String {
                switch self {
                case .informal: return "Informal"
                case .neutral: return "Neutral"
                case .formal: return "Formal"
                case .veryFormal: return "Very Formal"
                }
            }
        }
        
        struct CulturalNote: Codable {
            let category: String
            let note: String
            let importance: ImportanceLevel
            
            enum ImportanceLevel: String, Codable {
                case low, medium, high, critical
                
                var color: Color {
                    switch self {
                    case .low: return .gray
                    case .medium: return .blue
                    case .high: return .orange
                    case .critical: return .red
                    }
                }
            }
        }
    }
    
    var formattedDuration: String {
        guard let duration = durationSeconds else { return "Unknown duration" }
        
        let minutes = duration / 60
        let seconds = duration % 60
        
        if minutes > 0 {
            return seconds > 0 ? "\(minutes)m \(seconds)s" : "\(minutes)m"
        } else {
            return "\(seconds)s"
        }
    }
    
    var fileSizeFormatted: String {
        guard let size = fileSizeBytes else { return "Unknown size" }
        
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useKB, .useMB, .useGB]
        formatter.countStyle = .file
        return formatter.string(fromByteCount: size)
    }
}

// MARK: - Learning Progress & Analytics

struct UserProgress: Identifiable, Codable {
    let id: UUID
    let userId: UUID
    let languageCode: String
    var currentCEFRLevel: CEFRLevel
    var skillScores: SkillScores
    var lessonsCompleted: Int
    var conversationMinutes: Double
    var wordsLearned: Int
    var currentStreakDays: Int
    var longestStreakDays: Int
    var lastActivityAt: Date?
    var weeklyGoalMinutes: Int
    var weeklyProgressMinutes: Double
    var learningVelocity: Double
    var retentionRate: Double
    var engagementScore: Double
    var predictedNextLevelDate: Date?
    var personalizedDifficulty: Double
    let createdAt: Date
    let updatedAt: Date
    
    struct SkillScores: Codable {
        var speaking: Double = 0.0
        var listening: Double = 0.0
        var reading: Double = 0.0
        var writing: Double = 0.0
        var vocabulary: Double = 0.0
        var grammar: Double = 0.0
        var pronunciation: Double = 0.0
        var culture: Double = 0.0
        
        var overall: Double {
            let scores = [speaking, listening, reading, writing, vocabulary, grammar, pronunciation, culture]
            return scores.reduce(0, +) / Double(scores.count)
        }
        
        var skillArray: [(skill: LessonContent.ContentMetadata.LanguageSkill, score: Double)] {
            [
                (.speaking, speaking),
                (.listening, listening),
                (.reading, reading),
                (.writing, writing),
                (.vocabulary, vocabulary),
                (.grammar, grammar),
                (.pronunciation, pronunciation),
                (.culture, culture)
            ]
        }
    }
    
    var weeklyProgress: Double {
        weeklyGoalMinutes > 0 ? min(1.0, weeklyProgressMinutes / Double(weeklyGoalMinutes)) : 0.0
    }
    
    var streakStatus: StreakStatus {
        switch currentStreakDays {
        case 0: return .none
        case 1...6: return .building
        case 7...29: return .strong
        case 30...99: return .impressive
        default: return .legendary
        }
    }
    
    enum StreakStatus {
        case none, building, strong, impressive, legendary
        
        var displayName: String {
            switch self {
            case .none: return "No streak"
            case .building: return "Building streak"
            case .strong: return "Strong streak"
            case .impressive: return "Impressive streak!"
            case .legendary: return "Legendary streak! 🔥"
            }
        }
        
        var color: Color {
            switch self {
            case .none: return .gray
            case .building: return .blue
            case .strong: return .green
            case .impressive: return .orange
            case .legendary: return .red
            }
        }
        
        var emoji: String {
            switch self {
            case .none: return ""
            case .building: return "💪"
            case .strong: return "🔥"
            case .impressive: return "⚡"
            case .legendary: return "🏆"
            }
        }
    }
}

// MARK: - AI Agent & Conversation Domain

struct AgentSession: Identifiable, Codable {
    let id: UUID
    let userId: UUID
    let agentPersonalities: [String]
    let languageCode: String
    let sessionType: SessionType
    var conversationContext: ConversationContext
    var userProficiencySnapshot: UserProficiencySnapshot
    var culturalFocus: CulturalFocus
    var learningObjectives: [String]
    var sessionQualityMetrics: SessionQualityMetrics
    let startedAt: Date
    var endedAt: Date?
    var totalTurns: Int
    var userSatisfactionScore: Int?
    var aiConfidenceAverage: Double?
    let lessonId: UUID?
    var isVoiceEnabled: Bool
    var sessionNotes: String?
    let createdAt: Date
    
    enum SessionType: String, Codable, CaseIterable {
        case lesson, freeChat = "free_chat", assessment, culturalExploration = "cultural_exploration", pronunciationPractice = "pronunciation_practice", vocabularyReview = "vocabulary_review"
        
        var displayName: String {
            switch self {
            case .lesson: return "Lesson"
            case .freeChat: return "Free Chat"
            case .assessment: return "Assessment"
            case .culturalExploration: return "Cultural Exploration"
            case .pronunciationPractice: return "Pronunciation Practice"
            case .vocabularyReview: return "Vocabulary Review"
            }
        }
        
        var icon: String {
            switch self {
            case .lesson: return "book.fill"
            case .freeChat: return "bubble.left.and.bubble.right.fill"
            case .assessment: return "checkmark.circle.fill"
            case .culturalExploration: return "globe"
            case .pronunciationPractice: return "waveform"
            case .vocabularyReview: return "textformat.abc"
            }
        }
        
        var color: Color {
            switch self {
            case .lesson: return .blue
            case .freeChat: return .green
            case .assessment: return .orange
            case .culturalExploration: return .purple
            case .pronunciationPractice: return .red
            case .vocabularyReview: return .indigo
            }
        }
    }
    
    struct ConversationContext: Codable {
        var topics: [String]
        var mood: ConversationMood
        var complexity: Double
        var culturalReferences: [String]
        var recentMistakes: [String]
        var strengthsObserved: [String]
        
        enum ConversationMood: String, Codable {
            case formal, casual, playful, serious, encouraging, challenging
            
            var displayName: String {
                switch self {
                case .formal: return "Formal"
                case .casual: return "Casual"
                case .playful: return "Playful"
                case .serious: return "Serious"
                case .encouraging: return "Encouraging"
                case .challenging: return "Challenging"
                }
            }
        }
    }
    
    struct UserProficiencySnapshot: Codable {
        let overallLevel: CEFRLevel
        let skillBreakdown: UserProgress.SkillScores
        let estimatedAt: Date
        let confidence: Double
    }
    
    struct CulturalFocus: Codable {
        let regions: [String]
        let topics: [CulturalTopic]
        let formality: LessonContent.CulturalContext.FormalityLevel
        
        enum CulturalTopic: String, Codable, CaseIterable {
            case greetings, business, food, family, holidays, traditions, etiquette, history, arts, sports
            
            var displayName: String {
                switch self {
                case .greetings: return "Greetings & Introductions"
                case .business: return "Business Culture"
                case .food: return "Food & Dining"
                case .family: return "Family & Relationships"
                case .holidays: return "Holidays & Celebrations"
                case .traditions: return "Traditions & Customs"
                case .etiquette: return "Social Etiquette"
                case .history: return "History & Heritage"
                case .arts: return "Arts & Literature"
                case .sports: return "Sports & Recreation"
                }
            }
            
            var icon: String {
                switch self {
                case .greetings: return "hand.wave.fill"
                case .business: return "briefcase.fill"
                case .food: return "fork.knife"
                case .family: return "person.3.fill"
                case .holidays: return "gift.fill"
                case .traditions: return "star.fill"
                case .etiquette: return "heart.fill"
                case .history: return "clock.fill"
                case .arts: return "paintbrush.fill"
                case .sports: return "sportscourt.fill"
                }
            }
        }
    }
    
    struct SessionQualityMetrics: Codable {
        var responseRelevance: Double = 0.0
        var culturalAccuracy: Double = 0.0
        var grammarCorrections: Int = 0
        var vocabularyIntroduced: Int = 0
        var userEngagement: Double = 0.0
        var conversationFlow: Double = 0.0
        
        var overallQuality: Double {
            (responseRelevance + culturalAccuracy + userEngagement + conversationFlow) / 4.0
        }
    }
    
    var duration: TimeInterval? {
        guard let endedAt = endedAt else { return nil }
        return endedAt.timeIntervalSince(startedAt)
    }
    
    var formattedDuration: String {
        guard let duration = duration else { return "Ongoing" }
        
        let minutes = Int(duration) / 60
        let seconds = Int(duration) % 60
        
        if minutes > 0 {
            return "\(minutes)m \(seconds)s"
        } else {
            return "\(seconds)s"
        }
    }
    
    var isActive: Bool {
        endedAt == nil
    }
}

struct ConversationTurn: Identifiable, Codable {
    let id: UUID
    let sessionId: UUID
    let turnNumber: Int
    let speakerType: SpeakerType
    let speakerId: String?
    let messageText: String
    let messageAudioPath: String?
    let languageDetected: String?
    let translationText: String?
    var sentimentAnalysis: SentimentAnalysis
    var grammarAnalysis: GrammarAnalysis
    var vocabularyAnalysis: VocabularyAnalysis
    var culturalNotes: [CulturalNote]
    var aiProcessingMetadata: AIProcessingMetadata
    var responseTimeMs: Int?
    var aiConfidenceScore: Double?
    var userFeedback: FeedbackType?
    var spacedRepetitionTriggers: [UUID]
    let timestamp: Date
    
    enum SpeakerType: String, Codable {
        case user, agent, system
        
        var displayName: String {
            switch self {
            case .user: return "You"
            case .agent: return "Assistant"
            case .system: return "System"
            }
        }
        
        var color: Color {
            switch self {
            case .user: return .blue
            case .agent: return .green
            case .system: return .gray
            }
        }
    }
    
    struct SentimentAnalysis: Codable {
        let emotion: Emotion
        let confidence: Double
        let engagement: Double
        let frustration: Double
        
        enum Emotion: String, Codable {
            case positive, neutral, negative, confused, excited, frustrated
            
            var emoji: String {
                switch self {
                case .positive: return "😊"
                case .neutral: return "😐"
                case .negative: return "😔"
                case .confused: return "😕"
                case .excited: return "😄"
                case .frustrated: return "😤"
                }
            }
            
            var color: Color {
                switch self {
                case .positive, .excited: return .green
                case .neutral: return .blue
                case .negative, .frustrated: return .red
                case .confused: return .orange
                }
            }
        }
    }
    
    struct GrammarAnalysis: Codable {
        let errors: [GrammarError]
        let suggestions: [GrammarSuggestion]
        let accuracy: Double
        
        struct GrammarError: Codable {
            let type: ErrorType
            let position: Range<Int>
            let description: String
            let correction: String
            let severity: Severity
            
            enum ErrorType: String, Codable {
                case verb, noun, adjective, preposition, syntax, punctuation
                
                var displayName: String {
                    switch self {
                    case .verb: return "Verb"
                    case .noun: return "Noun"
                    case .adjective: return "Adjective"
                    case .preposition: return "Preposition"
                    case .syntax: return "Syntax"
                    case .punctuation: return "Punctuation"
                    }
                }
            }
            
            enum Severity: String, Codable {
                case minor, moderate, major
                
                var color: Color {
                    switch self {
                    case .minor: return .yellow
                    case .moderate: return .orange
                    case .major: return .red
                    }
                }
            }
        }
        
        struct GrammarSuggestion: Codable {
            let type: SuggestionType
            let text: String
            let explanation: String
            
            enum SuggestionType: String, Codable {
                case alternative, enhancement, style, formality
            }
        }
    }
    
    struct VocabularyAnalysis: Codable {
        let newWords: [NewWord]
        let difficultyLevel: Double
        let complexity: VocabularyComplexity
        let recommendations: [VocabularyRecommendation]
        
        struct NewWord: Codable {
            let word: String
            let definition: String
            let difficulty: Double
            let frequency: WordFrequency
            let shouldReview: Bool
            
            enum WordFrequency: String, Codable {
                case veryCommon, common, uncommon, rare
                
                var color: Color {
                    switch self {
                    case .veryCommon: return .green
                    case .common: return .blue
                    case .uncommon: return .orange
                    case .rare: return .purple
                    }
                }
            }
        }
        
        enum VocabularyComplexity: String, Codable {
            case basic, intermediate, advanced, expert
            
            var color: Color {
                switch self {
                case .basic: return .green
                case .intermediate: return .blue
                case .advanced: return .orange
                case .expert: return .red
                }
            }
        }
        
        struct VocabularyRecommendation: Codable {
            let type: RecommendationType
            let words: [String]
            let reason: String
            
            enum RecommendationType: String, Codable {
                case synonym, antonym, related, cultural
            }
        }
    }
    
    struct CulturalNote: Codable {
        let category: String
        let note: String
        let importance: LessonContent.CulturalContext.CulturalNote.ImportanceLevel
        let region: String?
    }
    
    struct AIProcessingMetadata: Codable {
        let modelVersion: String
        let tokensUsed: Int
        let processingTimeMs: Int
        let confidence: Double
        let features: [ProcessingFeature]
        
        enum ProcessingFeature: String, Codable {
            case sentimentAnalysis, grammarCheck, vocabularyAnalysis, culturalContext, translation
        }
    }
    
    enum FeedbackType: String, Codable {
        case helpful, unhelpful, incorrect, inappropriate, excellent, needsImprovement = "needs_improvement"
        
        var displayName: String {
            switch self {
            case .helpful: return "Helpful"
            case .unhelpful: return "Not helpful"
            case .incorrect: return "Incorrect"
            case .inappropriate: return "Inappropriate"
            case .excellent: return "Excellent"
            case .needsImprovement: return "Needs improvement"
            }
        }
        
        var color: Color {
            switch self {
            case .helpful, .excellent: return .green
            case .unhelpful, .needsImprovement: return .orange
            case .incorrect, .inappropriate: return .red
            }
        }
    }
    
    var isFromUser: Bool {
        speakerType == .user
    }
    
    var isFromAgent: Bool {
        speakerType == .agent
    }
}

// MARK: - Mobile & Offline Support

struct OfflineContentPackage: Identifiable, Codable {
    let id: UUID
    let userId: UUID
    let languageCode: String
    let packageType: PackageType
    let contentManifest: ContentManifest
    let totalSizeMB: Double
    var downloadStatus: DownloadStatus
    var downloadProgress: Double
    let createdAt: Date
    var downloadedAt: Date?
    var expiresAt: Date?
    var lastAccessedAt: Date?
    var accessCount: Int
    
    enum PackageType: String, Codable, CaseIterable {
        case essential, lessonBundle = "lesson_bundle", conversationCache = "conversation_cache", offlineDictionary = "offline_dictionary", pronunciationGuide = "pronunciation_guide"
        
        var displayName: String {
            switch self {
            case .essential: return "Essential Content"
            case .lessonBundle: return "Lesson Bundle"
            case .conversationCache: return "Conversation Cache"
            case .offlineDictionary: return "Offline Dictionary"
            case .pronunciationGuide: return "Pronunciation Guide"
            }
        }
        
        var icon: String {
            switch self {
            case .essential: return "star.fill"
            case .lessonBundle: return "folder.fill"
            case .conversationCache: return "bubble.left.and.bubble.right.fill"
            case .offlineDictionary: return "book.closed.fill"
            case .pronunciationGuide: return "waveform"
            }
        }
    }
    
    enum DownloadStatus: String, Codable {
        case pending, downloading, completed, failed, paused, cancelled
        
        var displayName: String {
            switch self {
            case .pending: return "Pending"
            case .downloading: return "Downloading"
            case .completed: return "Downloaded"
            case .failed: return "Failed"
            case .paused: return "Paused"
            case .cancelled: return "Cancelled"
            }
        }
        
        var color: Color {
            switch self {
            case .pending: return .blue
            case .downloading: return .orange
            case .completed: return .green
            case .failed: return .red
            case .paused: return .yellow
            case .cancelled: return .gray
            }
        }
        
        var icon: String {
            switch self {
            case .pending: return "clock"
            case .downloading: return "arrow.down.circle"
            case .completed: return "checkmark.circle.fill"
            case .failed: return "xmark.circle.fill"
            case .paused: return "pause.circle.fill"
            case .cancelled: return "stop.circle.fill"
            }
        }
    }
    
    struct ContentManifest: Codable {
        let lessons: [UUID]
        let vocabulary: [UUID]
        let audio: [UUID]
        let images: [UUID]
        let metadata: ManifestMetadata
        
        struct ManifestMetadata: Codable {
            let version: String
            let checksum: String
            let totalItems: Int
            let estimatedTime: TimeInterval
        }
    }
    
    var formattedSize: String {
        if totalSizeMB < 1 {
            return String(format: "%.1f KB", totalSizeMB * 1024)
        } else if totalSizeMB < 1024 {
            return String(format: "%.1f MB", totalSizeMB)
        } else {
            return String(format: "%.2f GB", totalSizeMB / 1024)
        }
    }
    
    var isDownloaded: Bool {
        downloadStatus == .completed
    }
    
    var isDownloading: Bool {
        downloadStatus == .downloading
    }
} 