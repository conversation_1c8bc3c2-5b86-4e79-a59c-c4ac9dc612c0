{"version": 3, "file": "mcp.d.ts", "sourceRoot": "", "sources": ["../../../src/server/mcp.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,YAAY,CAAC;AAEnD,OAAO,EACL,CAAC,EACD,WAAW,EACX,SAAS,EAET,YAAY,EACZ,UAAU,EACV,OAAO,EACP,UAAU,EACV,WAAW,EACZ,MAAM,KAAK,CAAC;AACb,OAAO,EACL,cAAc,EAGd,cAAc,EAOd,QAAQ,EACR,mBAAmB,EAYnB,eAAe,EACf,kBAAkB,EAClB,aAAa,EACb,kBAAkB,EAClB,eAAe,EAChB,MAAM,aAAa,CAAC;AAErB,OAAO,EAAE,WAAW,EAAE,SAAS,EAAE,MAAM,0BAA0B,CAAC;AAClE,OAAO,EAAE,mBAAmB,EAAE,MAAM,uBAAuB,CAAC;AAC5D,OAAO,EAAE,SAAS,EAAE,MAAM,wBAAwB,CAAC;AAEnD;;;;GAIG;AACH,qBAAa,SAAS;IACpB;;OAEG;IACH,SAAgB,MAAM,EAAE,MAAM,CAAC;IAE/B,OAAO,CAAC,oBAAoB,CAA6C;IACzE,OAAO,CAAC,4BAA4B,CAE7B;IACP,OAAO,CAAC,gBAAgB,CAA0C;IAClE,OAAO,CAAC,kBAAkB,CAA4C;gBAE1D,UAAU,EAAE,cAAc,EAAE,OAAO,CAAC,EAAE,aAAa;IAI/D;;;;OAIG;IACG,OAAO,CAAC,SAAS,EAAE,SAAS,GAAG,OAAO,CAAC,IAAI,CAAC;IAIlD;;OAEG;IACG,KAAK,IAAI,OAAO,CAAC,IAAI,CAAC;IAI5B,OAAO,CAAC,wBAAwB,CAAS;IAEzC,OAAO,CAAC,sBAAsB;IA2I9B,OAAO,CAAC,6BAA6B,CAAS;IAE9C,OAAO,CAAC,2BAA2B;YA+BrB,sBAAsB;YAiCtB,wBAAwB;IA+BtC,OAAO,CAAC,4BAA4B,CAAS;IAE7C,OAAO,CAAC,0BAA0B;IA+GlC,OAAO,CAAC,0BAA0B,CAAS;IAE3C,OAAO,CAAC,wBAAwB;IAiFhC;;OAEG;IACH,QAAQ,CAAC,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,YAAY,EAAE,oBAAoB,GAAG,kBAAkB;IAE3F;;OAEG;IACH,QAAQ,CACN,IAAI,EAAE,MAAM,EACZ,GAAG,EAAE,MAAM,EACX,QAAQ,EAAE,gBAAgB,EAC1B,YAAY,EAAE,oBAAoB,GACjC,kBAAkB;IAErB;;OAEG;IACH,QAAQ,CACN,IAAI,EAAE,MAAM,EACZ,QAAQ,EAAE,gBAAgB,EAC1B,YAAY,EAAE,4BAA4B,GACzC,0BAA0B;IAE7B;;OAEG;IACH,QAAQ,CACN,IAAI,EAAE,MAAM,EACZ,QAAQ,EAAE,gBAAgB,EAC1B,QAAQ,EAAE,gBAAgB,EAC1B,YAAY,EAAE,4BAA4B,GACzC,0BAA0B;IA+E7B,OAAO,CAAC,qBAAqB;IAyC7B;;OAEG;IACH,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,EAAE,EAAE,YAAY,GAAG,cAAc;IAEpD;;OAEG;IACH,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,EAAE,EAAE,YAAY,GAAG,cAAc;IAEzE;;;;;;OAMG;IACH,IAAI,CAAC,IAAI,SAAS,WAAW,EAC3B,IAAI,EAAE,MAAM,EACZ,yBAAyB,EAAE,IAAI,GAAG,eAAe,EACjD,EAAE,EAAE,YAAY,CAAC,IAAI,CAAC,GACrB,cAAc;IAEjB;;;;;;;OAOG;IACH,IAAI,CAAC,IAAI,SAAS,WAAW,EAC3B,IAAI,EAAE,MAAM,EACZ,WAAW,EAAE,MAAM,EACnB,yBAAyB,EAAE,IAAI,GAAG,eAAe,EACjD,EAAE,EAAE,YAAY,CAAC,IAAI,CAAC,GACrB,cAAc;IAEjB;;OAEG;IACH,IAAI,CAAC,IAAI,SAAS,WAAW,EAC3B,IAAI,EAAE,MAAM,EACZ,YAAY,EAAE,IAAI,EAClB,WAAW,EAAE,eAAe,EAC5B,EAAE,EAAE,YAAY,CAAC,IAAI,CAAC,GACrB,cAAc;IAEjB;;OAEG;IACH,IAAI,CAAC,IAAI,SAAS,WAAW,EAC3B,IAAI,EAAE,MAAM,EACZ,WAAW,EAAE,MAAM,EACnB,YAAY,EAAE,IAAI,EAClB,WAAW,EAAE,eAAe,EAC5B,EAAE,EAAE,YAAY,CAAC,IAAI,CAAC,GACrB,cAAc;IAmDjB;;OAEG;IACH,YAAY,CAAC,SAAS,SAAS,WAAW,EAAE,UAAU,SAAS,WAAW,EACxE,IAAI,EAAE,MAAM,EACZ,MAAM,EAAE;QACN,WAAW,CAAC,EAAE,MAAM,CAAC;QACrB,WAAW,CAAC,EAAE,SAAS,CAAC;QACxB,YAAY,CAAC,EAAE,UAAU,CAAC;QAC1B,WAAW,CAAC,EAAE,eAAe,CAAC;KAC/B,EACD,EAAE,EAAE,YAAY,CAAC,SAAS,CAAC,GAC1B,cAAc;IAiBjB;;OAEG;IACH,MAAM,CAAC,IAAI,EAAE,MAAM,EAAE,EAAE,EAAE,cAAc,GAAG,gBAAgB;IAE1D;;OAEG;IACH,MAAM,CAAC,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,EAAE,EAAE,cAAc,GAAG,gBAAgB;IAE/E;;OAEG;IACH,MAAM,CAAC,IAAI,SAAS,kBAAkB,EACpC,IAAI,EAAE,MAAM,EACZ,UAAU,EAAE,IAAI,EAChB,EAAE,EAAE,cAAc,CAAC,IAAI,CAAC,GACvB,gBAAgB;IAEnB;;OAEG;IACH,MAAM,CAAC,IAAI,SAAS,kBAAkB,EACpC,IAAI,EAAE,MAAM,EACZ,WAAW,EAAE,MAAM,EACnB,UAAU,EAAE,IAAI,EAChB,EAAE,EAAE,cAAc,CAAC,IAAI,CAAC,GACvB,gBAAgB;IA8CnB;;;OAGG;IACH,WAAW;IAIX;;OAEG;IACH,uBAAuB;IAMvB;;OAEG;IACH,mBAAmB;IAMnB;;OAEG;IACH,qBAAqB;CAKtB;AAED;;GAEG;AACH,MAAM,MAAM,gCAAgC,GAAG,CAC7C,KAAK,EAAE,MAAM,KACV,MAAM,EAAE,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;AAElC;;;GAGG;AACH,qBAAa,gBAAgB;IAKzB,OAAO,CAAC,UAAU;IAJpB,OAAO,CAAC,YAAY,CAAc;gBAGhC,WAAW,EAAE,MAAM,GAAG,WAAW,EACzB,UAAU,EAAE;QAClB;;WAEG;QACH,IAAI,EAAE,qBAAqB,GAAG,SAAS,CAAC;QAExC;;WAEG;QACH,QAAQ,CAAC,EAAE;YACT,CAAC,QAAQ,EAAE,MAAM,GAAG,gCAAgC,CAAC;SACtD,CAAC;KACH;IAQH;;OAEG;IACH,IAAI,WAAW,IAAI,WAAW,CAE7B;IAED;;OAEG;IACH,IAAI,YAAY,IAAI,qBAAqB,GAAG,SAAS,CAEpD;IAED;;OAEG;IACH,gBAAgB,CACd,QAAQ,EAAE,MAAM,GACf,gCAAgC,GAAG,SAAS;CAGhD;AAED;;;;;;;;;GASG;AACH,MAAM,MAAM,YAAY,CAAC,IAAI,SAAS,SAAS,GAAG,WAAW,GAAG,SAAS,IACvE,IAAI,SAAS,WAAW,GACtB,CACA,IAAI,EAAE,CAAC,CAAC,gBAAgB,CAAC,IAAI,EAAE,UAAU,CAAC,EAC1C,KAAK,EAAE,mBAAmB,CAAC,aAAa,EAAE,kBAAkB,CAAC,KAC1D,cAAc,GAAG,OAAO,CAAC,cAAc,CAAC,GAC3C,CAAC,KAAK,EAAE,mBAAmB,CAAC,aAAa,EAAE,kBAAkB,CAAC,KAAK,cAAc,GAAG,OAAO,CAAC,cAAc,CAAC,CAAC;AAEhH,MAAM,MAAM,cAAc,GAAG;IAC3B,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,WAAW,CAAC,EAAE,YAAY,CAAC;IAC3B,YAAY,CAAC,EAAE,YAAY,CAAC;IAC5B,WAAW,CAAC,EAAE,eAAe,CAAC;IAC9B,QAAQ,EAAE,YAAY,CAAC,SAAS,GAAG,WAAW,CAAC,CAAC;IAChD,OAAO,EAAE,OAAO,CAAC;IACjB,MAAM,IAAI,IAAI,CAAC;IACf,OAAO,IAAI,IAAI,CAAC;IAChB,MAAM,CAAC,SAAS,SAAS,WAAW,EAAE,UAAU,SAAS,WAAW,EAClE,OAAO,EAAE;QACP,IAAI,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;QACrB,WAAW,CAAC,EAAE,MAAM,CAAC;QACrB,YAAY,CAAC,EAAE,SAAS,CAAC;QACzB,YAAY,CAAC,EAAE,UAAU,CAAC;QAC1B,WAAW,CAAC,EAAE,eAAe,CAAC;QAC9B,QAAQ,CAAC,EAAE,YAAY,CAAC,SAAS,CAAC,CAAC;QACnC,OAAO,CAAC,EAAE,OAAO,CAAA;KACpB,GAAG,IAAI,CAAA;IACR,MAAM,IAAI,IAAI,CAAA;CACf,CAAC;AAwBF;;GAEG;AACH,MAAM,MAAM,gBAAgB,GAAG,IAAI,CAAC,QAAQ,EAAE,KAAK,GAAG,MAAM,CAAC,CAAC;AAE9D;;GAEG;AACH,MAAM,MAAM,qBAAqB,GAAG,CAClC,KAAK,EAAE,mBAAmB,CAAC,aAAa,EAAE,kBAAkB,CAAC,KAC1D,mBAAmB,GAAG,OAAO,CAAC,mBAAmB,CAAC,CAAC;AAExD;;GAEG;AACH,MAAM,MAAM,oBAAoB,GAAG,CACjC,GAAG,EAAE,GAAG,EACR,KAAK,EAAE,mBAAmB,CAAC,aAAa,EAAE,kBAAkB,CAAC,KAC1D,kBAAkB,GAAG,OAAO,CAAC,kBAAkB,CAAC,CAAC;AAEtD,MAAM,MAAM,kBAAkB,GAAG;IAC/B,IAAI,EAAE,MAAM,CAAC;IACb,QAAQ,CAAC,EAAE,gBAAgB,CAAC;IAC5B,YAAY,EAAE,oBAAoB,CAAC;IACnC,OAAO,EAAE,OAAO,CAAC;IACjB,MAAM,IAAI,IAAI,CAAC;IACf,OAAO,IAAI,IAAI,CAAC;IAChB,MAAM,CAAC,OAAO,EAAE;QAAE,IAAI,CAAC,EAAE,MAAM,CAAC;QAAC,GAAG,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;QAAC,QAAQ,CAAC,EAAE,gBAAgB,CAAC;QAAC,QAAQ,CAAC,EAAE,oBAAoB,CAAC;QAAC,OAAO,CAAC,EAAE,OAAO,CAAA;KAAE,GAAG,IAAI,CAAA;IAC9I,MAAM,IAAI,IAAI,CAAA;CACf,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,4BAA4B,GAAG,CACzC,GAAG,EAAE,GAAG,EACR,SAAS,EAAE,SAAS,EACpB,KAAK,EAAE,mBAAmB,CAAC,aAAa,EAAE,kBAAkB,CAAC,KAC1D,kBAAkB,GAAG,OAAO,CAAC,kBAAkB,CAAC,CAAC;AAEtD,MAAM,MAAM,0BAA0B,GAAG;IACvC,gBAAgB,EAAE,gBAAgB,CAAC;IACnC,QAAQ,CAAC,EAAE,gBAAgB,CAAC;IAC5B,YAAY,EAAE,4BAA4B,CAAC;IAC3C,OAAO,EAAE,OAAO,CAAC;IACjB,MAAM,IAAI,IAAI,CAAC;IACf,OAAO,IAAI,IAAI,CAAC;IAChB,MAAM,CAAC,OAAO,EAAE;QAAE,IAAI,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;QAAC,QAAQ,CAAC,EAAE,gBAAgB,CAAC;QAAC,QAAQ,CAAC,EAAE,gBAAgB,CAAC;QAAC,QAAQ,CAAC,EAAE,4BAA4B,CAAC;QAAC,OAAO,CAAC,EAAE,OAAO,CAAA;KAAE,GAAG,IAAI,CAAA;IACrK,MAAM,IAAI,IAAI,CAAA;CACf,CAAC;AAEF,KAAK,kBAAkB,GAAG;IACxB,CAAC,CAAC,EAAE,MAAM,GACR,OAAO,CAAC,MAAM,EAAE,UAAU,EAAE,MAAM,CAAC,GACnC,WAAW,CAAC,OAAO,CAAC,MAAM,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC,CAAC;CACpD,CAAC;AAEF,MAAM,MAAM,cAAc,CACxB,IAAI,SAAS,SAAS,GAAG,kBAAkB,GAAG,SAAS,IACrD,IAAI,SAAS,kBAAkB,GAC/B,CACA,IAAI,EAAE,CAAC,CAAC,gBAAgB,CAAC,IAAI,EAAE,UAAU,CAAC,EAC1C,KAAK,EAAE,mBAAmB,CAAC,aAAa,EAAE,kBAAkB,CAAC,KAC1D,eAAe,GAAG,OAAO,CAAC,eAAe,CAAC,GAC7C,CAAC,KAAK,EAAE,mBAAmB,CAAC,aAAa,EAAE,kBAAkB,CAAC,KAAK,eAAe,GAAG,OAAO,CAAC,eAAe,CAAC,CAAC;AAElH,MAAM,MAAM,gBAAgB,GAAG;IAC7B,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,UAAU,CAAC,EAAE,SAAS,CAAC,kBAAkB,CAAC,CAAC;IAC3C,QAAQ,EAAE,cAAc,CAAC,SAAS,GAAG,kBAAkB,CAAC,CAAC;IACzD,OAAO,EAAE,OAAO,CAAC;IACjB,MAAM,IAAI,IAAI,CAAC;IACf,OAAO,IAAI,IAAI,CAAC;IAChB,MAAM,CAAC,IAAI,SAAS,kBAAkB,EAAE,OAAO,EAAE;QAAE,IAAI,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;QAAC,WAAW,CAAC,EAAE,MAAM,CAAC;QAAC,UAAU,CAAC,EAAE,IAAI,CAAC;QAAC,QAAQ,CAAC,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC;QAAC,OAAO,CAAC,EAAE,OAAO,CAAA;KAAE,GAAG,IAAI,CAAA;IAC7K,MAAM,IAAI,IAAI,CAAA;CACf,CAAC"}